const Badge = require('../models/Badge');
const BadgeAward = require('../models/BadgeAward');
const User = require('../models/User');
const Initiative = require('../models/Initiative');
const mongoose = require('mongoose');
const { validateObjectId } = require('../utils/validation');

// Get all badges
exports.getAllBadges = async (req, res) => {
  try {
    const badges = await Badge.find().sort({ category: 1, level: 1 });
    
    // Count how many times each badge has been issued
    const badgesWithCounts = await Promise.all(
      badges.map(async (badge) => {
        const issuedCount = await BadgeAward.countDocuments({ badge: badge._id });
        return {
          ...badge.toObject(),
          issuedCount
        };
      })
    );
    
    res.status(200).json({
      success: true,
      badges: badgesWithCounts
    });
  } catch (error) {
    console.error('Error fetching badges:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch badges',
      error: error.message
    });
  }
};

// Get a single badge by ID
exports.getBadgeById = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!validateObjectId(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid badge ID format'
      });
    }
    
    const badge = await Badge.findById(id);
    
    if (!badge) {
      return res.status(404).json({
        success: false,
        message: 'Badge not found'
      });
    }
    
    // Get count of how many times this badge has been awarded
    const issuedCount = await BadgeAward.countDocuments({ badge: badge._id });
    
    res.status(200).json({
      success: true,
      badge: {
        ...badge.toObject(),
        issuedCount
      }
    });
  } catch (error) {
    console.error('Error fetching badge:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch badge',
      error: error.message
    });
  }
};

// Create a new badge
exports.createBadge = async (req, res) => {
  try {
    const {
      name,
      arabicName,
      description,
      arabicDescription,
      icon,
      color,
      criteria,
      category,
      level
    } = req.body;
    
    // Validate required fields
    if (!name || !arabicName || !description || !arabicDescription || !category) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }
    
    const newBadge = new Badge({
      name,
      arabicName,
      description,
      arabicDescription,
      icon: icon || 'award',
      color: color || '#4CAF50',
      criteria,
      category,
      level: level || 1
    });
    
    await newBadge.save();
    
    res.status(201).json({
      success: true,
      message: 'Badge created successfully',
      badge: newBadge
    });
  } catch (error) {
    console.error('Error creating badge:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create badge',
      error: error.message
    });
  }
};

// Update a badge
exports.updateBadge = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!validateObjectId(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid badge ID format'
      });
    }
    
    const {
      name,
      arabicName,
      description,
      arabicDescription,
      icon,
      color,
      criteria,
      category,
      level
    } = req.body;
    
    const badge = await Badge.findById(id);
    
    if (!badge) {
      return res.status(404).json({
        success: false,
        message: 'Badge not found'
      });
    }
    
    // Update badge fields
    badge.name = name || badge.name;
    badge.arabicName = arabicName || badge.arabicName;
    badge.description = description || badge.description;
    badge.arabicDescription = arabicDescription || badge.arabicDescription;
    badge.icon = icon || badge.icon;
    badge.color = color || badge.color;
    badge.criteria = criteria || badge.criteria;
    badge.category = category || badge.category;
    badge.level = level || badge.level;
    
    await badge.save();
    
    res.status(200).json({
      success: true,
      message: 'Badge updated successfully',
      badge
    });
  } catch (error) {
    console.error('Error updating badge:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update badge',
      error: error.message
    });
  }
};

// Delete a badge
exports.deleteBadge = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!validateObjectId(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid badge ID format'
      });
    }
    
    // Check if badge exists
    const badge = await Badge.findById(id);
    
    if (!badge) {
      return res.status(404).json({
        success: false,
        message: 'Badge not found'
      });
    }
    
    // Check if badge has been awarded to anyone
    const awardCount = await BadgeAward.countDocuments({ badge: id });
    
    if (awardCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete badge that has been awarded to users',
        awardCount
      });
    }
    
    // Delete the badge
    await Badge.findByIdAndDelete(id);
    
    res.status(200).json({
      success: true,
      message: 'Badge deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting badge:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete badge',
      error: error.message
    });
  }
};

// Award a badge to a user
exports.awardBadge = async (req, res) => {
  try {
    const { initiativeId, volunteerId } = req.params;
    const { badgeId, reason } = req.body;
    const awardedBy = req.user.id;
    
    // Validate IDs
    if (!validateObjectId(initiativeId) || !validateObjectId(volunteerId) || !validateObjectId(badgeId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid ID format'
      });
    }
    
    // Check if initiative exists
    const initiative = await Initiative.findById(initiativeId);
    if (!initiative) {
      return res.status(404).json({
        success: false,
        message: 'Initiative not found'
      });
    }
    
    // Check if user is authorized (must be initiative author)
    if (initiative.author.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to award badges for this initiative'
      });
    }
    
    // Check if volunteer exists
    const volunteer = await User.findById(volunteerId);
    if (!volunteer) {
      return res.status(404).json({
        success: false,
        message: 'Volunteer not found'
      });
    }
    
    // Check if badge exists
    const badge = await Badge.findById(badgeId);
    if (!badge) {
      return res.status(404).json({
        success: false,
        message: 'Badge not found'
      });
    }
    
    // Check if badge has already been awarded to this user for this initiative
    const existingAward = await BadgeAward.findOne({
      badge: badgeId,
      user: volunteerId,
      initiative: initiativeId
    });
    
    if (existingAward) {
      return res.status(400).json({
        success: false,
        message: 'This badge has already been awarded to this user for this initiative'
      });
    }
    
    // Create badge award
    const badgeAward = new BadgeAward({
      badge: badgeId,
      user: volunteerId,
      initiative: initiativeId,
      awardedBy,
      reason,
      awardedAt: new Date()
    });
    
    await badgeAward.save();
    
    res.status(201).json({
      success: true,
      message: 'Badge awarded successfully',
      badgeAward
    });
  } catch (error) {
    console.error('Error awarding badge:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to award badge',
      error: error.message
    });
  }
};

// Get badges for a user
exports.getUserBadges = async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!validateObjectId(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID format'
      });
    }
    
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Get all badge awards for this user with populated badge, initiative, and awardedBy fields
    const badgeAwards = await BadgeAward.find({ user: userId })
      .populate('badge')
      .populate('initiative', 'title')
      .populate('awardedBy', 'name')
      .sort({ awardedAt: -1 });
    
    res.status(200).json({
      success: true,
      badgeAwards
    });
  } catch (error) {
    console.error('Error fetching user badges:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user badges',
      error: error.message
    });
  }
};

// Script pour mettre à jour directement les compétences d'un utilisateur dans la base de données
const mongoose = require('mongoose');
const { MongoClient, ObjectId } = require('mongodb');

// Configuration de la connexion MongoDB
const MONGODB_URI = 'mongodb://localhost:27017/initiatives_dz';
const USER_ID = '681396ffa9c3356546496c2b'; // ID de l'utilisateur à mettre à jour

// Compétences à ajouter
const skills = [
  {
    name: "التفكير المنطقي",
    category: "cognitive",
    level: "intermediate"
  },
  {
    name: "التفكير النقدي",
    category: "cognitive",
    level: "intermediate"
  },
  {
    name: "التسويق الرقمي",
    category: "technical",
    level: "intermediate"
  },
  {
    name: "الكتابة التقنية",
    category: "technical",
    level: "intermediate"
  }
];

// Fonction principale
async function updateUserSkills() {
  let client;
  
  try {
    // Connexion à MongoDB
    client = await MongoClient.connect(MONGODB_URI, { useUnifiedTopology: true });
    console.log('Connecté à MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Vérifier si l'utilisateur existe
    const user = await usersCollection.findOne({ _id: new ObjectId(USER_ID) });
    
    if (!user) {
      console.error(`Utilisateur avec l'ID ${USER_ID} non trouvé`);
      return;
    }
    
    console.log('Utilisateur trouvé:', {
      id: user._id,
      name: user.name,
      username: user.username,
      userType: user.userType
    });
    
    // Mettre à jour les compétences de l'utilisateur
    const result = await usersCollection.updateOne(
      { _id: new ObjectId(USER_ID) },
      { $set: { skills: skills } }
    );
    
    if (result.modifiedCount === 1) {
      console.log('Compétences mises à jour avec succès');
      
      // Vérifier la mise à jour
      const updatedUser = await usersCollection.findOne({ _id: new ObjectId(USER_ID) });
      console.log('Compétences mises à jour:', updatedUser.skills);
    } else {
      console.log('Aucune modification effectuée');
    }
  } catch (error) {
    console.error('Erreur lors de la mise à jour des compétences:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('Connexion MongoDB fermée');
    }
  }
}

// Exécuter la fonction principale
updateUserSkills().catch(console.error);

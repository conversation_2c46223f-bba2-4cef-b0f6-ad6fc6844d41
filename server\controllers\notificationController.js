const Notification = require('../models/Notification');
const User = require('../models/User');
const { handleError } = require('../utils/errorHandler');

// Get all notifications for the current user
exports.getUserNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const notifications = await Notification.find({ recipient: userId })
      .sort({ createdAt: -1 })
      .populate('sender', 'name username avatar')
      .populate('initiative', 'title shortDescription mainImage')
      .limit(50);
    
    res.status(200).json({ success: true, data: notifications });
  } catch (error) {
    handleError(res, error);
  }
};

// Get unread notifications count
exports.getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const count = await Notification.countDocuments({ 
      recipient: userId,
      read: false
    });
    
    res.status(200).json({ success: true, count });
  } catch (error) {
    handleError(res, error);
  }
};

// Mark notification as read
exports.markAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    
    const notification = await Notification.findById(notificationId);
    
    if (!notification) {
      return res.status(404).json({ success: false, message: 'Notification not found' });
    }
    
    // Check if the notification belongs to the current user
    if (notification.recipient.toString() !== req.user.id) {
      return res.status(403).json({ success: false, message: 'Not authorized to access this notification' });
    }
    
    notification.read = true;
    await notification.save();
    
    res.status(200).json({ success: true, data: notification });
  } catch (error) {
    handleError(res, error);
  }
};

// Mark all notifications as read
exports.markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    
    await Notification.updateMany(
      { recipient: userId, read: false },
      { read: true }
    );
    
    res.status(200).json({ success: true, message: 'All notifications marked as read' });
  } catch (error) {
    handleError(res, error);
  }
};

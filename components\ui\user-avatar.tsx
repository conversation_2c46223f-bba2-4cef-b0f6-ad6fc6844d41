"use client"

import React from 'react'
import { Avatar, AvatarFallback, AvatarImage } from './avatar'
import { User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface UserAvatarProps {
  src?: string | null
  alt?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function UserAvatar({ src, alt = '', className, size = 'md' }: UserAvatarProps) {
  // Définir les tailles en fonction du paramètre size
  const avatarSize = size === 'sm' ? 'h-8 w-8' : size === 'md' ? 'h-10 w-10' : 'h-12 w-12'
  const iconSize = size === 'sm' ? 'h-4 w-4' : size === 'md' ? 'h-5 w-5' : 'h-6 w-6'
  
  return (
    <Avatar className={cn(avatarSize, className)}>
      <AvatarImage src={src || ''} alt={alt} />
      <AvatarFallback className="bg-gray-200">
        <User className={cn(iconSize, 'text-gray-600')} />
      </AvatarFallback>
    </Avatar>
  )
}

import mongoose, { Document, Schema } from "mongoose"

export interface ISettings extends Document {
  // Add a unique identifier field
  settingsId: string;
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportPhone: string;
  maintenanceMode: boolean;
  allowRegistration: boolean;
  requireEmailVerification: boolean;
  maxUploadSize: number;
  termsAndConditions: string;
  privacyPolicy: string;
  aboutUs: string;
  socialLinks: {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const settingsSchema: Schema = new Schema(
  {
    // Add a unique identifier field with a default value
    settingsId: {
      type: String,
      required: true,
      default: "global",
      unique: true
    },
    siteName: {
      type: String,
      required: true,
      default: "منصة المبادرات"
    },
    siteDescription: {
      type: String,
      default: "منصة لإدارة ومتابعة المبادرات المجتمعية"
    },
    contactEmail: {
      type: String,
      default: "<EMAIL>"
    },
    supportPhone: {
      type: String,
      default: "+213 000 000 000"
    },
    maintenanceMode: {
      type: Boolean,
      default: false
    },
    allowRegistration: {
      type: Boolean,
      default: true
    },
    requireEmailVerification: {
      type: Boolean,
      default: true
    },
    maxUploadSize: {
      type: Number,
      default: 5 // in MB
    },
    termsAndConditions: {
      type: String,
      default: ""
    },
    privacyPolicy: {
      type: String,
      default: ""
    },
    aboutUs: {
      type: String,
      default: ""
    },
    socialLinks: {
      facebook: {
        type: String,
        default: ""
      },
      twitter: {
        type: String,
        default: ""
      },
      instagram: {
        type: String,
        default: ""
      },
      linkedin: {
        type: String,
        default: ""
      }
    }
  },
  {
    timestamps: true
  }
)

// Ensure there's only one settings document
settingsSchema.statics.getSettings = async function() {
  const settings = await this.findOne({ settingsId: "global" })
  if (settings) {
    return settings
  }

  // Create default settings if none exist
  return await this.create({ settingsId: "global" })
}

export const Settings = mongoose.model<ISettings>("Settings", settingsSchema)

export default settingsSchema

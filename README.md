# منصة المبادرات المواطنة الجزائرية

A platform for proposing and implementing community initiatives in Algeria.

## Project Overview

This platform allows citizens to propose, support, and collaborate on community initiatives. It provides a space for civic engagement and community development.

## Features

- User authentication and profile management
- Initiative creation and management
- Commenting and discussion system
- Voting on initiatives
- Progress tracking with milestones
- Community engagement tools
- Administrative dashboard

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Express.js, Node.js, TypeScript
- **Database**: MongoDB
- **Authentication**: JWT (JSON Web Tokens)

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas account)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/initiatives-dz.git
   cd initiatives-dz
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up environment variables:
   - Copy `.env.example` to `.env` for backend configuration
   - Copy `.env.local.example` to `.env.local` for frontend configuration
   - Update the values in these files with your own configuration

4. Set up the database:
   ```
   npm run setup-db
   ```

5. Start the development server:
   ```
   npm run dev:all
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Development

- Frontend code is in the `app/` and `components/` directories
- Backend code is in the `src/` directory
- Database models are in `src/models/`
- API routes are in `src/routes/`

## Available Scripts

- `npm run dev` - Start the Next.js development server
- `npm run server` - Start the Express.js API server
- `npm run dev:all` - Start both frontend and backend servers
- `npm run build` - Build the Next.js application
- `npm run start` - Start the production Next.js server
- `npm run setup-db` - Set up the database with initial data

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Express.js](https://expressjs.com/)
- [MongoDB](https://www.mongodb.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)

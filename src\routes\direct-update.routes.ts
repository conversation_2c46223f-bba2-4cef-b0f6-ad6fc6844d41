import express from "express"
import { authenticate } from "../middleware/auth"
import { MongoClient, ObjectId } from "mongodb"

const router = express.Router()

// Endpoint pour mettre à jour directement les compétences d'un utilisateur
router.post("/", authenticate, async (req, res) => {
  try {
    const { userId, skills } = req.body

    // Vérifier si l'utilisateur est autorisé à mettre à jour ces données
    if (req.user.id !== userId && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "Vous n'êtes pas autorisé à mettre à jour ces données"
      })
    }

    // Vérifier que les compétences sont correctement formatées
    if (!skills || !Array.isArray(skills)) {
      return res.status(400).json({
        success: false,
        message: "Les compétences doivent être un tableau"
      })
    }

    // Vérifier que chaque compétence a les propriétés requises
    const validSkills = skills.map(skill => {
      if (!skill.category) {
        return {
          ...skill,
          category: 'technical' // Catégorie par défaut
        };
      }
      if (!skill.level) {
        return {
          ...skill,
          level: 'intermediate' // Niveau par défaut
        };
      }
      return skill;
    });

    console.log("Mise à jour directe des compétences pour l'utilisateur:", userId);
    console.log("Compétences à mettre à jour:", validSkills);

    // Connexion à MongoDB
    const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz';
    const client = await MongoClient.connect(MONGODB_URI);
    const db = client.db();
    const usersCollection = db.collection('users');

    // Mettre à jour les compétences de l'utilisateur
    const result = await usersCollection.updateOne(
      { _id: new ObjectId(userId) },
      { $set: { skills: validSkills } }
    );

    await client.close();

    if (result.modifiedCount === 1) {
      console.log("Compétences mises à jour avec succès");
      return res.status(200).json({
        success: true,
        message: "Compétences mises à jour avec succès"
      });
    } else {
      console.log("Aucune modification effectuée");
      return res.status(400).json({
        success: false,
        message: "Aucune modification effectuée"
      });
    }
  } catch (error) {
    console.error("Erreur lors de la mise à jour directe des compétences:", error);
    return res.status(500).json({
      success: false,
      message: "Erreur lors de la mise à jour des compétences"
    });
  }
});

export default router;

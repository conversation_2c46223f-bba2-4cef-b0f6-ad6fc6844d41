import type { Request, Response, NextFunction } from "express"
import <PERSON><PERSON> from "joi"
import { createError } from "../../utils/error"

// Validate post creation/update
export const validatePost = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    content: Joi.string().required().min(1).max(2000).messages({
      "string.empty": "Post content is required",
      "string.min": "Post content must be at least 1 character long",
      "string.max": "Post content cannot exceed 2000 characters",
      "any.required": "Post content is required",
    }),
    images: Joi.array().items(Joi.string()).max(10).messages({
      "array.max": "Maximum 10 images allowed",
    }),
    relatedInitiative: Joi.string().allow(null, "").messages({
      "string.base": "Initiative ID must be a string",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

// Validate post comment creation
export const validatePostComment = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    content: Joi.string().required().min(1).max(500).messages({
      "string.empty": "Comment content is required",
      "string.min": "Comment content must be at least 1 character long",
      "string.max": "Comment content cannot exceed 500 characters",
      "any.required": "Comment content is required",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}


"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Loader2, Save } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
// Removed AdminLayout import as it's handled by app/admin/layout.tsx
import { api } from "@/lib/api"

type Settings = {
  siteName: string
  siteDescription: string
  contactEmail: string
  supportPhone: string
  maintenanceMode: boolean
  allowRegistration: boolean
  requireEmailVerification: boolean
  maxUploadSize: number
  termsAndConditions: string
  privacyPolicy: string
  aboutUs: string
  socialLinks: {
    facebook: string
    twitter: string
    instagram: string
    linkedin: string
  }
}

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<Settings>({
    siteName: "منصة المبادرات",
    siteDescription: "منصة لإدارة ومتابعة المبادرات المجتمعية",
    contactEmail: "<EMAIL>",
    supportPhone: "+213 000 000 000",
    maintenanceMode: false,
    allowRegistration: true,
    requireEmailVerification: true,
    maxUploadSize: 5,
    termsAndConditions: "",
    privacyPolicy: "",
    aboutUs: "",
    socialLinks: {
      facebook: "",
      twitter: "",
      instagram: "",
      linkedin: ""
    }
  })

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("general")

  // Fetch settings
  const fetchSettings = async () => {
    setIsLoading(true)
    try {
      const response = await api.get("/api/settings", false) // Changed to false to skip authentication

      if (response && response.success) {
        console.log("Settings response:", response)
        setSettings(response.settings || settings)
        setError(null)
      } else {
        setError("Failed to fetch settings")
      }
    } catch (err) {
      console.error("Error fetching settings:", err)
      setError("An error occurred while fetching data")

      // Use default settings if API fails
      setSettings({
        siteName: "منصة المبادرات",
        siteDescription: "منصة لإدارة ومتابعة المبادرات المجتمعية",
        contactEmail: "<EMAIL>",
        supportPhone: "+213 000 000 000",
        maintenanceMode: false,
        allowRegistration: true,
        requireEmailVerification: true,
        maxUploadSize: 5,
        termsAndConditions: "",
        privacyPolicy: "",
        aboutUs: "",
        socialLinks: {
          facebook: "",
          twitter: "",
          instagram: "",
          linkedin: ""
        }
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Save settings
  const saveSettings = async () => {
    setIsSaving(true)
    setSuccess(null)
    setError(null)

    try {
      const response = await api.put("/api/settings", settings, false) // Changed to false to skip authentication

      if (response && response.success) {
        setSuccess("تم حفظ الإعدادات بنجاح")
        setTimeout(() => setSuccess(null), 3000)

        // Update settings with the response
        if (response.settings) {
          setSettings(response.settings)
        }
      } else {
        setError("Failed to save settings")
      }
    } catch (err) {
      console.error("Error saving settings:", err)
      setError("An error occurred while saving settings")
    } finally {
      setIsSaving(false)
    }
  }

  // Initial data fetch
  useEffect(() => {
    fetchSettings()
  }, [])

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إعدادات النظام</h1>

        <Button
          className="bg-[#0a8754] hover:bg-[#097548]"
          onClick={saveSettings}
          disabled={isSaving}
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 animate-spin ml-2" />
          ) : (
            <Save size={16} className="ml-2" />
          )}
          حفظ الإعدادات
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-green-600" />
            <span className="mr-2">جاري التحميل...</span>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="general">عام</TabsTrigger>
              <TabsTrigger value="security">الأمان</TabsTrigger>
              <TabsTrigger value="content">المحتوى</TabsTrigger>
              <TabsTrigger value="social">التواصل الاجتماعي</TabsTrigger>
            </TabsList>

            <TabsContent value="general">
              <Card>
                <CardHeader>
                  <CardTitle>الإعدادات العامة</CardTitle>
                  <CardDescription>
                    إعدادات عامة للموقع
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="siteName">اسم الموقع</Label>
                      <Input
                        id="siteName"
                        value={settings.siteName}
                        onChange={(e) => setSettings({ ...settings, siteName: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contactEmail">البريد الإلكتروني للتواصل</Label>
                      <Input
                        id="contactEmail"
                        type="email"
                        value={settings.contactEmail}
                        onChange={(e) => setSettings({ ...settings, contactEmail: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="supportPhone">رقم الهاتف للدعم</Label>
                      <Input
                        id="supportPhone"
                        value={settings.supportPhone}
                        onChange={(e) => setSettings({ ...settings, supportPhone: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxUploadSize">الحد الأقصى لحجم الملفات (ميجابايت)</Label>
                      <Input
                        id="maxUploadSize"
                        type="number"
                        value={settings.maxUploadSize}
                        onChange={(e) => setSettings({ ...settings, maxUploadSize: Number(e.target.value) })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="siteDescription">وصف الموقع</Label>
                    <Textarea
                      id="siteDescription"
                      value={settings.siteDescription}
                      onChange={(e) => setSettings({ ...settings, siteDescription: e.target.value })}
                      rows={4}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>إعدادات الأمان</CardTitle>
                  <CardDescription>
                    إعدادات الأمان والخصوصية
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">وضع الصيانة</h3>
                      <p className="text-sm text-gray-500">تفعيل وضع الصيانة للموقع</p>
                    </div>
                    <Switch
                      checked={settings.maintenanceMode}
                      onCheckedChange={(checked) => setSettings({ ...settings, maintenanceMode: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">السماح بالتسجيل</h3>
                      <p className="text-sm text-gray-500">السماح للمستخدمين الجدد بالتسجيل</p>
                    </div>
                    <Switch
                      checked={settings.allowRegistration}
                      onCheckedChange={(checked) => setSettings({ ...settings, allowRegistration: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">التحقق من البريد الإلكتروني</h3>
                      <p className="text-sm text-gray-500">طلب التحقق من البريد الإلكتروني عند التسجيل</p>
                    </div>
                    <Switch
                      checked={settings.requireEmailVerification}
                      onCheckedChange={(checked) => setSettings({ ...settings, requireEmailVerification: checked })}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="content">
              <Card>
                <CardHeader>
                  <CardTitle>إعدادات المحتوى</CardTitle>
                  <CardDescription>
                    إعدادات المحتوى والصفحات الثابتة
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="termsAndConditions">الشروط والأحكام</Label>
                    <Textarea
                      id="termsAndConditions"
                      value={settings.termsAndConditions}
                      onChange={(e) => setSettings({ ...settings, termsAndConditions: e.target.value })}
                      rows={8}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="privacyPolicy">سياسة الخصوصية</Label>
                    <Textarea
                      id="privacyPolicy"
                      value={settings.privacyPolicy}
                      onChange={(e) => setSettings({ ...settings, privacyPolicy: e.target.value })}
                      rows={8}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="aboutUs">من نحن</Label>
                    <Textarea
                      id="aboutUs"
                      value={settings.aboutUs}
                      onChange={(e) => setSettings({ ...settings, aboutUs: e.target.value })}
                      rows={8}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="social">
              <Card>
                <CardHeader>
                  <CardTitle>روابط التواصل الاجتماعي</CardTitle>
                  <CardDescription>
                    روابط حسابات التواصل الاجتماعي للموقع
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="facebook">فيسبوك</Label>
                      <Input
                        id="facebook"
                        value={settings.socialLinks.facebook}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {
                            ...settings.socialLinks,
                            facebook: e.target.value
                          }
                        })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="twitter">تويتر</Label>
                      <Input
                        id="twitter"
                        value={settings.socialLinks.twitter}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {
                            ...settings.socialLinks,
                            twitter: e.target.value
                          }
                        })}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="instagram">انستغرام</Label>
                      <Input
                        id="instagram"
                        value={settings.socialLinks.instagram}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {
                            ...settings.socialLinks,
                            instagram: e.target.value
                          }
                        })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="linkedin">لينكد إن</Label>
                      <Input
                        id="linkedin"
                        value={settings.socialLinks.linkedin}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {
                            ...settings.socialLinks,
                            linkedin: e.target.value
                          }
                        })}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
  )
}

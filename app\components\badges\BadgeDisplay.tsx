"use client"

import { useState } from "react"
import { Badge } from "../../components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "../../components/ui/dialog"
import { Award, Calendar, User, Info } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "../../components/ui/avatar"
import { formatDate } from "../../lib/utils"

interface BadgeAward {
  _id: string
  badge: {
    _id: string
    name: string
    arabicName: string
    description: string
    arabicDescription: string
    icon: string
    color: string
    category: string
    level: number
  }
  initiative: {
    _id: string
    title: string
  }
  issuedBy: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  issuedAt: string
  reason: string
}

interface BadgeDisplayProps {
  badges: BadgeAward[]
  showTitle?: boolean
}

export default function BadgeDisplay({ badges, showTitle = true }: BadgeDisplayProps) {
  const [selectedBadge, setSelectedBadge] = useState<BadgeAward | null>(null)

  if (!badges || badges.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-gray-500">لا توجد شارات بعد</p>
      </div>
    )
  }

  const getBadgeLevelText = (level: number) => {
    switch (level) {
      case 1: return "المستوى الأول"
      case 2: return "المستوى الثاني"
      case 3: return "المستوى الثالث"
      case 4: return "المستوى الرابع"
      case 5: return "المستوى الخامس"
      default: return `المستوى ${level}`
    }
  }

  const getCategoryText = (category: string) => {
    switch (category) {
      case "participation": return "المشاركة"
      case "achievement": return "الإنجاز"
      case "contribution": return "المساهمة"
      case "special": return "خاص"
      case "skill": return "المهارة"
      default: return category
    }
  }

  return (
    <div className="space-y-4">
      {showTitle && <h3 className="text-lg font-semibold mb-2">الشارات</h3>}
      
      <div className="flex flex-wrap gap-3">
        {badges.map((badgeAward) => (
          <div 
            key={badgeAward._id} 
            className="relative cursor-pointer group"
            onClick={() => setSelectedBadge(badgeAward)}
          >
            <div 
              className="w-16 h-16 rounded-full flex items-center justify-center border-2 transition-all group-hover:scale-105"
              style={{ 
                backgroundColor: `${badgeAward.badge.color}20`, 
                borderColor: badgeAward.badge.color 
              }}
            >
              <Award 
                className="h-8 w-8" 
                style={{ color: badgeAward.badge.color }}
              />
            </div>
            <div className="absolute -top-1 -right-1 bg-gray-100 text-xs rounded-full w-5 h-5 flex items-center justify-center border border-gray-300">
              {badgeAward.badge.level}
            </div>
            <div className="opacity-0 group-hover:opacity-100 absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs py-1 px-2 rounded whitespace-nowrap transition-opacity">
              {badgeAward.badge.arabicName}
            </div>
          </div>
        ))}
      </div>

      <Dialog open={!!selectedBadge} onOpenChange={(open) => !open && setSelectedBadge(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center mb-4">شارة {selectedBadge?.badge.arabicName}</DialogTitle>
          </DialogHeader>
          
          <div className="flex flex-col items-center">
            <div 
              className="w-24 h-24 rounded-full flex items-center justify-center border-4 mb-4"
              style={{ 
                backgroundColor: `${selectedBadge?.badge.color}20`, 
                borderColor: selectedBadge?.badge.color 
              }}
            >
              <Award 
                className="h-12 w-12" 
                style={{ color: selectedBadge?.badge.color }}
              />
            </div>
            
            <Badge className="mb-4" style={{ backgroundColor: selectedBadge?.badge.color }}>
              {getCategoryText(selectedBadge?.badge.category || "")} - {getBadgeLevelText(selectedBadge?.badge.level || 1)}
            </Badge>
            
            <DialogDescription className="text-center mb-6">
              {selectedBadge?.badge.arabicDescription}
            </DialogDescription>
            
            <div className="w-full space-y-3 text-sm">
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <Info className="h-4 w-4 text-gray-500" />
                <span className="text-gray-700">{selectedBadge?.reason}</span>
              </div>
              
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-gray-700">تم المنح في {formatDate(selectedBadge?.issuedAt || "")}</span>
              </div>
              
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-gray-700">منحت بواسطة:</span>
                <div className="flex items-center gap-2 mr-auto">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={selectedBadge?.issuedBy.avatar} />
                    <AvatarFallback>{selectedBadge?.issuedBy.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span>{selectedBadge?.issuedBy.name}</span>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

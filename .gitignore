# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build
/dist

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
logs
*.log

# env files
.env*
!.env.example
!.env.local.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# uploads
/uploads
/public/uploads
components/header_*.tsx

# IDE
.idea
.vscode

# OS
.DS_Store
Thumbs.db
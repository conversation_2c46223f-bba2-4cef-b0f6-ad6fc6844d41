import express from "express"
import {
  getCommentsByInitiative,
  createComment,
  updateComment,
  deleteComment,
  likeComment,
  unlikeComment,
  replyToComment,
  reportComment,
} from "../controllers/comment.controller"
import { authenticate } from "../middleware/auth"
import { validateComment, validateCommentUpdate } from "../middleware/validators/comment.validator"

const router = express.Router()

// Public routes
router.get("/initiative/:initiativeId", getCommentsByInitiative)

// Protected routes
router.post("/", authenticate, validateComment, createComment)
router.put("/:id", authenticate, validateCommentUpdate, updateComment)
router.delete("/:id", authenticate, deleteComment)
router.post("/:id/like", authenticate, likeComment)
router.delete("/:id/like", authenticate, unlikeComment)
router.post("/:id/reply", authenticate, validateComment, replyToComment)
router.post("/:id/report", authenticate, reportComment)

export default router


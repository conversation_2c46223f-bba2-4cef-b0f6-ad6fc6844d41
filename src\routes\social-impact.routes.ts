import express from "express"
import { authenticate, adminOnly } from "../middleware/auth"
import {
  getAllSocialImpacts,
  getSocialImpactById,
  createSocialImpact,
  updateSocialImpact,
  addImpactToCategory,
  deleteSocialImpact,
  seedSocialImpacts
} from "../controllers/social-impact.controller"

// Import des fonctions de seed supplémentaires si nécessaire
// import { ... } from "../controllers/social-impact-seed.controller"

const router = express.Router()

// Public routes
router.get("/", getAllSocialImpacts)
router.get("/:id", getSocialImpactById)

// Protected routes (require authentication)
router.use(authenticate)

// Admin routes (require admin role)
router.post("/", adminOnly, createSocialImpact)
router.put("/:id", adminOnly, updateSocialImpact)
router.post("/:id/impacts", adminOnly, addImpactToCategory)
router.delete("/:id", adminOnly, deleteSocialImpact)

// Seed routes (for initial data setup, admin only)
router.post("/seed/initial", adminOnly, seedSocialImpacts)

export default router

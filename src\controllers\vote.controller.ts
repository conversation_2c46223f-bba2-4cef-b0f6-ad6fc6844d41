import { Request, Response, NextFunction } from "express"
import { Initiative, VotingOption, User } from "../models"
import { createError } from "../utils/error"
import { asyncHandler } from "../utils/error"
import mongoose from "mongoose"

/**
 * Get voting options for an initiative
 * @route GET /api/votes/initiative/:initiativeId
 * @access Public
 */
export const getVotingOptionsByInitiative = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiativeId } = req.params

  // Validate initiative ID
  if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
    return next(createError(400, "Invalid initiative ID"))
  }

  // Find initiative
  const initiative = await Initiative.findById(initiativeId)
  if (!initiative) {
    return next(createError(404, "Initiative not found"))
  }

  // Check if voting is enabled
  if (!initiative.isVotingEnabled) {
    return next(createError(400, "Voting is not enabled for this initiative"))
  }

  // Get voting options
  const votingOptions = await VotingOption.find({ initiative: initiativeId })
    .select("title description voteCount")
    .sort({ voteCount: -1 })

  res.status(200).json({
    success: true,
    data: votingOptions,
  })
})

/**
 * Create a voting option for an initiative
 * @route POST /api/votes/options
 * @access Private
 */
export const createVotingOption = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiative, title, description } = req.body
  const userId = req.user.id

  // Validate initiative ID
  if (!mongoose.Types.ObjectId.isValid(initiative)) {
    return next(createError(400, "Invalid initiative ID"))
  }

  // Find initiative
  const initiativeDoc = await Initiative.findById(initiative)
  if (!initiativeDoc) {
    return next(createError(404, "Initiative not found"))
  }

  // Check if user is the author of the initiative
  if (initiativeDoc.author.toString() !== userId && req.user.role !== "admin") {
    return next(createError(403, "You are not authorized to create voting options for this initiative"))
  }

  // Create voting option
  const votingOption = new VotingOption({
    initiative,
    title,
    description,
  })

  await votingOption.save()

  // Add voting option to initiative
  await Initiative.findByIdAndUpdate(initiative, {
    $push: { votingOptions: votingOption._id },
    $set: { isVotingEnabled: true },
  })

  res.status(201).json({
    success: true,
    data: votingOption,
  })
})

/**
 * Vote on an initiative
 * @route POST /api/votes/:initiativeId
 * @access Private
 */
export const voteOnInitiative = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiativeId } = req.params
  const { optionId } = req.body
  const userId = req.user.id

  // Validate IDs
  if (!mongoose.Types.ObjectId.isValid(initiativeId) || !mongoose.Types.ObjectId.isValid(optionId)) {
    return next(createError(400, "Invalid ID format"))
  }

  // Find initiative
  const initiative = await Initiative.findById(initiativeId)
  if (!initiative) {
    return next(createError(404, "Initiative not found"))
  }

  // Check if voting is enabled
  if (!initiative.isVotingEnabled) {
    return next(createError(400, "Voting is not enabled for this initiative"))
  }

  // Check if voting has ended
  if (initiative.votingEndDate && new Date(initiative.votingEndDate) < new Date()) {
    return next(createError(400, "Voting period has ended"))
  }

  // Find voting option
  const votingOption = await VotingOption.findById(optionId)
  if (!votingOption) {
    return next(createError(404, "Voting option not found"))
  }

  // Check if option belongs to the initiative
  if (votingOption.initiative.toString() !== initiativeId) {
    return next(createError(400, "Voting option does not belong to this initiative"))
  }

  // Find user
  const user = await User.findById(userId)
  if (!user) {
    return next(createError(404, "User not found"))
  }

  // Check if user has already voted on this initiative
  const hasVoted = user.votedInitiatives.some(
    (vote: { initiative: mongoose.Types.ObjectId }) => vote.initiative.toString() === initiativeId
  )

  if (hasVoted) {
    return next(createError(400, "You have already voted on this initiative"))
  }

  // Add vote
  await VotingOption.findByIdAndUpdate(optionId, {
    $push: { votes: userId },
    $inc: { voteCount: 1 },
  })

  // Update user's voted initiatives
  await User.findByIdAndUpdate(userId, {
    $push: {
      votedInitiatives: {
        initiative: initiativeId,
        option: optionId,
        date: new Date(),
      },
    },
  })

  res.status(200).json({
    success: true,
    message: "Vote recorded successfully",
  })
})

/**
 * Get vote results for an initiative
 * @route GET /api/votes/results/:initiativeId
 * @access Public
 */
export const getVoteResults = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiativeId } = req.params

  // Validate initiative ID
  if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
    return next(createError(400, "Invalid initiative ID"))
  }

  // Find initiative
  const initiative = await Initiative.findById(initiativeId)
  if (!initiative) {
    return next(createError(404, "Initiative not found"))
  }

  // Get voting options with vote counts
  const votingOptions = await VotingOption.find({ initiative: initiativeId })
    .select("title description voteCount")
    .sort({ voteCount: -1 })

  // Calculate total votes
  const totalVotes = votingOptions.reduce((sum, option) => sum + option.voteCount, 0)

  // Calculate percentages
  const results = votingOptions.map((option) => ({
    _id: option._id,
    title: option.title,
    description: option.description,
    voteCount: option.voteCount,
    percentage: totalVotes > 0 ? Math.round((option.voteCount / totalVotes) * 100) : 0,
  }))

  res.status(200).json({
    success: true,
    data: {
      results,
      totalVotes,
      votingEndDate: initiative.votingEndDate,
      isVotingEnabled: initiative.isVotingEnabled,
    },
  })
})

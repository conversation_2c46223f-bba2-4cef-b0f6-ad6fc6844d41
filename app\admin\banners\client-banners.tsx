"use client"

import { useState, useEffect } from "react"
import { DataTable } from "@/components/ui/data-table"
import { columns } from "./columns"
import { api } from "@/lib/api"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Plus, AlertCircle } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export function ClientBanners() {
  const [banners, setBanners] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setIsLoading(true)
        // Corrected API endpoint to match the backend route
        const response = await api.get('/api/banners', true)
        
        if (response.success) {
          // Transform the data to match the expected format in columns.tsx
          const formattedBanners = response.banners.map((banner: any) => ({
            id: banner._id,
            image: banner.image,
            mainText: banner.mainText,
            subText: banner.subText,
            mainTextColor: banner.mainTextColor,
            subTextColor: banner.subTextColor,
            order: banner.order,
            isActive: banner.isActive
          }))
          
          setBanners(formattedBanners)
        } else {
          setError(response.message || "Failed to fetch banners")
          toast({
            title: "Error",
            description: "Failed to fetch banners",
            variant: "destructive",
          })
        }
      } catch (err: any) {
        console.error("Error fetching banners:", err)
        setError(err.message || "An error occurred while fetching banners")
        toast({
          title: "Error",
          description: err.message || "An error occurred while fetching banners",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchBanners()
  }, [])

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Bannières</h1>
        <Button asChild>
          <Link href="/admin/banners/new">
            <Plus className="mr-2 h-4 w-4" />
            Ajouter
          </Link>
        </Button>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      <DataTable 
        columns={columns} 
        data={banners || []} 
        searchKey="mainText"
      />
      
      <Toaster />
    </div>
  )
}

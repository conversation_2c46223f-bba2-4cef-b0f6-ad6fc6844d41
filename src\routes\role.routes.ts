import express from "express";
import { authenticate } from "../middleware/auth";
import { hasPermission } from "../middleware/permissions";
import {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getAllPermissions,
} from "../controllers/role.controller";

const router = express.Router();

// All routes require authentication and admin:manage_roles permission
router.use(authenticate);
router.use(hasPermission("admin:manage_roles"));

// Role routes
router.get("/", getAllRoles);
router.get("/:id", getRoleById);
router.post("/", createRole);
router.put("/:id", updateRole);
router.delete("/:id", deleteRole);

// Permission routes
router.get("/permissions/all", getAllPermissions);

export default router;

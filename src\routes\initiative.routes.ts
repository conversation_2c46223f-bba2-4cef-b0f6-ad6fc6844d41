import express from "express"
import {
  getAllInitiatives,
  getInitiativeById,
  createInitiative,
  updateInitiative,
  deleteInitiative,
  supportInitiative,
  unsupportInitiative,
  getFeaturedInitiatives,
  searchInitiatives,
  getInitiativesByCategory,
  getInitiativesByStatus,
  getInitiativesByUser,
  getInitiativeStats,
} from "../controllers/initiative.controller"
import {
  joinInitiative,
  leaveInitiative,
  getInitiativeVolunteers,
  addVolunteerPoints,
  getVolunteerContributions,
  inviteVolunteer,
} from "../controllers/volunteer.controller"
import { getSimilarInitiatives } from "../controllers/similar-initiatives.controller"
import {
  uploadInitiativeImage,
  updateInitiativeImage,
  addInitiativeImages
} from "../controllers/initiativeImage.controller"
import { authenticate } from "../middleware/auth"
import { validateInitiative, validateInitiativeUpdate } from "../middleware/validators/initiative.validator"
import { awardBadgeToUser, getInitiativeBadges, getVolunteerBadgesForInitiative } from "../controllers/badge.controller"

const router = express.Router()

// Public routes
router.get("/", getAllInitiatives)
router.get("/featured", getFeaturedInitiatives)
router.get("/search", searchInitiatives)
router.get("/category/:categoryId", getInitiativesByCategory)
router.get("/status/:status", getInitiativesByStatus)
router.get("/stats", getInitiativeStats)
router.get("/:id", getInitiativeById)
router.get("/:id/similar", getSimilarInitiatives)

// Protected routes
router.post("/", authenticate, validateInitiative, createInitiative)
router.put("/:id", authenticate, updateInitiative) // Temporairement désactivé: validateInitiativeUpdate
router.delete("/:id", authenticate, deleteInitiative)
router.post("/:id/support", authenticate, supportInitiative)
router.delete("/:id/support", authenticate, unsupportInitiative)
router.get("/user/:userId", getInitiativesByUser)

// Image upload routes
router.post("/:initiativeId/image", authenticate, uploadInitiativeImage, updateInitiativeImage)
router.post("/:initiativeId/images", authenticate, uploadInitiativeImage, addInitiativeImages)

// Volunteer routes
router.post("/:id/join", authenticate, joinInitiative)
router.delete("/:id/join", authenticate, leaveInitiative)
router.get("/:id/volunteers", getInitiativeVolunteers)
router.post("/:id/volunteers/:volunteerId/points", authenticate, addVolunteerPoints)
router.get("/:id/volunteers/:volunteerId/contributions", authenticate, getVolunteerContributions)
router.post("/:id/invite", authenticate, inviteVolunteer)

// Badge routes
router.get("/:id/badges", getInitiativeBadges)
router.get("/:id/volunteers/:volunteerId/badges", getVolunteerBadgesForInitiative)
router.post("/:id/volunteers/:userId/award-badge", authenticate, awardBadgeToUser)

export default router


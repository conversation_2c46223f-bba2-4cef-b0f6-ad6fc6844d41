"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "../../../components/ui/button"
import { Input } from "../../../components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../../components/ui/card"
import { Label } from "../../../components/ui/label"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import { AlertCircle, Loader2, CheckCircle2, User, Users, Building, Heart, ArrowLeft } from "lucide-react"
import { useAuth } from "../../../components/auth-provider"
import { api } from "../../../lib/api"
import { toast } from "../../../components/ui/use-toast"
import { Toaster } from "../../../components/ui/toaster"
import { RadioGroup, RadioGroupItem } from "../../../components/ui/radio-group"

export default function RegisterPage() {
  const router = useRouter()
  // Ajouter un état pour suivre l'étape actuelle (1: sélection du type, 2: formulaire)
  const [currentStep, setCurrentStep] = useState(1)

  const [formData, setFormData] = useState({
    name: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    userType: "", // Pas de valeur par défaut, l'utilisateur doit choisir
    // Champs spécifiques pour les acteurs de la société civile
    organizationName: "",
    organizationType: "association",
    activitySector: "",
    scope: "local",
    address: "",
    approvalNumber: "", // Numéro d'agrément
    // Champs spécifiques pour les entreprises
    companyName: "",
    commerceRegisterNumber: "", // Numéro de registre de commerce
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  // Fonction pour gérer la sélection du type d'utilisateur
  const handleUserTypeSelection = (type: string) => {
    setFormData(prev => ({ ...prev, userType: type }))
    setCurrentStep(2) // Passer à l'étape 2 (formulaire)
  }

  // Fonction pour revenir à l'étape de sélection du type
  const goBackToTypeSelection = () => {
    setCurrentStep(1)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleRadioChange = (value: string) => {
    setFormData((prev) => ({ ...prev, userType: value }))
  }

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setError("كلمات المرور غير متطابقة")
      return false
    }

    if (formData.password.length < 8) {
      setError("يجب أن تتكون كلمة المرور من 8 أحرف على الأقل")
      return false
    }

    // Check if password contains at least one uppercase, one lowercase, and one number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/
    if (!passwordRegex.test(formData.password)) {
      setError("يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل، وحرف صغير واحد، ورقم واحد")
      return false
    }

    return true
  }

  const { register } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // Préparer les données d'inscription
      const registrationData: any = {
        name: formData.name,
        username: formData.username,
        email: formData.email,
        password: formData.password,
        userType: formData.userType,
      }

      // Ajouter les champs spécifiques pour les acteurs de la société civile si nécessaire
      if (formData.userType === "civilSociety") {
        registrationData.organizationName = formData.organizationName;
        registrationData.organizationType = formData.organizationType;
        registrationData.activitySector = formData.activitySector;
        registrationData.scope = formData.scope;
        registrationData.address = formData.address;
        registrationData.approvalNumber = formData.approvalNumber;
      }

      // Ajouter les champs spécifiques pour les entreprises si nécessaire
      if (formData.userType === "company") {
        registrationData.companyName = formData.companyName;
        registrationData.commerceRegisterNumber = formData.commerceRegisterNumber;
      }

      // Use the register function from auth context
      await register(registrationData)

      setSuccess(true)

      toast({
        title: "تم التسجيل بنجاح",
        description: "تم إنشاء حسابك. يرجى التحقق من بريدك الإلكتروني لتأكيد حسابك.",
        variant: "default"
      })

      // Clear form
      setFormData({
        name: "",
        username: "",
        email: "",
        password: "",
        confirmPassword: "",
        userType: "volunteer",
        // Champs spécifiques pour les acteurs de la société civile
        organizationName: "",
        organizationType: "association",
        activitySector: "",
        scope: "local",
        address: "",
        approvalNumber: "", // Numéro d'agrément
        // Champs spécifiques pour les entreprises
        companyName: "",
        commerceRegisterNumber: "", // Numéro de registre de commerce
      })

      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push("/auth/login")
      }, 3000)
    } catch (err: any) {
      console.error('Registration error:', err)
      setError(err.message || "حدث خطأ أثناء التسجيل")

      toast({
        title: "فشل التسجيل",
        description: err.message || "حدث خطأ أثناء التسجيل",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 px-4 py-8" dir="rtl">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">إنشاء حساب</CardTitle>
          <CardDescription className="text-center">
            {currentStep === 1
              ? "اختر نوع الحساب الذي تريد إنشاءه"
              : "أدخل بياناتك لإنشاء حسابك"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4 ml-2" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-4 bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600 ml-2" />
              <AlertDescription className="text-green-800">
                تم التسجيل بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد حسابك. جاري التحويل إلى صفحة تسجيل الدخول...
              </AlertDescription>
            </Alert>
          )}

          {/* Étape 1: Sélection du type d'utilisateur */}
          {currentStep === 1 && (
            <div className="grid grid-cols-2 gap-4 py-4">
              <div
                className="flex flex-col items-center justify-center p-6 border rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-colors"
                onClick={() => handleUserTypeSelection("volunteer")}
              >
                <Heart className="h-12 w-12 text-green-600 mb-4" />
                <h3 className="font-medium text-lg mb-2">متطوع</h3>
                <p className="text-sm text-center text-gray-500">أريد المشاركة في المبادرات</p>
              </div>

              <div
                className="flex flex-col items-center justify-center p-6 border rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-colors"
                onClick={() => handleUserTypeSelection("proposer")}
              >
                <User className="h-12 w-12 text-green-600 mb-4" />
                <h3 className="font-medium text-lg mb-2">مقترح</h3>
                <p className="text-sm text-center text-gray-500">لدي أفكار للمبادرات</p>
              </div>

              <div
                className="flex flex-col items-center justify-center p-6 border rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-colors"
                onClick={() => handleUserTypeSelection("company")}
              >
                <Building className="h-12 w-12 text-green-600 mb-4" />
                <h3 className="font-medium text-lg mb-2">شركة</h3>
                <p className="text-sm text-center text-gray-500">أمثل مؤسسة</p>
              </div>

              <div
                className="flex flex-col items-center justify-center p-6 border rounded-lg cursor-pointer hover:border-green-500 hover:bg-green-50 transition-colors"
                onClick={() => handleUserTypeSelection("civilSociety")}
              >
                <Users className="h-12 w-12 text-green-600 mb-4" />
                <h3 className="font-medium text-lg mb-2">ممثل المجتمع المدني</h3>
                <p className="text-sm text-center text-gray-500">أمثل جمعية أو منظمة</p>
              </div>
            </div>
          )}

          {/* Étape 2: Formulaire d'inscription */}
          {currentStep === 2 && (
            <>
              <div className="mb-4">
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={goBackToTypeSelection}
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>العودة إلى اختيار نوع الحساب</span>
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">الاسم الكامل</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="محمد أحمد"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="text-right"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="username">اسم المستخدم</Label>
                  <Input
                    id="username"
                    name="username"
                    placeholder="mohamed123"
                    value={formData.username}
                    onChange={handleChange}
                    required
                    className="text-right"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="text-right"
                  />
                </div>

                {/* Champs spécifiques pour les entreprises */}
                {formData.userType === "company" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="companyName">اسم الشركة</Label>
                      <Input
                        id="companyName"
                        name="companyName"
                        placeholder="اسم الشركة الكامل"
                        value={formData.companyName}
                        onChange={handleChange}
                        required
                        className="text-right"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="commerceRegisterNumber">رقم السجل التجاري</Label>
                      <Input
                        id="commerceRegisterNumber"
                        name="commerceRegisterNumber"
                        placeholder="رقم السجل التجاري للشركة"
                        value={formData.commerceRegisterNumber}
                        onChange={handleChange}
                        required
                        className="text-right"
                      />
                    </div>
                  </>
                )}

                {/* Champs spécifiques pour les acteurs de la société civile */}
                {formData.userType === "civilSociety" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="organizationName">اسم المنظمة</Label>
                      <Input
                        id="organizationName"
                        name="organizationName"
                        placeholder="اسم الجمعية أو المنظمة"
                        value={formData.organizationName}
                        onChange={handleChange}
                        required
                        className="text-right"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="organizationType">نوع المنظمة</Label>
                      <select
                        id="organizationType"
                        name="organizationType"
                        value={formData.organizationType}
                        onChange={(e) => setFormData((prev) => ({ ...prev, organizationType: e.target.value }))}
                        required
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-right"
                      >
                        <option value="association">جمعية</option>
                        <option value="club">نادي</option>
                        <option value="foundation">مؤسسة</option>
                        <option value="organization">منظمة</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="activitySector">قطاع النشاط</Label>
                      <Input
                        id="activitySector"
                        name="activitySector"
                        placeholder="مثال: التعليم، الصحة، البيئة، الثقافة..."
                        value={formData.activitySector}
                        onChange={handleChange}
                        required
                        className="text-right"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="scope">النطاق الجغرافي</Label>
                      <select
                        id="scope"
                        name="scope"
                        value={formData.scope}
                        onChange={(e) => setFormData((prev) => ({ ...prev, scope: e.target.value }))}
                        required
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-right"
                      >
                        <option value="local">محلي</option>
                        <option value="regional">إقليمي</option>
                        <option value="national">وطني</option>
                        <option value="international">دولي</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address">عنوان المقر</Label>
                      <Input
                        id="address"
                        name="address"
                        placeholder="العنوان الكامل للمقر"
                        value={formData.address}
                        onChange={handleChange}
                        required
                        className="text-right"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="approvalNumber">رقم الاعتماد</Label>
                      <Input
                        id="approvalNumber"
                        name="approvalNumber"
                        placeholder="رقم الاعتماد الرسمي للمنظمة"
                        value={formData.approvalNumber}
                        onChange={handleChange}
                        required
                        className="text-right"
                      />
                    </div>
                  </>
                )}

                <div className="space-y-2">
                  <Label htmlFor="password">كلمة المرور</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    className="text-right"
                  />
                  <p className="text-xs text-gray-500">
                    يجب أن تتكون كلمة المرور من 8 أحرف على الأقل وتتضمن أحرفًا كبيرة وصغيرة وأرقامًا
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    required
                    className="text-right"
                  />
                </div>

                <Button type="submit" className="w-full bg-green-600 hover:bg-green-700" disabled={isLoading || success}>
                  {isLoading ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري إنشاء الحساب...
                    </>
                  ) : (
                    "تسجيل"
                  )}
                </Button>
              </form>
            </>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-sm">
            لديك حساب بالفعل؟{" "}
            <Link href="/auth/login" className="text-green-600 hover:text-green-800 font-medium">
              تسجيل الدخول
            </Link>
          </div>
        </CardFooter>
      </Card>
      <Toaster />
    </div>
  )
}

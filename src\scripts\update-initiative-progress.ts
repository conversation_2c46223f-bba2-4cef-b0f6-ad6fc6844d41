import mongoose from "mongoose";
import { Initiative, Milestone } from "../models";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/initiatives_dz")
  .then(() => console.log("MongoDB connected"))
  .catch(err => console.error("MongoDB connection error:", err));

// Function to update initiative progress
const updateInitiativeProgress = async (initiativeId: string) => {
  try {
    // Find initiative
    const initiative = await Initiative.findById(initiativeId);
    if (!initiative) {
      console.error("Initiative not found");
      return;
    }

    console.log("Current initiative status:", initiative.status);
    console.log("Current initiative progress:", initiative.progress);

    // Get milestones
    const milestones = await Milestone.find({ initiative: initiativeId });
    console.log("Total milestones:", milestones.length);
    
    // Count completed milestones
    const completedMilestones = milestones.filter(m => m.isCompleted).length;
    console.log("Completed milestones:", completedMilestones);

    // Calculate progress
    const progress = milestones.length > 0 ? Math.round((completedMilestones / milestones.length) * 100) : 0;
    console.log("Calculated progress:", progress);

    // Update initiative
    let updateData: any = { progress };
    
    // If all milestones are completed, mark the initiative as completed
    if (progress === 100) {
      updateData.status = "completed";
      console.log("Setting status to completed");
    } else if (progress > 0) {
      updateData.status = "active";
      console.log("Setting status to active");
    }

    // Update initiative
    await Initiative.findByIdAndUpdate(initiativeId, updateData);
    console.log("Initiative updated successfully");

    // Verify update
    const updatedInitiative = await Initiative.findById(initiativeId);
    console.log("Updated initiative status:", updatedInitiative?.status);
    console.log("Updated initiative progress:", updatedInitiative?.progress);

  } catch (error) {
    console.error("Error updating initiative progress:", error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
  }
};

// Check if initiative ID is provided
if (process.argv.length < 3) {
  console.error("Please provide an initiative ID");
  process.exit(1);
}

// Get initiative ID from command line arguments
const initiativeId = process.argv[2];

// Update initiative progress
updateInitiativeProgress(initiativeId);

import mongoose, { Schema, Document } from "mongoose"

export interface IActivity extends Document {
  user: mongoose.Types.ObjectId | string
  type: string
  action: string
  content: string
  date: Date
  relatedInitiative?: mongoose.Types.ObjectId | string
  relatedUser?: mongoose.Types.ObjectId | string
  relatedComment?: mongoose.Types.ObjectId | string
  metadata?: Record<string, any>
  isPublic: boolean
}

const ActivitySchema: Schema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: ["initiative", "support", "comment", "volunteer", "badge", "profile", "system"],
      required: true,
    },
    action: {
      type: String,
      enum: ["create", "update", "delete", "join", "leave", "support", "unsupport", "comment", "reply", "earn", "complete", "invite", "accept", "reject", "login", "register"],
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    date: {
      type: Date,
      default: Date.now,
    },
    relatedInitiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
    },
    relatedUser: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    relatedComment: {
      type: Schema.Types.ObjectId,
      ref: "Comment",
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
)

// Index pour améliorer les performances des requêtes
ActivitySchema.index({ user: 1, date: -1 })
ActivitySchema.index({ relatedInitiative: 1 })
ActivitySchema.index({ relatedUser: 1 })
ActivitySchema.index({ type: 1, action: 1 })
ActivitySchema.index({ isPublic: 1 })

export const Activity = mongoose.model<IActivity>("Activity", ActivitySchema)

import type { Request, Response, NextFunction } from "express"
import { SocialImpact } from "../models"
import { createError } from "../utils/error"

// Seed education, awareness and skills impacts
export const seedEducationImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Define education impact category and impacts
    const educationData = {
      name: "Education, Awareness and Skills",
      arabicName: "التعليم والتوعية والمهارات",
      impacts: [
        {
          name: "Improving access to education / knowledge",
          arabicName: "تحسين الوصول إلى التعليم / المعرفة",
        },
        {
          name: "Developing skills (personal, professional, civic)",
          arabicName: "تطوير المهارات (الشخصية، المهنية، المدنية)",
        },
        {
          name: "Increasing awareness on a social / environmental issue",
          arabicName: "زيادة الوعي حول قضية اجتماعية / بيئية",
        },
        {
          name: "Changing mentalities / behaviors on a given subject",
          arabicName: "تغيير العقليات / السلوكيات حول موضوع معين",
        },
        {
          name: "Prevention (health, delinquency, school dropout...)",
          arabicName: "الوقاية (الصحة، الجنوح، التسرب المدرسي...)",
        },
        {
          name: "Promoting critical thinking and active citizenship",
          arabicName: "تعزيز التفكير النقدي والمواطنة الفعالة",
        },
      ],
      order: 3,
    }

    // Create education impact category
    await SocialImpact.create(educationData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Education impact category seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed environment and living environment impacts
export const seedEnvironmentImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Define environment impact category and impacts
    const environmentData = {
      name: "Environment and Living Environment",
      arabicName: "البيئة وبيئة المعيشة",
      impacts: [
        {
          name: "Protection / Restoration of the environment (biodiversity, water, air, soil)",
          arabicName: "حماية / استعادة البيئة (التنوع البيولوجي، الماء، الهواء، التربة)",
        },
        {
          name: "Beautification / Improvement of living environment (green spaces, cleanliness...)",
          arabicName: "تجميل / تحسين بيئة المعيشة (المساحات الخضراء، النظافة...)",
        },
        {
          name: "Promotion of sustainable / eco-responsible practices (recycling, energy saving, soft mobility...)",
          arabicName: "تعزيز الممارسات المستدامة / المسؤولة بيئياً (إعادة التدوير، توفير الطاقة، التنقل اللطيف...)",
        },
        {
          name: "Awareness of ecology / sustainable development",
          arabicName: "التوعية بالبيئة / التنمية المستدامة",
        },
        {
          name: "Reduction of pollution / nuisances",
          arabicName: "الحد من التلوث / الإزعاج",
        },
      ],
      order: 4,
    }

    // Create environment impact category
    await SocialImpact.create(environmentData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Environment impact category seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed local economy and employment impacts
export const seedEconomyImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Define economy impact category and impacts
    const economyData = {
      name: "Local Economy and Employment",
      arabicName: "الاقتصاد المحلي والتوظيف",
      impacts: [
        {
          name: "Supporting local economy / short circuits",
          arabicName: "دعم الاقتصاد المحلي / الدوائر القصيرة",
        },
        {
          name: "Creating jobs / income-generating activities",
          arabicName: "خلق فرص عمل / أنشطة مدرة للدخل",
        },
        {
          name: "Improving employability / Professional insertion",
          arabicName: "تحسين القابلية للتوظيف / الإدماج المهني",
        },
        {
          name: "Fighting against economic precariousness / poverty",
          arabicName: "مكافحة الهشاشة الاقتصادية / الفقر",
        },
        {
          name: "Promoting social entrepreneurship",
          arabicName: "تعزيز ريادة الأعمال الاجتماعية",
        },
      ],
      order: 5,
    }

    // Create economy impact category
    await SocialImpact.create(economyData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Economy impact category seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed health and well-being impacts
export const seedHealthImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Define health impact category and impacts
    const healthData = {
      name: "Health and Well-being",
      arabicName: "الصحة والرفاهية",
      impacts: [
        {
          name: "Improving access to healthcare / health prevention",
          arabicName: "تحسين الوصول إلى الرعاية الصحية / الوقاية الصحية",
        },
        {
          name: "Promoting physical and mental health",
          arabicName: "تعزيز الصحة البدنية والعقلية",
        },
        {
          name: "Improving general well-being of individuals / community",
          arabicName: "تحسين الرفاهية العامة للأفراد / المجتمع",
        },
        {
          name: "Supporting sick people / their caregivers",
          arabicName: "دعم المرضى / مقدمي الرعاية لهم",
        },
      ],
      order: 6,
    }

    // Create health impact category
    await SocialImpact.create(healthData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Health impact category seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed citizenship, democracy and engagement impacts
export const seedCitizenshipImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Define citizenship impact category and impacts
    const citizenshipData = {
      name: "Citizenship, Democracy and Engagement",
      arabicName: "المواطنة والديمقراطية والمشاركة",
      impacts: [
        {
          name: "Strengthening citizen participation / volunteer engagement",
          arabicName: "تعزيز المشاركة المدنية / المشاركة التطوعية",
        },
        {
          name: "Improving dialogue between citizens and institutions",
          arabicName: "تحسين الحوار بين المواطنين والمؤسسات",
        },
        {
          name: "Promoting local / participatory democracy",
          arabicName: "تعزيز الديمقراطية المحلية / التشاركية",
        },
        {
          name: "Developing culture of debate and deliberation",
          arabicName: "تطوير ثقافة النقاش والمداولة",
        },
      ],
      order: 7,
    }

    // Create citizenship impact category
    await SocialImpact.create(citizenshipData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Citizenship impact category seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed culture and heritage impacts
export const seedCultureImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Define culture impact category and impacts
    const cultureData = {
      name: "Culture and Heritage",
      arabicName: "الثقافة والتراث",
      impacts: [
        {
          name: "Improving access to culture / artistic practices",
          arabicName: "تحسين الوصول إلى الثقافة / الممارسات الفنية",
        },
        {
          name: "Enhancing local heritage (material or immaterial)",
          arabicName: "تعزيز التراث المحلي (المادي أو غير المادي)",
        },
        {
          name: "Promoting cultural diversity",
          arabicName: "تعزيز التنوع الثقافي",
        },
        {
          name: "Creating cultural / artistic events",
          arabicName: "إنشاء فعاليات ثقافية / فنية",
        },
      ],
      order: 8,
    }

    // Create culture impact category
    await SocialImpact.create(cultureData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Culture impact category seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

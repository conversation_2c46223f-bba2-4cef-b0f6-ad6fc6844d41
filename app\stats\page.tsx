"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { Button } from "../../components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "../../components/ui/card"
import { Alert, AlertDescription } from "../../components/ui/alert"
import {
  Loader2,
  AlertCircle,
  BarChart3,
  Users,
  MapPin,
  MessageSquare,
  Package,
  Truck,
  ListChecks
} from "lucide-react"
import { api } from "../../lib/api"

export default function StatsPage() {
  const [stats, setStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    setIsLoading(true)
    try {
      const response = await api.get("/api/stats", false)

      if (response.success) {
        // Restructure the response to match our component's expectations
        setStats({
          mainStats: response.statistics,
          additionalStats: response.additionalStats
        })
      } else {
        setError("Failed to fetch statistics")
      }
    } catch (err: any) {
      console.error("Error fetching statistics:", err)
      setError(err.message || "An error occurred while fetching statistics")
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري تحميل الإحصائيات...</span>
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "فشل في تحميل الإحصائيات"}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">إحصائيات المنصة</h1>

      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        {stats.mainStats.map((stat: any) => (
          <Card key={stat.key}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{stat.label}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Stats Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              التعليقات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats.additionalStats.totalComments}</div>
            <p className="text-sm text-gray-500">إجمالي التعليقات على جميع المبادرات</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              التصنيفات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats.additionalStats.totalCategories}</div>
            <p className="text-sm text-gray-500">تصنيفات المبادرات</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              الموارد
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats.additionalStats.resources.total}</div>
            <div className="flex justify-between mt-2">
              <div>
                <div className="text-sm font-medium">{stats.additionalStats.resources.deliveryRate}%</div>
                <div className="text-xs text-gray-500">معدل التسليم</div>
              </div>
              <div>
                <div className="text-sm font-medium">{stats.additionalStats.resources.delivered}</div>
                <div className="text-xs text-gray-500">تم تسليمها</div>
              </div>
            </div>
            <div className="mt-4">
              <Link href="/stats/resources">
                <Button variant="outline" size="sm" className="w-full">
                  عرض إحصائيات الموارد
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resource Stats Summary */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-4">إدارة الموارد</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                عروض الموارد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold">{stats.additionalStats.resources.total}</div>
                  <p className="text-sm text-gray-500">إجمالي عروض الموارد</p>
                </div>
                <div className="text-right">
                  <div className="text-xl font-semibold text-green-600">
                    {stats.additionalStats.resources.deliveryRate}%
                  </div>
                  <p className="text-sm text-gray-500">معدل التسليم</p>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <Truck className="h-5 w-5 mr-2 text-purple-600" />
                <span className="text-sm">
                  <span className="font-medium">{stats.additionalStats.resources.delivered}</span> موارد تم تسليمها
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ListChecks className="mr-2 h-5 w-5" />
                احتياجات الموارد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-3xl font-bold">{stats.additionalStats.resourceNeeds.total}</div>
                  <p className="text-sm text-gray-500">إجمالي احتياجات الموارد</p>
                </div>
                <div className="text-right">
                  <div className="text-xl font-semibold text-green-600">
                    {stats.additionalStats.resourceNeeds.fulfillmentRate}%
                  </div>
                  <p className="text-sm text-gray-500">معدل الإنجاز</p>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <Truck className="h-5 w-5 mr-2 text-green-600" />
                <span className="text-sm">
                  <span className="font-medium">{stats.additionalStats.resourceNeeds.fulfilled}</span> احتياجات تم تلبيتها
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-6 text-center">
          <Link href="/stats/resources">
            <Button className="bg-green-600 hover:bg-green-700">
              <BarChart3 className="mr-2 h-4 w-4" />
              عرض إحصائيات الموارد المفصلة
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

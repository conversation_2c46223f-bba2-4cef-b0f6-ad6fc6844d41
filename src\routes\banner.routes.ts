import express from 'express';
import {
  getAllBanners,
  getActiveBanners,
  getBannerById,
  createBanner,
  updateBanner,
  deleteBanner,
  updateBannerOrder,
  toggleBannerStatus,
} from '../controllers/banner.controller';
import { authenticate, adminOnly } from '../middleware/auth';

const router = express.Router();

// Public routes
router.get('/active', getActiveBanners);

// Admin routes
router.get('/', authenticate, adminOnly, getAllBanners);
router.post('/', authenticate, adminOnly, createBanner);

// Special routes with specific paths (must come before generic /:id routes)
router.put('/order/update', authenticate, adminOnly, updateBannerOrder);

// Routes with ID parameter
router.get('/:id', authenticate, adminOnly, getBannerById);
router.put('/:id', authenticate, adminOnly, updateBanner);
router.delete('/:id', authenticate, adminOnly, deleteBanner);
router.put('/:id/toggle', authenticate, adminOnly, toggleBannerStatus);

export default router;

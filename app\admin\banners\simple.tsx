"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "../../../components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { api } from "../../../lib/api"

export default function SimpleBannerAdmin() {
  const [banners, setBanners] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true)
        const response = await api.get("/api/banners", true)
        console.log("API Response:", response)
        
        if (response.success) {
          setBanners(response.banners || [])
        } else {
          setError(response.message || "Failed to fetch banners")
        }
      } catch (err) {
        console.error("Error fetching banners:", err)
        setError("An error occurred while fetching banners")
      } finally {
        setLoading(false)
      }
    }

    fetchBanners()
  }, [])

  const createTestBanner = async () => {
    try {
      const response = await api.post("/api/banners", {
        image: "/placeholder.svg",
        mainText: "Test Banner",
        subText: "This is a test banner",
        mainTextColor: "#FFFFFF",
        subTextColor: "#FFFFFF",
        order: 0,
        isActive: true
      })

      if (response.success) {
        alert("Banner created successfully!")
        // Refresh banners
        const bannersResponse = await api.get("/api/banners", true)
        if (bannersResponse.success) {
          setBanners(bannersResponse.banners || [])
        }
      } else {
        alert("Failed to create banner: " + (response.message || "Unknown error"))
      }
    } catch (err) {
      console.error("Error creating banner:", err)
      alert("An error occurred while creating the banner")
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Simple Banner Management</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading banners...</p>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : (
            <>
              <Button onClick={createTestBanner} className="mb-4">Create Test Banner</Button>
              
              <h2 className="text-xl font-bold mb-2">Banners ({banners.length})</h2>
              
              {banners.length === 0 ? (
                <p>No banners found</p>
              ) : (
                <div className="space-y-4">
                  {banners.map((banner: any) => (
                    <div key={banner._id} className="border p-4 rounded">
                      <p><strong>ID:</strong> {banner._id}</p>
                      <p><strong>Main Text:</strong> {banner.mainText}</p>
                      <p><strong>Sub Text:</strong> {banner.subText}</p>
                      <p><strong>Status:</strong> {banner.isActive ? "Active" : "Inactive"}</p>
                      <p><strong>Order:</strong> {banner.order}</p>
                      {banner.image && (
                        <img 
                          src={banner.image} 
                          alt={banner.mainText} 
                          className="mt-2 h-32 object-cover rounded"
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { create<PERSON>ontext, use<PERSON>ontex<PERSON>, useState, useEffect, type ReactNode } from "react"
import { useRout<PERSON> } from "next/navigation"
import { getCurrentUser, getToken, saveUserData, clearUserData } from "../lib/auth"
import { api } from "../lib/api"

interface User {
  id: string
  name: string
  email: string
  username: string
  avatar?: string
  role: string | { code: string; name?: string } // Allow role to be string or object with code
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (identifier: string, password: string, rememberMe: boolean) => Promise<boolean>
  register: (userData: any) => Promise<void>
  logout: () => void
  isAuthenticated: boolean
  updateUser: (userData: User) => void
  refreshUserData: () => Promise<User | null>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Cache for user data to prevent infinite API calls
let userCache: User | null = null
let lastFetchTime = 0
const CACHE_TTL = 60000 // 1 minute cache TTL

// Flag to prevent concurrent API calls
let isFetchingUser = false

// Counter to detect and block excessive API calls
let apiCallsCount = 0
let apiCallsResetTime = Date.now()
const API_CALLS_THRESHOLD = 10 // Maximum number of calls allowed in the reset period
const API_CALLS_RESET_PERIOD = 5000 // 5 seconds

export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Function to get user data with caching and protection against excessive calls
  const getUserData = async (forceRefresh = false) => {
    const now = Date.now()
    const token = getToken()

    console.log('[AuthProvider] getUserData called, forceRefresh:', forceRefresh)

    // Si pas de token, retourner null immédiatement
    if (!token) {
      console.log('[AuthProvider] No token found, returning null')
      return null
    }

    // Reset API calls counter if the reset period has passed
    if (now - apiCallsResetTime > API_CALLS_RESET_PERIOD) {
      apiCallsCount = 0
      apiCallsResetTime = now
    }

    // Increment API calls counter
    apiCallsCount++

    // If we're making too many calls, block them and return cached data
    if (apiCallsCount > API_CALLS_THRESHOLD && !forceRefresh) {
      console.warn(`⚠️ EXCESSIVE API CALLS BLOCKED: ${apiCallsCount} calls in ${API_CALLS_RESET_PERIOD}ms`)
      console.warn('This is likely caused by a React component re-rendering too frequently.')
      console.warn('Stack trace:', new Error().stack)

      // Get user from storage
      const storedUser = getCurrentUser()
      console.log('[AuthProvider] Returning stored user due to excessive API calls:', storedUser)

      // Ensure role is properly formatted
      if (storedUser && storedUser.role && typeof storedUser.role === 'object') {
        if (storedUser.role._id && !storedUser.role.code) {
          console.log('[AuthProvider] Fixing role format for stored user in getUserData')
          storedUser.role.code = 'admin' // Assume admin role
          // Save the fixed user data back to storage
          saveUserData(storedUser, token, true)
        }
      }

      // Return cached user or fixed stored user
      return userCache || storedUser
    }

    // If we have a cached user and it's not expired and we're not forcing a refresh
    if (userCache && (now - lastFetchTime < CACHE_TTL) && !forceRefresh) {
      console.log('[AuthProvider] Using cached user data')
      return userCache
    }

    // If we're already fetching user data, don't start another request
    if (isFetchingUser && !forceRefresh) {
      console.log('[AuthProvider] Already fetching user data, using cached data')
      return userCache || getCurrentUser()
    }

    // Try to get fresh user data
    try {
      // Set the fetching flag to prevent concurrent requests
      isFetchingUser = true

      console.log('[AuthProvider] Fetching fresh user data')
      const response = await api.get('/api/users/me', true)

      // Reset the fetching flag
      isFetchingUser = false

      if (response && response.user) {
        console.log('[AuthProvider] Fresh user data received:', response.user)
        userCache = response.user
        lastFetchTime = now

        // Sauvegarder les données utilisateur mises à jour
        saveUserData(response.user, token, true)

        return response.user
      } else {
        console.log('[AuthProvider] No user data in response:', response)
        // Si pas de données utilisateur dans la réponse, utiliser les données stockées
        const storedUser = getCurrentUser()
        return storedUser
      }
    } catch (error) {
      // Reset the fetching flag
      isFetchingUser = false

      console.error('[AuthProvider] Error fetching user data:', error)

      // Vérifier si l'erreur est due à un token expiré ou invalide
      if (error.message && (
          error.message.includes('401') ||
          error.message.includes('Unauthorized') ||
          error.message.includes('token')
        )) {
        console.log('[AuthProvider] Token may be expired or invalid, clearing user data')
        // Ne pas déconnecter l'utilisateur immédiatement, mais retourner les données stockées
        // pour éviter une déconnexion brutale
        const storedUser = getCurrentUser()
        return storedUser
      }

      // If we have a cached user, return it as fallback
      if (userCache) {
        return userCache
      }

      // If all else fails, try to get user from storage
      return getCurrentUser()
    }
  }

  useEffect(() => {
    // Check if user is already logged in
    const initializeUser = async () => {
      const storedUser = getCurrentUser()
      const token = getToken()

      console.log('[AuthProvider] Initializing user from storage:', storedUser)

      if (storedUser && token) {
        // Ensure role is properly formatted before setting user
        if (storedUser.role && typeof storedUser.role === 'object') {
          // If role is an object with _id but no code, add code property
          if (storedUser.role._id && !storedUser.role.code) {
            console.log('[AuthProvider] Fixing role format for stored user')
            storedUser.role.code = 'admin' // Assume admin role
            // Save the fixed user data back to storage
            saveUserData(storedUser, token, true)
          }
        }

        // Mettre à jour l'état avec les données stockées
        setUser(storedUser)
        setIsAuthenticated(true)
        console.log('[AuthProvider] User authenticated from storage:', storedUser)

        // Mettre à jour le cache avec les données stockées
        userCache = storedUser
        lastFetchTime = Date.now() - (CACHE_TTL / 2) // Définir un temps de cache intermédiaire

        // Try to get fresh user data in the background
        try {
          const freshUser = await getUserData(false) // Ne pas forcer le rafraîchissement
          console.log('[AuthProvider] Fresh user data received:', freshUser)

          if (freshUser) {
            // Ensure role is properly formatted in fresh user data
            if (freshUser.role && typeof freshUser.role === 'object') {
              // If role is an object with _id but no code, add code property
              if (freshUser.role._id && !freshUser.role.code) {
                console.log('[AuthProvider] Fixing role format for fresh user')
                freshUser.role.code = 'admin' // Assume admin role
              }
            }

            // Only update if there are actual differences
            if (JSON.stringify(freshUser) !== JSON.stringify(storedUser)) {
              console.log('[AuthProvider] Updating user with fresh data')
              setUser(freshUser)
              // Update stored user data
              saveUserData(freshUser, token, true)
              // Update cache
              userCache = freshUser
              lastFetchTime = Date.now()
            }
          }
        } catch (error) {
          console.error('[AuthProvider] Error refreshing user data:', error)
          // En cas d'erreur, continuer à utiliser les données stockées
          // Ne pas déconnecter l'utilisateur
        }
      } else {
        console.log('[AuthProvider] No stored user or token found')
        // Effacer l'état utilisateur
        setUser(null)
        setIsAuthenticated(false)
        // Effacer le cache
        userCache = null
        lastFetchTime = 0
      }

      setIsLoading(false)
    }

    initializeUser()
  }, [])

  const login = async (identifier: string, password: string, rememberMe: boolean) => {
    setIsLoading(true)

    try {
      console.log('[AuthProvider] Login attempt with identifier:', identifier)

      // Check if we're using username or email
      const isEmail = identifier.includes('@')
      const loginData = isEmail
        ? { email: identifier, password }
        : { username: identifier, password }

      console.log('[AuthProvider] Sending login data:', {
        ...loginData,
        password: '********',
        isEmail
      })

      const data = await api.post("/api/auth/login", loginData, false)
      console.log('[AuthProvider] Login response:', {
        success: !!data.accessToken,
        hasUser: !!data.user,
        userType: data.user?.type,
        userRoles: data.user?.roles
      })

      // Ensure role is properly formatted before saving
      if (data.user && data.user.role && typeof data.user.role === 'object') {
        // If role is an object with _id but no code, add code property
        if (data.user.role._id && !data.user.role.code) {
          console.log('[AuthProvider] Fixing role format for login user')
          data.user.role.code = 'admin' // Assume admin role
        }
      }

      // Save user data and token
      saveUserData(data.user, data.accessToken, rememberMe)

      // Update cache
      userCache = data.user
      lastFetchTime = Date.now()

      // Update state
      setUser(data.user)
      setIsAuthenticated(true)
      console.log('[AuthProvider] User logged in successfully:', data.user)

      // Return success
      return true
    } catch (error) {
      console.error('[AuthProvider] Login error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: any) => {
    setIsLoading(true)

    try {
      await api.post("/api/auth/register", userData, false)
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)

    try {
      // Call logout API
      await api.post("/api/auth/logout", {})

      // Clear user data
      clearUserData()

      // Clear cache
      userCache = null
      lastFetchTime = 0

      // Update state
      setUser(null)
      setIsAuthenticated(false)

      // Force redirect to home page
      window.location.href = "/"
    } catch (error) {
      console.error("Logout error:", error)

      // Even if API call fails, clear local data and redirect
      clearUserData()
      setUser(null)
      setIsAuthenticated(false)

      // Force redirect to home page
      window.location.href = "/"

      // Clear cache
      userCache = null
      lastFetchTime = 0

      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  // Function to update user data
  const updateUser = (userData: User) => {
    console.log('[AuthProvider] Updating user data:', userData)

    // Ensure role is properly formatted
    if (userData && userData.role && typeof userData.role === 'object') {
      // If role is an object with _id but no code, add code property
      if (userData.role._id && !userData.role.code) {
        console.log('[AuthProvider] Fixing role format in updateUser')
        userData.role.code = 'admin' // Assume admin role
      }
    }

    // Update cache
    userCache = userData
    lastFetchTime = Date.now()

    // Update state
    setUser(userData)

    // Update stored user data
    const token = getToken()
    if (token) {
      saveUserData(userData, token, true)
    }

    console.log('[AuthProvider] User data updated successfully')
  }

  // Function to refresh user data from API
  const refreshUserData = async (): Promise<User | null> => {
    try {
      console.log('[AuthProvider] Refreshing user data...')
      const freshUser = await getUserData(true) // Force refresh

      if (freshUser) {
        // Ensure role is properly formatted
        if (freshUser.role && typeof freshUser.role === 'object') {
          // If role is an object with _id but no code, add code property
          if (freshUser.role._id && !freshUser.role.code) {
            console.log('[AuthProvider] Fixing role format in refreshUserData')
            freshUser.role.code = 'admin' // Assume admin role
          }
        }

        setUser(freshUser)
        setIsAuthenticated(true)

        // Update stored user data
        const token = getToken()
        if (token) {
          saveUserData(freshUser, token, true)
        }

        console.log('[AuthProvider] User data refreshed successfully:', freshUser)
        return freshUser
      }
    } catch (error) {
      console.error('[AuthProvider] Error refreshing user data:', error)
    }
    return user
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        login,
        register,
        logout,
        updateUser,
        refreshUserData,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }

  return context
}

"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "../../../../components/ui/button"
import { Input } from "../../../../components/ui/input"
import { Textarea } from "../../../../components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../../components/ui/select"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "../../../../components/ui/card"
import { Alert, AlertDescription } from "../../../../components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../../../components/ui/tabs"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTrigger } from "../../../../components/ui/dialog"
import { AlertCircle, Loader2, ArrowLeft, Save, Upload, Image as ImageIcon, X, Camera, FileImage, Trash2 } from "lucide-react"
import { api } from "../../../../lib/api"
import { uploadFile } from "../../../../lib/fileUpload"
import { useAuth } from "../../../../components/auth-provider"
import { toast } from "../../../../components/ui/use-toast"
import { Toaster } from "../../../../components/ui/toaster"

interface Category {
  _id: string
  name: string
  arabicName: string
  color: string
}

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  fullDescription: string
  category: {
    _id: string
    name: string
    arabicName?: string
    color?: string
  }
  author: {
    _id: string
    name: string
    username: string
    avatar?: string
  }
  location: string
  wilaya?: string
  goal: number
  mainImage: string
  images: string[]
  tags: string[]
  budget?: number
  requiredVolunteers?: number
  status?: string
  createdAt?: string
  updatedAt?: string
}

export default function EditInitiativePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params

  const [initiative, setInitiative] = useState<Initiative | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [socialImpactCategories, setSocialImpactCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState("")
  const [formData, setFormData] = useState({
    title: "",
    shortDescription: "",
    fullDescription: "",
    category: "",
    location: "",
    wilaya: "",
    goal: 0,
    mainImage: "",
    images: [] as string[],
    tags: "",
    budget: 0,
    requiredVolunteers: 0,
    // Nouveaux champs structurants
    problem: "",
    solution: "",
    beneficiaries: "",
    quantitativeObjectives: "",
    qualitativeObjectives: "",
    // Impacts sociaux
    socialImpacts: [] as {category: string, impacts: string[]}[],
    selectedImpacts: [] as any[],
  })

  // Image upload states
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadError, setUploadError] = useState("")
  const [activeTab, setActiveTab] = useState("details")

  // Delete states
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // Refs for file inputs
  const mainImageInputRef = useRef<HTMLInputElement>(null)
  const additionalImagesInputRef = useRef<HTMLInputElement>(null)

  // Use the auth context instead of direct localStorage access
  const { user, isAuthenticated } = useAuth()

  useEffect(() => {
    const fetchInitiative = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/initiatives/${id}`, false)

        if (!data || !data.initiative) {
          throw new Error('Initiative not found')
        }

        setInitiative(data.initiative)
        console.log('Initiative data:', data.initiative)

        // Populate form data
        setFormData({
          title: data.initiative.title,
          shortDescription: data.initiative.shortDescription,
          fullDescription: data.initiative.fullDescription,
          category: data.initiative.category._id,
          location: data.initiative.location,
          wilaya: data.initiative.wilaya || "",
          goal: data.initiative.goal,
          mainImage: data.initiative.mainImage,
          images: data.initiative.images || [],
          tags: data.initiative.tags.join(", "),
          budget: data.initiative.budget || 0,
          requiredVolunteers: data.initiative.requiredVolunteers || 0,
          // Nouveaux champs structurants
          problem: data.initiative.problem || "",
          solution: data.initiative.solution || "",
          beneficiaries: data.initiative.beneficiaries || "",
          quantitativeObjectives: data.initiative.quantitativeObjectives || "",
          qualitativeObjectives: data.initiative.qualitativeObjectives || "",
          // Impacts sociaux
          socialImpacts: data.initiative.socialImpacts || [],
          selectedImpacts: data.initiative.selectedImpacts || [],
        })

        // Check if user is authorized to edit
        if (!user || (user.id !== data.initiative.author._id && user.role !== "admin")) {
          toast({
            title: "Unauthorized",
            description: "You can only edit your own initiatives",
            variant: "destructive"
          })
          router.push(`/initiatives/${id}`)
        }
      } catch (err: any) {
        console.error('Error fetching initiative:', err)
        setError(err.message || "Failed to fetch initiative")
      }
    }

    const fetchSocialImpacts = async () => {
      try {
        // Utiliser fetch directement au lieu de api.get
        const response = await fetch('http://localhost:5000/api/social-impacts')
        console.log('Social impacts response status:', response.status)

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`)
        }

        const data = await response.json()
        console.log('Social impacts data:', data)

        if (data && data.success && data.socialImpacts) {
          setSocialImpactCategories(data.socialImpacts)
        } else {
          console.error('Invalid social impacts API response format:', data)
        }
      } catch (err: any) {
        console.error('Error fetching social impacts:', err)
      }
    }

    const fetchCategories = async () => {
      try {
        // Use the API utility for consistent handling
        const response = await api.get('/api/categories', false)

        console.log('Categories response:', response)

        // Check if response has categories property
        if (response && response.categories && Array.isArray(response.categories)) {
          setCategories(response.categories)
        } else if (response && response.data && Array.isArray(response.data)) {
          // Backward compatibility with old API
          setCategories(response.data)
        } else if (response && Array.isArray(response)) {
          // If response is an array, use it directly
          setCategories(response)
        } else {
          // Last resort: try to extract categories from any property that might be an array
          const possibleArrays = Object.values(response || {}).filter(val => Array.isArray(val))
          if (possibleArrays.length > 0) {
            // Use the first array found
            setCategories(possibleArrays[0] as any[])
          } else {
            console.error('Unexpected categories response format:', response)
            setCategories([])
          }
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err)
        setError(err.message || "Failed to fetch categories")
      } finally {
        setIsLoading(false)
      }
    }

    fetchInitiative()
    fetchCategories()
    fetchSocialImpacts()
  }, [id, router, user, isAuthenticated])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: Number.parseInt(value) || 0 }))
  }

  // Gérer la sélection d'une catégorie d'impact social
  const handleSocialImpactCategoryChange = (categoryId: string, checked: boolean) => {
    if (checked) {
      // Ajouter la catégorie si elle n'existe pas déjà
      if (!formData.socialImpacts.some(item => item.category === categoryId)) {
        setFormData(prev => ({
          ...prev,
          socialImpacts: [...prev.socialImpacts, { category: categoryId, impacts: [] }]
        }))
      }
    } else {
      // Supprimer la catégorie et ses impacts
      setFormData(prev => ({
        ...prev,
        socialImpacts: prev.socialImpacts.filter(item => item.category !== categoryId),
        selectedImpacts: prev.selectedImpacts.filter(impact =>
          !socialImpactCategories.find(cat => cat._id === categoryId)?.impacts.some(imp => imp._id === impact._id)
        )
      }))
    }
  }

  // Gérer la sélection d'un impact social
  const handleSocialImpactChange = (categoryId: string, impactId: string, checked: boolean) => {
    // Trouver la catégorie dans les données du formulaire
    const categoryIndex = formData.socialImpacts.findIndex(item => item.category === categoryId)

    if (categoryIndex === -1) {
      // Si la catégorie n'existe pas, l'ajouter avec cet impact
      if (checked) {
        setFormData(prev => ({
          ...prev,
          socialImpacts: [...prev.socialImpacts, { category: categoryId, impacts: [impactId] }]
        }))
      }
    } else {
      // La catégorie existe, mettre à jour ses impacts
      const updatedSocialImpacts = [...formData.socialImpacts]

      if (checked) {
        // Ajouter l'impact s'il n'existe pas déjà
        if (!updatedSocialImpacts[categoryIndex].impacts.includes(impactId)) {
          updatedSocialImpacts[categoryIndex].impacts.push(impactId)
        }
      } else {
        // Supprimer l'impact
        updatedSocialImpacts[categoryIndex].impacts =
          updatedSocialImpacts[categoryIndex].impacts.filter(id => id !== impactId)
      }

      setFormData(prev => ({
        ...prev,
        socialImpacts: updatedSocialImpacts
      }))
    }

    // Mettre à jour également selectedImpacts pour faciliter l'affichage
    if (checked) {
      // Trouver l'impact dans les catégories
      const category = socialImpactCategories.find(cat => cat._id === categoryId)
      const impact = category?.impacts.find(imp => imp._id === impactId)

      if (impact && !formData.selectedImpacts.some(imp => imp._id === impactId)) {
        setFormData(prev => ({
          ...prev,
          selectedImpacts: [...prev.selectedImpacts, impact]
        }))
      }
    } else {
      // Supprimer l'impact de selectedImpacts
      setFormData(prev => ({
        ...prev,
        selectedImpacts: prev.selectedImpacts.filter(impact => impact._id !== impactId)
      }))
    }
  }

  // Vérifier si une catégorie est sélectionnée
  const isCategorySelected = (categoryId: string) => {
    return formData.socialImpacts.some(item => item.category === categoryId)
  }

  // Vérifier si un impact est sélectionné
  const isImpactSelected = (impactId: string) => {
    return formData.selectedImpacts.some(impact => impact._id === impactId)
  }

  // Handle main image upload
  const handleMainImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0]) return

    const file = e.target.files[0]
    console.log('Selected file:', file.name, file.type, file.size)

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file (JPEG, PNG, etc.)",
        variant: "destructive"
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Image must be less than 5MB",
        variant: "destructive"
      })
      return
    }

    setIsUploading(true)
    setUploadError("")

    try {
      console.log('Starting file upload...')
      // Check authentication status
      console.log('Authentication status:', isAuthenticated)
      console.log('Current user:', user)

      // Upload the file to the initiative-specific endpoint
      const response = await uploadFile(`/api/initiatives/${id}/image`, file, 'image')
      console.log('Upload response:', response)

      if (response && response.file && response.file.url) {
        // Update form data with the new image URL
        setFormData(prev => ({
          ...prev,
          mainImage: response.file.url
        }))

        toast({
          title: "Image uploaded",
          description: "Main image has been uploaded successfully",
          variant: "default"
        })
      } else {
        throw new Error("Failed to upload image: Invalid response format")
      }
    } catch (err: any) {
      console.error('Error uploading image:', err)
      setUploadError(err.message || "Failed to upload image")

      // Check token
      const token = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken')
      console.log('Current token exists:', !!token)
      if (token) {
        console.log('Token first 10 chars:', token.substring(0, 10) + '...')
      }

      toast({
        title: "Upload failed",
        description: err.message || "Failed to upload image. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsUploading(false)
      // Reset the file input
      if (mainImageInputRef.current) {
        mainImageInputRef.current.value = ''
      }
    }
  }

  // Handle additional images upload
  const handleAdditionalImagesUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return

    console.log('Selected files:', e.target.files.length)
    setIsUploading(true)
    setUploadError("")

    try {
      const newImages = [...formData.images]

      // Process each file
      for (let i = 0; i < e.target.files.length; i++) {
        const file = e.target.files[i]
        console.log(`Processing file ${i+1}/${e.target.files.length}:`, file.name, file.type, file.size)

        // Validate file type
        if (!file.type.startsWith('image/')) {
          toast({
            title: "Invalid file type",
            description: `File ${file.name} is not an image. Skipping.`,
            variant: "destructive"
          })
          continue
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          toast({
            title: "File too large",
            description: `File ${file.name} is larger than 5MB. Skipping.`,
            variant: "destructive"
          })
          continue
        }

        // Update progress
        setUploadProgress(Math.round((i / e.target.files.length) * 100))

        // Upload the file to the initiative-specific endpoint for additional images
        console.log(`Uploading file ${i+1}:`, file.name)
        const response = await uploadFile(`/api/initiatives/${id}/images`, file, 'image')
        console.log(`Upload response for file ${i+1}:`, response)

        if (response && response.file && response.file.url) {
          newImages.push(response.file.url)
          console.log('Added new image URL:', response.file.url)
        } else {
          console.warn(`File ${i+1} upload response did not contain expected URL:`, response)
        }
      }

      // Update form data with the new images
      setFormData(prev => ({
        ...prev,
        images: newImages
      }))
      console.log('Updated images array:', newImages)

      toast({
        title: "Images uploaded",
        description: "Additional images have been uploaded successfully",
        variant: "default"
      })
    } catch (err: any) {
      console.error('Error uploading images:', err)
      setUploadError(err.message || "Failed to upload images")

      // Check token
      const token = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken')
      console.log('Current token exists:', !!token)
      if (token) {
        console.log('Token first 10 chars:', token.substring(0, 10) + '...')
      }

      toast({
        title: "Upload failed",
        description: err.message || "Failed to upload images. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
      // Reset the file input
      if (additionalImagesInputRef.current) {
        additionalImagesInputRef.current.value = ''
      }
    }
  }

  // Remove an image from additional images
  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))

    toast({
      title: "Image removed",
      description: "Image has been removed from the initiative",
      variant: "default"
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isAuthenticated) {
      toast({
        title: "You need to be logged in",
        description: "Please log in to edit an initiative",
        variant: "destructive"
      })
      router.push("/auth/login")
      return
    }

    setIsSaving(true)

    try {
      // Process tags
      const tagsArray = formData.tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0)

      // Use the API utility for consistent handling
      const initiativeData = {
        title: formData.title,
        shortDescription: formData.shortDescription,
        fullDescription: formData.fullDescription,
        category: formData.category,
        location: formData.location,
        wilaya: formData.wilaya,
        goal: formData.goal,
        mainImage: formData.mainImage,
        images: formData.images,
        tags: tagsArray,
        budget: formData.budget || null,
        requiredVolunteers: formData.requiredVolunteers || null,
        // Nouveaux champs structurants
        problem: formData.problem || "",
        solution: formData.solution || "",
        beneficiaries: formData.beneficiaries || "",
        quantitativeObjectives: formData.quantitativeObjectives || "",
        qualitativeObjectives: formData.qualitativeObjectives || "",
        // Impacts sociaux - Assurons-nous que les données sont dans le bon format
        socialImpacts: formData.socialImpacts.map(item => ({
          category: item.category,
          impacts: item.impacts || []
        })),
        selectedImpacts: formData.selectedImpacts.map(impact => ({
          _id: impact._id,
          name: impact.name,
          arabicName: impact.arabicName,
          isActive: impact.isActive !== false
        }))
      }

      console.log('Sending initiative data:', JSON.stringify(initiativeData, null, 2))

      await api.put(`/api/initiatives/${id}`, initiativeData)

      toast({
        title: "Initiative Updated",
        description: "Your initiative has been updated successfully!",
        variant: "default"
      })

      // Redirect to initiative page
      router.push(`/initiatives/${id}`)
    } catch (err: any) {
      console.error('Error updating initiative:', err)
      setError(err.message || "Failed to update initiative")

      toast({
        title: "Update Failed",
        description: err.message || "Failed to update initiative. Please try again.",
        variant: "destructive"
      })

      setIsSaving(false)
    }
  }

  const handleDelete = async () => {
    if (!isAuthenticated) {
      toast({
        title: "You need to be logged in",
        description: "Please log in to delete an initiative",
        variant: "destructive"
      })
      router.push("/auth/login")
      return
    }

    setIsDeleting(true)

    try {
      await api.delete(`/api/initiatives/${id}`)

      toast({
        title: "Initiative Deleted",
        description: "Your initiative has been deleted successfully",
        variant: "default"
      })

      // Redirect to initiatives list
      router.push('/initiatives')
    } catch (err: any) {
      console.error('Error deleting initiative:', err)
      setError(err.message || "Failed to delete initiative")

      toast({
        title: "Delete Failed",
        description: err.message || "Failed to delete initiative. Please try again.",
        variant: "destructive"
      })

      setIsDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading initiative...</span>
      </div>
    )
  }

  if (!initiative) {
    return (
      <div className="container mx-auto px-4 py-8" dir="rtl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "لم يتم العثور على المبادرة"}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Link href="/initiatives">
            <Button variant="outline">العودة إلى المبادرات</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <div className="mb-6">
        <Link href={`/initiatives/${id}`} className="flex items-center text-green-600 hover:text-green-800">
          <ArrowLeft className="h-4 w-4 ml-1 transform rotate-180" />
          العودة إلى المبادرة
        </Link>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-2xl">تعديل المبادرة</CardTitle>
          <CardDescription>
            تحديث تفاصيل المبادرة والصور والمعلومات الأخرى
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-8">
              <TabsTrigger value="details">التفاصيل</TabsTrigger>
              <TabsTrigger value="images">الصور</TabsTrigger>
              <TabsTrigger value="additional">معلومات إضافية</TabsTrigger>
              <TabsTrigger value="social-impact">التأثير الاجتماعي</TabsTrigger>
            </TabsList>

            <form onSubmit={handleSubmit}>
              <TabsContent value="details" className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block font-medium mb-1 text-right w-full">
                      العنوان
                    </label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      placeholder="عنوان المبادرة"
                      required
                      dir="rtl"
                    />
                  </div>

                  <div>
                    <label htmlFor="shortDescription" className="block font-medium mb-1 text-right w-full">
                      وصف مختصر
                    </label>
                    <Textarea
                      id="shortDescription"
                      name="shortDescription"
                      value={formData.shortDescription}
                      onChange={handleChange}
                      placeholder="وصف موجز للمبادرة (الحد الأقصى 200 حرف)"
                      maxLength={200}
                      required
                      dir="rtl"
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.shortDescription.length}/200 حرف</p>
                  </div>

                  <div>
                    <label htmlFor="fullDescription" className="block font-medium mb-1 text-right w-full">
                      الوصف الكامل
                    </label>
                    <Textarea
                      id="fullDescription"
                      name="fullDescription"
                      value={formData.fullDescription}
                      onChange={handleChange}
                      placeholder="وصف تفصيلي للمبادرة"
                      className="min-h-[200px]"
                      required
                      dir="rtl"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="category" className="block font-medium mb-1 text-right w-full">
                        الفئة
                      </label>
                      <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر فئة" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem
                              key={category._id || category.id || Math.random().toString()}
                              value={category._id || category.id || ''}>
                              {category.arabicName || category.name || 'فئة غير معروفة'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label htmlFor="location" className="block font-medium mb-1 text-right w-full">
                        الموقع
                      </label>
                      <Input
                        id="location"
                        name="location"
                        value={formData.location}
                        onChange={handleChange}
                        placeholder="المدينة أو المنطقة"
                        required
                        dir="rtl"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="wilaya" className="block font-medium mb-1 text-right w-full">
                        الولاية (اختياري)
                      </label>
                      <Input
                        id="wilaya"
                        name="wilaya"
                        value={formData.wilaya}
                        onChange={handleChange}
                        placeholder="الولاية"
                        dir="rtl"
                      />
                    </div>

                    <div>
                      <label htmlFor="goal" className="block font-medium mb-1 text-right w-full">
                        هدف الدعم
                      </label>
                      <Input
                        id="goal"
                        name="goal"
                        type="number"
                        value={formData.goal}
                        onChange={handleNumberChange}
                        min={1}
                        required
                        dir="rtl"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="tags" className="block font-medium mb-1 text-right w-full">
                      الوسوم (مفصولة بفواصل)
                    </label>
                    <Input
                      id="tags"
                      name="tags"
                      value={formData.tags}
                      onChange={handleChange}
                      placeholder="بيئة، تعليم، مجتمع، إلخ."
                      dir="rtl"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="images" className="space-y-6">
                <div className="space-y-6">
                  {/* Main Image Section */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h3 className="text-lg font-medium mb-4 text-right w-full">الصورة الرئيسية</h3>

                    <div className="flex flex-col md:flex-row gap-6">
                      <div className="w-full md:w-1/2 flex justify-center items-center">
                        <div className="relative w-full aspect-video rounded-lg overflow-hidden border bg-white">
                          {formData.mainImage ? (
                            <Image
                              src={formData.mainImage}
                              alt="صورة المبادرة الرئيسية"
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <div className="flex flex-col items-center justify-center h-full text-gray-400">
                              <ImageIcon size={48} />
                              <p className="mt-2">لم يتم اختيار صورة</p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="w-full md:w-1/2 flex flex-col justify-center">
                        <p className="text-sm text-gray-600 mb-4 text-right">
                          هذه هي الصورة الرئيسية التي سيتم عرضها لمبادرتك.
                          اختر صورة عالية الجودة تمثل مبادرتك بشكل جيد.
                        </p>

                        <div className="flex flex-col gap-2">
                          <input
                            type="file"
                            ref={mainImageInputRef}
                            onChange={handleMainImageUpload}
                            accept="image/*"
                            className="hidden"
                          />

                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => mainImageInputRef.current?.click()}
                            disabled={isUploading}
                            className="flex items-center gap-2 justify-center"
                          >
                            {isUploading ? <Loader2 className="h-4 w-4 animate-spin ml-2" /> : <Upload className="h-4 w-4 ml-2" />}
                            <span>{isUploading ? "جاري الرفع..." : "رفع صورة"}</span>
                          </Button>

                          {formData.mainImage && (
                            <Button
                              type="button"
                              variant="ghost"
                              onClick={() => setFormData(prev => ({ ...prev, mainImage: "" }))}
                              className="flex items-center gap-2 text-red-500 hover:text-red-700 justify-center"
                            >
                              <X className="h-4 w-4 ml-2" />
                              <span>إزالة الصورة</span>
                            </Button>
                          )}

                          <p className="text-xs text-gray-500 mt-1 text-right">الحجم الموصى به: 1200×600 بكسل. الحد الأقصى: 5 ميجابايت.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional Images Section */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h3 className="text-lg font-medium mb-4 text-right w-full">صور إضافية</h3>

                    <div className="mb-4">
                      <p className="text-sm text-gray-600 text-right">
                        أضف المزيد من الصور لعرض جوانب مختلفة من مبادرتك.
                      </p>

                      <div className="mt-4 flex flex-wrap gap-2 justify-end">
                        <input
                          type="file"
                          ref={additionalImagesInputRef}
                          onChange={handleAdditionalImagesUpload}
                          accept="image/*"
                          multiple
                          className="hidden"
                        />

                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => additionalImagesInputRef.current?.click()}
                          disabled={isUploading}
                          className="flex items-center gap-2"
                        >
                          {isUploading ? <Loader2 className="h-4 w-4 animate-spin ml-2" /> : <Camera className="h-4 w-4 ml-2" />}
                          <span>{isUploading ? `جاري الرفع... ${uploadProgress}%` : "إضافة صور"}</span>
                        </Button>
                      </div>
                    </div>

                    {uploadError && (
                      <Alert variant="destructive" className="mb-4">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{uploadError}</AlertDescription>
                      </Alert>
                    )}

                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
                      {formData.images.length > 0 ? (
                        formData.images.map((image, index) => (
                          <div key={index} className="relative group aspect-square rounded-md overflow-hidden border bg-white">
                            <Image
                              src={image}
                              alt={`صورة المبادرة ${index + 1}`}
                              fill
                              className="object-cover"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={() => removeImage(index)}
                                className="h-8 w-8 p-0 rounded-full"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="col-span-full flex flex-col items-center justify-center p-8 border rounded-md bg-gray-100 text-gray-400">
                          <FileImage size={48} />
                          <p className="mt-2">لا توجد صور إضافية حتى الآن</p>
                          <p className="text-sm">انقر على "إضافة صور" للرفع</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="additional" className="space-y-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="budget" className="block font-medium mb-1 text-right w-full">
                        الميزانية (اختياري)
                      </label>
                      <Input
                        id="budget"
                        name="budget"
                        type="number"
                        value={formData.budget}
                        onChange={handleNumberChange}
                        min={0}
                        placeholder="مبلغ الميزانية"
                        dir="rtl"
                      />
                      <p className="text-xs text-gray-500 mt-1 text-right">الميزانية التقديرية اللازمة لمبادرتك</p>
                    </div>

                    <div>
                      <label htmlFor="requiredVolunteers" className="block font-medium mb-1 text-right w-full">
                        المتطوعين المطلوبين (اختياري)
                      </label>
                      <Input
                        id="requiredVolunteers"
                        name="requiredVolunteers"
                        type="number"
                        value={formData.requiredVolunteers}
                        onChange={handleNumberChange}
                        min={0}
                        placeholder="عدد المتطوعين المطلوبين"
                        dir="rtl"
                      />
                      <p className="text-xs text-gray-500 mt-1 text-right">كم عدد المتطوعين الذين تحتاجهم؟</p>
                    </div>
                  </div>

                  {/* Nouveaux champs structurants */}
                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4 text-right">معلومات هيكلية</h3>

                    <div className="space-y-4">
                      <div>
                        <label htmlFor="problem" className="block font-medium mb-1 text-right w-full">
                          المشكلة / الفرصة
                        </label>
                        <Textarea
                          id="problem"
                          name="problem"
                          value={formData.problem}
                          onChange={handleChange}
                          placeholder="ما هي المشكلة أو الفرصة التي تعالجها مبادرتك؟"
                          dir="rtl"
                        />
                      </div>

                      <div>
                        <label htmlFor="solution" className="block font-medium mb-1 text-right w-full">
                          الحل
                        </label>
                        <Textarea
                          id="solution"
                          name="solution"
                          value={formData.solution}
                          onChange={handleChange}
                          placeholder="كيف تقترح مبادرتك حلاً للمشكلة المذكورة؟"
                          dir="rtl"
                        />
                      </div>

                      <div>
                        <label htmlFor="beneficiaries" className="block font-medium mb-1 text-right w-full">
                          المستفيدون
                        </label>
                        <Textarea
                          id="beneficiaries"
                          name="beneficiaries"
                          value={formData.beneficiaries}
                          onChange={handleChange}
                          placeholder="من هم المستفيدون من مبادرتك؟"
                          dir="rtl"
                        />
                      </div>

                      <div>
                        <label htmlFor="quantitativeObjectives" className="block font-medium mb-1 text-right w-full">
                          الأهداف الكمية
                        </label>
                        <Textarea
                          id="quantitativeObjectives"
                          name="quantitativeObjectives"
                          value={formData.quantitativeObjectives}
                          onChange={handleChange}
                          placeholder="ما هي الأهداف الكمية لمبادرتك؟"
                          dir="rtl"
                        />
                      </div>

                      <div>
                        <label htmlFor="qualitativeObjectives" className="block font-medium mb-1 text-right w-full">
                          الأهداف النوعية
                        </label>
                        <Textarea
                          id="qualitativeObjectives"
                          name="qualitativeObjectives"
                          value={formData.qualitativeObjectives}
                          onChange={handleChange}
                          placeholder="ما هي الأهداف النوعية لمبادرتك؟"
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="social-impact" className="space-y-6">
                <div className="space-y-6">
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <h3 className="text-lg font-medium mb-2 text-right">التأثير الاجتماعي</h3>
                    <p className="text-sm text-gray-600 mb-4 text-right">
                      حدد فئات وعناصر التأثير الاجتماعي التي تتوقع أن تحققها مبادرتك.
                    </p>

                    {socialImpactCategories.length === 0 ? (
                      <div className="text-center p-8 bg-gray-100 rounded-md">
                        <p className="text-gray-500">جاري تحميل فئات التأثير الاجتماعي...</p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {socialImpactCategories.map((category) => (
                          <div key={category._id} className="border rounded-md p-4">
                            <div className="flex items-center justify-end mb-4">
                              <label htmlFor={`category-${category._id}`} className="font-medium text-right flex-grow">
                                {category.arabicName}
                              </label>
                              <input
                                type="checkbox"
                                id={`category-${category._id}`}
                                checked={isCategorySelected(category._id)}
                                onChange={(e) => handleSocialImpactCategoryChange(category._id, e.target.checked)}
                                className="h-4 w-4 text-green-600 focus:ring-green-500 mr-2"
                              />
                            </div>

                            {isCategorySelected(category._id) && (
                              <div className="pr-6 border-r-2 border-green-200 mr-2">
                                <div className="space-y-2">
                                  {category.impacts.map((impact) => (
                                    <div key={impact._id} className="flex items-center justify-end">
                                      <label htmlFor={`impact-${impact._id}`} className="text-sm text-right flex-grow">
                                        {impact.arabicName}
                                      </label>
                                      <input
                                        type="checkbox"
                                        id={`impact-${impact._id}`}
                                        checked={isImpactSelected(impact._id)}
                                        onChange={(e) => handleSocialImpactChange(category._id, impact._id, e.target.checked)}
                                        className="h-4 w-4 text-green-600 focus:ring-green-500 mr-2"
                                      />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {formData.selectedImpacts.length > 0 && (
                      <div className="mt-6 border-t pt-4">
                        <h4 className="font-medium mb-2 text-right">التأثيرات المختارة:</h4>
                        <div className="flex flex-wrap gap-2 justify-end">
                          {formData.selectedImpacts.map((impact) => (
                            <div key={impact._id} className="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full flex items-center">
                              <span className="text-right">{impact.arabicName}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <div className="flex justify-between mt-8">
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                  disabled={isDeleting}
                  className="flex items-center gap-2"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin ml-2" />
                      <span>جاري الحذف...</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 ml-2" />
                      <span>حذف المبادرة</span>
                    </>
                  )}
                </Button>

                <div className="flex gap-4">
                  <Link href={`/initiatives/${id}`}>
                    <Button type="button" variant="outline">
                      إلغاء
                    </Button>
                  </Link>
                  <Button type="submit" className="bg-green-600 hover:bg-green-700" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                        <span>جاري الحفظ...</span>
                      </>
                    ) : (
                      <>
                        <Save className="ml-2 h-4 w-4" />
                        <span>حفظ التغييرات</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Tabs>
        </CardContent>
      </Card>
      <Toaster />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف المبادرة</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف هذه المبادرة؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex items-center justify-between mt-4">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="flex items-center gap-2"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin ml-2" />
                  <span>جاري الحذف...</span>
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 ml-2" />
                  <span>حذف المبادرة</span>
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


import express from "express"
import cors from "cors"
import helmet from "helmet"
import morgan from "morgan"
import dotenv from "dotenv"
import { connectToDatabase } from "./db/connection"
import routes from "./routes"
import errorHandler from "./middleware/errorHandler"
import { createRateLimiter, createAuthRateLimiter } from "./middleware/rateLimiter"
import { serveStaticFiles } from "./middleware/staticFiles"
import { updateInitiativesProgress } from "./tasks/update-initiatives-progress"
import { logger } from "./utils/logger"

// Load environment variables
dotenv.config()

// Create Express app
const app = express()
const PORT = process.env.PORT || 5000
const isDevelopment = process.env.NODE_ENV === 'development';

// Middleware
// Configure CORS for development
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
}));

// Add CORS headers to all responses as a fallback
app.use((req, res, next) => {
  const origin = req.headers.origin;
  if (origin && (origin === 'http://localhost:3000' || origin === 'http://localhost:3001' || origin === 'http://localhost:3002')) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    // Allow any origin in development mode
    res.header('Access-Control-Allow-Origin', '*');
  }
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Add Cross-Origin-Resource-Policy header to allow cross-origin resource sharing
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  // Add Cross-Origin-Embedder-Policy header
  res.header('Cross-Origin-Embedder-Policy', 'credentialless');

  next();
});

// Configure Helmet security headers

// Disable Helmet for development to avoid CORS issues
if (isDevelopment) {
  // More permissive configuration for development
  app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false,
    crossOriginOpenerPolicy: false,
    crossOriginResourcePolicy: false // Disable CORP completely for development
  }));
} else {
  // Stricter configuration for production
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        imgSrc: ["'self'", 'data:', 'https://res.cloudinary.com'],
        fontSrc: ["'self'", 'https://fonts.gstatic.com'],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: [],
      },
    },
    crossOriginResourcePolicy: { policy: 'same-origin' },
    crossOriginOpenerPolicy: { policy: 'same-origin' },
    crossOriginEmbedderPolicy: { policy: 'require-corp' },
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true,
    },
  }));
}
app.use(express.json({ limit: "10mb" })) // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: "10mb" })) // Parse URL-encoded bodies
// app.use(morgan("dev")) // Logging disabled

// Rate limiting disabled for development
// Uncomment these lines for production
// app.use(createRateLimiter())
// app.use("/api/auth/login", createAuthRateLimiter())
// app.use("/api/auth/register", createAuthRateLimiter())
// app.use("/api/auth/forgot-password", createAuthRateLimiter())
// app.use("/api/auth/reset-password", createAuthRateLimiter())

// Serve static files
serveStaticFiles(app)

// API routes
app.use("/api", routes)

// Error handling middleware
app.use(errorHandler)

// Start server
const startServer = async () => {
  try {
    // Connect to MongoDB
    await connectToDatabase()

    // Start listening
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`)
    })
  } catch (error) {
    console.error("Failed to start server:", error)
    process.exit(1)
  }
}

startServer()

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("Unhandled Promise Rejection:", err)
  // Don't crash the server, but log the error
})

export default app


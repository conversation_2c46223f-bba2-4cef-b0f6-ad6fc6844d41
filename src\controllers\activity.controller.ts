import { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Activity, User, Initiative } from "../models"
import { asyncHand<PERSON> } from "../utils/error"
import { createError } from "../utils/error"

/**
 * Créer une nouvelle activité
 * @route POST /api/activities
 * @access Private
 */
export const createActivity = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { type, action, content, relatedInitiative, relatedUser, relatedComment, metadata, isPublic } = req.body
  const userId = req.user.id

  // Vérifier que le type et l'action sont valides
  const validTypes = ["initiative", "support", "comment", "volunteer", "badge", "profile", "system"]
  const validActions = ["create", "update", "delete", "join", "leave", "support", "unsupport", "comment", "reply", "earn", "complete", "invite", "accept", "reject", "login", "register"]

  if (!validTypes.includes(type)) {
    return next(createError(400, `Type d'activité invalide. Types valides: ${validTypes.join(", ")}`))
  }

  if (!validActions.includes(action)) {
    return next(createError(400, `Action d'activité invalide. Actions valides: ${validActions.join(", ")}`))
  }

  // Créer l'activité
  const activity = new Activity({
    user: userId,
    type,
    action,
    content,
    relatedInitiative,
    relatedUser,
    relatedComment,
    metadata,
    isPublic: isPublic !== undefined ? isPublic : true
  })

  await activity.save()

  res.status(201).json({
    success: true,
    data: activity
  })
})

/**
 * Récupérer les activités d'un utilisateur
 * @route GET /api/users/:userId/activities
 * @access Public
 */
export const getUserActivities = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  // Accepter soit userId soit id comme paramètre
  const userId = req.params.userId || req.params.id
  const { page = 1, limit = 10, type, action } = req.query
  const pageNumber = parseInt(page as string)
  const limitNumber = parseInt(limit as string)

  console.log("Fetching activities for user:", userId);

  // Vérifier que l'ID utilisateur est valide
  if (!mongoose.Types.ObjectId.isValid(userId)) {
    return next(createError(400, "ID utilisateur invalide"))
  }

  // Vérifier que l'utilisateur existe
  const user = await User.findById(userId)
  if (!user) {
    return next(createError(404, "Utilisateur non trouvé"))
  }

  // Construire la requête
  const query: any = { user: userId }

  // Si l'utilisateur n'est pas connecté ou n'est pas l'utilisateur concerné, ne montrer que les activités publiques
  if (!req.user || req.user.id !== userId) {
    query.isPublic = true
  }

  // Filtrer par type si spécifié
  if (type) {
    query.type = type
  }

  // Filtrer par action si spécifiée
  if (action) {
    query.action = action
  }

  // Compter le nombre total d'activités
  const total = await Activity.countDocuments(query)

  // Récupérer les activités
  const activities = await Activity.find(query)
    .sort({ date: -1 })
    .skip((pageNumber - 1) * limitNumber)
    .limit(limitNumber)
    .populate("relatedInitiative", "title _id")
    .populate("relatedUser", "name username avatar _id")
    .populate("relatedComment", "content _id")

  res.status(200).json({
    success: true,
    activities,
    pagination: {
      total,
      page: pageNumber,
      limit: limitNumber,
      pages: Math.ceil(total / limitNumber)
    }
  })
})

/**
 * Récupérer les activités liées à une initiative
 * @route GET /api/initiatives/:initiativeId/activities
 * @access Public
 */
export const getInitiativeActivities = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiativeId } = req.params
  const { page = 1, limit = 10, type, action } = req.query
  const pageNumber = parseInt(page as string)
  const limitNumber = parseInt(limit as string)

  // Vérifier que l'ID initiative est valide
  if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
    return next(createError(400, "ID initiative invalide"))
  }

  // Vérifier que l'initiative existe
  const initiative = await Initiative.findById(initiativeId)
  if (!initiative) {
    return next(createError(404, "Initiative non trouvée"))
  }

  // Construire la requête
  const query: any = {
    relatedInitiative: initiativeId,
    isPublic: true
  }

  // Filtrer par type si spécifié
  if (type) {
    query.type = type
  }

  // Filtrer par action si spécifiée
  if (action) {
    query.action = action
  }

  // Compter le nombre total d'activités
  const total = await Activity.countDocuments(query)

  // Récupérer les activités
  const activities = await Activity.find(query)
    .sort({ date: -1 })
    .skip((pageNumber - 1) * limitNumber)
    .limit(limitNumber)
    .populate("user", "name username avatar _id")
    .populate("relatedUser", "name username avatar _id")
    .populate("relatedComment", "content _id")

  res.status(200).json({
    success: true,
    activities,
    pagination: {
      total,
      page: pageNumber,
      limit: limitNumber,
      pages: Math.ceil(total / limitNumber)
    }
  })
})

/**
 * Supprimer une activité
 * @route DELETE /api/activities/:id
 * @access Private
 */
export const deleteActivity = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const userId = req.user.id

  // Vérifier que l'ID activité est valide
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "ID activité invalide"))
  }

  // Récupérer l'activité
  const activity = await Activity.findById(id)
  if (!activity) {
    return next(createError(404, "Activité non trouvée"))
  }

  // Vérifier que l'utilisateur est autorisé à supprimer l'activité
  if (activity.user.toString() !== userId && req.user.role !== "admin") {
    return next(createError(403, "Vous n'êtes pas autorisé à supprimer cette activité"))
  }

  // Supprimer l'activité
  await activity.deleteOne()

  res.status(200).json({
    success: true,
    message: "Activité supprimée avec succès"
  })
})

/**
 * Utilitaire pour créer une activité
 * @param userId ID de l'utilisateur
 * @param type Type d'activité
 * @param action Action effectuée
 * @param content Contenu de l'activité
 * @param options Options supplémentaires
 */
export const createActivityUtil = async (
  userId: string,
  type: string,
  action: string,
  content: string,
  options: {
    relatedInitiative?: string;
    relatedUser?: string;
    relatedComment?: string;
    metadata?: Record<string, any>;
    isPublic?: boolean;
  } = {}
) => {
  try {
    const startTime = Date.now();
    const { relatedInitiative, relatedUser, relatedComment, metadata, isPublic = true } = options

    const activity = new Activity({
      user: userId,
      type,
      action,
      content,
      relatedInitiative,
      relatedUser,
      relatedComment,
      metadata,
      isPublic
    })

    // Enregistrer l'activité de manière asynchrone sans attendre
    // Cela permet de ne pas bloquer l'exécution du reste du code
    activity.save().catch(err => {
      console.error("Erreur lors de l'enregistrement asynchrone de l'activité:", err);
    });

    const endTime = Date.now();
    console.log(`Activité créée en ${endTime - startTime}ms: ${type}:${action} pour utilisateur ${userId}`);

    return activity;
  } catch (error) {
    console.error("Erreur lors de la création de l'activité:", error)
    return null
  }
}

import type { Request, Response, NextFunction } from "express"
import { Settings } from "../models"
import { createError } from "../utils/error"

// Get all settings (admin only)
export const getAllSettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Temporarily removed admin check for testing
    // if (req.user && req.user.role !== "admin") {
    //   return next(createError(403, "You are not authorized to access all settings"))
    // }

    // Get settings from database
    let settings = await Settings.findOne({ settingsId: "global" })

    // If no settings exist, create default settings
    if (!settings) {
      settings = await Settings.create({ settingsId: "global" })
    }

    res.status(200).json({
      success: true,
      settings
    })
  } catch (error) {
    next(error)
  }
}

// Get public settings
export const getPublicSettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get settings from database
    let settings = await Settings.findOne({ settingsId: "global" })

    // If no settings exist, create default settings
    if (!settings) {
      settings = await Settings.create({ settingsId: "global" })
    }

    // Return only public settings
    const publicSettings = {
      siteName: settings.siteName,
      siteDescription: settings.siteDescription,
      contactEmail: settings.contactEmail,
      supportPhone: settings.supportPhone,
      maintenanceMode: settings.maintenanceMode,
      termsAndConditions: settings.termsAndConditions,
      privacyPolicy: settings.privacyPolicy,
      aboutUs: settings.aboutUs,
      socialLinks: settings.socialLinks
    }

    res.status(200).json({
      success: true,
      settings: publicSettings
    })
  } catch (error) {
    next(error)
  }
}

// Update settings (admin only)
export const updateSettings = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Temporarily removed admin check for testing
    // if (req.user && req.user.role !== "admin") {
    //   return next(createError(403, "You are not authorized to update settings"))
    // }

    // Get settings from database
    let settings = await Settings.findOne({ settingsId: "global" })

    // If no settings exist, create default settings
    if (!settings) {
      settings = await Settings.create({ settingsId: "global" })
    }

    // Update settings
    const updatedSettings = await Settings.findOneAndUpdate(
      { settingsId: "global" },
      req.body,
      { new: true, runValidators: true }
    )

    res.status(200).json({
      success: true,
      settings: updatedSettings
    })
  } catch (error) {
    next(error)
  }
}


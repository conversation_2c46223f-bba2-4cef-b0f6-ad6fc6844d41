import { Request, Response, NextFunction } from "express";
import { createError } from "../utils/error";
import { User } from "../models";

/**
 * Middleware to check if user has the required permission
 * @param permissionCode The permission code to check
 */
export const hasPermission = (permissionCode: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        return next(createError(401, "Unauthorized"));
      }

      // Special case for admin role - grant access immediately
      if (req.user.role === 'admin') {
        return next();
      }

      // Check if permissions are included in the JWT token
      if (req.user.permissions && Array.isArray(req.user.permissions)) {
        // If the permission is in the token, grant access immediately
        if (req.user.permissions.includes(permissionCode)) {
          return next();
        }
      }

      // If permission not found in token or token doesn't have permissions,
      // fall back to database check

      // Get user from database
      const user = await User.findById(req.user.id)
        .populate({
          path: "role",
          populate: {
            path: "permissions",
          },
        })
        .populate("directPermissions");

      if (!user) {
        return next(createError(404, "User not found"));
      }

      // Special case for admin role (double-check from database)
      if (typeof user.role === 'string' && user.role === 'admin') {
        return next();
      } else if (typeof user.role === 'object' && (user.role as any)?.code === 'admin') {
        return next();
      }

      // Check if user has the required permission
      const hasRequiredPermission = await user.hasPermission(permissionCode);

      if (!hasRequiredPermission) {
        return next(createError(403, "Forbidden: Insufficient permissions"));
      }

      // User has the required permission, proceed
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to check if user has any of the required permissions
 * @param permissionCodes Array of permission codes to check
 */
export const hasAnyPermission = (permissionCodes: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        return next(createError(401, "Unauthorized"));
      }

      // Get user from database
      const user = await User.findById(req.user.id)
        .populate({
          path: "role",
          populate: {
            path: "permissions",
          },
        })
        .populate("directPermissions");

      if (!user) {
        return next(createError(404, "User not found"));
      }

      // Check if user has any of the required permissions
      for (const permissionCode of permissionCodes) {
        const hasPermission = await user.hasPermission(permissionCode);
        if (hasPermission) {
          // User has at least one of the required permissions, proceed
          return next();
        }
      }

      // User doesn't have any of the required permissions
      return next(createError(403, "Forbidden: Insufficient permissions"));
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to check if user has all of the required permissions
 * @param permissionCodes Array of permission codes to check
 */
export const hasAllPermissions = (permissionCodes: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        return next(createError(401, "Unauthorized"));
      }

      // Get user from database
      const user = await User.findById(req.user.id)
        .populate({
          path: "role",
          populate: {
            path: "permissions",
          },
        })
        .populate("directPermissions");

      if (!user) {
        return next(createError(404, "User not found"));
      }

      // Check if user has all of the required permissions
      for (const permissionCode of permissionCodes) {
        const hasPermission = await user.hasPermission(permissionCode);
        if (!hasPermission) {
          // User is missing at least one required permission
          return next(createError(403, "Forbidden: Insufficient permissions"));
        }
      }

      // User has all required permissions, proceed
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to check if user is an admin (has system:super_admin permission)
 */
export const isAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      return next(createError(401, "Unauthorized"));
    }

    // Get user from database
    const user = await User.findById(req.user.id)
      .populate({
        path: "role",
        populate: {
          path: "permissions",
        },
      })
      .populate("directPermissions");

    if (!user) {
      return next(createError(404, "User not found"));
    }

    // Check if user has admin permission
    const isAdmin = await user.hasPermission("system:super_admin");

    if (!isAdmin) {
      return next(createError(403, "Forbidden: Admin access required"));
    }

    // User is an admin, proceed
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check if user is a moderator (has admin:access permission)
 */
export const isModerator = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      return next(createError(401, "Unauthorized"));
    }

    // Get user from database
    const user = await User.findById(req.user.id)
      .populate({
        path: "role",
        populate: {
          path: "permissions",
        },
      })
      .populate("directPermissions");

    if (!user) {
      return next(createError(404, "User not found"));
    }

    // Check if user has moderator permission
    const isModerator = await user.hasPermission("admin:access");

    if (!isModerator) {
      return next(createError(403, "Forbidden: Moderator access required"));
    }

    // User is a moderator, proceed
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check if user is an admin or moderator
 */
export const isAdminOrModerator = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      return next(createError(401, "Unauthorized"));
    }

    // Get user from database
    const user = await User.findById(req.user.id)
      .populate({
        path: "role",
        populate: {
          path: "permissions",
        },
      })
      .populate("directPermissions");

    if (!user) {
      return next(createError(404, "User not found"));
    }

    // Check if user has admin or moderator permission
    const isAdmin = await user.hasPermission("system:super_admin");
    const isModerator = await user.hasPermission("admin:access");

    if (!isAdmin && !isModerator) {
      return next(createError(403, "Forbidden: Admin or moderator access required"));
    }

    // User is an admin or moderator, proceed
    next();
  } catch (error) {
    next(error);
  }
};

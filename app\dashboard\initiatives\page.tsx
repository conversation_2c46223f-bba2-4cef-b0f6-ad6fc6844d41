"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { Input } from "../../../components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "../../../components/ui/tabs"
import { Badge } from "../../../components/ui/badge"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import { 
  Loader2, 
  AlertCircle, 
  Search, 
  Plus, 
  Filter, 
  ArrowUpDown, 
  Edit, 
  Trash2, 
  Eye, 
  BarChart, 
  CheckCircle2, 
  Clock, 
  AlertTriangle,
  Calendar,
  Users,
  ThumbsUp
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../../components/ui/dialog"
import { Progress } from "../../../components/ui/progress"
import { useAuth } from "../../../components/auth-provider"
import { api } from "../../../lib/api"
import { toast } from "../../../components/ui/use-toast"
import { Toaster } from "../../../components/ui/toaster"

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  author: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  location: string
  wilaya?: string
  mainImage: string
  status: "draft" | "pending" | "active" | "completed" | "rejected"
  goal: number
  supportCount: number
  commentCount: number
  viewCount: number
  progress: number
  createdAt: string
  updatedAt: string
  milestones?: {
    total: number
    completed: number
  }
}

interface InitiativeStats {
  total: number
  active: number
  completed: number
  pending: number
  rejected: number
  draft: number
  totalSupports: number
  totalViews: number
  totalComments: number
}

export default function InitiativesDashboardPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [filteredInitiatives, setFilteredInitiatives] = useState<Initiative[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("newest")
  const [stats, setStats] = useState<InitiativeStats>({
    total: 0,
    active: 0,
    completed: 0,
    pending: 0,
    rejected: 0,
    draft: 0,
    totalSupports: 0,
    totalViews: 0,
    totalComments: 0
  })
  const [initiativeToDelete, setInitiativeToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login")
      return
    }

    fetchInitiatives()
  }, [isAuthenticated, router])

  useEffect(() => {
    if (initiatives.length > 0) {
      applyFilters()
    }
  }, [initiatives, searchQuery, statusFilter, sortBy, activeTab])

  const fetchInitiatives = async () => {
    setIsLoading(true)
    try {
      if (!user) return

      const response = await api.get(`/api/initiatives/user/${user.id}?includeStats=true`, true)

      if (response.success) {
        setInitiatives(response.initiatives || [])
        
        // Calculate stats
        const initiativesData = response.initiatives || []
        const stats: InitiativeStats = {
          total: initiativesData.length,
          active: initiativesData.filter(i => i.status === 'active').length,
          completed: initiativesData.filter(i => i.status === 'completed').length,
          pending: initiativesData.filter(i => i.status === 'pending').length,
          rejected: initiativesData.filter(i => i.status === 'rejected').length,
          draft: initiativesData.filter(i => i.status === 'draft').length,
          totalSupports: initiativesData.reduce((sum, i) => sum + (i.supportCount || 0), 0),
          totalViews: initiativesData.reduce((sum, i) => sum + (i.viewCount || 0), 0),
          totalComments: initiativesData.reduce((sum, i) => sum + (i.commentCount || 0), 0)
        }
        setStats(stats)
      } else {
        setError("Failed to fetch initiatives")
      }
    } catch (err: any) {
      console.error("Error fetching initiatives:", err)
      setError(err.message || "An error occurred while fetching initiatives")
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...initiatives]

    // Apply tab filter
    if (activeTab !== "all") {
      filtered = filtered.filter(initiative => initiative.status === activeTab)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        initiative =>
          initiative.title.toLowerCase().includes(query) ||
          initiative.shortDescription.toLowerCase().includes(query) ||
          initiative.location.toLowerCase().includes(query)
      )
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(initiative => initiative.status === statusFilter)
    }

    // Apply sorting
    switch (sortBy) {
      case "newest":
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        break
      case "oldest":
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        break
      case "most-supported":
        filtered.sort((a, b) => b.supportCount - a.supportCount)
        break
      case "most-viewed":
        filtered.sort((a, b) => b.viewCount - a.viewCount)
        break
      case "progress-high":
        filtered.sort((a, b) => b.progress - a.progress)
        break
      case "progress-low":
        filtered.sort((a, b) => a.progress - b.progress)
        break
      default:
        break
    }

    setFilteredInitiatives(filtered)
  }

  const handleDeleteInitiative = async () => {
    if (!initiativeToDelete) return

    setIsDeleting(true)
    try {
      const response = await api.delete(`/api/initiatives/${initiativeToDelete}`, true)

      if (response.success) {
        // Remove from state
        setInitiatives(initiatives.filter(i => i._id !== initiativeToDelete))
        toast({
          title: "Initiative Deleted",
          description: "The initiative has been successfully deleted.",
          variant: "default"
        })
      } else {
        throw new Error(response.message || "Failed to delete initiative")
      }
    } catch (err: any) {
      console.error("Error deleting initiative:", err)
      toast({
        title: "Error",
        description: err.message || "An error occurred while deleting the initiative",
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setInitiativeToDelete(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border-green-200">نشطة</Badge>
      case "completed":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مكتملة</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">قيد المراجعة</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 border-red-200">مرفوضة</Badge>
      case "draft":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">مسودة</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (!isAuthenticated) {
    return null // Redirect handled in useEffect
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="max-w-7xl mx-auto p-4 md:p-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-[#0a8754]">إدارة المبادرات</h1>
            <p className="text-gray-600 mt-1">إدارة وتتبع جميع المبادرات الخاصة بك</p>
          </div>
          <Link href="/initiatives/create">
            <Button className="bg-[#0a8754] hover:bg-[#097548]">
              <Plus className="ml-2 h-4 w-4" />
              إنشاء مبادرة جديدة
            </Button>
          </Link>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">إجمالي المبادرات</p>
                  <h3 className="text-3xl font-bold mt-1">{stats.total}</h3>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                  <BarChart className="h-6 w-6" />
                </div>
              </div>
              <div className="mt-4 grid grid-cols-3 gap-2 text-center text-sm">
                <div className="bg-green-50 p-1 rounded">
                  <span className="text-green-700 font-medium">{stats.active}</span>
                  <p className="text-xs text-gray-500">نشطة</p>
                </div>
                <div className="bg-blue-50 p-1 rounded">
                  <span className="text-blue-700 font-medium">{stats.completed}</span>
                  <p className="text-xs text-gray-500">مكتملة</p>
                </div>
                <div className="bg-yellow-50 p-1 rounded">
                  <span className="text-yellow-700 font-medium">{stats.pending}</span>
                  <p className="text-xs text-gray-500">قيد المراجعة</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">إجمالي الدعم</p>
                  <h3 className="text-3xl font-bold mt-1">{stats.totalSupports}</h3>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                  <ThumbsUp className="h-6 w-6" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">متوسط الدعم لكل مبادرة</p>
                <p className="text-lg font-medium">
                  {stats.total > 0 ? (stats.totalSupports / stats.total).toFixed(1) : 0}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">إجمالي المشاهدات</p>
                  <h3 className="text-3xl font-bold mt-1">{stats.totalViews}</h3>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600">
                  <Eye className="h-6 w-6" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">متوسط المشاهدات لكل مبادرة</p>
                <p className="text-lg font-medium">
                  {stats.total > 0 ? (stats.totalViews / stats.total).toFixed(1) : 0}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">إجمالي التعليقات</p>
                  <h3 className="text-3xl font-bold mt-1">{stats.totalComments}</h3>
                </div>
                <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center text-amber-600">
                  <Users className="h-6 w-6" />
                </div>
              </div>
              <div className="mt-4">
                <p className="text-sm text-gray-500">متوسط التعليقات لكل مبادرة</p>
                <p className="text-lg font-medium">
                  {stats.total > 0 ? (stats.totalComments / stats.total).toFixed(1) : 0}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="البحث عن مبادرة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشطة</SelectItem>
                  <SelectItem value="completed">مكتملة</SelectItem>
                  <SelectItem value="pending">قيد المراجعة</SelectItem>
                  <SelectItem value="rejected">مرفوضة</SelectItem>
                  <SelectItem value="draft">مسودة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                  <SelectItem value="most-supported">الأكثر دعماً</SelectItem>
                  <SelectItem value="most-viewed">الأكثر مشاهدة</SelectItem>
                  <SelectItem value="progress-high">التقدم (الأعلى أولاً)</SelectItem>
                  <SelectItem value="progress-low">التقدم (الأدنى أولاً)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Tabs and Initiatives Table */}
        <Card>
          <CardHeader className="pb-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-5 w-full">
                <TabsTrigger value="all">الكل ({stats.total})</TabsTrigger>
                <TabsTrigger value="active">نشطة ({stats.active})</TabsTrigger>
                <TabsTrigger value="pending">قيد المراجعة ({stats.pending})</TabsTrigger>
                <TabsTrigger value="completed">مكتملة ({stats.completed})</TabsTrigger>
                <TabsTrigger value="rejected">مرفوضة ({stats.rejected})</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent className="pt-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-[#0a8754]" />
                <span className="mr-2">جاري التحميل...</span>
              </div>
            ) : filteredInitiatives.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">لا توجد مبادرات تطابق معايير البحث</p>
                <Link href="/initiatives/create">
                  <Button className="bg-[#0a8754] hover:bg-[#097548]">
                    <Plus className="ml-2 h-4 w-4" />
                    إنشاء مبادرة جديدة
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المبادرة</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>التقدم</TableHead>
                      <TableHead>الدعم</TableHead>
                      <TableHead>المشاهدات</TableHead>
                      <TableHead>تاريخ الإنشاء</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInitiatives.map((initiative) => (
                      <TableRow key={initiative._id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="relative h-12 w-12 rounded-md overflow-hidden">
                              <Image
                                src={initiative.mainImage || "/placeholder.svg"}
                                alt={initiative.title}
                                fill
                                style={{ objectFit: "cover" }}
                              />
                            </div>
                            <div>
                              <p className="font-medium">{initiative.title}</p>
                              <p className="text-sm text-gray-500 truncate max-w-[200px]">
                                {initiative.shortDescription}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(initiative.status)}</TableCell>
                        <TableCell>
                          <div className="w-full max-w-[100px]">
                            <Progress value={initiative.progress} className="h-2" />
                            <p className="text-xs text-gray-500 mt-1">{initiative.progress}%</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <ThumbsUp className="h-4 w-4 text-green-600 ml-1" />
                            {initiative.supportCount}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Eye className="h-4 w-4 text-blue-600 ml-1" />
                            {initiative.viewCount}
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(initiative.createdAt)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Link href={`/initiatives/${initiative._id}`}>
                              <Button variant="ghost" size="icon">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Link href={`/initiatives/${initiative._id}/edit`}>
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="icon" className="text-red-600">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>حذف المبادرة</DialogTitle>
                                  <DialogDescription>
                                    هل أنت متأكد من رغبتك في حذف هذه المبادرة؟ هذا الإجراء لا يمكن التراجع عنه.
                                  </DialogDescription>
                                </DialogHeader>
                                <DialogFooter>
                                  <Button variant="outline" onClick={() => setInitiativeToDelete(null)}>
                                    إلغاء
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    onClick={() => handleDeleteInitiative()}
                                    disabled={isDeleting}
                                  >
                                    {isDeleting ? (
                                      <>
                                        <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                                        جاري الحذف...
                                      </>
                                    ) : (
                                      "حذف"
                                    )}
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Toaster />
    </div>
  )
}

import type { Request, Response, NextFunction } from 'express';
import { createError } from "../utils/error";

const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Set default values if not provided
  const status = err.status || err.statusCode || 500;
  const message = err.message || "Something went wrong";

  // Log the error
  console.error(`[Error] ${status} - ${message}`, {
    path: req.path,
    method: req.method,
    body: req.body,
    stack: err.stack
  });

  // Return JSON response
  res.status(status).json({
    success: false,
    error: {
      status,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
};

export default errorHandler;

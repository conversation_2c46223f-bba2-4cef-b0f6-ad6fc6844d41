"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Edit, Trash2, Save, AlertCircle } from "lucide-react"
import { api } from "@/lib/api"
import { toast } from "sonner"

interface ImpactItem {
  _id?: string
  name: string
  arabicName: string
  description?: string
  arabicDescription?: string
  isActive: boolean
}

interface SocialImpactCategory {
  _id: string
  name: string
  arabicName: string
  description?: string
  arabicDescription?: string
  order: number
  isActive: boolean
  impacts: ImpactItem[]
  createdAt: string
  updatedAt: string
}

export default function SocialImpactsPage() {
  const router = useRouter()
  const [categories, setCategories] = useState<SocialImpactCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // État pour le dialogue d'ajout/modification de catégorie
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false)
  const [currentCategory, setCurrentCategory] = useState<Partial<SocialImpactCategory> | null>(null)

  // État pour le dialogue d'ajout/modification d'impact
  const [isAddImpactOpen, setIsAddImpactOpen] = useState(false)
  const [currentImpact, setCurrentImpact] = useState<Partial<ImpactItem> | null>(null)
  const [currentCategoryId, setCurrentCategoryId] = useState<string | null>(null)

  // Charger les catégories d'impact social
  useEffect(() => {
    const fetchSocialImpacts = async () => {
      try {
        setLoading(true)
        console.log('Fetching social impacts directly...')

        // Utiliser fetch directement au lieu de api.get
        const response = await fetch('http://localhost:5000/api/social-impacts')
        console.log('Response status:', response.status)

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`)
        }

        const data = await response.json()
        console.log('Raw API response:', data)

        if (data && data.success && data.socialImpacts) {
          console.log('Setting categories with:', data.socialImpacts.length, 'items')
          setCategories(data.socialImpacts)
        } else {
          console.error('Invalid API response format:', data)
          setError("Format de réponse API invalide")
        }
      } catch (err) {
        console.error("Erreur lors du chargement des impacts sociaux:", err)
        setError(`Erreur lors du chargement des impacts sociaux: ${err instanceof Error ? err.message : 'Unknown error'}`)
      } finally {
        setLoading(false)
      }
    }

    fetchSocialImpacts()
  }, [])

  // Gérer l'ajout/modification d'une catégorie
  const handleSaveCategory = async () => {
    if (!currentCategory || !currentCategory.name || !currentCategory.arabicName) {
      toast.error("Veuillez remplir tous les champs obligatoires")
      return
    }

    try {
      let response
      if (currentCategory._id) {
        // Mise à jour d'une catégorie existante
        response = await api.put(`/social-impacts/${currentCategory._id}`, currentCategory)
      } else {
        // Création d'une nouvelle catégorie
        response = await api.post('/social-impacts', currentCategory)
      }

      if (response.success) {
        toast.success(currentCategory._id ? "Catégorie mise à jour avec succès" : "Catégorie ajoutée avec succès")

        // Mettre à jour la liste des catégories
        const response = await fetch('http://localhost:5000/api/social-impacts')
        if (response.ok) {
          const data = await response.json()
          if (data && data.success && data.socialImpacts) {
            setCategories(data.socialImpacts)
          }
        }

        // Fermer le dialogue
        setIsAddCategoryOpen(false)
        setCurrentCategory(null)
      } else {
        toast.error(response.error || "Une erreur est survenue")
      }
    } catch (err) {
      console.error("Erreur lors de l'enregistrement de la catégorie:", err)
      toast.error("Erreur lors de l'enregistrement de la catégorie")
    }
  }

  // Gérer l'ajout/modification d'un impact
  const handleSaveImpact = async () => {
    if (!currentImpact || !currentImpact.name || !currentImpact.arabicName || !currentCategoryId) {
      toast.error("Veuillez remplir tous les champs obligatoires")
      return
    }

    try {
      let response
      if (currentImpact._id) {
        // Mise à jour d'un impact existant (à implémenter côté API)
        response = await api.put(`/social-impacts/${currentCategoryId}/impacts/${currentImpact._id}`, currentImpact)
      } else {
        // Ajout d'un nouvel impact
        response = await api.post(`/social-impacts/${currentCategoryId}/impacts`, currentImpact)
      }

      if (response.success) {
        toast.success(currentImpact._id ? "Impact mis à jour avec succès" : "Impact ajouté avec succès")

        // Mettre à jour la liste des catégories
        const response = await fetch('http://localhost:5000/api/social-impacts')
        if (response.ok) {
          const data = await response.json()
          if (data && data.success && data.socialImpacts) {
            setCategories(data.socialImpacts)
          }
        }

        // Fermer le dialogue
        setIsAddImpactOpen(false)
        setCurrentImpact(null)
        setCurrentCategoryId(null)
      } else {
        toast.error(response.error || "Une erreur est survenue")
      }
    } catch (err) {
      console.error("Erreur lors de l'enregistrement de l'impact:", err)
      toast.error("Erreur lors de l'enregistrement de l'impact")
    }
  }

  // Gérer la suppression d'une catégorie
  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cette catégorie ? Cette action est irréversible.")) {
      return
    }

    try {
      const response = await api.delete(`/social-impacts/${categoryId}`)
      if (response.success) {
        toast.success("Catégorie supprimée avec succès")

        // Mettre à jour la liste des catégories
        setCategories(categories.filter(cat => cat._id !== categoryId))
      } else {
        toast.error(response.error || "Une erreur est survenue")
      }
    } catch (err) {
      console.error("Erreur lors de la suppression de la catégorie:", err)
      toast.error("Erreur lors de la suppression de la catégorie")
    }
  }

  // Gérer la suppression d'un impact
  const handleDeleteImpact = async (categoryId: string, impactId: string) => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cet impact ? Cette action est irréversible.")) {
      return
    }

    try {
      const response = await api.delete(`/social-impacts/${categoryId}/impacts/${impactId}`)
      if (response.success) {
        toast.success("Impact supprimé avec succès")

        // Mettre à jour la liste des catégories
        const response = await fetch('http://localhost:5000/api/social-impacts')
        if (response.ok) {
          const data = await response.json()
          if (data && data.success && data.socialImpacts) {
            setCategories(data.socialImpacts)
          }
        }
      } else {
        toast.error(response.error || "Une erreur est survenue")
      }
    } catch (err) {
      console.error("Erreur lors de la suppression de l'impact:", err)
      toast.error("Erreur lors de la suppression de l'impact")
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-bold mb-2">Erreur de chargement</h2>
        <p className="text-gray-600">{error}</p>
        <Button onClick={() => window.location.reload()} className="mt-4">
          Réessayer
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">إدارة تأثيرات الاجتماعية</h1>
          <p className="text-gray-500">إدارة فئات وعناصر التأثير الاجتماعي للمبادرات</p>
        </div>
        <Button onClick={() => {
          setCurrentCategory({
            name: '',
            arabicName: '',
            description: '',
            arabicDescription: '',
            order: categories.length + 1,
            isActive: true
          })
          setIsAddCategoryOpen(true)
        }}>
          <Plus className="h-4 w-4 ml-2" />
          إضافة فئة جديدة
        </Button>
      </div>

      {categories.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <AlertCircle className="h-12 w-12 text-yellow-500 mb-4" />
            <h3 className="text-xl font-bold mb-2">لا توجد فئات تأثير اجتماعي</h3>
            <p className="text-gray-600 mb-4">قم بإضافة فئات جديدة للبدء</p>
            <Button onClick={() => {
              setCurrentCategory({
                name: '',
                arabicName: '',
                description: '',
                arabicDescription: '',
                order: 1,
                isActive: true
              })
              setIsAddCategoryOpen(true)
            }}>
              <Plus className="h-4 w-4 ml-2" />
              إضافة فئة جديدة
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {categories.map((category) => (
            <Card key={category._id} className={!category.isActive ? "opacity-60" : ""}>
              <CardHeader className="flex flex-row items-start justify-between">
                <div>
                  <CardTitle>{category.arabicName}</CardTitle>
                  <CardDescription>{category.name}</CardDescription>
                  {category.arabicDescription && (
                    <p className="mt-2 text-sm text-gray-600">{category.arabicDescription}</p>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="icon" onClick={() => {
                    setCurrentCategory(category)
                    setIsAddCategoryOpen(true)
                  }}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="destructive" size="icon" onClick={() => handleDeleteCategory(category._id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-semibold">التأثيرات ({category.impacts.length})</h3>
                  <Button variant="outline" size="sm" onClick={() => {
                    setCurrentImpact({
                      name: '',
                      arabicName: '',
                      description: '',
                      arabicDescription: '',
                      isActive: true
                    })
                    setCurrentCategoryId(category._id)
                    setIsAddImpactOpen(true)
                  }}>
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة تأثير
                  </Button>
                </div>
                {category.impacts.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">لا توجد تأثيرات في هذه الفئة</p>
                ) : (
                  <div className="grid gap-2">
                    {category.impacts.map((impact) => (
                      <div key={impact._id} className={`p-3 border rounded-md flex justify-between items-center ${!impact.isActive ? "opacity-60" : ""}`}>
                        <div>
                          <h4 className="font-medium">{impact.arabicName}</h4>
                          <p className="text-sm text-gray-500">{impact.name}</p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="ghost" size="icon" onClick={() => {
                            setCurrentImpact(impact)
                            setCurrentCategoryId(category._id)
                            setIsAddImpactOpen(true)
                          }}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="text-red-500" onClick={() => handleDeleteImpact(category._id, impact._id!)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialogue d'ajout/modification de catégorie */}
      <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{currentCategory?._id ? "تعديل فئة" : "إضافة فئة جديدة"}</DialogTitle>
            <DialogDescription>
              أدخل معلومات فئة التأثير الاجتماعي
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="arabicName">الاسم بالعربية</Label>
                <Input
                  id="arabicName"
                  value={currentCategory?.arabicName || ''}
                  onChange={(e) => setCurrentCategory({...currentCategory!, arabicName: e.target.value})}
                  placeholder="الاسم بالعربية"
                  className="text-right"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">الاسم بالإنجليزية</Label>
                <Input
                  id="name"
                  value={currentCategory?.name || ''}
                  onChange={(e) => setCurrentCategory({...currentCategory!, name: e.target.value})}
                  placeholder="الاسم بالإنجليزية"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="arabicDescription">الوصف بالعربية</Label>
                <Textarea
                  id="arabicDescription"
                  value={currentCategory?.arabicDescription || ''}
                  onChange={(e) => setCurrentCategory({...currentCategory!, arabicDescription: e.target.value})}
                  placeholder="الوصف بالعربية"
                  className="text-right"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">الوصف بالإنجليزية</Label>
                <Textarea
                  id="description"
                  value={currentCategory?.description || ''}
                  onChange={(e) => setCurrentCategory({...currentCategory!, description: e.target.value})}
                  placeholder="الوصف بالإنجليزية"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="order">الترتيب</Label>
                <Input
                  id="order"
                  type="number"
                  value={currentCategory?.order || 0}
                  onChange={(e) => setCurrentCategory({...currentCategory!, order: parseInt(e.target.value)})}
                />
              </div>
              <div className="flex items-center space-x-2 space-x-reverse pt-6">
                <Switch
                  id="isActive"
                  checked={currentCategory?.isActive}
                  onCheckedChange={(checked) => setCurrentCategory({...currentCategory!, isActive: checked})}
                />
                <Label htmlFor="isActive">نشط</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddCategoryOpen(false)}>إلغاء</Button>
            <Button onClick={handleSaveCategory}>حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialogue d'ajout/modification d'impact */}
      <Dialog open={isAddImpactOpen} onOpenChange={setIsAddImpactOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{currentImpact?._id ? "تعديل تأثير" : "إضافة تأثير جديد"}</DialogTitle>
            <DialogDescription>
              أدخل معلومات التأثير الاجتماعي
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="impactArabicName">الاسم بالعربية</Label>
                <Input
                  id="impactArabicName"
                  value={currentImpact?.arabicName || ''}
                  onChange={(e) => setCurrentImpact({...currentImpact!, arabicName: e.target.value})}
                  placeholder="الاسم بالعربية"
                  className="text-right"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="impactName">الاسم بالإنجليزية</Label>
                <Input
                  id="impactName"
                  value={currentImpact?.name || ''}
                  onChange={(e) => setCurrentImpact({...currentImpact!, name: e.target.value})}
                  placeholder="الاسم بالإنجليزية"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="impactArabicDescription">الوصف بالعربية</Label>
                <Textarea
                  id="impactArabicDescription"
                  value={currentImpact?.arabicDescription || ''}
                  onChange={(e) => setCurrentImpact({...currentImpact!, arabicDescription: e.target.value})}
                  placeholder="الوصف بالعربية (اختياري)"
                  className="text-right"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="impactDescription">الوصف بالإنجليزية</Label>
                <Textarea
                  id="impactDescription"
                  value={currentImpact?.description || ''}
                  onChange={(e) => setCurrentImpact({...currentImpact!, description: e.target.value})}
                  placeholder="الوصف بالإنجليزية (اختياري)"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                id="impactIsActive"
                checked={currentImpact?.isActive}
                onCheckedChange={(checked) => setCurrentImpact({...currentImpact!, isActive: checked})}
              />
              <Label htmlFor="impactIsActive">نشط</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddImpactOpen(false)}>إلغاء</Button>
            <Button onClick={handleSaveImpact}>حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

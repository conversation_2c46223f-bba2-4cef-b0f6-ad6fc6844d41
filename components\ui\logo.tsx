"use client"

import React, { useEffect, useState } from 'react'

export function Logo({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const [timestamp, setTimestamp] = useState<number>(0)
  
  useEffect(() => {
    setTimestamp(Date.now())
  }, [])
  
  const height = size === 'sm' ? 32 : size === 'md' ? 40 : 48
  
  return (
    <img
      src={`/logo-direct.png?v=${timestamp}`}
      alt="Logo"
      height={height}
      style={{ height: `${height}px`, width: 'auto' }}
    />
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "../../components/ui/dialog"
import { But<PERSON> } from "../../components/ui/button"
import { Label } from "../../components/ui/label"
import { Textarea } from "../../components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { Award, Loader2 } from "lucide-react"
import { api } from "../../lib/api"
import { toast } from "../../components/ui/use-toast"

interface Badge {
  _id: string
  name: string
  arabicName: string
  description: string
  arabicDescription: string
  icon: string
  color: string
  category: string
  level: number
}

interface AwardBadgeDialogProps {
  isOpen: boolean
  onClose: () => void
  initiativeId: string
  volunteerId: string
  volunteerName: string
  onBadgeAwarded: () => void
}

export default function AwardBadgeDialog({
  isOpen,
  onClose,
  initiativeId,
  volunteerId,
  volunteerName,
  onBadgeAwarded
}: AwardBadgeDialogProps) {
  const [badges, setBadges] = useState<Badge[]>([])
  const [selectedBadge, setSelectedBadge] = useState<string>("")
  const [reason, setReason] = useState<string>("")
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isFetchingBadges, setIsFetchingBadges] = useState<boolean>(false)

  useEffect(() => {
    if (isOpen) {
      fetchBadges()
    }
  }, [isOpen])

  const fetchBadges = async () => {
    try {
      setIsFetchingBadges(true)
      const response = await api.get("/api/badges", false)
      if (response.success) {
        setBadges(response.badges || [])
      } else {
        toast({
          title: "خطأ",
          description: "فشل في جلب الشارات المتاحة",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error fetching badges:", error)
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء جلب الشارات المتاحة",
        variant: "destructive"
      })
    } finally {
      setIsFetchingBadges(false)
    }
  }

  const handleAwardBadge = async () => {
    if (!selectedBadge) {
      toast({
        title: "تنبيه",
        description: "يرجى اختيار شارة لمنحها",
        variant: "destructive"
      })
      return
    }

    try {
      setIsLoading(true)
      const response = await api.post(
        `/api/initiatives/${initiativeId}/volunteers/${volunteerId}/award-badge`,
        { badgeId: selectedBadge, reason },
        true
      )

      if (response.success) {
        toast({
          title: "تم بنجاح",
          description: "تم منح الشارة بنجاح",
          variant: "default"
        })
        onBadgeAwarded()
        handleClose()
      } else {
        toast({
          title: "خطأ",
          description: response.message || "فشل في منح الشارة",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error("Error awarding badge:", error)
      toast({
        title: "خطأ",
        description: error.message || "حدث خطأ أثناء منح الشارة",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setSelectedBadge("")
    setReason("")
    onClose()
  }

  const getCategoryText = (category: string) => {
    switch (category) {
      case "participation": return "المشاركة"
      case "achievement": return "الإنجاز"
      case "contribution": return "المساهمة"
      case "special": return "خاص"
      case "skill": return "المهارة"
      default: return category
    }
  }

  const getBadgeLevelText = (level: number) => {
    switch (level) {
      case 1: return "المستوى الأول"
      case 2: return "المستوى الثاني"
      case 3: return "المستوى الثالث"
      case 4: return "المستوى الرابع"
      case 5: return "المستوى الخامس"
      default: return `المستوى ${level}`
    }
  }

  const selectedBadgeData = badges.find(badge => badge._id === selectedBadge)

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>منح شارة للمتطوع</DialogTitle>
          <DialogDescription>
            منح شارة تقديرية للمتطوع {volunteerName} لمساهمته في المبادرة
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="badge">اختر الشارة</Label>
            {isFetchingBadges ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : (
              <Select value={selectedBadge} onValueChange={setSelectedBadge}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر شارة" />
                </SelectTrigger>
                <SelectContent>
                  {badges.map((badge) => (
                    <SelectItem key={badge._id} value={badge._id}>
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: badge.color }}
                        ></div>
                        <span>{badge.arabicName}</span>
                        <span className="text-xs text-gray-500">
                          ({getCategoryText(badge.category)} - {getBadgeLevelText(badge.level)})
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {selectedBadgeData && (
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="flex items-center gap-3 mb-2">
                <div 
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ 
                    backgroundColor: `${selectedBadgeData.color}20`,
                    border: `2px solid ${selectedBadgeData.color}`
                  }}
                >
                  <Award style={{ color: selectedBadgeData.color }} className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium">{selectedBadgeData.arabicName}</h4>
                  <p className="text-xs text-gray-500">
                    {getCategoryText(selectedBadgeData.category)} - {getBadgeLevelText(selectedBadgeData.level)}
                  </p>
                </div>
              </div>
              <p className="text-sm text-gray-600">{selectedBadgeData.arabicDescription}</p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="reason">سبب منح الشارة</Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="اذكر سبب منح هذه الشارة للمتطوع"
              className="min-h-[100px]"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            إلغاء
          </Button>
          <Button onClick={handleAwardBadge} disabled={isLoading || !selectedBadge}>
            {isLoading ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                جاري المنح...
              </>
            ) : (
              <>
                <Award className="ml-2 h-4 w-4" />
                منح الشارة
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

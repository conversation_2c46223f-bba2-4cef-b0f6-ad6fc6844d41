import { Schema, model, models } from "mongoose"

// Schema for individual impact items
const impactItemSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    arabicName: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    arabicDescription: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
)

// Schema for impact categories
const socialImpactSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    arabicName: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    arabicDescription: {
      type: String,
      trim: true,
    },
    order: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    impacts: [impactItemSchema],
  },
  {
    timestamps: true,
  }
)

// Create and export the model
export const SocialImpact = models.SocialImpact || model("SocialImpact", socialImpactSchema)

// Export the schemas for use in other models
export { socialImpactSchema, impactItemSchema }

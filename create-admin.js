const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives-dz';

// User schema
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 8,
  },
  name: {
    type: String,
    required: true,
  },
  avatar: {
    type: String,
    default: "/placeholder.svg?height=100&width=100",
  },
  role: {
    type: String,
    enum: ["user", "moderator", "admin"],
    default: "user",
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  joinDate: {
    type: Date,
    default: Date.now,
  }
});

// Create User model
const User = mongoose.model('User', userSchema);

/**
 * <PERSON><PERSON>t to create an admin user
 */
async function createAdminUser() {
  try {
    console.log("Connecting to database...");
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ username: "admin" });
    if (existingAdmin) {
      console.log("Admin user already exists");
      return;
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash("Admin123!", salt);

    // Create admin user
    const adminUser = new User({
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      name: "Admin User",
      role: "admin",
      isVerified: true,
      avatar: "https://ui-avatars.com/api/?name=Admin+User&background=0D8ABC&color=fff",
      joinDate: new Date("2023-01-01")
    });

    // Save admin user
    await adminUser.save();

    console.log("Admin user created successfully!");
    console.log("Username: admin");
    console.log("Password: Admin123!");
    console.log("Email: <EMAIL>");
  } catch (error) {
    console.error("Error creating admin user:", error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log("Database connection closed");
  }
}

// Run the script
createAdminUser();

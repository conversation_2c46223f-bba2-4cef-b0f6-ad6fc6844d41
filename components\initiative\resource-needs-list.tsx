"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../ui/button"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { Alert, AlertDescription } from "../ui/alert"
import { 
  Loader2, 
  AlertCircle, 
  Plus, 
  Package, 
  DollarSign, 
  Users, 
  Wrench,
  HelpCircle,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  ArrowUpDown
} from "lucide-react"
import { Input } from "../ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"
import ResourceNeedDialog from "./resource-need-dialog"

interface ResourceNeedsListProps {
  initiativeId: string
  isAuthor: boolean
  status: string
  isDetailPage?: boolean
}

interface ResourceNeed {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  priority: "low" | "medium" | "high" | "critical"
  status: "open" | "in_progress" | "fulfilled" | "canceled"
  createdDate: string
  fulfilledDate?: string
  notes?: string
}

export default function ResourceNeedsList({ initiativeId, isAuthor, status, isDetailPage = false }: ResourceNeedsListProps) {
  const [resourceNeeds, setResourceNeeds] = useState<ResourceNeed[]>([])
  const [filteredNeeds, setFilteredNeeds] = useState<ResourceNeed[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showNeedDialog, setShowNeedDialog] = useState(false)
  const [selectedResourceNeed, setSelectedResourceNeed] = useState<ResourceNeed | null>(null)
  
  // Filters
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("priority")
  
  useEffect(() => {
    fetchResourceNeeds()
  }, [initiativeId])
  
  useEffect(() => {
    applyFilters()
  }, [resourceNeeds, searchQuery, typeFilter, priorityFilter, statusFilter, sortBy])
  
  const fetchResourceNeeds = async () => {
    setIsLoading(true)
    try {
      const response = await api.get(`/api/resources/needs/initiative/${initiativeId}`, false)
      
      if (response.success) {
        setResourceNeeds(response.resourceNeeds || [])
      } else {
        setError("Failed to fetch resource needs")
      }
    } catch (err: any) {
      console.error("Error fetching resource needs:", err)
      setError(err.message || "An error occurred while fetching resource needs")
    } finally {
      setIsLoading(false)
    }
  }
  
  const applyFilters = () => {
    let filtered = [...resourceNeeds]
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(need => 
        need.name.toLowerCase().includes(query) || 
        need.description.toLowerCase().includes(query) ||
        (need.notes && need.notes.toLowerCase().includes(query))
      )
    }
    
    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter(need => need.type === typeFilter)
    }
    
    // Apply priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter(need => need.priority === priorityFilter)
    }
    
    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(need => need.status === statusFilter)
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "priority":
          return getPriorityValue(b.priority) - getPriorityValue(a.priority)
        case "date":
          return new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
        case "name":
          return a.name.localeCompare(b.name)
        case "quantity":
          return b.quantity - a.quantity
        default:
          return 0
      }
    })
    
    setFilteredNeeds(filtered)
  }
  
  const getPriorityValue = (priority: string) => {
    switch (priority) {
      case "critical": return 4
      case "high": return 3
      case "medium": return 2
      case "low": return 1
      default: return 0
    }
  }
  
  const handleResourceNeedUpdate = async (resourceNeedId: string, newStatus: string) => {
    try {
      const response = await api.put(`/api/resources/needs/${resourceNeedId}`, {
        status: newStatus
      }, true)
      
      if (response.success) {
        toast({
          title: "تم تحديث حالة الاحتياج",
          description: "تم تحديث حالة الاحتياج بنجاح",
          variant: "default",
        })
        
        // Refresh resource needs
        fetchResourceNeeds()
      } else {
        throw new Error(response.message || "Failed to update resource need status")
      }
    } catch (err: any) {
      console.error("Error updating resource need status:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء تحديث حالة الاحتياج",
        variant: "destructive",
      })
    }
  }
  
  const handleResourceNeedDelete = async (resourceNeedId: string) => {
    try {
      const response = await api.delete(`/api/resources/needs/${resourceNeedId}`, true)
      
      if (response.success) {
        toast({
          title: "تم حذف الاحتياج",
          description: "تم حذف الاحتياج بنجاح",
          variant: "default",
        })
        
        // Refresh resource needs
        fetchResourceNeeds()
      } else {
        throw new Error(response.message || "Failed to delete resource need")
      }
    } catch (err: any) {
      console.error("Error deleting resource need:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء حذف الاحتياج",
        variant: "destructive",
      })
    }
  }
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }
  
  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "مادي"
      case "financial":
        return "مالي"
      case "human":
        return "بشري"
      case "service":
        return "خدمة"
      default:
        return "آخر"
    }
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "open":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مفتوح</Badge>
      case "in_progress":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">قيد التنفيذ</Badge>
      case "fulfilled":
        return <Badge className="bg-green-100 text-green-800 border-green-200">مكتمل</Badge>
      case "canceled":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">ملغي</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }
  
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "low":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">منخفضة</Badge>
      case "medium":
        return <Badge className="bg-green-100 text-green-800 border-green-200">متوسطة</Badge>
      case "high":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">عالية</Badge>
      case "critical":
        return <Badge className="bg-red-100 text-red-800 border-red-200">حرجة</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }
  
  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {/* Header with Add Button */}
      {isDetailPage && (
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">احتياجات الموارد</h2>
          
          {isAuthor && status === "active" && (
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => {
                setSelectedResourceNeed(null)
                setShowNeedDialog(true)
              }}
            >
              <Plus className="ml-2 h-4 w-4" />
              إضافة احتياج
            </Button>
          )}
        </div>
      )}
      
      {/* Filters */}
      {isDetailPage && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="البحث عن احتياج..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع المورد" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="material">مادي</SelectItem>
                  <SelectItem value="financial">مالي</SelectItem>
                  <SelectItem value="human">بشري</SelectItem>
                  <SelectItem value="service">خدمة</SelectItem>
                  <SelectItem value="other">آخر</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الأولوية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأولويات</SelectItem>
                  <SelectItem value="critical">حرجة</SelectItem>
                  <SelectItem value="high">عالية</SelectItem>
                  <SelectItem value="medium">متوسطة</SelectItem>
                  <SelectItem value="low">منخفضة</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="open">مفتوح</SelectItem>
                  <SelectItem value="in_progress">قيد التنفيذ</SelectItem>
                  <SelectItem value="fulfilled">مكتمل</SelectItem>
                  <SelectItem value="canceled">ملغي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex justify-end mt-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="ترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="priority">الأولوية</SelectItem>
                  <SelectItem value="date">تاريخ الإنشاء</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                  <SelectItem value="quantity">الكمية</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Resource Needs List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-green-600" />
          <span className="mr-2">جاري تحميل الاحتياجات...</span>
        </div>
      ) : filteredNeeds.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500 mb-4">لا توجد احتياجات محددة لهذه المبادرة حتى الآن.</p>
          {isAuthor && status === "active" && (
            <Button 
              onClick={() => {
                setSelectedResourceNeed(null)
                setShowNeedDialog(true)
              }} 
              variant="outline" 
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <Plus className="ml-2 h-4 w-4" />
              إضافة احتياج جديد
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredNeeds.map((need) => (
            <Card key={need._id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-2 rounded-full bg-blue-100">
                      {getTypeIcon(need.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{need.name}</CardTitle>
                      <div className="text-sm text-gray-500">{getTypeText(need.type)}</div>
                    </div>
                  </div>
                  {getStatusBadge(need.status)}
                </div>
              </CardHeader>
              
              <CardContent className="pb-2">
                <p className="text-sm text-gray-600 mb-3">{need.description}</p>
                
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-1 text-sm">
                    <span className="font-medium">الكمية:</span>
                    <span>{need.quantity} {need.unit}</span>
                  </div>
                  {getPriorityBadge(need.priority)}
                </div>
                
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <Clock className="h-4 w-4 ml-1" />
                  <span>تم الإنشاء في {formatDate(need.createdDate)}</span>
                </div>
                
                {need.fulfilledDate && (
                  <div className="flex items-center text-sm text-green-600">
                    <CheckCircle className="h-4 w-4 ml-1" />
                    <span>تم الاكتمال في {formatDate(need.fulfilledDate)}</span>
                  </div>
                )}
                
                {need.notes && (
                  <div className="mt-2 p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                    <p className="font-medium mb-1">ملاحظات:</p>
                    <p>{need.notes}</p>
                  </div>
                )}
              </CardContent>
              
              {isAuthor && need.status !== "fulfilled" && need.status !== "canceled" && (
                <CardFooter className="pt-2">
                  <div className="flex justify-between w-full">
                    <div className="flex gap-2">
                      {need.status === "open" && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-amber-600 border-amber-200 hover:bg-amber-50"
                          onClick={() => handleResourceNeedUpdate(need._id, "in_progress")}
                        >
                          قيد التنفيذ
                        </Button>
                      )}
                      
                      {(need.status === "open" || need.status === "in_progress") && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-green-600 border-green-200 hover:bg-green-50"
                          onClick={() => handleResourceNeedUpdate(need._id, "fulfilled")}
                        >
                          تم الاكتمال
                        </Button>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                        onClick={() => {
                          setSelectedResourceNeed(need)
                          setShowNeedDialog(true)
                        }}
                      >
                        تعديل
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                        onClick={() => handleResourceNeedDelete(need._id)}
                      >
                        حذف
                      </Button>
                    </div>
                  </div>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      )}
      
      {/* Resource Need Dialog */}
      <ResourceNeedDialog
        open={showNeedDialog}
        onOpenChange={setShowNeedDialog}
        initiativeId={initiativeId}
        resourceNeed={selectedResourceNeed}
        onSuccess={() => {
          fetchResourceNeeds()
          setShowNeedDialog(false)
          setSelectedResourceNeed(null)
        }}
      />
    </div>
  )
}

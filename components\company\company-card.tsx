"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Building, MapPin, Briefcase } from "lucide-react"

// Liste des secteurs d'activité
const INDUSTRY_SECTORS = [
  { id: "technology", name: "تكنولوجيا المعلومات" },
  { id: "education", name: "التعليم والتدريب" },
  { id: "health", name: "الصحة والرعاية الطبية" },
  { id: "finance", name: "المالية والمصرفية" },
  { id: "manufacturing", name: "التصنيع" },
  { id: "retail", name: "التجزئة والتجارة" },
  { id: "construction", name: "البناء والإنشاءات" },
  { id: "agriculture", name: "الزراعة" },
  { id: "energy", name: "الطاقة" },
  { id: "transportation", name: "النقل والمواصلات" },
  { id: "media", name: "الإعلام والاتصالات" },
  { id: "tourism", name: "السياحة والضيافة" },
  { id: "consulting", name: "الاستشارات" },
  { id: "nonprofit", name: "المنظمات غير الربحية" },
  { id: "government", name: "القطاع الحكومي" },
  { id: "other", name: "أخرى" }
]

// Liste des types de ressources
const RESOURCE_TYPES = [
  { id: "financial", name: "دعم مالي", icon: "💰" },
  { id: "space", name: "مساحات عمل", icon: "🏢" },
  { id: "equipment", name: "معدات وتجهيزات", icon: "🔧" },
  { id: "expertise", name: "خبرات ومهارات", icon: "🧠" },
  { id: "mentorship", name: "إرشاد وتوجيه", icon: "👨‍🏫" },
  { id: "networking", name: "شبكة علاقات", icon: "🔗" },
  { id: "marketing", name: "تسويق وترويج", icon: "📢" },
  { id: "logistics", name: "خدمات لوجستية", icon: "🚚" },
  { id: "technology", name: "حلول تقنية", icon: "💻" },
  { id: "training", name: "تدريب", icon: "📚" }
]

interface CompanyCardProps {
  company: {
    _id: string
    name: string
    username: string
    avatar?: string
    bio?: string
    location?: string
    companyName?: string
    industry?: string
    services?: string[]
    resources?: string[]
    companyDescription?: string
  }
}

export default function CompanyCard({ company }: CompanyCardProps) {
  // Obtenir le nom du secteur d'activité
  const getIndustryName = (industryId?: string) => {
    if (!industryId) return ''
    const found = INDUSTRY_SECTORS.find(sector => sector.id === industryId)
    return found ? found.name : industryId
  }

  // Obtenir les informations sur les ressources
  const getResourceInfo = (resourceId: string) => {
    return RESOURCE_TYPES.find(resource => resource.id === resourceId) || {
      id: resourceId,
      name: resourceId,
      icon: "📦"
    }
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-4">
          <Avatar className="h-20 w-20 shrink-0" square>
            <AvatarImage
              src={company.avatar}
              alt={company.name}
              preserveAspectRatio={true}
              style={{ objectFit: "contain", maxWidth: "100%", maxHeight: "100%" }}
            />
            <AvatarFallback square>
              <Building className="h-8 w-8 text-gray-400" />
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-bold mb-1">{company.companyName || company.name}</h3>
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-1">
              <Briefcase className="h-4 w-4 shrink-0" />
              <span>{getIndustryName(company.industry)}</span>
            </div>
            {company.location && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <MapPin className="h-4 w-4 shrink-0" />
                <span>{company.location}</span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {(company.bio || company.companyDescription) && (
          <p className="text-gray-600 mb-4 line-clamp-2">{company.bio || company.companyDescription}</p>
        )}

        {company.resources && company.resources.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">الموارد المتاحة</h4>
            <div className="flex flex-wrap gap-1">
              {company.resources.slice(0, 3).map((resourceId, index) => {
                const resource = getResourceInfo(resourceId)
                return (
                  <Badge key={index} variant="outline" className="bg-green-50 text-green-800">
                    <span className="mr-1">{resource.icon}</span> {resource.name}
                  </Badge>
                )
              })}
              {company.resources.length > 3 && (
                <Badge variant="outline" className="bg-gray-50 text-gray-800">
                  +{company.resources.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        <Link href={`/users/${company._id}`}>
          <Button className="w-full bg-green-600 hover:bg-green-700">عرض الملف الكامل</Button>
        </Link>
      </CardContent>
    </Card>
  )
}

import express from "express"
import { authenticate } from "../middleware/auth"
import { User } from "../models"
import { createError } from "../utils/error"
import { asyncHandler } from "../utils/error"

const router = express.Router()

// Endpoint pour mettre à jour uniquement les compétences d'un utilisateur
router.post("/:userId/update-skills", authenticate, asyncHandler(async (req, res, next) => {
  try {
    const { userId } = req.params
    const { skills } = req.body

    // Vérifier si l'utilisateur est autorisé à mettre à jour ces données
    if (req.user.id !== userId && req.user.role !== "admin") {
      return next(createError(403, "Vous n'êtes pas autorisé à mettre à jour ces données"))
    }

    // Vérifier que les compétences sont correctement formatées
    if (!skills || !Array.isArray(skills)) {
      return next(createError(400, "Les compétences doivent être un tableau"))
    }

    // Vérifier que chaque compétence a les propriétés requises
    const validSkills = skills.map(skill => {
      if (!skill.category) {
        return {
          ...skill,
          category: 'technical' // Catégorie par défaut
        };
      }
      if (!skill.level) {
        return {
          ...skill,
          level: 'intermediate' // Niveau par défaut
        };
      }
      return skill;
    });

    console.log("Mise à jour des compétences pour l'utilisateur:", userId);
    console.log("Compétences à mettre à jour:", validSkills);

    // Mettre à jour uniquement les compétences de l'utilisateur
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: { skills: validSkills } },
      { new: true }
    );

    if (!updatedUser) {
      return next(createError(404, "Utilisateur non trouvé"))
    }

    console.log("Compétences mises à jour avec succès");
    console.log("Nouvelles compétences:", updatedUser.skills);

    res.status(200).json({
      success: true,
      message: "Compétences mises à jour avec succès",
      skills: updatedUser.skills
    });
  } catch (error) {
    console.error("Erreur lors de la mise à jour des compétences:", error);
    next(error);
  }
}));

export default router;

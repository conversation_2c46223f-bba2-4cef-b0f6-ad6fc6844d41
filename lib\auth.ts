// Helper functions for authentication

/**
 * Get the current user from localStorage
 */
export const getCurrentUser = () => {
  if (typeof window !== "undefined") {
    const userStr = localStorage.getItem("user")

    if (userStr) {
      try {
        const user = JSON.parse(userStr)
        console.log('[Auth] Retrieved user from localStorage:', user)

        // Ensure role is properly formatted
        if (user && user.role) {
          // If role is an object with code property, ensure it's properly structured
          if (typeof user.role === 'object' && user.role !== null) {
            console.log('[Auth] User has object role:', user.role)
            // Make sure the role object has a code property
            if (!user.role.code && user.role._id) {
              console.log('[Auth] Fixing role object format')
              user.role.code = 'admin' // Assume admin if we have a role object without code
            }
          }
        }

        return user
      } catch (error) {
        console.error('[Auth] Error parsing user data:', error)
        return null
      }
    }
    return null
  }
  return null
}

/**
 * Get the authentication token from localStorage
 */
export const getToken = () => {
  if (typeof window !== "undefined") {
    // Get token from localStorage only
    const token = localStorage.getItem("accessToken")
    return token
  }
  return null
}

/**
 * Save user data and token to storage
 * Always use localStorage to ensure data is shared between tabs
 */
export const saveUserData = (userData: any, token: string, rememberMe = false) => {
  // Always use localStorage to ensure data is shared between tabs
  // The rememberMe parameter is kept for backward compatibility

  // Ensure role is properly formatted before saving
  if (userData && userData.role) {
    // If role is an object with code property, ensure it's properly structured
    if (typeof userData.role === 'object' && userData.role !== null) {
      console.log('[Auth] Saving user with object role:', userData.role)
      // Make sure the role object has a code property
      if (!userData.role.code && userData.role._id) {
        console.log('[Auth] Fixing role object format before saving')
        userData.role.code = 'admin' // Assume admin if we have a role object without code
      }
    }
  }

  // Save user data and token to localStorage
  localStorage.setItem("user", JSON.stringify(userData))
  localStorage.setItem("accessToken", token)

  // For debugging
  console.log('[Auth] Saved user data and token to localStorage:', userData)
}

/**
 * Clear user data and token from storage
 */
export const clearUserData = () => {
  // Clear from localStorage
  localStorage.removeItem("user")
  localStorage.removeItem("accessToken")

  // For backward compatibility, also clear from sessionStorage
  sessionStorage.removeItem("user")
  sessionStorage.removeItem("accessToken")

  // For debugging
  console.log('[Auth] Cleared user data and token from storage')
}

/**
 * Check if the user is authenticated
 */
export const isAuthenticated = () => {
  const token = getToken();
  return !!token
}

/**
 * Check if the user has a specific role
 */
export const hasRole = (role: string) => {
  const user = getCurrentUser()

  if (!user) {
    console.log('[Auth] hasRole check failed: No user found');
    return false
  }

  console.log('[Auth] Checking if user has role:', role);
  console.log('[Auth] User role:', user.role, typeof user.role);

  // Pour le débogage, afficher l'objet utilisateur complet
  console.log('[Auth] User object:', JSON.stringify(user));

  // Vérification plus permissive pour le rôle admin
  if (role === 'admin') {
    // Cas 1: rôle est une chaîne exacte 'admin'
    if (typeof user.role === 'string' && user.role === 'admin') {
      console.log('[Auth] User has string role "admin"');
      return true;
    }

    // Cas 2: rôle est un objet avec code === 'admin'
    if (typeof user.role === 'object' && user.role && user.role.code === 'admin') {
      console.log('[Auth] User has object role with code "admin"');
      return true;
    }

    // Cas 3: rôle est une chaîne contenant 'admin'
    if (typeof user.role === 'string' && user.role.includes('admin')) {
      console.log('[Auth] User has string role containing "admin"');
      return true;
    }

    // Cas 4: rôle est un objet avec _id mais sans code (cas particulier)
    if (typeof user.role === 'object' && user.role && user.role._id && !user.role.code) {
      console.log('[Auth] User has object role with _id but no code, assuming admin');
      // Corriger le rôle pour les prochaines vérifications
      user.role.code = 'admin';
      // Sauvegarder la correction dans localStorage
      if (getToken()) {
        saveUserData(user, getToken());
      }
      return true;
    }

    // Cas 5: username est 'admin' (pour le développement)
    if (user.username === 'admin') {
      console.log('[Auth] User has username "admin"');
      return true;
    }
  }

  // Gestion standard des rôles pour les autres cas
  if (typeof user.role === 'string') {
    return user.role === role;
  } else if (typeof user.role === 'object' && user.role) {
    return user.role.code === role;
  }

  console.log('[Auth] Role check failed');
  return false;
}

/**
 * Check if the user is the owner of a resource
 */
export const isOwner = (resourceAuthorId: string) => {
  const user = getCurrentUser()
  return user && user.id === resourceAuthorId
}

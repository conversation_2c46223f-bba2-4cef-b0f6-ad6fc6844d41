"use client"

import { ColumnDef } from "@tanstack/react-table"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowUpDown, Edit, Trash2, Eye } from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { api } from "@/lib/api"
import { useRouter } from "next/navigation"
import { toast } from "@/components/ui/use-toast"

export type BannerColumn = {
  id: string
  image: string
  mainText: string
  subText: string
  mainTextColor: string
  subTextColor: string
  order: number
  isActive: boolean
}

export const columns: ColumnDef<BannerColumn>[] = [
  {
    accessorKey: "image",
    header: "Image",
    cell: ({ row }) => (
      <div className="w-20 h-12 rounded-md overflow-hidden">
        <img
          src={row.original.image}
          alt={row.original.mainText}
          className="w-full h-full object-cover"
        />
      </div>
    ),
  },
  {
    accessorKey: "mainText",
    header: "Texte principal",
  },
  {
    accessorKey: "subText",
    header: "Texte secondaire",
  },
  {
    accessorKey: "order",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Ordre
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
  },
  {
    accessorKey: "isActive",
    header: "Statut",
    cell: ({ row }) => (
      <Badge variant={row.original.isActive ? "default" : "secondary"}>
        {row.original.isActive ? "Actif" : "Inactif"}
      </Badge>
    ),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const router = useRouter()
      const handleDelete = async () => {
        try {
          await api.delete(`/api/banners/${row.original.id}`)
          toast({
            title: "Bannière supprimée",
            description: "La bannière a été supprimée avec succès",
          })
          router.refresh()
        } catch (error) {
          toast({
            title: "Erreur",
            description: "Une erreur est survenue lors de la suppression",
            variant: "destructive",
          })
        }
      }

      return (
        <div className="flex space-x-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href={`/admin/banners/${row.original.id}`}>
              <Edit className="h-4 w-4" />
            </Link>
          </Button>
          <Button variant="ghost" size="icon" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      )
    },
  },
]

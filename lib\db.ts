// Dummy db module for API routes
// This is a placeholder to satisfy imports in API routes

export const connectToDatabase = async () => {
  console.log('Using mock database connection for API routes');
  return {
    db: {
      collection: (name: string) => ({
        find: () => ({
          toArray: () => Promise.resolve([])
        }),
        findOne: () => Promise.resolve(null),
        insertOne: () => Promise.resolve({ insertedId: 'mock-id' }),
        updateOne: () => Promise.resolve({ modifiedCount: 1 }),
        deleteOne: () => Promise.resolve({ deletedCount: 1 })
      })
    },
    client: {
      close: () => Promise.resolve()
    }
  };
};

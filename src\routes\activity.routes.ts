import express from "express"
import { authenticate, adminOnly } from "../middleware/auth"
import {
  createActivity,
  getUserActivities,
  getInitiativeActivities,
  deleteActivity
} from "../controllers/activity.controller"

const router = express.Router()

// Routes publiques
router.get("/users/:userId", getUserActivities)
router.get("/initiatives/:initiativeId", getInitiativeActivities)

// Routes protégées
router.post("/", authenticate, createActivity)
router.delete("/:id", authenticate, deleteActivity)

export default router

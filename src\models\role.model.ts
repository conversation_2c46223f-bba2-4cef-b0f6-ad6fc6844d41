import mongoose, { Schema, Document } from "mongoose";
import { IPermission } from "./permission.model";

export interface IRole extends Document {
  name: string;
  description: string;
  code: string;
  permissions: mongoose.Types.ObjectId[] | IPermission[];
  isDefault: boolean;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const roleSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    permissions: [{
      type: Schema.Types.ObjectId,
      ref: "Permission",
    }],
    isDefault: {
      type: Boolean,
      default: false,
    },
    isSystem: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

export const Role = mongoose.model<IRole>("Role", roleSchema);

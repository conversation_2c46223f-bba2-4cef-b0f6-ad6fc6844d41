"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../../../components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../../../../components/ui/card"
import { Badge } from "../../../../components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "../../../../components/ui/avatar"
import { Alert, AlertDescription } from "../../../../components/ui/alert"
import { 
  Loader2, 
  AlertCircle, 
  ChevronLeft, 
  Users, 
  UserPlus, 
  Award, 
  Star,
  Calendar,
  Clock,
  Search,
  Filter,
  ArrowUpDown
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../../components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rig<PERSON>,
} from "../../../../components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select"
import { Input } from "../../../../components/ui/input"
import { Textarea } from "../../../../components/ui/textarea"
import { Progress } from "../../../../components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../../../components/ui/tabs"
import { useAuth } from "../../../../components/auth-provider"
import { api } from "../../../../lib/api"
import { toast } from "../../../../components/ui/use-toast"
import { Toaster } from "../../../../components/ui/toaster"

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  author: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  status: string
  progress: number
  requiredVolunteers?: number
  currentVolunteers?: string[]
}

interface Volunteer {
  _id: string
  name: string
  username: string
  avatar: string
  bio?: string
  location?: string
  userType: string
  skills?: string[]
  qualifications?: string[]
}

interface VolunteerData {
  user: string
  role: string
  skills: string[]
  availability: string
  message: string
  joinedAt: string
  leftAt?: string
  status: string
  contributions: {
    description: string
    points: number
    date: string
  }[]
  points: number
}

export default function InitiativeVolunteersPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params
  const { user, isAuthenticated } = useAuth()
  
  const [initiative, setInitiative] = useState<Initiative | null>(null)
  const [volunteers, setVolunteers] = useState<Volunteer[]>([])
  const [volunteersData, setVolunteersData] = useState<Record<string, VolunteerData>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isInitiativeLoading, setIsInitiativeLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isAuthor, setIsAuthor] = useState(false)
  const [totalVolunteers, setTotalVolunteers] = useState(0)
  const [activeTab, setActiveTab] = useState("active")
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [sortBy, setSortBy] = useState("joinDate")
  
  const [selectedVolunteer, setSelectedVolunteer] = useState<string | null>(null)
  const [showPointsForm, setShowPointsForm] = useState(false)
  const [pointsFormData, setPointsFormData] = useState({
    points: 0,
    description: "",
  })
  
  useEffect(() => {
    fetchInitiative()
  }, [id])
  
  useEffect(() => {
    if (initiative) {
      fetchVolunteers()
      
      // Check if user is the author
      if (user && initiative.author._id === user.id) {
        setIsAuthor(true)
      }
    }
  }, [initiative, user])
  
  const fetchInitiative = async () => {
    setIsInitiativeLoading(true)
    try {
      const response = await api.get(`/api/initiatives/${id}`, false)
      
      if (response.success && response.initiative) {
        setInitiative(response.initiative)
      } else {
        setError("Failed to fetch initiative")
      }
    } catch (err: any) {
      console.error("Error fetching initiative:", err)
      setError(err.message || "An error occurred while fetching initiative")
    } finally {
      setIsInitiativeLoading(false)
    }
  }
  
  const fetchVolunteers = async () => {
    setIsLoading(true)
    try {
      const response = await api.get(`/api/initiatives/${id}/volunteers`, false)
      
      if (response.success) {
        setVolunteers(response.volunteers || [])
        setTotalVolunteers(response.totalVolunteers || 0)
        
        // Fetch volunteer data for each volunteer if author
        if (isAuthor && response.volunteers && response.volunteers.length > 0) {
          const volunteerDataMap: Record<string, VolunteerData> = {}
          
          for (const volunteer of response.volunteers) {
            try {
              const dataResponse = await api.get(
                `/api/initiatives/${id}/volunteers/${volunteer._id}/contributions`, 
                true
              )
              
              if (dataResponse.success && dataResponse.volunteerData) {
                volunteerDataMap[volunteer._id] = dataResponse.volunteerData
              }
            } catch (err) {
              console.error(`Error fetching data for volunteer ${volunteer._id}:`, err)
            }
          }
          
          setVolunteersData(volunteerDataMap)
        }
      } else {
        setError("Failed to fetch volunteers")
      }
    } catch (err: any) {
      console.error("Error fetching volunteers:", err)
      setError(err.message || "An error occurred while fetching volunteers")
    } finally {
      setIsLoading(false)
    }
  }
  
  const handleAddPoints = async () => {
    if (!selectedVolunteer) return
    
    try {
      const response = await api.post(
        `/api/initiatives/${id}/volunteers/${selectedVolunteer}/points`,
        pointsFormData,
        true
      )
      
      if (response.success) {
        toast({
          title: "تمت إضافة النقاط بنجاح",
          description: `تمت إضافة ${pointsFormData.points} نقطة للمتطوع`,
          variant: "default",
        })
        
        // Refresh volunteer data
        fetchVolunteers()
        setShowPointsForm(false)
        
        // Reset form
        setPointsFormData({
          points: 0,
          description: "",
        })
      } else {
        throw new Error(response.message || "Failed to add points")
      }
    } catch (err: any) {
      console.error("Error adding points:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء محاولة إضافة النقاط",
        variant: "destructive",
      })
    }
  }
  
  const handlePointsFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setPointsFormData(prev => ({ 
      ...prev, 
      [name]: name === "points" ? Number(value) : value 
    }))
  }
  
  const getRoleBadge = (role: string) => {
    switch (role) {
      case "leader":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">قائد</Badge>
      case "coordinator":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">منسق</Badge>
      case "specialist":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">متخصص</Badge>
      case "general":
      default:
        return <Badge className="bg-green-100 text-green-800 border-green-200">متطوع عام</Badge>
    }
  }
  
  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case "flexible":
        return "مرن"
      case "weekends":
        return "عطلة نهاية الأسبوع"
      case "evenings":
        return "المساء"
      case "fulltime":
        return "دوام كامل"
      default:
        return availability
    }
  }
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }
  
  const filteredVolunteers = volunteers.filter(volunteer => {
    // Filter by tab (status)
    if (activeTab !== "all") {
      const volunteerData = volunteersData[volunteer._id]
      if (!volunteerData) return activeTab === "active"
      if (volunteerData.status !== activeTab) return false
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      if (
        !volunteer.name.toLowerCase().includes(query) &&
        !volunteer.username.toLowerCase().includes(query) &&
        !(volunteer.bio && volunteer.bio.toLowerCase().includes(query))
      ) {
        return false
      }
    }
    
    // Filter by role
    if (roleFilter !== "all") {
      const volunteerData = volunteersData[volunteer._id]
      if (!volunteerData || volunteerData.role !== roleFilter) return false
    }
    
    return true
  })
  
  // Sort volunteers
  const sortedVolunteers = [...filteredVolunteers].sort((a, b) => {
    const dataA = volunteersData[a._id]
    const dataB = volunteersData[b._id]
    
    switch (sortBy) {
      case "joinDate":
        if (!dataA || !dataB) return 0
        return new Date(dataB.joinedAt).getTime() - new Date(dataA.joinedAt).getTime()
      case "points":
        if (!dataA || !dataB) return 0
        return (dataB.points || 0) - (dataA.points || 0)
      case "name":
        return a.name.localeCompare(b.name)
      case "role":
        if (!dataA || !dataB) return 0
        return dataA.role.localeCompare(dataB.role)
      default:
        return 0
    }
  })
  
  if (isInitiativeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }
  
  if (error || !initiative) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "لم يتم العثور على المبادرة"}</AlertDescription>
        </Alert>
        <div className="mt-6">
          <Button onClick={() => router.back()} variant="outline">
            <ChevronLeft className="ml-2 h-4 w-4" />
            العودة
          </Button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-2">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ChevronLeft className="ml-2 h-4 w-4" />
            العودة
          </Button>
          <Link href={`/initiatives/${id}`}>
            <Button variant="outline" size="sm">
              عرض المبادرة
            </Button>
          </Link>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">{initiative.title}</h1>
            <p className="text-gray-600 mt-1">{initiative.shortDescription}</p>
            <div className="flex items-center gap-2 mt-2">
              <Badge style={{ backgroundColor: initiative.category.color }}>
                {initiative.category.arabicName || initiative.category.name}
              </Badge>
              <Badge
                className={
                  initiative.status === "active"
                    ? "bg-green-100 text-green-800 border-green-200"
                    : initiative.status === "completed"
                      ? "bg-blue-100 text-blue-800 border-blue-200"
                      : "bg-gray-100 text-gray-800 border-gray-200"
                }
              >
                {initiative.status === "active"
                  ? "نشطة"
                  : initiative.status === "completed"
                    ? "مكتملة"
                    : initiative.status === "pending"
                      ? "قيد المراجعة"
                      : initiative.status}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">المتطوعون</p>
              <p className="text-2xl font-bold">
                {totalVolunteers}
                {initiative.requiredVolunteers ? ` / ${initiative.requiredVolunteers}` : ""}
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-500">التقدم</p>
              <p className="text-2xl font-bold">{initiative.progress}%</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Progress Bar */}
      {initiative.requiredVolunteers && (
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium">نسبة اكتمال فريق المتطوعين</span>
            <span className="text-sm font-medium">
              {totalVolunteers} / {initiative.requiredVolunteers} ({Math.round((totalVolunteers / initiative.requiredVolunteers) * 100)}%)
            </span>
          </div>
          <Progress 
            value={Math.min(100, (totalVolunteers / initiative.requiredVolunteers) * 100)} 
            className="h-2"
          />
        </div>
      )}
      
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="البحث عن متطوع..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأدوار</SelectItem>
                <SelectItem value="general">متطوع عام</SelectItem>
                <SelectItem value="specialist">متخصص</SelectItem>
                <SelectItem value="coordinator">منسق</SelectItem>
                <SelectItem value="leader">قائد</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="الترتيب حسب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="joinDate">تاريخ الانضمام</SelectItem>
                <SelectItem value="points">النقاط</SelectItem>
                <SelectItem value="name">الاسم</SelectItem>
                <SelectItem value="role">الدور</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      {/* Add Points Dialog */}
      <Dialog open={showPointsForm} onOpenChange={setShowPointsForm}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>إضافة نقاط للمتطوع</DialogTitle>
            <DialogDescription>
              أضف نقاط للمتطوع مقابل مساهمته في المبادرة.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="points" className="font-medium">
                النقاط
              </label>
              <Input
                id="points"
                name="points"
                type="number"
                min="1"
                placeholder="عدد النقاط"
                value={pointsFormData.points || ""}
                onChange={handlePointsFormChange}
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="description" className="font-medium">
                وصف المساهمة
              </label>
              <Textarea
                id="description"
                name="description"
                placeholder="وصف المساهمة التي قدمها المتطوع"
                value={pointsFormData.description}
                onChange={handlePointsFormChange}
                className="min-h-[100px]"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPointsForm(false)}>
              إلغاء
            </Button>
            <Button 
              className="bg-green-600 hover:bg-green-700"
              onClick={handleAddPoints}
              disabled={!pointsFormData.points || !pointsFormData.description}
            >
              إضافة النقاط
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Tabs and Volunteers List */}
      <Card>
        <CardHeader className="pb-0">
          <div className="flex items-center justify-between">
            <CardTitle>المتطوعون</CardTitle>
            <Link href={`/initiatives/${id}`}>
              <Button variant="outline" size="sm">
                <UserPlus className="ml-2 h-4 w-4" />
                انضم كمتطوع
              </Button>
            </Link>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="active">نشط</TabsTrigger>
              <TabsTrigger value="left">غادر</TabsTrigger>
              <TabsTrigger value="all">الكل</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-[#0a8754]" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : sortedVolunteers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500 mb-4">لا يوجد متطوعون يطابقون معايير البحث</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المتطوع</TableHead>
                    <TableHead>الدور</TableHead>
                    <TableHead>المهارات</TableHead>
                    <TableHead>التوفر</TableHead>
                    <TableHead>تاريخ الانضمام</TableHead>
                    <TableHead>النقاط</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedVolunteers.map((volunteer) => {
                    const volunteerData = volunteersData[volunteer._id]
                    
                    return (
                      <TableRow key={volunteer._id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={volunteer.avatar || "/placeholder.svg?height=40&width=40"} alt={volunteer.name} />
                              <AvatarFallback>{volunteer.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{volunteer.name}</p>
                              <p className="text-sm text-gray-500">@{volunteer.username}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {volunteerData ? getRoleBadge(volunteerData.role) : "-"}
                        </TableCell>
                        <TableCell>
                          {volunteerData && volunteerData.skills && volunteerData.skills.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {volunteerData.skills.slice(0, 3).map((skill, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                              {volunteerData.skills.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{volunteerData.skills.length - 3}
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-500">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {volunteerData ? getAvailabilityText(volunteerData.availability) : "-"}
                        </TableCell>
                        <TableCell>
                          {volunteerData ? (
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 ml-1 text-gray-500" />
                              {formatDate(volunteerData.joinedAt)}
                            </div>
                          ) : (
                            "-"
                          )}
                        </TableCell>
                        <TableCell>
                          {volunteerData && volunteerData.points > 0 ? (
                            <div className="flex items-center text-amber-600 font-medium">
                              <Star className="h-4 w-4 ml-1" />
                              {volunteerData.points}
                            </div>
                          ) : (
                            <span className="text-gray-500">0</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Link href={`/users/${volunteer._id}`}>
                              <Button variant="outline" size="sm">
                                عرض الملف
                              </Button>
                            </Link>
                            
                            {isAuthor && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-amber-600 border-amber-200 hover:bg-amber-50"
                                onClick={() => {
                                  setSelectedVolunteer(volunteer._id)
                                  setShowPointsForm(true)
                                }}
                              >
                                <Award className="h-4 w-4 ml-1" />
                                إضافة نقاط
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      
      <Toaster />
    </div>
  )
}

import nodemailer from "nodemailer"

// Determine if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Create nodemailer transporter
let transporter: nodemailer.Transporter;

if (isDevelopment) {
  // In development, use a mock transporter that just logs emails
  console.log('[Email] Using mock email transporter for development');

  // Create a mock transporter that doesn't actually send emails
  transporter = {
    sendMail: async (mailOptions: any) => {
      console.log('[Email] Mock email sent:');
      console.log('  To:', mailOptions.to);
      console.log('  Subject:', mailOptions.subject);
      console.log('  Content:', mailOptions.html.substring(0, 100) + '...');
      return { messageId: 'mock-email-id-' + Date.now() };
    }
  } as any;
} else {
  // In production, use the real transporter
  transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST || "smtp.mailtrap.io",
    port: Number.parseInt(process.env.EMAIL_PORT || "2525"),
    auth: {
      user: process.env.EMAIL_USER || "",
      pass: process.env.EMAIL_PASS || "",
    },
  });
}

// Send verification email
export const sendVerificationEmail = async (email: string, token: string) => {
  const verificationUrl = `${process.env.FRONTEND_URL || "http://localhost:3000"}/api/auth/verify-email/${token}`

  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: email,
    subject: "Verify Your Email - Initiatives.dz",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #0a8754; text-align: center;">Welcome to Initiatives.dz!</h2>
        <p>Thank you for registering. Please verify your email address by clicking the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #0a8754; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">Verify Email</a>
        </div>
        <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't register for an account, please ignore this email.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #666; font-size: 12px;">
          <p>© ${new Date().getFullYear()} Initiatives.dz. All rights reserved.</p>
        </div>
      </div>
    `,
  }

  await transporter.sendMail(mailOptions)
}

// Send password reset email
export const sendPasswordResetEmail = async (email: string, token: string) => {
  const resetUrl = `${process.env.FRONTEND_URL || "http://localhost:3000"}/auth/reset-password/${token}`

  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: email,
    subject: "Reset Your Password - Initiatives.dz",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #0a8754; text-align: center;">Reset Your Password</h2>
        <p>You requested a password reset. Please click the button below to reset your password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #0a8754; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">Reset Password</a>
        </div>
        <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${resetUrl}</p>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request a password reset, please ignore this email.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #666; font-size: 12px;">
          <p>© ${new Date().getFullYear()} Initiatives.dz. All rights reserved.</p>
        </div>
      </div>
    `,
  }

  await transporter.sendMail(mailOptions)
}

// Send notification email
export const sendNotificationEmail = async (email: string, subject: string, message: string, link?: string) => {
  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: email,
    subject,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #0a8754; text-align: center;">Notification from Initiatives.dz</h2>
        <p>${message}</p>
        ${
          link
            ? `
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL || "http://localhost:3000"}${link}" style="background-color: #0a8754; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Details</a>
        </div>
        `
            : ""
        }
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #666; font-size: 12px;">
          <p>© ${new Date().getFullYear()} Initiatives.dz. All rights reserved.</p>
        </div>
      </div>
    `,
  }

  await transporter.sendMail(mailOptions)
}


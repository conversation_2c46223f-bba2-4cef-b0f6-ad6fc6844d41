"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Loader2 } from "lucide-react"
import { api } from "@/lib/api"
import UserSkillsDisplay from "@/components/user/user-skills-display"
import UserProposerDisplay from "@/components/user/user-proposer-display"
import UserCompanyDisplay from "@/components/user/user-company-display"

export default function UserProfilePage() {
  const params = useParams()
  const id = params.id as string
  
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const data = await api.get(`/api/users/${id}`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user profile")
        }

        setUser(data.user || data.data)
      } catch (err: any) {
        console.error('Error fetching user profile:', err)
        setError(err.message || "An error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserProfile()
  }, [id])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading profile...</span>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error || "User not found"}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">الملف الشخصي</h1>
      
      {user.userType === 'volunteer' && (
        <UserSkillsDisplay 
          skills={user.skills || []} 
          qualifications={user.qualifications || []} 
          interests={user.interests || []}
          userType={user.userType}
        />
      )}
      
      {user.userType === 'proposer' && (
        <UserProposerDisplay 
          ideaDescription={user.ideaDescription}
          objectives={user.objectives}
          needs={user.needs}
          qualifications={user.qualifications || []}
          skills={user.skills || []}
          interests={user.interests || []}
        />
      )}
      
      {user.userType === 'company' && (
        <UserCompanyDisplay 
          companyName={user.companyName}
          industry={user.industry}
          customIndustry={user.customIndustry}
          companyDescription={user.companyDescription}
          employeeCount={user.employeeCount}
          foundingYear={user.foundingYear}
          contactPhone={user.contactPhone}
          contactEmail={user.contactEmail}
          website={user.website}
          address={user.address}
          services={user.services || []}
          resources={user.resources || []}
          customResources={user.customResources || []}
          supportDescription={user.supportDescription}
        />
      )}
    </div>
  )
}

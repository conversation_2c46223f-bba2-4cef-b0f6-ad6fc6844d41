import { Request, Response, NextFunction } from "express"
import { body, param, validationResult } from "express-validator"
import { createError } from "../../utils/error"
import mongoose from "mongoose"

// Validate resource creation
export const validateResource = [
  body("type")
    .isIn(["material", "financial", "human", "service", "other"])
    .withMessage("Resource type must be one of: material, financial, human, service, other"),
  body("name")
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage("Resource name must be between 3 and 100 characters"),
  body("description")
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage("Resource description must be between 10 and 500 characters"),
  body("quantity")
    .isNumeric()
    .withMessage("Quantity must be a number")
    .custom((value) => value > 0)
    .withMessage("Quantity must be greater than 0"),
  body("unit")
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage("Unit must be between 1 and 50 characters"),
  body("notes")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Notes must not exceed 500 characters"),
  body("attachments")
    .optional()
    .isArray()
    .withMessage("Attachments must be an array"),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return next(createError(400, errors.array()[0].msg))
    }
    next()
  },
]

// Validate resource need creation/update
export const validateResourceNeed = [
  body("type")
    .isIn(["material", "financial", "human", "service", "other"])
    .withMessage("Resource type must be one of: material, financial, human, service, other"),
  body("name")
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage("Resource name must be between 3 and 100 characters"),
  body("description")
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage("Resource description must be between 10 and 500 characters"),
  body("quantity")
    .isNumeric()
    .withMessage("Quantity must be a number")
    .custom((value) => value > 0)
    .withMessage("Quantity must be greater than 0"),
  body("unit")
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage("Unit must be between 1 and 50 characters"),
  body("priority")
    .optional()
    .isIn(["low", "medium", "high", "critical"])
    .withMessage("Priority must be one of: low, medium, high, critical"),
  body("status")
    .optional()
    .isIn(["open", "in_progress", "fulfilled", "canceled"])
    .withMessage("Status must be one of: open, in_progress, fulfilled, canceled"),
  body("notes")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Notes must not exceed 500 characters"),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return next(createError(400, errors.array()[0].msg))
    }
    next()
  },
]

// Validate resource status update
export const validateResourceStatusUpdate = [
  param("id")
    .custom((value) => mongoose.Types.ObjectId.isValid(value))
    .withMessage("Invalid resource ID"),
  body("status")
    .isIn(["requested", "approved", "rejected", "delivered", "canceled"])
    .withMessage("Status must be one of: requested, approved, rejected, delivered, canceled"),
  body("notes")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Notes must not exceed 500 characters"),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return next(createError(400, errors.array()[0].msg))
    }
    next()
  },
]

// Validate resource need status update
export const validateResourceNeedStatusUpdate = [
  param("id")
    .custom((value) => mongoose.Types.ObjectId.isValid(value))
    .withMessage("Invalid resource need ID"),
  body("status")
    .isIn(["open", "in_progress", "fulfilled", "canceled"])
    .withMessage("Status must be one of: open, in_progress, fulfilled, canceled"),
  body("notes")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Notes must not exceed 500 characters"),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return next(createError(400, errors.array()[0].msg))
    }
    next()
  },
]
"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ThumbsUp, Flag, Reply } from "lucide-react"

// <PERSON><PERSON> comments data
const mockComments = [
  {
    id: "1",
    author: "سارة بن علي",
    authorImage: "/placeholder.svg?height=50&width=50",
    date: "قبل 3 أيام",
    content: "فكرة رائعة! أنا أدعم هذه المبادرة بشدة وأتمنى أن تنجح. سأكون من المتطوعين الأوائل.",
    likes: 12,
    replies: [
      {
        id: "1-1",
        author: "أحمد مصطفى",
        authorImage: "/placeholder.svg?height=50&width=50",
        date: "قبل يومين",
        content: "شكراً لدعمك سارة! سنتواصل معك قريباً بخصوص التطوع.",
        likes: 3,
      },
    ],
  },
  {
    id: "2",
    author: "محمد عبد الله",
    authorImage: "/placeholder.svg?height=50&width=50",
    date: "قبل 5 أيام",
    content: "هل هناك خطة لصيانة الأشجار بعد زراعتها؟ أعتقد أن هذا مهم جداً لضمان استمرارية المشروع.",
    likes: 8,
    replies: [],
  },
]

interface CommentProps {
  comment: {
    id: string
    author: string
    authorImage: string
    date: string
    content: string
    likes: number
    replies?: any[]
  }
  isReply?: boolean
}

const Comment = ({ comment, isReply = false }: CommentProps) => {
  const [liked, setLiked] = useState(false)
  const [likeCount, setLikeCount] = useState(comment.likes)
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [replyText, setReplyText] = useState("")

  const handleLike = () => {
    if (!liked) {
      setLiked(true)
      setLikeCount(likeCount + 1)
    } else {
      setLiked(false)
      setLikeCount(likeCount - 1)
    }
  }

  const handleReply = () => {
    // In a real app, this would submit the reply to the server
    setShowReplyForm(false)
    setReplyText("")
    // For demo purposes, we're not actually adding the reply
  }

  return (
    <div className={`${isReply ? "mr-12 mt-4" : "mb-6 pb-6 border-b border-gray-200"}`}>
      <div className="flex gap-4">
        <Avatar>
          <AvatarImage src={comment.authorImage} alt={comment.author} />
          <AvatarFallback>{comment.author.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <div>
              <p className="font-medium">{comment.author}</p>
              <p className="text-sm text-gray-500">{comment.date}</p>
            </div>
          </div>
          <p className="my-3 text-gray-700">{comment.content}</p>
          <div className="flex gap-4">
            <button
              className={`text-sm flex items-center gap-1 ${liked ? "text-[#0a8754]" : "text-gray-500"}`}
              onClick={handleLike}
            >
              <ThumbsUp size={14} />
              <span>{likeCount}</span>
            </button>
            {!isReply && (
              <button
                className="text-sm flex items-center gap-1 text-gray-500"
                onClick={() => setShowReplyForm(!showReplyForm)}
              >
                <Reply size={14} />
                <span>رد</span>
              </button>
            )}
            <button className="text-sm flex items-center gap-1 text-gray-500">
              <Flag size={14} />
              <span>إبلاغ</span>
            </button>
          </div>

          {showReplyForm && (
            <div className="mt-4">
              <Textarea
                placeholder="اكتب ردك هنا..."
                className="mb-2"
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" size="sm" onClick={() => setShowReplyForm(false)}>
                  إلغاء
                </Button>
                <Button size="sm" onClick={handleReply} disabled={!replyText.trim()}>
                  إرسال
                </Button>
              </div>
            </div>
          )}

          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4">
              {comment.replies.map((reply) => (
                <Comment key={reply.id} comment={reply} isReply={true} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface CommentSectionProps {
  initiativeId: string
}

export default function CommentSection({ initiativeId }: CommentSectionProps) {
  const [commentText, setCommentText] = useState("")
  const [comments, setComments] = useState(mockComments)

  const handleAddComment = () => {
    if (!commentText.trim()) return

    // In a real app, this would submit to the server
    const newComment = {
      id: `new-${Date.now()}`,
      author: "أنت",
      authorImage: "/placeholder.svg?height=50&width=50",
      date: "الآن",
      content: commentText,
      likes: 0,
      replies: [],
    }

    setComments([newComment, ...comments])
    setCommentText("")
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold mb-6">التعليقات ({comments.length})</h2>

      <div className="mb-6">
        <Textarea
          placeholder="شارك برأيك حول هذه المبادرة..."
          className="mb-3"
          value={commentText}
          onChange={(e) => setCommentText(e.target.value)}
        />
        <div className="flex justify-end">
          <Button onClick={handleAddComment} disabled={!commentText.trim()}>
            إرسال التعليق
          </Button>
        </div>
      </div>

      <div>
        {comments.map((comment) => (
          <Comment key={comment.id} comment={comment} />
        ))}
      </div>
    </div>
  )
}


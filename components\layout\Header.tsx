"use client"

import React, { useEffect, useState, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { LogoSVG } from '@/components/ui/logo-svg'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Menu, User, LogOut, Settings, Shield, LayoutDashboard, Leaf, BarChart } from 'lucide-react'
import { api } from '@/lib/api'
import { useAuth } from '@/components/auth-provider'

type Settings = {
  siteName: string
  maintenanceMode: boolean
}

const Header = () => {
  const pathname = usePathname()
  // Get auth context directly
  const auth = useAuth()
  const [authState, setAuthState] = useState({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated
  })

  // Update auth state when it changes
  useEffect(() => {
    setAuthState({
      user: auth.user,
      isAuthenticated: auth.isAuthenticated
    })
  }, [auth.user, auth.isAuthenticated])

  // Listen for storage events to sync auth state between tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      // If user or token was removed in another tab
      if ((e.key === 'user' || e.key === 'accessToken' || e.key === 'isAuthenticated') && !e.newValue) {
        // Force refresh the page to update auth state
        window.location.reload()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // Destructure for convenience
  const { user, isAuthenticated } = authState // Revert: Use local state, remove isAuthLoading
  const { logout } = auth
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [settings, setSettings] = useState<Settings>({
    siteName: 'منصة المبادرات',
    maintenanceMode: false
  })

  // Use a ref to track if settings have been fetched
  const settingsFetchedRef = useRef(false)

  useEffect(() => {
    // Only fetch settings once
    if (!settingsFetchedRef.current) {
      const fetchSettings = async () => {
        try {
          const response = await api.get('/api/settings/public', false)

          if (response && response.success && response.settings) {
            setSettings(response.settings)
          }
          // Mark settings as fetched
          settingsFetchedRef.current = true
        } catch (error) {
          console.error('Error fetching settings:', error)
        }
      }

      fetchSettings()
    }
  }, [])

  // Show maintenance mode banner if enabled
  const showMaintenanceBanner = settings.maintenanceMode

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'المبادرات', href: '/initiatives' },
    { name: 'الشركات الداعمة', href: '/companies' },
    { name: 'متابعة التقدم', href: '/progress' },
    { name: 'المجتمع', href: '/community' },
    { name: 'الإحصائيات', href: '/stats' },
    { name: 'من نحن', href: '/about' },
    { name: 'كيف يعمل', href: '/how-it-works' },
  ]

  return (
    <header dir="rtl">
      {showMaintenanceBanner && (
        <div className="bg-yellow-500 text-black py-2 px-4 text-center">
          <p className="text-sm font-medium">
            الموقع في وضع الصيانة. بعض الميزات قد لا تعمل بشكل صحيح.
          </p>
        </div>
      )}

      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <Link href="/" className="flex items-center gap-6 text-2xl font-bold text-[#0a8754]">
                  <img
                    src="/placeholder.svg"
                    alt="Logo"
                    className="h-8 w-auto"
                    style={{ maxHeight: '32px' }}
                  />
                  {settings.siteName}
                </Link>
              </div>
              <div className="hidden md:ml-12 md:flex">
                {navigation.map((item, index) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                      pathname === item.href
                        ? 'border-[#0a8754] text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    } ${index === 0 ? 'mr-8' : ''}`}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>

            <div className="hidden md:ml-6 md:flex md:items-center">
              {/* Revert: Remove isAuthLoading check */}
              {isAuthenticated === true && user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar || ''} alt={user.name} />
                        <AvatarFallback className="bg-gray-200">
                          <User className="h-4 w-4 text-gray-600" />
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel className="text-right">حسابي</DropdownMenuLabel>
                    <DropdownMenuItem>
                      <Link href="/profile" className="flex items-center w-full justify-end">
                        <span>الملف الشخصي</span>
                        <User className="mr-2 h-4 w-4" />
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Link href="/settings" className="flex items-center w-full justify-end">
                        <span>الإعدادات</span>
                        <Settings className="mr-2 h-4 w-4" />
                      </Link>
                    </DropdownMenuItem>

                    {/* Afficher le lien admin si l'utilisateur a le rôle admin */}
                    {/* Revert: Original simpler check */}
                    {(user.role === 'admin' ||
                      (typeof user.role === 'object' && user.role?.code === 'admin') ||
                      (typeof user.role === 'string' && user.role.includes('admin'))) && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Link href="/admin/dashboard" className="flex items-center w-full justify-end">
                            <span>لوحة التحكم</span>
                            <LayoutDashboard className="mr-2 h-4 w-4" />
                          </Link>
                        </DropdownMenuItem>
                      </>
                    )}

                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout} className="w-full">
                      <div className="flex items-center w-full justify-end">
                        <span>تسجيل الخروج</span>
                        <LogOut className="mr-2 h-4 w-4" />
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="flex items-center space-x-4 space-x-reverse">
                  <Link href="/auth/login">
                    <Button variant="outline">تسجيل الدخول</Button>
                  </Link>
                  <Link href="/auth/register">
                    <Button className="bg-[#0a8754] hover:bg-[#097548]">إنشاء حساب</Button>
                  </Link>
                </div>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="flex items-center md:hidden">
              <div className="mr-2">
                <img
                  src="/placeholder.svg"
                  alt="Logo"
                  className="h-6 w-auto"
                  style={{ maxHeight: '24px' }}
                />
              </div>
              <button
                type="button"
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#0a8754]"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <span className="sr-only">فتح القائمة</span>
                <Menu className="block h-6 w-6" aria-hidden="true" />
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        <div className={`${isMenuOpen ? 'block' : 'hidden'} md:hidden`}>
          <div className="pt-2 pb-3 space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`block pl-3 pr-4 py-2 border-r-4 text-base font-medium ${
                  pathname === item.href
                    ? 'border-[#0a8754] bg-[#f0fdf4] text-[#0a8754]'
                    : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}
          </div>

          {/* Mobile menu content */}
          <div className="pt-4 pb-3 border-t border-gray-200">
            {/* Revert: Remove isAuthLoading check */}
            {isAuthenticated === true && user ? (
              <>
                <div className="flex items-center px-4">
                  <div className="flex-shrink-0">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={user.avatar || ''} alt={user.name} />
                      <AvatarFallback className="bg-gray-200">
                        <User className="h-5 w-5 text-gray-600" />
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="mr-3">
                    <div className="text-base font-medium text-gray-800">{user.name}</div>
                    <div className="text-sm font-medium text-gray-500">{user.email}</div>
                  </div>
                </div>
                <div className="mt-3 space-y-1">
                  <Link
                    href="/profile"
                    className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    الملف الشخصي
                  </Link>
                  <Link
                    href="/settings"
                    className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    الإعدادات
                  </Link>

                  {/* Afficher le lien admin si l'utilisateur a le rôle admin */}
                  {/* Revert: Original simpler check */}
                  {(user.role === 'admin' ||
                    (typeof user.role === 'object' && user.role?.code === 'admin') ||
                    (typeof user.role === 'string' && user.role.includes('admin'))) && (
                    <Link
                      href="/admin/dashboard"
                      className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 text-right"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      لوحة التحكم
                    </Link>
                  )}

                  <button
                    onClick={() => {
                      logout()
                      setIsMenuOpen(false)
                    }}
                    className="block w-full text-right px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                  >
                    تسجيل الخروج
                  </button>
                </div>
              </>
            ) : (
              <div className="mt-3 space-y-1 px-2">
                <Link
                  href="/auth/login"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                  onClick={() => setIsMenuOpen(false)}
                >
                  تسجيل الدخول
                </Link>
                <Link
                  href="/auth/register"
                  className="block px-3 py-2 rounded-md text-base font-medium bg-[#0a8754] text-white hover:bg-[#097548]"
                  onClick={() => setIsMenuOpen(false)}
                >
                  إنشاء حساب
                </Link>
              </div>
            )}
          </div>
        </div>
      </nav>
    </header>
  )
}

export default Header

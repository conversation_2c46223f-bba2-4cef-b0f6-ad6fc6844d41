"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { Card, CardContent } from "../../../components/ui/card"
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "../../../components/ui/tabs"
import { Progress } from "../../../components/ui/progress"
import { Badge } from "../../../components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "../../../components/ui/avatar"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import { Dialog, DialogContent, DialogTrigger } from "../../../components/ui/dialog"
import { api } from "../../../lib/api"
import { useAuth } from "../../../components/auth-provider"
import { getImageUrl } from "../../../lib/imageUtils"
import {
  AlertCircle,
  MapPin,
  Calendar,
  Users,
  ThumbsUp,
  MessageSquare,
  Eye,
  Edit,
  Share2,
  Loader2,
  CheckCircle2,
  Vote,
  X,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  UserPlus,
  BarChart
} from "lucide-react"
import CommentSection from "../../../components/initiative/comment-section"
import UpdatesList from "../../../components/initiative/updates-list"
import MilestonesList from "../../../components/initiative/milestones-list"
import VolunteersSection from "../../../components/initiative/volunteers-section"
import ResourcesSection from "../../../components/initiative/resources-section"
import SocialImpactSection from "../../../components/initiative/social-impact-section"

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  fullDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  author: {
    _id: string
    name: string
    username: string
    avatar: string
    bio: string
  }
  location: string
  wilaya?: string
  mainImage: string
  images: string[]
  status: "draft" | "pending" | "active" | "completed" | "rejected"
  goal: number
  supportCount: number
  commentCount: number
  viewCount: number
  progress: number
  createdAt: string
  updatedAt: string
  isVotingEnabled: boolean
  votingEndDate?: string
  supporters: Array<{
    _id: string
    name: string
    username: string
    avatar: string
  }>
  tags: string[]
  requiredVolunteers?: number
  currentVolunteers?: {
    _id: string
    name: string
    username: string
    avatar: string
  }[]
  // Nouveaux champs structurants
  problem?: string
  solution?: string
  beneficiaries?: string
  quantitativeObjectives?: string
  qualitativeObjectives?: string
  socialImpacts?: Array<{
    category: string
    impacts: string[]
  }>
  selectedImpacts?: Array<{
    _id: string
    name: string
    arabicName: string
    description?: string
    arabicDescription?: string
  }>
}

interface Update {
  _id: string
  title: string
  content: string
  author: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  images: string[]
  createdAt: string
}

interface Milestone {
  _id: string
  title: string
  description: string
  targetDate: string
  completedDate?: string
  isCompleted: boolean
  order: number
}

export default function InitiativeDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params

  const [initiative, setInitiative] = useState<Initiative | null>(null)
  const [updates, setUpdates] = useState<Update[]>([])
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [similarInitiatives, setSimilarInitiatives] = useState<Initiative[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSimilarLoading, setIsSimilarLoading] = useState(true)
  const [error, setError] = useState("")
  const [isSupporting, setIsSupporting] = useState(false)
  const [hasSupported, setHasSupported] = useState(false)
  const [supportMessage, setSupportMessage] = useState("")

  // Gallery states
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [galleryIndex, setGalleryIndex] = useState(0)

  // Use the auth context
  const { user, isAuthenticated } = useAuth()

  // We don't need to get the token manually anymore as the api utility handles it

  useEffect(() => {
    const fetchInitiative = async () => {
      try {
        console.log(`Fetching initiative with ID: ${id}`);

        // Use the API utility to make the request
        try {
          // Use the API utility for consistent handling
          const data = await api.get(`/api/initiatives/${id}`, false);
          console.log('Initiative data received:', data);

          if (!data || !data.initiative) {
            console.error('Invalid response data:', data);
            throw new Error('Initiative data not found in response');
          }

          console.log('Initiative data loaded successfully:', data.initiative.title);

          // Process images to ensure they have absolute URLs
          const processedInitiative = {
            ...data.initiative,
            mainImage: getImageUrl(data.initiative.mainImage),
            images: data.initiative.images ? data.initiative.images.map(img => getImageUrl(img)) : []
          };

          setInitiative(processedInitiative);
          setUpdates(data.updates || []);
          setMilestones(data.milestones || []);

          // Check if user has already supported this initiative
          if (user && data.initiative.supporters) {
            const supported = data.initiative.supporters.some((supporter) => supporter._id === user.id);
            setHasSupported(supported);
          }

          setIsLoading(false);

          // Fetch similar initiatives
          fetchSimilarInitiatives(data.initiative._id);

          return;
        } catch (apiError) {
          console.error('Error using API utility:', apiError);
          // Fall back to direct fetch if API utility fails
        }

        // Fallback: Direct approach with hardcoded URL
        const apiUrl = `http://localhost:5000/api/initiatives/${id}`;
        console.log(`Fallback API URL: ${apiUrl}`);

        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          mode: 'cors',
          credentials: 'omit',
          cache: 'no-cache'
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Error response: ${errorText}`);
          throw new Error(`Failed to fetch initiative: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Initiative data received from direct fetch:', data);

        if (!data || !data.initiative) {
          console.error('Invalid response data from direct fetch:', data);
          throw new Error('Initiative data not found in response');
        }

        console.log('Initiative data loaded successfully from direct fetch:', data.initiative.title);

        // Process images to ensure they have absolute URLs
        const processedInitiative = {
          ...data.initiative,
          mainImage: getImageUrl(data.initiative.mainImage),
          images: data.initiative.images ? data.initiative.images.map(img => getImageUrl(img)) : []
        };

        setInitiative(processedInitiative);
        setUpdates(data.updates || []);
        setMilestones(data.milestones || []);

        // Check if user has already supported this initiative
        if (user && data.initiative.supporters) {
          const supported = data.initiative.supporters.some((supporter: any) => supporter._id === user.id);
          setHasSupported(supported);
        }
      } catch (err: any) {
        console.error('Error fetching initiative:', err);
        setError(err.message || "An error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitiative();
  }, [id, user, isAuthenticated])

  const handleSupport = async () => {
    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    setIsSupporting(true)
    setSupportMessage("")

    try {
      // Use the API utility for consistent handling
      const data = await api.post(`/api/initiatives/${id}/support`, {}, true)

      // Update initiative with new support count
      setInitiative((prev) => {
        if (!prev) return null
        return {
          ...prev,
          supportCount: data.supportCount,
        }
      })

      setHasSupported(true)
      setSupportMessage("You have successfully supported this initiative!")
    } catch (err: any) {
      console.error('Error supporting initiative:', err)
      setSupportMessage(err.message || "Failed to support initiative")
    } finally {
      setIsSupporting(false)
    }
  }

  const handleUnsupport = async () => {
    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    setIsSupporting(true)
    setSupportMessage("")

    try {
      // Use the API utility for consistent handling
      const data = await api.delete(`/api/initiatives/${id}/support`)

      // Update initiative with new support count
      setInitiative((prev) => {
        if (!prev) return null
        return {
          ...prev,
          supportCount: data.supportCount,
        }
      })

      setHasSupported(false)
      setSupportMessage("You have removed your support from this initiative.")
    } catch (err: any) {
      console.error('Error unsupporting initiative:', err)
      setSupportMessage(err.message || "Failed to unsupport initiative")
    } finally {
      setIsSupporting(false)
    }
  }

  // Function to fetch similar initiatives
  const fetchSimilarInitiatives = async (initiativeId: string) => {
    setIsSimilarLoading(true)

    try {
      const data = await api.get(`/api/initiatives/${initiativeId}/similar?limit=3`, false)

      if (data && data.initiatives) {
        // Process images to ensure they have absolute URLs
        const processedInitiatives = data.initiatives.map(initiative => ({
          ...initiative,
          mainImage: getImageUrl(initiative.mainImage),
          images: initiative.images ? initiative.images.map(img => getImageUrl(img)) : []
        }));
        setSimilarInitiatives(processedInitiatives);
      }
    } catch (err: any) {
      console.error('Error fetching similar initiatives:', err)
      // Don't set an error state, just log it - this is a non-critical feature
    } finally {
      setIsSimilarLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading initiative...</span>
      </div>
    )
  }

  if (error || !initiative) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "Initiative not found"}</AlertDescription>
        </Alert>
        <div className="mt-6 p-4 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-2">Troubleshooting</h3>
          <p className="mb-4">Please try one of the following:</p>
          <ul className="list-disc pl-5 mb-4 space-y-1">
            <li>Check that the initiative ID in the URL is correct (should be a 24-character hexadecimal string)</li>
            <li>Make sure the backend server is running at {process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}</li>
            <li>Try refreshing the page</li>
          </ul>
          <div className="flex gap-3 mt-4">
            <Button onClick={() => router.back()} variant="outline">
              Go Back
            </Button>
            <Link href="/initiatives">
              <Button>View All Initiatives</Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Initiative Header */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">{initiative.title}</h1>
            <div className="flex flex-wrap items-center gap-2 mb-2">
              <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {initiative.location}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(initiative.createdAt).toLocaleDateString()}
              </Badge>
              <Badge
                variant="outline"
                className={
                  initiative.status === "active"
                    ? "bg-green-100 text-green-800 border-green-200"
                    : initiative.status === "completed"
                      ? "bg-blue-100 text-blue-800 border-blue-200"
                      : initiative.status === "pending"
                        ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                        : "bg-gray-100 text-gray-800 border-gray-200"
                }
              >
                {initiative.status === "active"
                  ? "نشطة"
                  : initiative.status === "completed"
                    ? "مكتملة"
                    : initiative.status === "pending"
                      ? "قيد المراجعة"
                      : initiative.status}
              </Badge>
            </div>
          </div>

          <div className="flex gap-2 mt-4 md:mt-0">
            {user && user.id === initiative.author._id && (
              <>
                <Link href={`/initiatives/${id}/edit`}>
                  <Button variant="outline" className="flex items-center gap-1">
                    <Edit className="h-4 w-4" />
                    تعديل
                  </Button>
                </Link>
                <Link href="/dashboard/initiatives">
                  <Button variant="outline" className="flex items-center gap-1">
                    <BarChart className="h-4 w-4" />
                    لوحة التحكم
                  </Button>
                </Link>
              </>
            )}

            {/* Bouton d'adhésion */}
            {user && user.id !== initiative.author._id && initiative.status === "active" && (
              <Link href="#volunteers">
                <Button
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                  onClick={() => {
                    // Sélectionner l'onglet des volontaires
                    const volunteersTab = document.querySelector('[value="volunteers"]') as HTMLElement;
                    if (volunteersTab) volunteersTab.click();
                  }}
                >
                  <UserPlus className="h-4 w-4" />
                  انضم كمتطوع
                </Button>
              </Link>
            )}

            <Button variant="outline" className="flex items-center gap-1">
              <Share2 className="h-4 w-4" />
              مشاركة
            </Button>
            {initiative.isVotingEnabled && (
              <Link href={`/initiatives/${id}/vote`}>
                <Button className="flex items-center gap-1 bg-purple-600 hover:bg-purple-700">
                  <Vote className="h-4 w-4" />
                  تصويت
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Initiative Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <Card className="bg-white shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">التقدم</p>
                  <h3 className="text-2xl font-bold">{initiative.progress}%</h3>
                </div>
                <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                  initiative.progress < 25 ? "bg-red-100 text-red-600" :
                  initiative.progress < 50 ? "bg-orange-100 text-orange-600" :
                  initiative.progress < 75 ? "bg-yellow-100 text-yellow-600" :
                  "bg-green-100 text-green-600"
                }`}>
                  <CheckCircle2 className="h-5 w-5" />
                </div>
              </div>
              <Progress
                value={initiative.progress}
                className="h-2 mt-2"
                indicatorClassName={
                  initiative.progress < 25 ? "bg-red-500" :
                  initiative.progress < 50 ? "bg-orange-500" :
                  initiative.progress < 75 ? "bg-yellow-500" :
                  "bg-green-500"
                }
              />
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">الدعم</p>
                  <h3 className="text-2xl font-bold">{initiative.supportCount}</h3>
                </div>
                <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                  <ThumbsUp className="h-5 w-5" />
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-500">
                {initiative.goal > 0 ? (
                  <div className="flex justify-between">
                    <span>الهدف: {initiative.goal}</span>
                    <span>{Math.round((initiative.supportCount / initiative.goal) * 100)}%</span>
                  </div>
                ) : (
                  <span>لا يوجد هدف محدد</span>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">المتطوعين</p>
                  <h3 className="text-2xl font-bold">
                    {initiative.currentVolunteers?.length || 0}
                    {initiative.requiredVolunteers ? ` / ${initiative.requiredVolunteers}` : ''}
                  </h3>
                </div>
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                  <Users className="h-5 w-5" />
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-500">
                {initiative.requiredVolunteers ? (
                  <div className="flex justify-between">
                    <span>المطلوب: {initiative.requiredVolunteers}</span>
                    <span>
                      {Math.round(((initiative.currentVolunteers?.length || 0) / initiative.requiredVolunteers) * 100)}%
                    </span>
                  </div>
                ) : (
                  <span>لا يوجد عدد محدد</span>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">المشاهدات</p>
                  <h3 className="text-2xl font-bold">{initiative.viewCount}</h3>
                </div>
                <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600">
                  <Eye className="h-5 w-5" />
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-500">
                منذ {new Date(initiative.createdAt).toLocaleDateString()}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">التعليقات</p>
                  <h3 className="text-2xl font-bold">{initiative.commentCount}</h3>
                </div>
                <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center text-amber-600">
                  <MessageSquare className="h-5 w-5" />
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-500">
                مشاركة المجتمع
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Image and Gallery */}
        <div className="mb-6">
          {/* Main Image */}
          <div
            className="relative w-full h-[400px] rounded-lg overflow-hidden mb-4 cursor-pointer group"
            onClick={() => {
              setSelectedImage(initiative.mainImage || "/placeholder.svg");
              setGalleryIndex(-1); // Special index for main image
            }}
          >
            {initiative.mainImage?.startsWith('http') ? (
              <img
                src={initiative.mainImage}
                alt={initiative.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <Image
                src={initiative.mainImage || "/placeholder.svg"}
                alt={initiative.title}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                style={{ objectFit: "cover" }}
                priority
                unoptimized
              />
            )}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all">
              <ZoomIn className="text-white h-12 w-12 drop-shadow-lg" />
            </div>
          </div>

          {/* Additional Images Gallery */}
          {initiative.images && initiative.images.length > 0 && (
            <>
              <h3 className="text-lg font-medium mb-2">Gallery</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 mt-2">
                {initiative.images.map((image, index) => (
                  <div
                    key={index}
                    className="relative aspect-square rounded-md overflow-hidden cursor-pointer border border-gray-200 hover:border-green-500 transition-all"
                    onClick={() => {
                      setSelectedImage(image);
                      setGalleryIndex(index);
                    }}
                  >
                    {image.startsWith('http') ? (
                      <img
                        src={image}
                        alt={`${initiative.title} - Image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <Image
                        src={image}
                        alt={`${initiative.title} - Image ${index + 1}`}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 20vw, 10vw"
                        style={{ objectFit: "cover" }}
                        unoptimized
                      />
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 flex items-center justify-center opacity-0 hover:opacity-100 transition-all">
                      <ZoomIn className="text-white h-8 w-8 drop-shadow-lg" />
                    </div>
                  </div>
                ))}
              </div>

              {/* Image Viewer Dialog */}
              <Dialog open={!!selectedImage} onOpenChange={(open) => !open && setSelectedImage(null)}>
                <DialogContent className="max-w-5xl w-[90vw] max-h-[90vh] p-0 bg-black/95 border-gray-800">
                  <div className="relative h-[80vh] w-full">
                    {selectedImage && (
                      selectedImage.startsWith('http') ? (
                        <img
                          src={selectedImage}
                          alt={`${initiative.title} - Gallery image`}
                          className="w-full h-full object-contain"
                        />
                      ) : (
                        <Image
                          src={selectedImage}
                          alt={`${initiative.title} - Gallery image`}
                          fill
                          sizes="(max-width: 768px) 100vw, 90vw"
                          style={{ objectFit: "contain" }}
                          unoptimized
                        />
                      )
                    )}

                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 text-white bg-black/50 hover:bg-black/70 rounded-full"
                      onClick={() => setSelectedImage(null)}
                    >
                      <X className="h-5 w-5" />
                    </Button>

                    {/* Navigation buttons */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute left-2 top-1/2 -translate-y-1/2 text-white bg-black/50 hover:bg-black/70 rounded-full"
                      onClick={() => {
                        if (galleryIndex === -1) {
                          // If we're on the main image, go to the last gallery image
                          if (initiative.images && initiative.images.length > 0) {
                            const newIndex = initiative.images.length - 1;
                            setGalleryIndex(newIndex);
                            setSelectedImage(initiative.images[newIndex]);
                          }
                        } else if (galleryIndex === 0) {
                          // If we're on the first gallery image, go to the main image
                          setGalleryIndex(-1);
                          setSelectedImage(initiative.mainImage || "/placeholder.svg");
                        } else {
                          // Otherwise, go to the previous gallery image
                          const newIndex = galleryIndex - 1;
                          setGalleryIndex(newIndex);
                          setSelectedImage(initiative.images[newIndex]);
                        }
                      }}
                    >
                      <ChevronLeft className="h-8 w-8" />
                    </Button>

                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-2 top-1/2 -translate-y-1/2 text-white bg-black/50 hover:bg-black/70 rounded-full"
                      onClick={() => {
                        if (galleryIndex === -1) {
                          // If we're on the main image, go to the first gallery image
                          if (initiative.images && initiative.images.length > 0) {
                            setGalleryIndex(0);
                            setSelectedImage(initiative.images[0]);
                          }
                        } else if (galleryIndex === initiative.images.length - 1) {
                          // If we're on the last gallery image, go to the main image
                          setGalleryIndex(-1);
                          setSelectedImage(initiative.mainImage || "/placeholder.svg");
                        } else {
                          // Otherwise, go to the next gallery image
                          const newIndex = galleryIndex + 1;
                          setGalleryIndex(newIndex);
                          setSelectedImage(initiative.images[newIndex]);
                        }
                      }}
                    >
                      <ChevronRight className="h-8 w-8" />
                    </Button>
                  </div>

                  <div className="bg-black text-white p-4 text-center">
                    <p className="text-sm">
                      {galleryIndex === -1
                        ? "Main Image"
                        : `Image ${galleryIndex + 1} / ${initiative.images.length}`
                      }
                    </p>
                  </div>
                </DialogContent>
              </Dialog>
            </>
          )}
        </div>

        {/* Stats and Support */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="col-span-2">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-semibold">Progress:</span>
                  <span className="text-green-600 font-bold">{initiative.progress}%</span>
                </div>
                <Badge
                  className={
                    initiative.status === "active"
                      ? "bg-green-600"
                      : initiative.status === "completed"
                        ? "bg-blue-600"
                        : initiative.status === "pending"
                          ? "bg-yellow-600"
                          : "bg-gray-600"
                  }
                >
                  {initiative.status.charAt(0).toUpperCase() + initiative.status.slice(1)}
                </Badge>
              </div>
              <Progress value={initiative.progress} className="h-2 mb-4" />

              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="flex items-center justify-center gap-1 text-gray-600">
                    <Users className="h-4 w-4" />
                    <span>Supporters</span>
                  </div>
                  <p className="font-bold text-xl">{initiative.supportCount}</p>
                </div>
                <div>
                  <div className="flex items-center justify-center gap-1 text-gray-600">
                    <MessageSquare className="h-4 w-4" />
                    <span>Comments</span>
                  </div>
                  <p className="font-bold text-xl">{initiative.commentCount}</p>
                </div>
                <div>
                  <div className="flex items-center justify-center gap-1 text-gray-600">
                    <Eye className="h-4 w-4" />
                    <span>Views</span>
                  </div>
                  <p className="font-bold text-xl">{initiative.viewCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center">
                <p className="text-center mb-4">
                  {hasSupported ? "You are supporting this initiative" : "Support this initiative to help it succeed"}
                </p>

                {supportMessage && (
                  <Alert
                    className={`mb-4 ${hasSupported ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}`}
                  >
                    {hasSupported ? (
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <AlertDescription className={hasSupported ? "text-green-800" : "text-red-800"}>
                      {supportMessage}
                    </AlertDescription>
                  </Alert>
                )}

                <Button
                  className={`w-full ${hasSupported ? "bg-red-600 hover:bg-red-700" : "bg-green-600 hover:bg-green-700"}`}
                  onClick={hasSupported ? handleUnsupport : handleSupport}
                  disabled={isSupporting}
                >
                  {isSupporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <ThumbsUp className="mr-2 h-4 w-4" />
                      {hasSupported ? "Remove Support" : "Support Initiative"}
                    </>
                  )}
                </Button>

                <p className="text-sm text-gray-500 mt-4">Goal: {initiative.goal} supporters</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Initiative Content Tabs */}
      <div className="border border-gray-200 rounded-lg p-4 mb-8 shadow-sm" dir="rtl">
        <Tabs defaultValue="about" className="min-h-[150px]">
          <TabsList className="mb-6 w-full flex justify-end">
            <TabsTrigger value="comments">التعليقات</TabsTrigger>
            <TabsTrigger value="resources">الموارد</TabsTrigger>
            <TabsTrigger value="volunteers">المتطوعون</TabsTrigger>
            <TabsTrigger value="milestones">المراحل</TabsTrigger>
            <TabsTrigger value="updates">التحديثات</TabsTrigger>
            <TabsTrigger value="about">نبذة</TabsTrigger>
          </TabsList>

          <TabsContent value="about" className="min-h-[150px]">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="md:col-span-2 text-right">
                <h2 className="text-2xl font-bold mb-6 mt-2">نبذة عن المبادرة</h2>
                <div className="prose max-w-none prose-headings:text-right prose-p:text-right prose-ul:text-right prose-ol:text-right rtl" dir="rtl" dangerouslySetInnerHTML={{ __html: initiative.fullDescription }} />

                {/* Section d'impact social */}
                <SocialImpactSection initiative={initiative} />

                {initiative.tags.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2">الوسوم</h3>
                    <div className="flex flex-wrap gap-2 justify-end">
                      {initiative.tags.map((tag) => (
                        <Badge key={tag} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="text-right">
                <h3 className="text-lg font-semibold mb-4">صاحب المبادرة</h3>
                <div className="flex items-center gap-4 mb-4 justify-end">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={initiative.author.avatar} alt={initiative.author.name} />
                    <AvatarFallback>{initiative.author.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{initiative.author.name}</p>
                    <p className="text-sm text-gray-500">@{initiative.author.username}</p>
                  </div>
                </div>

                {initiative.author.bio && <p className="text-sm text-gray-700 mb-4">{initiative.author.bio}</p>}

                <Link href={`/users/${initiative.author._id}`}>
                  <Button variant="outline" className="w-full">
                    عرض الملف الشخصي
                  </Button>
                </Link>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="updates" className="min-h-[150px]">
            <UpdatesList initiativeId={id} initialUpdates={updates} isAuthor={user?.id === initiative.author._id} />
          </TabsContent>

          <TabsContent value="milestones" className="min-h-[150px]">
            <MilestonesList
              initiativeId={id}
              initialMilestones={milestones}
              isAuthor={user?.id === initiative.author._id}
            />
          </TabsContent>

          <TabsContent value="volunteers" id="volunteers" className="min-h-[150px]">
            <VolunteersSection
              initiativeId={id}
              isAuthor={user?.id === initiative.author._id}
              status={initiative.status}
              requiredVolunteers={initiative.requiredVolunteers}
            />
          </TabsContent>

          <TabsContent value="resources" className="min-h-[150px]">
            <ResourcesSection
              initiativeId={id}
              isAuthor={user?.id === initiative.author._id}
              status={initiative.status}
            />
          </TabsContent>

          <TabsContent value="comments" className="min-h-[150px]">
            <CommentSection initiativeId={id} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Similar Initiatives */}
      <div className="mt-12" dir="rtl">
        <h2 className="text-2xl font-bold mb-6">مبادرات مشابهة</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {isSimilarLoading ? (
            <p className="col-span-3 text-center text-gray-500 py-8">
              <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
              جاري تحميل المبادرات المشابهة...
            </p>
          ) : similarInitiatives.length > 0 ? (
            similarInitiatives.map((similarInitiative) => (
              <Card key={similarInitiative._id} className="overflow-hidden flex flex-col h-full">
                <div className="relative h-48 w-full">
                  {similarInitiative.mainImage?.startsWith('http') ? (
                    <img
                      src={similarInitiative.mainImage}
                      alt={similarInitiative.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Image
                      src={similarInitiative.mainImage || "/placeholder.svg"}
                      alt={similarInitiative.title}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 33vw"
                      style={{ objectFit: "cover" }}
                      unoptimized
                    />
                  )}
                </div>
                <CardContent className="flex-grow flex flex-col p-4">
                  <div className="mb-2">
                    <Badge style={{ backgroundColor: similarInitiative.category.color }}>
                      {similarInitiative.category.name}
                    </Badge>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 line-clamp-2">{similarInitiative.title}</h3>
                  <p className="text-sm text-gray-600 mb-4 line-clamp-3">{similarInitiative.shortDescription}</p>
                  <div className="mt-auto flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={similarInitiative.author.avatar} alt={similarInitiative.author.name} />
                        <AvatarFallback>{similarInitiative.author.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <span className="text-xs">{similarInitiative.author.name}</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <ThumbsUp className="h-3 w-3" />
                      <span>{similarInitiative.supportCount}</span>
                    </div>
                  </div>
                  <Link href={`/initiatives/${similarInitiative._id}`} className="mt-4 w-full">
                    <Button variant="outline" className="w-full">عرض المبادرة</Button>
                  </Link>
                </CardContent>
              </Card>
            ))
          ) : (
            <p className="col-span-3 text-center text-gray-500 py-8">
              لم يتم العثور على مبادرات مشابهة.
            </p>
          )}
        </div>
      </div>
    </div>
  )
}


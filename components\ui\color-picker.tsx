"use client"

import { useState } from "react"
import { HexColorPicker } from "react-colorful"
import { Button } from "./button"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"
import { Input } from "./input"

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
}

export function ColorPicker({ value, onChange }: ColorPickerProps) {
  const [color, setColor] = useState(value)

  return (
    <div className="flex items-center gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-[60px] h-10 p-1"
          >
            <div 
              className="w-full h-full rounded"
              style={{ backgroundColor: color }}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2">
          <HexColorPicker 
            color={color} 
            onChange={(newColor) => {
              setColor(newColor)
              onChange(newColor)
            }} 
          />
        </PopoverContent>
      </Popover>
      <Input
        value={color}
        onChange={(e) => {
          const newColor = e.target.value
          if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(newColor)) {
            setColor(newColor)
            onChange(newColor)
          }
        }}
        className="w-24"
      />
    </div>
  )
}

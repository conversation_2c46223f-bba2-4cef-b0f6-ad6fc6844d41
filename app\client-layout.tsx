"use client"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/components/auth-provider"
import { DebugProvider } from "@/components/debug-provider"
import { NotificationProvider } from "@/components/notification-context"
import MainLayout from "@/components/layout/MainLayout"
import { ReactNode, useState } from "react"
import FetchInterceptor from "@/lib/fetch-interceptor"

export function ClientLayout({ children }: { children: ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60 * 5 // 5 minutes
      }
    }
  }))

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <NotificationProvider>
            <DebugProvider>
              <FetchInterceptor />
              <MainLayout>{children}</MainLayout>
            </DebugProvider>
          </NotificationProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

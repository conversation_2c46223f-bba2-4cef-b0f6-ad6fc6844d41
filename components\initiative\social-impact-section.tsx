"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs"
import { Badge } from "../ui/badge"
import { Separator } from "../ui/separator"

interface SocialImpactSectionProps {
  initiative: any
}

export default function SocialImpactSection({ initiative }: SocialImpactSectionProps) {
  const [activeTab, setActiveTab] = useState("problem")

  // Vérifier si les données d'impact social sont disponibles
  const hasImpactData = !!(
    initiative.problem ||
    initiative.solution ||
    initiative.beneficiaries ||
    initiative.quantitativeObjectives ||
    initiative.qualitativeObjectives ||
    (initiative.selectedImpacts && initiative.selectedImpacts.length > 0)
  )

  if (!hasImpactData) {
    return null
  }

  return (
    <Card className="mb-8">
      <CardHeader className="pb-3 text-right">
        <CardTitle className="text-xl font-bold text-green-700">التأثير الاجتماعي</CardTitle>
        <CardDescription>معلومات مفصلة حول تأثير المبادرة وأهدافها</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="problem" value={activeTab} onValueChange={setActiveTab} className="w-full" dir="rtl">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="problem">المشكلة والحل</TabsTrigger>
            <TabsTrigger value="objectives">الأهداف</TabsTrigger>
            <TabsTrigger value="impact">التأثير المتوقع</TabsTrigger>
          </TabsList>

          <TabsContent value="problem" className="space-y-4 text-right">
            {initiative.problem && (
              <div>
                <h3 className="font-semibold text-lg mb-2">المشكلة / الفرصة</h3>
                <p className="text-gray-700 whitespace-pre-line">{initiative.problem}</p>
              </div>
            )}

            {initiative.solution && (
              <div>
                <h3 className="font-semibold text-lg mb-2 mt-6">الحل المقترح</h3>
                <p className="text-gray-700 whitespace-pre-line">{initiative.solution}</p>
              </div>
            )}

            {initiative.beneficiaries && (
              <div>
                <h3 className="font-semibold text-lg mb-2 mt-6">المستفيدون</h3>
                <p className="text-gray-700 whitespace-pre-line">{initiative.beneficiaries}</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="objectives" className="space-y-4 text-right">
            {initiative.quantitativeObjectives && (
              <div>
                <h3 className="font-semibold text-lg mb-2">الأهداف الكمية</h3>
                <p className="text-gray-700 whitespace-pre-line">{initiative.quantitativeObjectives}</p>
              </div>
            )}

            {initiative.qualitativeObjectives && (
              <div>
                <h3 className="font-semibold text-lg mb-2 mt-6">الأهداف النوعية</h3>
                <p className="text-gray-700 whitespace-pre-line">{initiative.qualitativeObjectives}</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="impact" className="space-y-4 text-right">
            {initiative.selectedImpacts && initiative.selectedImpacts.length > 0 ? (
              <div>
                <h3 className="font-semibold text-lg mb-4">التأثير الاجتماعي المتوقع</h3>
                <div className="flex flex-wrap gap-2 justify-end">
                  {initiative.selectedImpacts.map((impact: any) => (
                    <Badge key={impact._id} variant="outline" className="bg-green-50 text-green-800 border-green-200 py-1.5 px-3">
                      {impact.arabicName}
                    </Badge>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-gray-500">لم يتم تحديد تأثيرات اجتماعية لهذه المبادرة.</p>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

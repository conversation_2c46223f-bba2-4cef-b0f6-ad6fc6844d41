"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertCircle, Loader2, Search, UserCheck, UserX, Shield, ChevronLeft, ChevronRight, Trash2, User } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
// Removed AdminLayout import as it's handled by app/admin/layout.tsx
import { api } from "@/lib/api"

export default function AdminUsersPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [users, setUsers] = useState([])
  const [totalUsers, setTotalUsers] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Filters
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [userTypeFilter, setUserTypeFilter] = useState("all")
  const [sortBy, setSortBy] = useState("newest")

  // Action dialogs
  const [selectedUser, setSelectedUser] = useState(null)
  const [showRoleDialog, setShowRoleDialog] = useState(false)
  const [showUserTypeDialog, setShowUserTypeDialog] = useState(false)
  const [showBlockDialog, setShowBlockDialog] = useState(false)
  const [showUnblockDialog, setShowUnblockDialog] = useState(false)
  const [showVerifyDialog, setShowVerifyDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [newRole, setNewRole] = useState("user")
  const [newUserType, setNewUserType] = useState("volunteer")
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    fetchUsers()
  }, [currentPage, roleFilter, statusFilter, userTypeFilter, sortBy])

  const fetchUsers = async () => {
    try {
      setIsLoading(true)

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        sort: sortBy,
      })

      if (roleFilter !== "all") {
        params.append("role", roleFilter)
      }

      if (statusFilter !== "all") {
        params.append("status", statusFilter)
      }

      if (userTypeFilter !== "all") {
        params.append("userType", userTypeFilter)
      }

      if (searchQuery) {
        params.append("q", searchQuery)
      }

      // Fetch users
      const response = await api.get(`/api/admin/users?${params.toString()}`)
      setUsers(response.users || [])
      if (response.pagination) {
        setTotalUsers(response.pagination.total || 0)
        setTotalPages(response.pagination.totalPages || 1)
      }
    } catch (err) {
      setError(err.message || "Failed to load users")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchUsers()
  }

  const handleChangeRole = async () => {
    if (!selectedUser || !newRole) return

    setIsProcessing(true)

    try {
      await api.patch(`/api/admin/users/${selectedUser._id}/role`, { role: newRole })

      // Update user in the list
      setUsers(users.map((user) => (user._id === selectedUser._id ? { ...user, role: newRole } : user)))

      setShowRoleDialog(false)
    } catch (err) {
      setError(err.message || "Failed to change user role")
    } finally {
      setIsProcessing(false)
      setSelectedUser(null)
    }
  }

  const handleChangeUserType = async () => {
    if (!selectedUser || !newUserType) return

    setIsProcessing(true)

    try {
      await api.patch(`/api/admin/users/${selectedUser._id}/userType`, { userType: newUserType })

      // Update user in the list
      setUsers(users.map((user) => (user._id === selectedUser._id ? { ...user, userType: newUserType } : user)))

      setShowUserTypeDialog(false)
    } catch (err) {
      setError(err.message || "Failed to change user type")
    } finally {
      setIsProcessing(false)
      setSelectedUser(null)
    }
  }

  const handleBlockUser = async () => {
    if (!selectedUser) return

    setIsProcessing(true)

    try {
      await api.patch(`/api/admin/users/${selectedUser._id}/block`, {})

      // Update user in the list
      setUsers(users.map((user) => (user._id === selectedUser._id ? { ...user, isBlocked: true } : user)))

      setShowBlockDialog(false)
    } catch (err) {
      setError(err.message || "Failed to block user")
    } finally {
      setIsProcessing(false)
      setSelectedUser(null)
    }
  }

  const handleUnblockUser = async () => {
    if (!selectedUser) return

    setIsProcessing(true)

    try {
      await api.patch(`/api/admin/users/${selectedUser._id}/unblock`, {})

      // Update user in the list
      setUsers(users.map((user) => (user._id === selectedUser._id ? { ...user, isBlocked: false } : user)))

      setShowUnblockDialog(false)
    } catch (err) {
      setError(err.message || "Failed to unblock user")
    } finally {
      setIsProcessing(false)
      setSelectedUser(null)
    }
  }

  const handleVerifyUser = async () => {
    if (!selectedUser) return

    setIsProcessing(true)

    try {
      await api.patch(`/api/admin/users/${selectedUser._id}/verify`, {})

      // Update user in the list
      setUsers(users.map((user) => (user._id === selectedUser._id ? { ...user, isVerified: true } : user)))

      setShowVerifyDialog(false)
    } catch (err) {
      setError(err.message || "Failed to verify user")
    } finally {
      setIsProcessing(false)
      setSelectedUser(null)
    }
  }

  const handleDeleteUser = async () => {
    if (!selectedUser) return

    setIsProcessing(true)

    try {
      await api.delete(`/api/users/${selectedUser._id}`)

      // Remove user from the list
      setUsers(users.filter((user) => user._id !== selectedUser._id))

      // Update total count
      setTotalUsers(totalUsers - 1)

      setShowDeleteDialog(false)
    } catch (err) {
      setError(err.message || "Failed to delete user")
    } finally {
      setIsProcessing(false)
      setSelectedUser(null)
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">إدارة المستخدمين</h1>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>تصفية المستخدمين</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <form onSubmit={handleSearch} className="flex-1 flex gap-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="البحث عن مستخدم..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button type="submit" className="bg-green-600 hover:bg-green-700">
                بحث
              </Button>
            </form>

            <div className="flex gap-2">
              <Select
                value={roleFilter}
                onValueChange={(value) => {
                  setRoleFilter(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الدور" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأدوار</SelectItem>
                  <SelectItem value="user">مستخدم</SelectItem>
                  <SelectItem value="moderator">مشرف</SelectItem>
                  <SelectItem value="admin">مدير</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="blocked">محظور</SelectItem>
                  <SelectItem value="unverified">غير مفعل</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={userTypeFilter}
                onValueChange={(value) => {
                  setUserTypeFilter(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="نوع المستخدم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="volunteer">متطوع</SelectItem>
                  <SelectItem value="proposer">مقترح مبادرات</SelectItem>
                  <SelectItem value="company">شركة/مؤسسة</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={sortBy}
                onValueChange={(value) => {
                  setSortBy(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الترتيب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                  <SelectItem value="initiatives">المبادرات</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-0">
          {isLoading && users.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري تحميل المستخدمين...</span>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">لا يوجد مستخدمين مطابقين للمعايير المحددة</div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المستخدم</TableHead>
                    <TableHead>البريد الإلكتروني</TableHead>
                    <TableHead>الدور</TableHead>
                    <TableHead>نوع المستخدم</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ التسجيل</TableHead>
                    <TableHead>المبادرات</TableHead>
                    <TableHead className="text-left">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user._id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatar} alt={user.name} />
                            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <Link href={`/users/${user._id}`} className="font-medium hover:underline">
                              {user.name}
                            </Link>
                            <p className="text-sm text-muted-foreground">@{user.username}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge
                          className={
                            user.role === "admin"
                              ? "bg-purple-600"
                              : user.role === "moderator"
                                ? "bg-blue-600"
                                : "bg-gray-600"
                          }
                        >
                          {user.role === "admin" ? "مدير" : user.role === "moderator" ? "مشرف" : "مستخدم"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            user.userType === "volunteer"
                              ? "bg-blue-100 text-blue-800"
                              : user.userType === "proposer"
                                ? "bg-green-100 text-green-800"
                                : user.userType === "company"
                                  ? "bg-amber-100 text-amber-800"
                                  : "bg-gray-100 text-gray-800"
                          }
                        >
                          {user.userType === "volunteer" ? "متطوع" :
                           user.userType === "proposer" ? "مقترح مبادرات" :
                           user.userType === "company" ? "شركة/مؤسسة" : "غير محدد"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.isBlocked ? (
                          <Badge variant="outline" className="text-red-600 border-red-200 bg-red-50">
                            محظور
                          </Badge>
                        ) : user.isVerified ? (
                          <Badge className="bg-green-600">نشط</Badge>
                        ) : (
                          <Badge variant="outline" className="text-yellow-600 border-yellow-200 bg-yellow-50">
                            غير مفعل
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{formatDate(user.joinDate)}</TableCell>
                      <TableCell>{user.initiativeCount || 0}</TableCell>
                      <TableCell className="text-left">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user)
                              setNewRole(user.role)
                              setShowRoleDialog(true)
                            }}
                          >
                            <Shield className="ml-1 h-4 w-4" />
                            تغيير الدور
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user)
                              setNewUserType(user.userType || "volunteer")
                              setShowUserTypeDialog(true)
                            }}
                          >
                            <User className="ml-1 h-4 w-4" />
                            تغيير النوع
                          </Button>

                          {user.isBlocked ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-green-600 border-green-600 hover:bg-green-50"
                              onClick={() => {
                                setSelectedUser(user)
                                setShowUnblockDialog(true)
                              }}
                            >
                              <UserCheck className="ml-1 h-4 w-4" />
                              إلغاء الحظر
                            </Button>
                          ) : !user.isVerified ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-blue-600 border-blue-600 hover:bg-blue-50"
                              onClick={() => {
                                setSelectedUser(user)
                                setShowVerifyDialog(true)
                              }}
                            >
                              <UserCheck className="ml-1 h-4 w-4" />
                              تفعيل الحساب
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 border-red-600 hover:bg-red-50"
                              onClick={() => {
                                setSelectedUser(user)
                                setShowBlockDialog(true)
                              }}
                            >
                              <UserX className="ml-1 h-4 w-4" />
                              حظر
                            </Button>
                          )}

                          {/* Delete button - only show for inactive users with no initiatives */}
                          {(!user.isVerified || user.isBlocked) && user.initiativeCount === 0 && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 border-red-600 hover:bg-red-50"
                              onClick={() => {
                                setSelectedUser(user)
                                setShowDeleteDialog(true)
                              }}
                            >
                              <Trash2 className="ml-1 h-4 w-4" />
                              حذف الحساب
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center py-4">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronRight className="h-4 w-4" />
                      السابق
                    </Button>

                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    ))}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      التالي
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Change Role Dialog */}
      <Dialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تغيير دور المستخدم</DialogTitle>
            <DialogDescription>اختر الدور الجديد للمستخدم {selectedUser?.name}</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Select value={newRole} onValueChange={setNewRole}>
              <SelectTrigger>
                <SelectValue placeholder="اختر الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">مستخدم</SelectItem>
                <SelectItem value="moderator">مشرف</SelectItem>
                <SelectItem value="admin">مدير</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRoleDialog(false)}>
              إلغاء
            </Button>
            <Button className="bg-green-600 hover:bg-green-700" onClick={handleChangeRole} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "تغيير الدور"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change User Type Dialog */}
      <Dialog open={showUserTypeDialog} onOpenChange={setShowUserTypeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تغيير نوع المستخدم</DialogTitle>
            <DialogDescription>اختر النوع الجديد للمستخدم {selectedUser?.name}</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Select value={newUserType} onValueChange={setNewUserType}>
              <SelectTrigger>
                <SelectValue placeholder="نوع المستخدم" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="volunteer">متطوع</SelectItem>
                <SelectItem value="proposer">مقترح مبادرات</SelectItem>
                <SelectItem value="company">شركة/مؤسسة</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowUserTypeDialog(false)}>
              إلغاء
            </Button>
            <Button className="bg-green-600 hover:bg-green-700" onClick={handleChangeUserType} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "تغيير النوع"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Block User Dialog */}
      <Dialog open={showBlockDialog} onOpenChange={setShowBlockDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حظر المستخدم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حظر المستخدم {selectedUser?.name}؟ لن يتمكن من تسجيل الدخول أو استخدام المنصة.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBlockDialog(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleBlockUser} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "حظر المستخدم"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Unblock User Dialog */}
      <Dialog open={showUnblockDialog} onOpenChange={setShowUnblockDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إلغاء حظر المستخدم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في إلغاء حظر المستخدم {selectedUser?.name}؟ سيتمكن من تسجيل الدخول واستخدام المنصة
              مرة أخرى.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowUnblockDialog(false)}>
              إلغاء
            </Button>
            <Button className="bg-green-600 hover:bg-green-700" onClick={handleUnblockUser} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "إلغاء الحظر"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Verify User Dialog */}
      <Dialog open={showVerifyDialog} onOpenChange={setShowVerifyDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تفعيل حساب المستخدم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في تفعيل حساب المستخدم {selectedUser?.name}؟ سيتمكن من استخدام جميع ميزات المنصة.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowVerifyDialog(false)}>
              إلغاء
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleVerifyUser} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "تفعيل الحساب"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف حساب المستخدم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف حساب المستخدم {selectedUser?.name} نهائياً؟
              <br />
              <span className="text-red-600 font-bold">لا يمكن التراجع عن هذا الإجراء!</span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleDeleteUser} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "حذف الحساب"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


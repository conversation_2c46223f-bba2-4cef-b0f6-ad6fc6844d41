"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../ui/button"
import { Input } from "../ui/input"
import { Textarea } from "../ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card"
import { Loader2, Package, DollarSign, Users, Wrench, HelpCircle, Calendar, User, Phone, Mail, Eye, FileText } from "lucide-react"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"

interface ResourceOfferDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initiativeId: string
  onSuccess: (newResource?: any) => void
  resourceNeedId?: string // ID du besoin auquel cette ressource répond
}

export default function ResourceOfferDialog({
  open,
  onOpenChange,
  initiativeId,
  onSuccess,
  resourceNeedId,
}: ResourceOfferDialogProps) {
  const [formData, setFormData] = useState({
    type: "material",
    name: "",
    description: "",
    quantity: 1,
    unit: "",
    notes: "",
    expectedDeliveryDate: "",
    contactPerson: "",
    contactEmail: "",
    contactPhone: "",
    isPublic: true,
    relatedResourceNeed: "",
  })

  const [activeTab, setActiveTab] = useState("info")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [resourceNeed, setResourceNeed] = useState<any>(null)
  const [isLoadingResourceNeed, setIsLoadingResourceNeed] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: name === "quantity" ? Number(value) :
              name === "isPublic" ? value === "true" :
              value,
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  // Charger les détails du besoin si un ID est fourni
  useEffect(() => {
    if (open && resourceNeedId) {
      setIsLoadingResourceNeed(true)
      setFormData(prev => ({ ...prev, relatedResourceNeed: resourceNeedId }))

      console.log("Fetching resource need with ID:", resourceNeedId)
      // Utiliser la nouvelle route pour récupérer un besoin par ID
      api.get(`/api/resource-needs/${resourceNeedId}`, false)
        .then(response => {
          console.log("API response:", response)
          if (response.success && response.resourceNeed) {
            console.log("Resource need loaded:", response.resourceNeed) // Pour le débogage
            setResourceNeed(response.resourceNeed)
            // Pré-remplir le formulaire avec les détails du besoin
            setFormData(prev => ({
              ...prev,
              type: response.resourceNeed.type,
              name: response.resourceNeed.name,
              description: response.resourceNeed.description,
              quantity: response.resourceNeed.quantity, // Pré-remplir avec la quantité totale
              unit: response.resourceNeed.unit,
              relatedResourceNeed: resourceNeedId
            }))
          } else {
            console.error("Failed to load resource need:", response)
            // Afficher un message d'erreur à l'utilisateur
            toast({
              title: "خطأ",
              description: "تعذر تحميل بيانات الاحتياج. الرجاء المحاولة مرة أخرى.",
              variant: "destructive",
            })
          }
        })
        .catch(err => {
          console.error("Error fetching resource need:", err)
          // Afficher un message d'erreur à l'utilisateur
          toast({
            title: "خطأ",
            description: "تعذر تحميل بيانات الاحتياج. الرجاء المحاولة مرة أخرى.",
            variant: "destructive",
          })
        })
        .finally(() => {
          setIsLoadingResourceNeed(false)
        })
    } else {
      setResourceNeed(null)
    }
  }, [open, resourceNeedId])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await api.post(`/api/resources/initiative/${initiativeId}`, formData, true)

      if (response.success) {
        toast({
          title: "تم تقديم المورد بنجاح",
          description: "سيتم إخطار صاحب المبادرة بعرضك",
          variant: "default",
        })

        // Reset form
        setFormData({
          type: "material",
          name: "",
          description: "",
          quantity: 1,
          unit: "",
          notes: "",
          expectedDeliveryDate: "",
          contactPerson: "",
          contactEmail: "",
          contactPhone: "",
          isPublic: true,
          relatedResourceNeed: "",
        })

        // Call success callback with the new resource data
        onSuccess(response.resource)
      } else {
        throw new Error(response.message || "Failed to offer resource")
      }
    } catch (err: any) {
      console.error("Error offering resource:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء تقديم المورد",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Helper functions for displaying type and priority
  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "مادي"
      case "financial":
        return "مالي"
      case "human":
        return "بشري"
      case "service":
        return "خدمة"
      default:
        return "آخر"
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case "low":
        return "منخفضة"
      case "medium":
        return "متوسطة"
      case "high":
        return "عالية"
      case "critical":
        return "حرجة"
      default:
        return priority
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>
            {resourceNeed ? `تقديم دعم لاحتياج: ${resourceNeed?.name || ''}` : "تقديم مورد للمبادرة"}
          </DialogTitle>
          <DialogDescription>
            {resourceNeed
              ? `أنت على وشك تقديم دعم لتلبية احتياج محدد في هذه المبادرة.`
              : `قم بتعبئة النموذج التالي لتقديم مورد للمبادرة.`
            }
          </DialogDescription>
        </DialogHeader>

        {isLoadingResourceNeed ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-green-600" />
            <span className="mr-2">جاري تحميل بيانات الاحتياج...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            {/* Affichage des informations du besoin en dehors des onglets */}
            {resourceNeed ? (
              <div className="mb-6 border border-blue-200 rounded-md p-4 bg-blue-50 text-right" dir="rtl">
                <div className="mb-4">
                  <h3 className="text-lg font-bold mb-1 flex items-center gap-2">
                    {getTypeIcon(resourceNeed.type)}
                    {resourceNeed.name}
                  </h3>
                  <p className="text-gray-700">{resourceNeed.description}</p>
                </div>

                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="bg-white px-3 py-2 rounded-md border border-blue-200">
                    <span className="font-medium ml-1">النوع:</span>
                    <span>{getTypeText(resourceNeed.type)}</span>
                  </div>
                  <div className="bg-white px-3 py-2 rounded-md border border-blue-200">
                    <span className="font-medium ml-1">الكمية المطلوبة:</span>
                    <span>{resourceNeed.quantity} {resourceNeed.unit}</span>
                  </div>
                  <div className="bg-white px-3 py-2 rounded-md border border-blue-200">
                    <span className="font-medium ml-1">الأولوية:</span>
                    <span>{getPriorityText(resourceNeed.priority)}</span>
                  </div>
                </div>

                <div className="mt-4 p-2 bg-purple-50 rounded-md border border-purple-200">
                  <p className="text-sm text-purple-800 font-medium">
                    يمكنك تقديم كامل الكمية المطلوبة أو جزء منها حسب قدرتك
                  </p>
                </div>
              </div>
            ) : resourceNeedId ? (
              <div className="mb-6 border border-red-200 rounded-md p-4 bg-red-50 text-right" dir="rtl">
                <p className="text-red-600 font-bold">تعذر تحميل بيانات الاحتياج. الرجاء المحاولة مرة أخرى.</p>
                <p className="text-gray-700">معرف الاحتياج: {resourceNeedId}</p>
              </div>
            ) : null}

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" dir="rtl">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="additional" className="flex items-center gap-2">
                  <FileText className="h-4 w-4 ml-2" />
                  معلومات إضافية
                </TabsTrigger>
                <TabsTrigger value="delivery" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 ml-2" />
                  معلومات التسليم
                </TabsTrigger>
                <TabsTrigger value="info" className="flex items-center gap-2">
                  <Package className="h-4 w-4 ml-2" />
                  معلومات المورد
                </TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="space-y-4 mt-0 min-h-[200px]" dir="rtl">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="quantity" className="font-medium">
                      الكمية المقدمة {resourceNeed && <span className="text-sm text-purple-600">(من أصل {resourceNeed.quantity} {resourceNeed.unit})</span>}
                    </label>
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      min="1"
                      max={resourceNeed ? resourceNeed.quantity : undefined}
                      placeholder="الكمية"
                      value={formData.quantity}
                      onChange={handleChange}
                      required
                      className="text-right"
                    />
                    {resourceNeed && (
                      <p className="text-xs text-purple-600">
                        يمكنك تقديم كامل الكمية المطلوبة أو جزء منها حسب قدرتك
                      </p>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="delivery" className="space-y-4 mt-0 min-h-[200px]" dir="rtl">
                <div className="space-y-2">
                  <label htmlFor="expectedDeliveryDate" className="font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    تاريخ التسليم المتوقع
                  </label>
                  <Input
                    id="expectedDeliveryDate"
                    name="expectedDeliveryDate"
                    type="date"
                    value={formData.expectedDeliveryDate}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="contactPerson" className="font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    الشخص المسؤول عن التسليم
                  </label>
                  <Input
                    id="contactPerson"
                    name="contactPerson"
                    placeholder="اسم الشخص المسؤول"
                    value={formData.contactPerson}
                    onChange={handleChange}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="contactPhone" className="font-medium flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      رقم الهاتف للتواصل
                    </label>
                    <Input
                      id="contactPhone"
                      name="contactPhone"
                      placeholder="رقم الهاتف"
                      value={formData.contactPhone}
                      onChange={handleChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="contactEmail" className="font-medium flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      البريد الإلكتروني للتواصل
                    </label>
                    <Input
                      id="contactEmail"
                      name="contactEmail"
                      type="email"
                      placeholder="البريد الإلكتروني"
                      value={formData.contactEmail}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="additional" className="space-y-4 mt-0 min-h-[200px]" dir="rtl">
                <div className="space-y-2">
                  <label className="font-medium flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    إظهار مساهمتك للعامة
                  </label>
                  <Select
                    value={formData.isPublic ? "true" : "false"}
                    onValueChange={(value) => handleSelectChange("isPublic", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">نعم، أظهر مساهمتي للعامة</SelectItem>
                      <SelectItem value="false">لا، أبقِ مساهمتي خاصة</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    إذا اخترت "نعم"، سيتم عرض اسمك كمساهم في هذه المبادرة.
                  </p>
                </div>

                <div className="space-y-2">
                  <label htmlFor="notes" className="font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    ملاحظات إضافية
                  </label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="أي ملاحظات إضافية حول المورد أو طريقة التسليم"
                    value={formData.notes}
                    onChange={handleChange}
                    className="min-h-[80px]"
                  />
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter className="mt-6" dir="rtl">
              <div className="flex gap-2 w-full justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isSubmitting}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  className={resourceNeed ? "bg-purple-600 hover:bg-purple-700" : "bg-green-600 hover:bg-green-700"}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري التقديم...
                    </>
                  ) : resourceNeed ? (
                    "تقديم الدعم للاحتياج"
                  ) : (
                    "تقديم المورد"
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}

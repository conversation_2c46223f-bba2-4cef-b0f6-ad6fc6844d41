@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-cairo), Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Styles RTL pour l'arabe - UNIQUEMENT pour la page des paramètres */
.settings-page[dir="rtl"] {
  text-align: right;
}

.settings-page[dir="rtl"] input,
.settings-page[dir="rtl"] textarea {
  text-align: right;
  direction: rtl;
}

.settings-page[dir="rtl"] .flex:not(.no-rtl) {
  flex-direction: row-reverse;
}

.settings-page[dir="rtl"] .justify-between:not(.no-rtl) {
  flex-direction: row-reverse;
}

.settings-page[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.settings-page[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.settings-page[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

.settings-page[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* Styles pour les composants UI dans la page des paramètres */
.settings-page[dir="rtl"] .accordion-trigger {
  flex-direction: row-reverse;
}

.settings-page[dir="rtl"] .accordion-content {
  text-align: right;
}

.settings-page[dir="rtl"] .select-trigger {
  flex-direction: row-reverse;
}

.settings-page[dir="rtl"] .select-content {
  text-align: right;
}

.settings-page[dir="rtl"] .tabs-list {
  flex-direction: row-reverse;
}

.settings-page[dir="rtl"] .card-header,
.settings-page[dir="rtl"] .card-content,
.settings-page[dir="rtl"] .card-footer {
  text-align: right;
}

/* Styles pour les boutons avec icônes dans la page des paramètres */
.settings-page[dir="rtl"] .button-icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Améliorations visuelles pour les formulaires */
.card {
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #eaeaea;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

.card-header {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(to bottom, #fafafa, #f5f5f5);
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: 1.25rem 1.5rem;
}

.card-content {
  padding: 1.5rem;
  background-color: white;
}

.card-title {
  color: #0a8754;
  font-weight: 700;
  text-align: right !important;
  width: 100%;
}

.card-description {
  margin-top: 0.5rem;
  color: #666;
}

.space-y-2 > label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
  transition: color 0.2s ease;
}

.space-y-2:hover > label {
  color: #0a8754;
}

.space-y-6 > div:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Style pour les groupes de formulaires */
.space-y-2 {
  margin-bottom: 1.25rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.space-y-2:hover {
  background-color: rgba(10, 135, 84, 0.03);
}

/* Style pour les sections */
.space-y-4 > h3 {
  color: #0a8754;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

/* Style pour les interrupteurs */
.switch-container {
  background-color: #f9f9f9;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #eaeaea;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.switch-container:hover {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Style pour les boutons */
button[class*="bg-[#0a8754]"] {
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button[class*="bg-[#0a8754]"]:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Style pour les onglets */
.tabs-list {
  background-color: #f5f5f5;
  padding: 0.25rem;
  border-radius: 0.5rem;
}

.tabs-trigger {
  font-weight: 600;
}

.tabs-trigger[data-state="active"] {
  background-color: #0a8754;
  color: white;
}

const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { checkMongoDBConnection } = require('./check-mongodb');

// Connection URI
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz';

// Categories data
const categories = [
  {
    name: 'environment',
    arabicName: 'بيئة',
    description: 'Environmental initiatives and projects',
    color: '#0a8754',
    icon: 'leaf',
    initiativeCount: 0
  },
  {
    name: 'education',
    arabicName: 'تعليم',
    description: 'Educational initiatives and projects',
    color: '#2563eb',
    icon: 'book',
    initiativeCount: 0
  },
  {
    name: 'health',
    arabicName: 'صحة',
    description: 'Health initiatives and projects',
    color: '#dc2626',
    icon: 'heart',
    initiativeCount: 0
  },
  {
    name: 'community',
    arabicName: 'مجتمع',
    description: 'Community development initiatives',
    color: '#9333ea',
    icon: 'users',
    initiativeCount: 0
  },
  {
    name: 'technology',
    arabicName: 'تكنولوجيا',
    description: 'Technology and innovation initiatives',
    color: '#0891b2',
    icon: 'cpu',
    initiativeCount: 0
  }
];

// Admin user data
const adminUser = {
  username: 'admin',
  email: '<EMAIL>',
  password: bcrypt.hashSync('Admin123!', 10),
  name: 'مدير النظام',
  avatar: '/placeholder.svg?height=100&width=100',
  bio: 'مدير منصة المبادرات المواطنة الجزائرية',
  location: 'الجزائر العاصمة',
  joinDate: new Date(),
  role: 'admin',
  isVerified: true,
  isBlocked: false,
  badges: [],
  initiatives: [],
  supportedInitiatives: [],
  votedInitiatives: [],
  socialLinks: {
    facebook: 'https://facebook.com/initiatives-dz',
    twitter: 'https://twitter.com/initiatives-dz',
    linkedin: 'https://linkedin.com/in/initiatives-dz',
    instagram: 'https://instagram.com/initiatives-dz'
  },
  settings: {
    emailNotifications: true,
    initiativeUpdates: true,
    commentReplies: true,
    supportNotifications: true,
    marketingEmails: false
  },
  refreshToken: null,
  resetPasswordToken: null,
  resetPasswordExpires: null,
  verificationToken: null,
  lastLogin: new Date()
};

// Sample initiatives data
const sampleInitiatives = [
  {
    title: 'حملة تشجير الأحياء السكنية',
    shortDescription: 'مبادرة لزراعة الأشجار في الأحياء السكنية لتحسين البيئة المحلية وزيادة المساحات الخضراء.',
    fullDescription: `
      تهدف هذه المبادرة إلى تحسين البيئة المحلية في الأحياء السكنية من خلال زراعة الأشجار والنباتات المحلية.

      الأهداف:
      - زراعة 1000 شجرة في 10 أحياء سكنية
      - إشراك السكان المحليين في عملية التشجير
      - تنظيم ورش عمل توعوية حول أهمية الحفاظ على البيئة
      - إنشاء فريق متطوعين للعناية بالأشجار المزروعة

      نحتاج إلى متطوعين ودعم مادي لشراء الشتلات والأدوات اللازمة للزراعة.
    `,
    location: 'الجزائر العاصمة',
    wilaya: 'الجزائر',
    mainImage: '/placeholder.svg?height=600&width=1200',
    images: ['/placeholder.svg?height=400&width=600'],
    status: 'active',
    goal: 100,
    supportCount: 0,
    commentCount: 0,
    viewCount: 0,
    progress: 0,
    tags: ['تشجير', 'بيئة', 'تطوع'],
    budget: 50000,
    requiredVolunteers: 20,
    currentVolunteers: [],
    isPromoted: true,
    isPublic: true
  },
  {
    title: 'مكتبات متنقلة للمناطق النائية',
    shortDescription: 'إنشاء مكتبات متنقلة للوصول إلى المناطق النائية وتوفير الكتب والموارد التعليمية للأطفال.',
    fullDescription: `
      تهدف هذه المبادرة إلى توفير فرص التعلم والقراءة للأطفال في المناطق النائية من خلال إنشاء مكتبات متنقلة.

      الأهداف:
      - إنشاء 5 مكتبات متنقلة مجهزة بالكتب والموارد التعليمية
      - الوصول إلى 20 منطقة نائية في مختلف أنحاء الجزائر
      - توفير 1000 كتاب متنوع للأطفال والشباب
      - تنظيم جلسات قراءة وأنشطة تعليمية

      نحتاج إلى دعم مادي لشراء الكتب وتجهيز المكتبات المتنقلة، بالإضافة إلى متطوعين للمساعدة في تنظيم الأنشطة.
    `,
    location: 'قسنطينة',
    wilaya: 'قسنطينة',
    mainImage: '/placeholder.svg?height=600&width=1200',
    images: ['/placeholder.svg?height=400&width=600'],
    status: 'active',
    goal: 150,
    supportCount: 0,
    commentCount: 0,
    viewCount: 0,
    progress: 0,
    tags: ['تعليم', 'قراءة', 'مناطق نائية'],
    budget: 100000,
    requiredVolunteers: 15,
    currentVolunteers: [],
    isPromoted: true,
    isPublic: true
  }
];

// Function to seed the database
async function seedDatabase() {
  // Check MongoDB connection first
  const isConnected = await checkMongoDBConnection();
  if (!isConnected) {
    console.error('Cannot seed database: MongoDB connection failed');
    process.exit(1);
  }

  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });

  try {
    await client.connect();
    console.log('Connected to MongoDB for seeding');

    const db = client.db();

    // Create collections if they don't exist
    const collections = [
      'users',
      'categories',
      'initiatives',
      'comments',
      'badges',
      'votingoptions',
      'updates',
      'milestones',
      'posts',
      'postcomments',
      'notifications',
      'reports',
      'settings'
    ];

    for (const collectionName of collections) {
      const collectionExists = await db.listCollections({ name: collectionName }).hasNext();
      if (!collectionExists) {
        await db.createCollection(collectionName);
        console.log(`Created collection: ${collectionName}`);
      }
    }

    // Insert categories
    const categoriesCollection = db.collection('categories');
    const existingCategories = await categoriesCollection.countDocuments();

    if (existingCategories === 0) {
      const result = await categoriesCollection.insertMany(categories);
      console.log(`${result.insertedCount} categories inserted`);
    } else {
      console.log('Categories already exist, skipping insertion');
    }

    // Insert admin user
    const usersCollection = db.collection('users');
    const existingAdmin = await usersCollection.findOne({ email: adminUser.email });

    if (!existingAdmin) {
      const result = await usersCollection.insertOne(adminUser);
      console.log(`Admin user inserted with ID: ${result.insertedId}`);

      // Get the inserted admin user ID
      const adminId = result.insertedId;

      // Insert sample initiatives
      const initiativesCollection = db.collection('initiatives');
      const existingInitiatives = await initiativesCollection.countDocuments();

      if (existingInitiatives === 0) {
        // Get category IDs
        const envCategory = await categoriesCollection.findOne({ name: 'environment' });
        const eduCategory = await categoriesCollection.findOne({ name: 'education' });

        // Prepare initiatives with proper references
        const initiative1 = {
          ...sampleInitiatives[0],
          author: adminId,
          category: envCategory._id,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const initiative2 = {
          ...sampleInitiatives[1],
          author: adminId,
          category: eduCategory._id,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const result = await initiativesCollection.insertMany([initiative1, initiative2]);
        console.log(`${result.insertedCount} initiatives inserted`);

        // Update category initiative counts
        await categoriesCollection.updateOne(
          { _id: envCategory._id },
          { $inc: { initiativeCount: 1 } }
        );

        await categoriesCollection.updateOne(
          { _id: eduCategory._id },
          { $inc: { initiativeCount: 1 } }
        );

        // Update admin user's initiatives array
        await usersCollection.updateOne(
          { _id: adminId },
          { $push: { initiatives: { $each: Object.values(result.insertedIds) } } }
        );
      } else {
        console.log('Initiatives already exist, skipping insertion');
      }
    } else {
      console.log('Admin user already exists, skipping insertion');
    }

    console.log('Database seeding completed successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the seeding function
seedDatabase();

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, MapPin, Calendar, Award, Edit, Settings, Package, User, Briefcase, Building, Lightbulb, Users, Heart, MessageCircle } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { api } from "@/lib/api"
import UserResourcesTab from "@/components/user/user-resources-tab"
import UserSkillsDisplay from "@/components/user/user-skills-display"
import UserProposerDisplay from "@/components/user/user-proposer-display"
import UserCompanyDisplay from "@/components/user/user-company-display"
import UserCivilSocietyDisplay from "@/components/user/user-civil-society-display"

interface User {
  _id: string
  name: string
  username: string
  email: string
  avatar: string
  bio?: string
  location?: string
  joinDate?: string
  createdAt?: string
  role: string
  userType: 'volunteer' | 'proposer' | 'company' | 'civilSociety'
  badges: Array<{
    _id: string
    name: string
    description: string
    icon: string
  }>
  socialLinks?: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
  }
  // Volunteer specific fields
  skills?: Array<{
    name: string
    category: 'cognitive' | 'technical' | 'interpersonal' | 'organizational' | 'digital' | 'linguistic' | 'transversal'
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  }>
  qualifications?: string[]
  interests?: string[]

  // Proposer specific fields
  ideaDescription?: string
  objectives?: string
  needs?: string

  // Company specific fields
  companyName?: string
  industry?: string
  customIndustry?: string
  companyDescription?: string
  employeeCount?: string
  foundingYear?: string
  contactPhone?: string
  contactEmail?: string
  website?: string
  address?: string
  services?: string[]
  resources?: string[]
  customResources?: string[]
  supportDescription?: string

  // Civil Society specific fields
  organizationName?: string
  organizationType?: string
  activitySector?: string
  scope?: string
  approvalNumber?: string
  memberCount?: string
  organizationDescription?: string
  contactPhone2?: string
}

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  location: string
  mainImage: string
  supportCount: number
  commentCount: number
  status: string
  createdAt: string
}

interface Activity {
  _id: string
  type: string
  action: string
  content: string
  date: string
  relatedInitiative?: {
    _id: string
    title: string
  }
  relatedUser?: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  relatedComment?: {
    _id: string
    content: string
  }
  metadata?: Record<string, any>
}

export default function UserProfilePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params

  const [user, setUser] = useState<User | null>(null)
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [supportedInitiatives, setSupportedInitiatives] = useState<Initiative[]>([])
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  // Use the auth context
  const { user: currentUser, isAuthenticated } = useAuth()
  const isOwnProfile = isAuthenticated && currentUser && currentUser.id === id

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/users/${id}`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user profile")
        }

        // Afficher les données reçues pour le débogage
        console.log("User data received:", JSON.stringify(data, null, 2));

        // Extraire les données utilisateur correctement
        const userData = data.data?.user || data.user || data;

        console.log("Structure des données:", {
          hasDataUser: !!data.data?.user,
          hasUser: !!data.user,
          userType: userData.userType
        });

        // Vérifier si les propriétés nécessaires existent
        if (!userData.joinDate && userData.createdAt) {
          // Si joinDate n'existe pas mais createdAt existe, utiliser createdAt comme joinDate
          userData.joinDate = userData.createdAt;
        } else if (!userData.joinDate && !userData.createdAt) {
          // Si ni joinDate ni createdAt n'existent, ajouter une date par défaut
          userData.joinDate = new Date().toISOString();
        }

        // Ajouter les données de l'utilisateur à l'objet principal pour l'affichage
        setUser({
          ...userData,
          userType: userData.userType, // S'assurer que le type d'utilisateur est correctement défini
          // Ajouter les données de l'API pour l'affichage du profil
          ideaDescription: userData.ideaDescription || "",
          objectives: userData.objectives || "",
          needs: userData.needs || "",
          qualifications: userData.qualifications || [],
          skills: userData.skills || [],
          interests: userData.interests || [],
          // Données pour les entreprises
          companyName: userData.companyName || "",
          industry: userData.industry || "",
          customIndustry: userData.customIndustry || "",
          companyDescription: userData.companyDescription || "",
          employeeCount: userData.employeeCount || "",
          foundingYear: userData.foundingYear || "",
          contactPhone: userData.contactPhone || "",
          contactEmail: userData.contactEmail || "",
          website: userData.website || "",
          address: userData.address || "",
          services: userData.services || [],
          resources: userData.resources || [],
          customResources: userData.customResources || [],
          supportDescription: userData.supportDescription || "",
          // Données pour les acteurs de la société civile
          organizationName: userData.organizationName || "",
          organizationType: userData.organizationType || "",
          activitySector: userData.activitySector || "",
          scope: userData.scope || "",
          approvalNumber: userData.approvalNumber || "",
          memberCount: userData.memberCount || "",
          organizationDescription: userData.organizationDescription || "",
          contactPhone2: userData.contactPhone2 || ""
        })
      } catch (err: any) {
        console.error('Error fetching user profile:', err)
        setError(err.message || "An error occurred")
      }
    }

    const fetchUserInitiatives = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/initiatives/user/${id}`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user initiatives")
        }

        setInitiatives(data.initiatives || [])
      } catch (err: any) {
        console.error('Error fetching user initiatives:', err)
        setError(err.message || "An error occurred")
      }
    }

    const fetchSupportedInitiatives = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/users/${id}/supported-initiatives`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch supported initiatives")
        }

        setSupportedInitiatives(data.initiatives || [])
      } catch (err: any) {
        console.error('Error fetching supported initiatives:', err)
        setError(err.message || "An error occurred")
      }
    }

    const fetchUserActivities = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/users/${id}/activities`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user activities")
        }

        setActivities(data.activities || [])
      } catch (err: any) {
        console.error('Error fetching user activities:', err)
        setError(err.message || "An error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserProfile()
    fetchUserInitiatives()
    fetchSupportedInitiatives()
    fetchUserActivities()
  }, [id])

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);

      // Vérifier si la date est valide
      if (isNaN(date.getTime())) {
        console.warn("Date invalide:", dateString);
        return "تاريخ غير معروف";
      }

      // Utiliser ar-DZ (arabe algérien) qui utilise les chiffres latins
      return date.toLocaleDateString("ar-DZ", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (error) {
      console.error("Erreur lors du formatage de la date:", error);
      return "تاريخ غير معروف";
    }
  }

  // Add a helper function to safely access user properties
  const getUserProperty = (path: string, defaultValue: any = "") => {
    if (!user) return defaultValue

    const parts = path.split('.')
    let value = user as any

    for (const part of parts) {
      if (value === null || value === undefined) return defaultValue
      value = value[part]
    }

    return value !== null && value !== undefined ? value : defaultValue
  }

  // Format date safely
  const safeFormatDate = (dateString: string | undefined) => {
    if (!dateString) {
      // Si aucune date n'est fournie, utiliser la date actuelle
      console.log("Aucune date fournie, utilisation de la date actuelle");
      return formatDate(new Date().toISOString());
    }

    try {
      // Vérifier si la date est valide
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn("Date invalide:", dateString);
        return "تاريخ غير معروف";
      }

      return formatDate(dateString);
    } catch (e) {
      console.error("Erreur lors du formatage de la date:", e);
      return "تاريخ غير معروف";
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري تحميل الملف الشخصي...</span>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "لم يتم العثور على المستخدم"}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Link href="/">
            <Button variant="outline">العودة إلى الصفحة الرئيسية</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mb-8">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center">
              <Avatar className="h-32 w-32" square={user.userType === 'company'}>
                <AvatarImage
                  src={getUserProperty('avatar')}
                  alt={getUserProperty('name')}
                  preserveAspectRatio={true}
                  style={{
                    objectFit: "contain",
                    maxWidth: "100%",
                    maxHeight: "100%",
                    backgroundColor: user.userType === 'company' ? "white" : "transparent"
                  }}
                />
                <AvatarFallback square={user.userType === 'company'} className="text-4xl">
                  {user.userType === 'company' ?
                    <Building className="h-12 w-12 text-gray-400" /> :
                    getUserProperty('name', '?').charAt(0)
                  }
                </AvatarFallback>
              </Avatar>

              {isOwnProfile && (
                <div className="mt-4 flex gap-2">
                  <Link href="/settings">
                    <Button variant="outline" className="flex items-center gap-1">
                      <Settings className="h-4 w-4" />
                      إعدادات
                    </Button>
                  </Link>
                </div>
              )}
            </div>

            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-1">{getUserProperty('name', 'المستخدم')}</h1>
              <p className="text-gray-500 mb-4">@{getUserProperty('username', '')}</p>

              <div className="flex flex-wrap gap-4 mb-4">
                {getUserProperty('location') && (
                  <div className="flex items-center gap-1 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{getUserProperty('location')}</span>
                  </div>
                )}
                <div className="flex items-center gap-1 text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>تاريخ الانضمام: {safeFormatDate(getUserProperty('joinDate') || getUserProperty('createdAt'))}</span>
                </div>
              </div>

              {getUserProperty('bio') && <p className="text-gray-700 mb-4">{getUserProperty('bio')}</p>}

              {getUserProperty('badges', []).length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {getUserProperty('badges', []).map((badge: any) => (
                    <Badge key={badge._id || badge.id || Math.random()} className="bg-green-600 hover:bg-green-700 flex items-center gap-1">
                      <Award className="h-3 w-3" />
                      {badge.name || 'Badge'}
                    </Badge>
                  ))}
                </div>
              )}

              <div className="flex flex-wrap gap-6 mt-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{initiatives.length}</p>
                  <p className="text-sm text-gray-500">المبادرات</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{supportedInitiatives.length}</p>
                  <p className="text-sm text-gray-500">المبادرات التي أدعمها</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{activities.length}</p>
                  <p className="text-sm text-gray-500">النشاطات</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    <Package className="h-5 w-5 inline-block mr-1" />
                  </p>
                  <p className="text-sm text-gray-500">الموارد</p>
                </div>
                <div className="text-center">
                  {user.userType === 'volunteer' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <User className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">متطوع</p>
                    </>
                  )}
                  {user.userType === 'proposer' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <Briefcase className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">مقترح مبادرات</p>
                    </>
                  )}
                  {user.userType === 'company' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <Building className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">شركة</p>
                    </>
                  )}
                  {user.userType === 'civilSociety' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <Users className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">شريك اجتماعي</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="border border-gray-200 rounded-lg p-4 mb-8 shadow-sm" dir="rtl">
        <Tabs defaultValue="profile" className="min-h-[150px]">
          <TabsList className="mb-4 w-full flex justify-start">
            <TabsTrigger value="activity">النشاطات</TabsTrigger>
            <TabsTrigger value="resources">الموارد</TabsTrigger>
            <TabsTrigger value="supported">المبادرات التي أدعمها</TabsTrigger>
            <TabsTrigger value="initiatives">المبادرات</TabsTrigger>
            <TabsTrigger value="profile">الملف الشخصي</TabsTrigger>
          </TabsList>

        <TabsContent value="initiatives" className="min-h-[150px] text-right">
          <h2 className="text-2xl font-bold mb-4">المبادرات</h2>

          {initiatives.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لم يتم إنشاء أي مبادرات بعد.</p>
                {isOwnProfile && (
                  <Link href="/initiatives/create">
                    <Button className="mt-4 bg-green-600 hover:bg-green-700">إنشاء أول مبادرة</Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {initiatives.map((initiative) => (
                <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-[160px] overflow-hidden">
                    <Image
                      src={initiative.mainImage || "/placeholder.svg"}
                      alt={initiative.title}
                      width={400}
                      height={160}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
                      <Badge
                        variant="outline"
                        className={
                          initiative.status === "active"
                            ? "text-green-600 border-green-200 bg-green-50"
                            : initiative.status === "completed"
                              ? "text-blue-600 border-blue-200 bg-blue-50"
                              : "text-gray-600 border-gray-200 bg-gray-50"
                        }
                      >
                        {initiative.status.charAt(0).toUpperCase() + initiative.status.slice(1)}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg mt-2 line-clamp-1">
                      <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600 transition-colors">
                        {initiative.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 line-clamp-2 text-sm mb-4">{initiative.shortDescription}</p>
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{initiative.supportCount} داعم</span>
                      <span>{initiative.commentCount} تعليق</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="supported" className="min-h-[150px] text-right">
          <h2 className="text-2xl font-bold mb-4">المبادرات التي أدعمها</h2>

          {supportedInitiatives.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لم يتم دعم أي مبادرات بعد.</p>
                <Link href="/initiatives">
                  <Button className="mt-4 bg-green-600 hover:bg-green-700">تصفح المبادرات</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {supportedInitiatives.map((initiative) => (
                <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-[160px] overflow-hidden">
                    <Image
                      src={initiative.mainImage || "/placeholder.svg"}
                      alt={initiative.title}
                      width={400}
                      height={160}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
                      <Badge
                        variant="outline"
                        className={
                          initiative.status === "active"
                            ? "text-green-600 border-green-200 bg-green-50"
                            : initiative.status === "completed"
                              ? "text-blue-600 border-blue-200 bg-blue-50"
                              : "text-gray-600 border-gray-200 bg-gray-50"
                        }
                      >
                        {initiative.status.charAt(0).toUpperCase() + initiative.status.slice(1)}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg mt-2 line-clamp-1">
                      <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600 transition-colors">
                        {initiative.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 line-clamp-2 text-sm mb-4">{initiative.shortDescription}</p>
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{initiative.supportCount} داعم</span>
                      <span>{initiative.commentCount} تعليق</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="resources" className="min-h-[150px] text-right">
          <UserResourcesTab userId={id} isOwnProfile={isOwnProfile} />
        </TabsContent>

        <TabsContent value="profile" className="min-h-[150px] text-right">
          <h2 className="text-2xl font-bold mb-4">الملف الشخصي</h2>

          {user && user.userType === 'volunteer' && (
            <UserSkillsDisplay
              skills={user.skills || []}
              qualifications={user.qualifications || []}
              interests={user.interests || []}
              userType="volunteer"
            />
          )}

          {user && user.userType === 'proposer' && (
            <div className="space-y-6" dir="rtl">
              {/* Afficher la bio si elle existe */}
              {user.bio && (
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-green-100 p-3 rounded-full">
                        <Lightbulb className="h-6 w-6 text-green-600" />
                      </div>
                      <h3 className="text-lg font-medium">نبذة عني</h3>
                    </div>
                    <p className="text-gray-700">{user.bio}</p>
                  </CardContent>
                </Card>
              )}

              {/* Utiliser le composant UserProposerDisplay pour les autres informations */}
              <UserProposerDisplay
                ideaDescription={user.ideaDescription || ""}
                objectives={user.objectives || ""}
                needs={user.needs || ""}
                qualifications={user.qualifications || []}
                skills={user.skills || []}
                interests={user.interests || []}
              />
            </div>
          )}

          {user && user.userType === 'company' && (
            <UserCompanyDisplay
              companyName={user.companyName || ""}
              industry={user.industry || ""}
              customIndustry={user.customIndustry || ""}
              companyDescription={user.companyDescription || ""}
              employeeCount={user.employeeCount || ""}
              foundingYear={user.foundingYear || ""}
              contactPhone={user.contactPhone || ""}
              contactEmail={user.contactEmail || ""}
              website={user.website || ""}
              address={user.address || ""}
              services={user.services || []}
              resources={user.resources || []}
              customResources={user.customResources || []}
              supportDescription={user.supportDescription || ""}
            />
          )}

          {user && user.userType === 'civilSociety' && (
            <UserCivilSocietyDisplay
              organizationName={user.organizationName || ""}
              organizationType={user.organizationType || ""}
              activitySector={user.activitySector || ""}
              scope={user.scope || ""}
              approvalNumber={user.approvalNumber || ""}
              memberCount={user.memberCount || ""}
              foundingYear={user.foundingYear || ""}
              organizationDescription={user.organizationDescription || ""}
              contactPhone={user.contactPhone || ""}
              contactPhone2={user.contactPhone2 || ""}
              contactEmail={user.contactEmail || ""}
              website={user.website || ""}
              address={user.address || ""}
              services={user.services || []}
              resources={user.resources || []}
              customResources={user.customResources || []}
              supportDescription={user.supportDescription || ""}
            />
          )}

          {/* Affichage par défaut si le type d'utilisateur n'est pas reconnu */}
          {user && !user.userType && (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لم يتم إضافة معلومات الملف الشخصي بعد.</p>
                {isOwnProfile && (
                  <div className="mt-4">
                    <p className="mb-2 text-sm">يمكنك إضافة معلومات الملف الشخصي من صفحة الإعدادات.</p>
                    <Link href="/settings">
                      <Button className="mt-2 bg-green-600 hover:bg-green-700">
                        <Settings className="h-4 w-4 ml-2" />
                        إكمال الملف الشخصي
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity" className="min-h-[150px] text-right">
          <h2 className="text-2xl font-bold mb-4">النشاطات الأخيرة</h2>

          {activities.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لا توجد نشاطات حديثة.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {activities.map((activity) => (
                <Card key={activity._id}>
                  <CardContent className="py-4">
                    <div className="flex items-start gap-4">
                      <div className="bg-green-100 rounded-full p-2 text-green-600">
                        {activity.type === "initiative" && <Briefcase className="h-5 w-5" />}
                        {activity.type === "support" && <Heart className="h-5 w-5" />}
                        {activity.type === "comment" && <MessageCircle className="h-5 w-5" />}
                        {activity.type === "volunteer" && <Users className="h-5 w-5" />}
                        {activity.type === "badge" && <Award className="h-5 w-5" />}
                        {activity.type === "profile" && <User className="h-5 w-5" />}
                        {activity.type === "system" && <Settings className="h-5 w-5" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-700" dir="rtl">{activity.content}</p>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {activity.relatedInitiative && (
                            <Link
                              href={`/initiatives/${activity.relatedInitiative._id}`}
                              className="text-sm text-green-600 hover:underline"
                            >
                              {activity.relatedInitiative.title}
                            </Link>
                          )}
                          {activity.relatedUser && (
                            <Link
                              href={`/users/${activity.relatedUser._id}`}
                              className="text-sm text-blue-600 hover:underline"
                            >
                              {activity.relatedUser.name}
                            </Link>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{formatDate(activity.date)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
      </div>
    </div>
  )
}


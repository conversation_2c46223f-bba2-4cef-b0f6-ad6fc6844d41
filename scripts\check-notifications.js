const { MongoClient, ObjectId } = require('mongodb');

async function checkNotifications() {
  try {
    const client = await MongoClient.connect('mongodb://localhost:27017/initiatives_dz');
    const db = client.db();
    
    // Vérifier toutes les notifications
    const allNotifications = await db.collection('notifications').find().toArray();
    console.log('Total notifications in database:', allNotifications.length);
    
    if (allNotifications.length > 0) {
      console.log('Sample notification:', JSON.stringify(allNotifications[0], null, 2));
    }
    
    // Vérifier les notifications pour l'utilisateur spécifique
    const userId = '681396ffa9c3356546496c2b'; // Mohamed
    const userNotifications = await db.collection('notifications').find({ 
      recipient: new ObjectId(userId) 
    }).toArray();
    
    console.log(`\nNotifications for user ${userId}:`, userNotifications.length);
    
    if (userNotifications.length > 0) {
      console.log('Sample user notification:', JSON.stringify(userNotifications[0], null, 2));
      
      // Compter les notifications non lues
      const unreadCount = userNotifications.filter(n => n.read === false).length;
      const isReadCount = userNotifications.filter(n => n.isRead === false).length;
      
      console.log(`\nUnread notifications (read=false): ${unreadCount}`);
      console.log(`Unread notifications (isRead=false): ${isReadCount}`);
      
      // Afficher les champs disponibles
      console.log('\nAvailable fields in notifications:');
      const fields = new Set();
      userNotifications.forEach(n => {
        Object.keys(n).forEach(k => fields.add(k));
      });
      console.log([...fields]);
    }
    
    await client.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkNotifications();

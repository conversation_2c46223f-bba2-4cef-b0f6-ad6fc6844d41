import mongoose, { Schema, model } from "mongoose"

const supportSchema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    type: {
      type: String,
      enum: ["financial", "volunteer", "resource", "other"],
      default: "other",
    },
    amount: {
      type: Number,
      default: 0,
    },
    message: {
      type: String,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
)

// Create a compound index to prevent duplicate supports
supportSchema.index({ user: 1, initiative: 1 }, { unique: true })

// Check if the model already exists to prevent overwriting
const Support = mongoose.models.Support || model("Support", supportSchema)

export default Support

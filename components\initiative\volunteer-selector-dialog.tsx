"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../ui/button"
import { Input } from "../ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Badge } from "../ui/badge"
import { Loader2, Search, UserPlus, Check, X, Briefcase } from "lucide-react"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"

interface User {
  _id: string
  name: string
  username: string
  email: string
  avatar: string
  bio?: string
  userType: string
  skills?: string[]
}

interface VolunteerSelectorDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initiativeId: string
  onSelect: (user: User) => void
}

export default function VolunteerSelectorDialog({
  open,
  onOpenChange,
  initiativeId,
  onSelect,
}: VolunteerSelectorDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchSkills, setSearchSkills] = useState("")
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedUser, setSelectedUser] = useState<string | null>(null)
  const [searchType, setSearchType] = useState<"name" | "skills">("name")

  useEffect(() => {
    if (open) {
      fetchUsers()
    } else {
      setSearchQuery("")
      setSelectedUser(null)
    }
  }, [open])

  useEffect(() => {
    if (searchType === "name") {
      if (searchQuery.trim() === "") {
        setFilteredUsers(users)
      } else {
        const query = searchQuery.toLowerCase()
        setFilteredUsers(
          users.filter(
            (user) =>
              user.name.toLowerCase().includes(query) ||
              user.username.toLowerCase().includes(query) ||
              user.email.toLowerCase().includes(query) ||
              (user.bio && user.bio.toLowerCase().includes(query))
          )
        )
      }
    } else if (searchType === "skills") {
      if (searchSkills.trim() === "") {
        setFilteredUsers(users)
      } else {
        const skills = searchSkills.toLowerCase().split(',').map(s => s.trim()).filter(Boolean)

        setFilteredUsers(
          users.filter(
            (user) => {
              if (!user.skills || user.skills.length === 0) return false

              // Check if user has any of the searched skills
              return skills.some(skill =>
                user.skills!.some(userSkill =>
                  userSkill.toLowerCase().includes(skill)
                )
              )
            }
          )
        )
      }
    }
  }, [searchQuery, searchSkills, searchType, users])

  const fetchUsers = async () => {
    try {
      setIsLoading(true)

      // Fetch users who are not already volunteers for this initiative
      const response = await api.get(`/api/users?excludeInitiativeVolunteers=${initiativeId}`, true)

      if (response.success) {
        setUsers(response.users || [])
        setFilteredUsers(response.users || [])
      } else {
        throw new Error(response.message || "Failed to fetch users")
      }
    } catch (err: any) {
      console.error("Error fetching users:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء جلب المستخدمين",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelectUser = (user: User) => {
    setSelectedUser(user._id)
    onSelect(user)
    onOpenChange(false)
  }

  const getUserTypeBadge = (userType: string) => {
    switch (userType) {
      case "volunteer":
        return <Badge className="bg-green-500">متطوع</Badge>
      case "proposer":
        return <Badge className="bg-blue-500">مقترح</Badge>
      case "company":
        return <Badge className="bg-purple-500">شركة</Badge>
      default:
        return <Badge>مستخدم</Badge>
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>اختيار متطوع للدعوة</DialogTitle>
          <DialogDescription>
            ابحث عن مستخدم لدعوته للانضمام إلى مبادرتك كمتطوع.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex gap-2 mb-4">
            <Button
              variant={searchType === "name" ? "default" : "outline"}
              className="flex-1"
              onClick={() => setSearchType("name")}
            >
              بحث بالاسم
            </Button>
            <Button
              variant={searchType === "skills" ? "default" : "outline"}
              className="flex-1"
              onClick={() => setSearchType("skills")}
            >
              بحث بالمهارات
            </Button>
          </div>

          {searchType === "name" ? (
            <div className="relative">
              <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="ابحث عن مستخدم بالاسم أو اسم المستخدم أو البريد الإلكتروني"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
            </div>
          ) : (
            <div className="relative">
              <Briefcase className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="أدخل المهارات مفصولة بفواصل (مثال: برمجة, تصميم, تسويق)"
                value={searchSkills}
                onChange={(e) => setSearchSkills(e.target.value)}
                className="pr-10"
              />
              <p className="text-xs text-muted-foreground mt-1">سيتم عرض المستخدمين الذين لديهم أي من المهارات المذكورة</p>
            </div>
          )}

          <div className="border rounded-md overflow-hidden">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
                <span className="mr-2">جاري تحميل المستخدمين...</span>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <UserPlus className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <p>لم يتم العثور على مستخدمين مطابقين للبحث</p>
              </div>
            ) : (
              <div className="max-h-[300px] overflow-y-auto">
                {filteredUsers.map((user) => (
                  <div
                    key={user._id}
                    className={`flex items-center justify-between p-3 hover:bg-muted/50 cursor-pointer ${
                      selectedUser === user._id ? "bg-muted" : ""
                    }`}
                    onClick={() => handleSelectUser(user)}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.avatar || "/placeholder.svg?height=40&width=40"} alt={user.name} />
                        <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>@{user.username}</span>
                          <span>•</span>
                          <span>{user.email}</span>
                        </div>
                        {user.skills && user.skills.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {user.skills?.slice(0, 3).map((skill, index) => {
                              // Vérifier si skill est un objet ou une chaîne
                              const skillText = typeof skill === 'object' ?
                                (skill.name || JSON.stringify(skill)) :
                                String(skill);

                              return (
                                <Badge key={index} variant="outline" className="text-xs py-0 h-5">
                                  {skillText}
                                </Badge>
                              );
                            })}
                            {user.skills && user.skills.length > 3 && (
                              <Badge variant="outline" className="text-xs py-0 h-5">
                                +{user.skills.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getUserTypeBadge(user.userType)}
                      {selectedUser === user._id && (
                        <Check className="h-5 w-5 text-green-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            إلغاء
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

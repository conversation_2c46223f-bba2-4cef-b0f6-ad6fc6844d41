"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { api } from "@/lib/api"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Loader2, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { uploadFile } from "@/lib/fileUpload"

interface BannerFormProps {
  initialData?: {
    id?: string
    image?: string
    mainText?: string
    subText?: string
    mainTextColor?: string
    subTextColor?: string
    order?: number
    isActive?: boolean
  }
}

export function BannerForm({ initialData }: BannerFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(initialData?.image || null)
  
  const [formData, setFormData] = useState({
    mainText: initialData?.mainText || "",
    subText: initialData?.subText || "",
    mainTextColor: initialData?.mainTextColor || "#FFFFFF",
    subTextColor: initialData?.subTextColor || "#FFFFFF",
    order: initialData?.order || 0,
    isActive: initialData?.isActive !== undefined ? initialData.isActive : true,
  })

  const isEditMode = !!initialData?.id

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    })
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setImageFile(file)
      
      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Upload image if a new one is selected
      let imageUrl = initialData?.image || ""
      if (imageFile) {
        try {
          console.log('Uploading banner image...')
          const uploadData = await uploadFile("/api/public-upload", imageFile)
          
          if (uploadData && uploadData.file && uploadData.file.url) {
            imageUrl = uploadData.file.url
            console.log('Image uploaded successfully:', imageUrl)
          } else {
            throw new Error("Invalid response format from upload API")
          }
        } catch (error) {
          console.error("Error uploading image:", error)
          toast({
            title: "Image Upload Failed",
            description: error instanceof Error ? error.message : "Could not upload the image. Please try again.",
            variant: "destructive",
          })
          setIsLoading(false)
          return
        }
      }

      if (!imageUrl && !isEditMode) {
        toast({
          title: "Image Required",
          description: "Please upload an image for the banner.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      const bannerData = {
        ...formData,
        image: imageUrl,
      }

      let response
      if (isEditMode) {
        response = await api.put(`/api/banners/${initialData.id}`, bannerData)
      } else {
        response = await api.post("/api/banners", bannerData)
      }

      if (response.success) {
        toast({
          title: isEditMode ? "Banner Updated" : "Banner Created",
          description: isEditMode ? "Banner has been updated successfully." : "Banner has been created successfully.",
        })
        router.push("/admin/banners")
      } else {
        toast({
          title: "Error",
          description: response.message || `Failed to ${isEditMode ? "update" : "create"} banner`,
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error(`Error ${isEditMode ? "updating" : "creating"} banner:`, err)
      toast({
        title: "Error",
        description: err.message || `An error occurred while ${isEditMode ? "updating" : "creating"} the banner`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Banner preview component
  const BannerPreview = () => (
    <div className="relative w-full h-[200px] rounded-md overflow-hidden mb-4">
      <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/40 z-10"></div>
      {imagePreview ? (
        <img
          src={imagePreview}
          alt="Banner preview"
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
          <p className="text-gray-500">No image selected</p>
        </div>
      )}
      <div className="absolute inset-0 flex flex-col items-center justify-center z-20 p-4">
        <h1 
          className="text-2xl md:text-3xl font-bold mb-2"
          style={{ color: formData.mainTextColor }}
        >
          {formData.mainText || "Main Text"}
        </h1>
        <p 
          className="text-sm md:text-base opacity-90"
          style={{ color: formData.subTextColor }}
        >
          {formData.subText || "Sub Text"}
        </p>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" asChild>
          <Link href="/admin/banners">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Link>
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{isEditMode ? "Modifier la bannière" : "Créer une bannière"}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Banner Preview */}
            <div className="space-y-2">
              <Label>Aperçu de la bannière</Label>
              <BannerPreview />
            </div>

            {/* Image Upload */}
            <div className="space-y-2">
              <Label htmlFor="image">Image de la bannière</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  id="banner-image"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById("banner-image")?.click()}
                  className="mb-2"
                >
                  {imagePreview ? "Changer l'image" : "Sélectionner une image"}
                </Button>
                <p className="text-sm text-gray-500">PNG, JPG, JPEG (Max: 5MB)</p>
              </div>
            </div>

            {/* Main Text */}
            <div className="space-y-2">
              <Label htmlFor="mainText">Texte principal</Label>
              <Input
                id="mainText"
                name="mainText"
                value={formData.mainText}
                onChange={handleInputChange}
                placeholder="Entrez le texte principal"
                required
              />
            </div>

            {/* Sub Text */}
            <div className="space-y-2">
              <Label htmlFor="subText">Texte secondaire</Label>
              <Input
                id="subText"
                name="subText"
                value={formData.subText}
                onChange={handleInputChange}
                placeholder="Entrez le texte secondaire"
                required
              />
            </div>

            {/* Text Colors */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="mainTextColor">Couleur du texte principal</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="mainTextColor"
                    name="mainTextColor"
                    type="color"
                    value={formData.mainTextColor}
                    onChange={handleInputChange}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    type="text"
                    value={formData.mainTextColor}
                    onChange={handleInputChange}
                    name="mainTextColor"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subTextColor">Couleur du texte secondaire</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="subTextColor"
                    name="subTextColor"
                    type="color"
                    value={formData.subTextColor}
                    onChange={handleInputChange}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    type="text"
                    value={formData.subTextColor}
                    onChange={handleInputChange}
                    name="subTextColor"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>
            </div>

            {/* Order and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="order">Ordre d'affichage</Label>
                <Input
                  id="order"
                  name="order"
                  type="number"
                  value={formData.order}
                  onChange={handleInputChange}
                  min={0}
                />
                <p className="text-xs text-gray-500">Les nombres plus petits apparaissent en premier</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="isActive" className="block mb-2">Statut</Label>
                <div className="flex items-center gap-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) =>
                      setFormData({ ...formData, isActive: checked })
                    }
                  />
                  <Label htmlFor="isActive">
                    {formData.isActive ? "Actif" : "Inactif"}
                  </Label>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditMode ? "Mise à jour..." : "Création..."}
                  </>
                ) : (
                  <>
                    {isEditMode ? "Mettre à jour" : "Créer"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      
      <Toaster />
    </div>
  )
}

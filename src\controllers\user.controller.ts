import { Request, Response, NextFunction } from "express"
import { User, Initiative, Badge } from "../models"
import { createError } from "../utils/error"
import { asyncHandler } from "../utils/error"
import mongoose from "mongoose"
import bcrypt from "bcryptjs"

/**
 * Get all users
 * @route GET /api/users
 * @access Public
 */
export const getUsers = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { page = 1, limit = 10, search, excludeInitiativeVolunteers, userType, industry, resource, sort } = req.query
  const pageNumber = parseInt(page as string)
  const limitNumber = parseInt(limit as string)

  // Build query
  const query: any = {}

  // Filter by user type if provided
  if (userType) {
    query.userType = userType
  }

  // Filters spécifiques pour les entreprises
  if (userType === 'company') {
    if (req.query.industry && req.query.industry !== 'all') {
      query.industry = req.query.industry
    }

    if (req.query.resource && req.query.resource !== 'all') {
      query.resources = req.query.resource
    }
  }

  // Filters spécifiques pour les acteurs de la société civile
  if (userType === 'civilSociety') {
    if (req.query.organizationType && req.query.organizationType !== 'all') {
      query.organizationType = req.query.organizationType
    }

    if (req.query.scope && req.query.scope !== 'all') {
      query.scope = req.query.scope
    }

    if (req.query.activitySector && req.query.activitySector !== 'all') {
      query.activitySector = req.query.activitySector
    }
  }

  // Add search if provided
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: "i" } },
      { username: { $regex: search, $options: "i" } },
      { email: { $regex: search, $options: "i" } },
    ]

    // Add company-specific search fields if userType is company
    if (userType === "company") {
      query.$or.push(
        { companyName: { $regex: search, $options: "i" } },
        { industry: { $regex: search, $options: "i" } }
      )
    }

    // Add civil society-specific search fields if userType is civilSociety
    if (userType === "civilSociety") {
      query.$or.push(
        { organizationName: { $regex: search, $options: "i" } },
        { organizationType: { $regex: search, $options: "i" } },
        { activitySector: { $regex: search, $options: "i" } },
        { "mainContact.name": { $regex: search, $options: "i" } },
        { organizationDescription: { $regex: search, $options: "i" } }
      )
    }
  }

  // Industry filter for companies
  if (industry && industry !== "all") {
    query.industry = industry
  }

  // Resource filter for companies
  if (resource && resource !== "all") {
    query.resources = resource
  }

  // Exclude users who are already volunteers for a specific initiative
  if (excludeInitiativeVolunteers && mongoose.Types.ObjectId.isValid(excludeInitiativeVolunteers as string)) {
    const initiative = await Initiative.findById(excludeInitiativeVolunteers)
    if (initiative) {
      query._id = { $nin: initiative.currentVolunteers }
      // Also exclude the initiative author
      query._id = { $ne: initiative.author }
    }
  }

  // Determine sort order
  let sortOption: any = { joinDate: -1 } // Default sort
  if (sort) {
    const sortField = sort as string
    if (sortField === "newest") {
      sortOption = { createdAt: -1 }
    } else if (sortField === "oldest") {
      sortOption = { createdAt: 1 }
    } else if (sortField === "alphabetical") {
      sortOption = { name: 1 }
    } else if (sortField.startsWith("-")) {
      sortOption = { [sortField.substring(1)]: -1 }
    } else {
      sortOption = { [sortField]: 1 }
    }
  }

  // Count total users
  const total = await User.countDocuments(query)

  // Get users
  const users = await User.find(query)
    .select("name username email avatar bio location userType skills joinDate isVerified companyName industry services resources companyDescription organizationName organizationType activitySector scope mainContact organizationDescription memberCount foundingYear contactPhone contactPhone2 contactEmail website address customResources supportDescription")
    .sort(sortOption)
    .skip((pageNumber - 1) * limitNumber)
    .limit(limitNumber)

  res.status(200).json({
    success: true,
    users,
    pagination: {
      total,
      currentPage: pageNumber,
      totalPages: Math.ceil(total / limitNumber),
      limit: limitNumber,
      hasNextPage: pageNumber < Math.ceil(total / limitNumber),
      hasPrevPage: pageNumber > 1
    },
  })
})

/**
 * Get user by ID
 * @route GET /api/users/:id
 * @access Public
 */
export const getUserById = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params

  // Validate user ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid user ID"))
  }

  // Find user
  const user = await User.findById(id)
    .select("-password -refreshToken -resetPasswordToken -resetPasswordExpires -verificationToken")
    .populate("badges", "name description icon category")

  if (!user) {
    return next(createError(404, "User not found"))
  }

  // Get user's initiatives
  const initiatives = await Initiative.find({ author: id })
    .select("_id title shortDescription location status supportCount commentCount viewCount mainImage createdAt")
    .sort({ createdAt: -1 })
    .limit(5)

  // Préparer la réponse en fonction du type d'utilisateur
  const userData: any = user.toObject();

  // Ajouter la liste des IDs d'initiatives pour faciliter la récupération des besoins de ressources
  userData.initiatives = initiatives.map(initiative => initiative._id);

  console.log("getUserById - User data before sending:", {
    id: userData._id,
    userType: userData.userType,
    hasSkills: !!userData.skills,
    skillsLength: userData.skills ? userData.skills.length : 0,
    skills: userData.skills
  });

  // Ajouter les champs spécifiques au type d'utilisateur
  if (userData.userType === 'volunteer') {
    // S'assurer que les champs pertinents pour les volontaires sont inclus
    userData.qualifications = userData.qualifications || [];
    userData.skills = userData.skills || [];
    userData.interests = userData.interests || [];
    userData.availability = userData.availability || "";
  } else if (userData.userType === 'proposer') {
    // S'assurer que les champs pertinents pour les proposeurs sont inclus
    userData.qualifications = userData.qualifications || [];
    userData.skills = userData.skills || [];
    userData.interests = userData.interests || [];
    userData.ideaDescription = userData.ideaDescription || "";
    userData.objectives = userData.objectives || "";
    userData.needs = userData.needs || "";
  } else if (userData.userType === 'company') {
    // S'assurer que les champs pertinents pour les entreprises sont inclus
    userData.companyName = userData.companyName || "";
    userData.industry = userData.industry || "";
    userData.services = userData.services || [];
    userData.resources = userData.resources || [];
  } else if (userData.userType === 'civilSociety') {
    // S'assurer que les champs pertinents pour les acteurs de la société civile sont inclus
    userData.organizationName = userData.organizationName || "";
    userData.organizationType = userData.organizationType || "";
    userData.activitySector = userData.activitySector || "";
    userData.scope = userData.scope || "";
    userData.approvalNumber = userData.approvalNumber || "";
    userData.memberCount = userData.memberCount || "";
    userData.foundingYear = userData.foundingYear || "";
    userData.organizationDescription = userData.organizationDescription || "";
    userData.contactPhone = userData.contactPhone || "";
    userData.contactPhone2 = userData.contactPhone2 || "";
    userData.contactEmail = userData.contactEmail || "";
    userData.website = userData.website || "";
    userData.address = userData.address || "";
    userData.services = userData.services || [];
    userData.resources = userData.resources || [];
    userData.customResources = userData.customResources || [];
    userData.supportDescription = userData.supportDescription || "";
    userData.mainContact = userData.mainContact || { name: "", phone: "", email: "" };
  }

  res.status(200).json({
    success: true,
    data: {
      user: userData,
      initiatives,
    },
  })
})

/**
 * Update user
 * @route PUT /api/users/:id
 * @access Private (Admin)
 */
export const updateUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const { name, email, username, role, isVerified, isBlocked } = req.body

  // Validate user ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid user ID"))
  }

  // Find user
  const user = await User.findById(id)
  if (!user) {
    return next(createError(404, "User not found"))
  }

  // Update user
  user.name = name || user.name
  user.email = email || user.email
  user.username = username || user.username
  user.role = role || user.role
  user.isVerified = isVerified !== undefined ? isVerified : user.isVerified
  user.isBlocked = isBlocked !== undefined ? isBlocked : user.isBlocked

  await user.save()

  res.status(200).json({
    success: true,
    data: {
      _id: user._id,
      name: user.name,
      email: user.email,
      username: user.username,
      role: user.role,
      isVerified: user.isVerified,
      isBlocked: user.isBlocked,
    },
  })
})

/**
 * Delete user
 * @route DELETE /api/users/:id
 * @access Private (Admin)
 */
export const deleteUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params

  // Validate user ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid user ID"))
  }

  // Find user
  const user = await User.findById(id)
  if (!user) {
    return next(createError(404, "User not found"))
  }

  // Delete user
  await user.deleteOne()

  res.status(200).json({
    success: true,
    message: "User deleted successfully",
  })
})

/**
 * Get user profile
 * @route GET /api/users/profile
 * @access Private
 */
export const getUserProfile = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user.id

  // Find user
  const user = await User.findById(userId)
    .select("-password -refreshToken -resetPasswordToken -resetPasswordExpires -verificationToken")
    .populate("badges", "name description icon category")

  if (!user) {
    return next(createError(404, "User not found"))
  }

  // Get user's initiatives
  const initiatives = await Initiative.find({ author: userId })
    .select("_id title shortDescription location status supportCount commentCount viewCount mainImage createdAt")
    .sort({ createdAt: -1 })

  // Get user's supported initiatives
  const supportedInitiatives = await Initiative.find({ _id: { $in: user.supportedInitiatives } })
    .select("_id title shortDescription location status supportCount commentCount viewCount mainImage createdAt")
    .sort({ createdAt: -1 })

  // Préparer la réponse en fonction du type d'utilisateur
  const userData: any = user.toObject();

  // Ajouter la liste des IDs d'initiatives pour faciliter la récupération des besoins de ressources
  userData.initiatives = initiatives.map(initiative => initiative._id);

  // Filtrer les champs en fonction du type d'utilisateur
  if (userData.userType === 'volunteer') {
    // Garder les champs pertinents pour les volontaires
  } else if (userData.userType === 'proposer') {
    // Garder les champs pertinents pour les proposeurs
  } else if (userData.userType === 'company') {
    // Garder les champs pertinents pour les entreprises
  }

  res.status(200).json({
    success: true,
    data: {
      user: userData,
      initiatives,
      supportedInitiatives,
    },
  })
})

/**
 * Update user profile
 * @route PUT /api/users/profile
 * @access Private
 */
export const updateUserProfile = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user.id
  const {
    name, username, bio, location, avatar, currentPassword, newPassword, socialLinks, settings,
    // Champs spécifiques pour les volontaires
    qualifications, skills, interests, availability,
    // Champs spécifiques pour les proposeurs d'idées
    ideaDescription, objectives, needs,
    // Champs spécifiques pour les entreprises
    companyName, industry, services, resources, customResources, supportDescription,
    // Champs spécifiques pour les acteurs de la société civile
    organizationName, organizationType, activitySector, scope, approvalNumber, memberCount, foundingYear,
    organizationDescription, contactPhone, contactPhone2, contactEmail, website, address, mainContact
  } = req.body

  // Find user
  const user = await User.findById(userId)
  if (!user) {
    return next(createError(404, "User not found"))
  }

  // Check if username is taken
  if (username && username !== user.username) {
    const existingUser = await User.findOne({ username })
    if (existingUser) {
      return next(createError(400, "Username is already taken"))
    }
  }

  // Update basic info
  user.name = name || user.name
  user.username = username || user.username
  user.bio = bio !== undefined ? bio : user.bio
  user.location = location !== undefined ? location : user.location
  user.avatar = avatar || user.avatar

  // Update social links if provided
  if (socialLinks) {
    user.socialLinks = {
      ...user.socialLinks,
      ...socialLinks,
    }
  }

  // Update settings if provided
  if (settings) {
    user.settings = {
      ...user.settings,
      ...settings,
    }
  }

  // Mettre à jour les champs spécifiques au type d'utilisateur
  if (user.userType === 'volunteer') {
    // Champs spécifiques pour les volontaires
    if (qualifications !== undefined) user.qualifications = qualifications;
    if (skills !== undefined) user.skills = skills;
    if (interests !== undefined) user.interests = interests;
    if (availability !== undefined) user.availability = availability;
  } else if (user.userType === 'proposer') {
    // Champs spécifiques pour les proposeurs d'idées
    if (ideaDescription !== undefined) user.ideaDescription = ideaDescription;
    if (objectives !== undefined) user.objectives = objectives;
    if (needs !== undefined) user.needs = needs;
  } else if (user.userType === 'company') {
    // Champs spécifiques pour les entreprises
    if (companyName !== undefined) user.companyName = companyName;
    if (industry !== undefined) user.industry = industry;
    if (services !== undefined) user.services = services;
    if (resources !== undefined) user.resources = resources;
    if (customResources !== undefined) user.customResources = customResources;
    if (supportDescription !== undefined) user.supportDescription = supportDescription;
    if (contactPhone !== undefined) user.contactPhone = contactPhone;
    if (contactEmail !== undefined) user.contactEmail = contactEmail;
    if (website !== undefined) user.website = website;
    if (address !== undefined) user.address = address;
  } else if (user.userType === 'civilSociety') {
    // Champs spécifiques pour les acteurs de la société civile
    if (organizationName !== undefined) user.organizationName = organizationName;
    if (organizationType !== undefined) user.organizationType = organizationType;
    if (activitySector !== undefined) user.activitySector = activitySector;
    if (scope !== undefined) user.scope = scope;
    if (approvalNumber !== undefined) user.approvalNumber = approvalNumber;
    if (memberCount !== undefined) user.memberCount = memberCount;
    if (foundingYear !== undefined) user.foundingYear = foundingYear;
    if (organizationDescription !== undefined) user.organizationDescription = organizationDescription;
    if (contactPhone !== undefined) user.contactPhone = contactPhone;
    if (contactPhone2 !== undefined) user.contactPhone2 = contactPhone2;
    if (contactEmail !== undefined) user.contactEmail = contactEmail;
    if (website !== undefined) user.website = website;
    if (address !== undefined) user.address = address;
    if (services !== undefined) user.services = services;
    if (resources !== undefined) user.resources = resources;
    if (customResources !== undefined) user.customResources = customResources;
    if (supportDescription !== undefined) user.supportDescription = supportDescription;
    if (mainContact !== undefined) user.mainContact = mainContact;
  }

  // Update password if provided
  if (currentPassword && newPassword) {
    // Check current password
    const isMatch = await bcrypt.compare(currentPassword, user.password)
    if (!isMatch) {
      return next(createError(400, "Current password is incorrect"))
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10)
    user.password = await bcrypt.hash(newPassword, salt)
  }

  await user.save()

  // Préparer la réponse en fonction du type d'utilisateur
  const responseData: any = {
    _id: user._id,
    name: user.name,
    email: user.email,
    username: user.username,
    avatar: user.avatar,
    bio: user.bio,
    location: user.location,
    role: user.role,
    userType: user.userType,
    socialLinks: user.socialLinks,
    settings: user.settings,
  };

  // Ajouter les champs spécifiques au type d'utilisateur
  if (user.userType === 'volunteer') {
    responseData.qualifications = user.qualifications;
    responseData.skills = user.skills;
    responseData.interests = user.interests;
    responseData.availability = user.availability;
  } else if (user.userType === 'proposer') {
    responseData.ideaDescription = user.ideaDescription;
    responseData.objectives = user.objectives;
    responseData.needs = user.needs;
  } else if (user.userType === 'company') {
    responseData.companyName = user.companyName;
    responseData.industry = user.industry;
    responseData.services = user.services;
    responseData.resources = user.resources;
  } else if (user.userType === 'civilSociety') {
    responseData.organizationName = user.organizationName;
    responseData.organizationType = user.organizationType;
    responseData.activitySector = user.activitySector;
    responseData.scope = user.scope;
    responseData.approvalNumber = user.approvalNumber;
    responseData.memberCount = user.memberCount;
    responseData.foundingYear = user.foundingYear;
    responseData.organizationDescription = user.organizationDescription;
    responseData.contactPhone = user.contactPhone;
    responseData.contactPhone2 = user.contactPhone2;
    responseData.contactEmail = user.contactEmail;
    responseData.website = user.website;
    responseData.address = user.address;
    responseData.services = user.services;
    responseData.resources = user.resources;
    responseData.customResources = user.customResources;
    responseData.supportDescription = user.supportDescription;
    responseData.mainContact = user.mainContact;
  }

  res.status(200).json({
    success: true,
    data: responseData,
  })
})

/**
 * Get user initiatives
 * @route GET /api/users/:id/initiatives
 * @access Public
 */
export const getUserInitiatives = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const { page = 1, limit = 10, status } = req.query
  const pageNumber = parseInt(page as string)
  const limitNumber = parseInt(limit as string)

  // Validate user ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid user ID"))
  }

  // Build query
  const query: any = { author: id }

  // Add status filter if provided
  if (status && ["pending", "active", "completed", "rejected"].includes(status as string)) {
    query.status = status
  }

  // Count total initiatives
  const total = await Initiative.countDocuments(query)

  // Get initiatives
  const initiatives = await Initiative.find(query)
    .select("title shortDescription location status supportCount commentCount viewCount mainImage createdAt category")
    .populate("category", "name arabicName color")
    .sort({ createdAt: -1 })
    .skip((pageNumber - 1) * limitNumber)
    .limit(limitNumber)

  res.status(200).json({
    success: true,
    initiatives,
    pagination: {
      total,
      page: pageNumber,
      limit: limitNumber,
      pages: Math.ceil(total / limitNumber),
    },
  })
})

/**
 * Get user supported initiatives
 * @route GET /api/users/:id/supported
 * @access Public
 */
export const getUserSupportedInitiatives = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const { page = 1, limit = 10 } = req.query
  const pageNumber = parseInt(page as string)
  const limitNumber = parseInt(limit as string)

  // Validate user ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid user ID"))
  }

  // Find user to get supported initiatives
  const user = await User.findById(id).select("supportedInitiatives")

  if (!user) {
    return next(createError(404, "User not found"))
  }

  // If user has no supported initiatives, return empty array
  if (!user.supportedInitiatives || user.supportedInitiatives.length === 0) {
    return res.status(200).json({
      success: true,
      initiatives: [],
      pagination: {
        total: 0,
        page: pageNumber,
        limit: limitNumber,
        pages: 0,
      },
    })
  }

  // Count total supported initiatives
  const total = await Initiative.countDocuments({ _id: { $in: user.supportedInitiatives } })

  // Find initiatives that user has supported
  const initiatives = await Initiative.find({ _id: { $in: user.supportedInitiatives } })
    .select("title shortDescription location status supportCount commentCount viewCount mainImage createdAt category author")
    .populate("category", "name arabicName color")
    .populate("author", "name username avatar")
    .sort({ createdAt: -1 })
    .skip((pageNumber - 1) * limitNumber)
    .limit(limitNumber)

  // Return initiatives
  res.status(200).json({
    success: true,
    initiatives,
    pagination: {
      total,
      page: pageNumber,
      limit: limitNumber,
      pages: Math.ceil(total / limitNumber),
    },
  })
})

/**
 * Delete user account
 * @route POST /api/users/delete-account
 * @access Private
 */
export const deleteAccount = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { password } = req.body

    if (!password) {
      return next(createError(400, "Password is required"))
    }

    // Get user from request (set by auth middleware)
    const userId = req.user.id

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Verify password
    const isMatch = await bcrypt.compare(password, user.password)
    if (!isMatch) {
      return next(createError(401, "Invalid password"))
    }

    // Start a transaction to ensure all operations succeed or fail together
    const session = await mongoose.startSession()
    session.startTransaction()

    try {
      // Delete user's initiatives
      await Initiative.deleteMany({ user: userId }, { session })

      // Delete user's comments (if you have a Comment model)
      // await Comment.deleteMany({ user: userId }, { session })

      // Delete user's activities (if you have an Activity model)
      // await Activity.deleteMany({ user: userId }, { session })

      // Delete user's badges
      await Badge.deleteMany({ user: userId }, { session })

      // Delete the user
      await User.findByIdAndDelete(userId, { session })

      // Commit the transaction
      await session.commitTransaction()
      session.endSession()

      res.status(200).json({
        success: true,
        message: "Account deleted successfully"
      })
    } catch (error) {
      // Abort the transaction on error
      await session.abortTransaction()
      session.endSession()
      throw error
    }
  } catch (error) {
    next(error)
  }
})

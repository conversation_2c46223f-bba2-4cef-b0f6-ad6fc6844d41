"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Info, Building, Briefcase, Users, MapPin, Globe, Phone, Mail, Link as LinkIcon, X } from "lucide-react"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface CivilSocietyTabContentProps {
  userSettings: any
  setUserSettings: (settings: any) => void
  handleArrayInput: (e: React.ChangeEvent<HTMLInputElement>, field: string) => void
}

// Liste des secteurs d'activité pour les acteurs de la société civile
const ACTIVITY_SECTORS = [
  { id: "education", name: "التعليم والتدريب" },
  { id: "health", name: "الصحة والرعاية الطبية" },
  { id: "environment", name: "البيئة والتنمية المستدامة" },
  { id: "culture", name: "الثقافة والفنون" },
  { id: "social", name: "التنمية الاجتماعية" },
  { id: "humanitarian", name: "العمل الإنساني والإغاثة" },
  { id: "human_rights", name: "حقوق الإنسان" },
  { id: "women", name: "تمكين المرأة" },
  { id: "youth", name: "تنمية الشباب" },
  { id: "children", name: "رعاية الطفولة" },
  { id: "disability", name: "ذوي الاحتياجات الخاصة" },
  { id: "sports", name: "الرياضة والترفيه" },
  { id: "religious", name: "الشؤون الدينية" },
  { id: "research", name: "البحث العلمي" },
  { id: "other", name: "أخرى" }
]

// Liste des types de ressources
const RESOURCE_TYPES = [
  { id: "financial", name: "دعم مالي", icon: "💰" },
  { id: "space", name: "مساحات عمل", icon: "🏢" },
  { id: "equipment", name: "معدات وتجهيزات", icon: "🔧" },
  { id: "expertise", name: "خبرات ومهارات", icon: "🧠" },
  { id: "mentorship", name: "إرشاد وتوجيه", icon: "👨‍🏫" },
  { id: "networking", name: "شبكة علاقات", icon: "🔗" },
  { id: "marketing", name: "تسويق وترويج", icon: "📢" },
  { id: "logistics", name: "خدمات لوجستية", icon: "🚚" },
  { id: "volunteers", name: "متطوعين", icon: "👥" },
  { id: "training", name: "تدريب", icon: "📚" }
]

export default function CivilSocietyTabContent({ 
  userSettings, 
  setUserSettings,
  handleArrayInput
}: CivilSocietyTabContentProps) {
  return (
    <Card>
      <CardHeader className="card-header">
        <CardTitle>أضف معلومات عن منظمتك أو جمعيتك</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 card-content">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="organizationProfile" className="text-lg font-medium">المعلومات الأساسية</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>أضف معلومات أساسية عن منظمتك أو جمعيتك لمساعدة المبادرات في التعرف عليك بشكل أفضل.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="organizationName" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                اسم الشريك الاجتماعي
              </Label>
              <Input
                id="organizationName"
                value={userSettings.organizationName || ''}
                onChange={(e) => setUserSettings({ ...userSettings, organizationName: e.target.value })}
                placeholder="أدخل اسم المنظمة أو الجمعية"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="activitySector" className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                مجال العمل
              </Label>
              <Select
                value={userSettings.activitySector || ''}
                onValueChange={(value) => setUserSettings({ ...userSettings, activitySector: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر مجال عمل المنظمة" />
                </SelectTrigger>
                <SelectContent>
                  {ACTIVITY_SECTORS.map(sector => (
                    <SelectItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="memberCount" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                عدد المنخرطين
              </Label>
              <Select
                value={userSettings.memberCount || ''}
                onValueChange={(value) => setUserSettings({ ...userSettings, memberCount: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر عدد المنخرطين" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-10">1-10 منخرطين</SelectItem>
                  <SelectItem value="11-50">11-50 منخرط</SelectItem>
                  <SelectItem value="51-200">51-200 منخرط</SelectItem>
                  <SelectItem value="201-500">201-500 منخرط</SelectItem>
                  <SelectItem value="501+">أكثر من 500 منخرط</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="foundingYear" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                سنة التأسيس
              </Label>
              <Input
                id="foundingYear"
                type="number"
                value={userSettings.foundingYear || ''}
                onChange={(e) => setUserSettings({ ...userSettings, foundingYear: e.target.value })}
                placeholder="مثال: 2010"
                dir="rtl"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="organizationDescription" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              نبذة عن الشريك الاجتماعي
            </Label>
            <Textarea
              id="organizationDescription"
              value={userSettings.organizationDescription || ''}
              onChange={(e) => setUserSettings({ ...userSettings, organizationDescription: e.target.value })}
              placeholder="اكتب نبذة مختصرة عن منظمتك أو جمعيتك ومجال عملها..."
              rows={4}
              dir="rtl"
            />
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <Label htmlFor="contactInfo" className="text-lg font-medium">معلومات الاتصال</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>أضف معلومات الاتصال الخاصة بمنظمتك لتسهيل التواصل معك من قبل المبادرات.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contactPhone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                رقم الهاتف
              </Label>
              <Input
                id="contactPhone"
                value={userSettings.contactPhone || ''}
                onChange={(e) => setUserSettings({ ...userSettings, contactPhone: e.target.value })}
                placeholder="أدخل رقم هاتف المنظمة"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactPhone2" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                رقم الهاتف
              </Label>
              <Input
                id="contactPhone2"
                value={userSettings.contactPhone2 || ''}
                onChange={(e) => setUserSettings({ ...userSettings, contactPhone2: e.target.value })}
                placeholder="أدخل رقم هاتف بديل (اختياري)"
                dir="rtl"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contactEmail" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                البريد الإلكتروني للتواصل
              </Label>
              <Input
                id="contactEmail"
                type="email"
                value={userSettings.contactEmail || ''}
                onChange={(e) => setUserSettings({ ...userSettings, contactEmail: e.target.value })}
                placeholder="أدخل البريد الإلكتروني للتواصل"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website" className="flex items-center gap-2">
                <LinkIcon className="h-4 w-4" />
                الموقع الإلكتروني
              </Label>
              <Input
                id="website"
                value={userSettings.website || ''}
                onChange={(e) => setUserSettings({ ...userSettings, website: e.target.value })}
                placeholder="https://example.com"
                dir="rtl"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              العنوان
            </Label>
            <Textarea
              id="address"
              value={userSettings.address || ''}
              onChange={(e) => setUserSettings({ ...userSettings, address: e.target.value })}
              placeholder="أدخل عنوان المنظمة أو الجمعية"
              rows={2}
              dir="rtl"
            />
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <Label htmlFor="services" className="text-lg font-medium">الخدمات والموارد</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>حدد الخدمات والموارد التي يمكن لمنظمتك تقديمها للمبادرات المختلفة.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="space-y-2">
            <Label htmlFor="services">الخدمات المقدمة</Label>
            <Input
              id="services"
              value={userSettings.services?.join(', ') || ''}
              onChange={(e) => handleArrayInput(e, 'services')}
              placeholder="أدخل الخدمات التي تقدمها مفصولة بفواصل"
              dir="rtl"
            />
            <p className="text-xs text-gray-500">مثال: تدريب، توعية، مساعدات اجتماعية</p>
          </div>

          <div className="space-y-4">
            <Label htmlFor="resources">الموارد المتاحة للمبادرات</Label>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {RESOURCE_TYPES.map(resource => (
                <Button
                  key={resource.id}
                  variant={userSettings.resources?.includes(resource.id) ? "default" : "outline"}
                  className={userSettings.resources?.includes(resource.id) ? "bg-[#0a8754]" : ""}
                  onClick={() => {
                    const currentResources = userSettings.resources || [];
                    if (currentResources.includes(resource.id)) {
                      setUserSettings({
                        ...userSettings,
                        resources: currentResources.filter((r: string) => r !== resource.id)
                      });
                    } else {
                      setUserSettings({
                        ...userSettings,
                        resources: [...currentResources, resource.id]
                      });
                    }
                  }}
                >
                  <span className="mr-1">{resource.icon}</span> {resource.name}
                </Button>
              ))}
            </div>
            
            <div className="space-y-2 mt-4">
              <Label htmlFor="customResources">موارد أخرى</Label>
              <Input
                id="customResources"
                value={userSettings.customResources?.join(', ') || ''}
                onChange={(e) => handleArrayInput(e, 'customResources')}
                placeholder="أدخل موارد أخرى مفصولة بفواصل"
                dir="rtl"
              />
              <p className="text-xs text-gray-500">أضف أي موارد أخرى غير مذكورة أعلاه</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="supportDescription">وصف الدعم الذي يمكن تقديمه</Label>
            <Textarea
              id="supportDescription"
              value={userSettings.supportDescription || ''}
              onChange={(e) => setUserSettings({ ...userSettings, supportDescription: e.target.value })}
              placeholder="اشرح كيف يمكن لمنظمتك أو جمعيتك دعم المبادرات المختلفة..."
              rows={4}
              dir="rtl"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

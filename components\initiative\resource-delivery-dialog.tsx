import { useState } from "react"
import { format } from "date-fns"
import { ar } from "date-fns/locale"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "../../lib/utils"
import { Button } from "../ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Calendar } from "../ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../ui/popover"
import { api } from "../../lib/api"

interface ResourceDeliveryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  resource: any
  onSuccess: () => void
}

export default function ResourceDeliveryDialog({
  open,
  onOpenChange,
  resource,
  onSuccess,
}: ResourceDeliveryDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [deliveredQuantity, setDeliveredQuantity] = useState<number>(resource?.quantity || 0)
  const [deliveryDate, setDeliveryDate] = useState<Date>(new Date())
  const [notes, setNotes] = useState<string>("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await api.put(`/api/resources/${resource._id}/status`, {
        status: "delivered",
        deliveredQuantity,
        deliveredDate: deliveryDate.toISOString(),
        deliveryNotes: notes
      }, true)

      if (response.success) {
        onSuccess()
      } else {
        throw new Error(response.message || "Failed to confirm delivery")
      }
    } catch (err: any) {
      console.error("Error confirming delivery:", err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]" dir="rtl">
        <DialogHeader>
          <DialogTitle>تأكيد استلام المورد</DialogTitle>
          <DialogDescription>
            يرجى تأكيد استلام المورد وتحديد الكمية المستلمة وتاريخ الاستلام
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 mt-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              اسم المورد
            </Label>
            <Input
              id="name"
              value={resource?.name || ""}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="quantity" className="text-right">
              الكمية المعروضة
            </Label>
            <Input
              id="quantity"
              value={`${resource?.quantity || 0} ${resource?.unit || ""}`}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="deliveredQuantity" className="text-right">
              الكمية المستلمة
            </Label>
            <Input
              id="deliveredQuantity"
              type="number"
              min={0}
              max={resource?.quantity || 0}
              value={deliveredQuantity}
              onChange={(e) => setDeliveredQuantity(Number(e.target.value))}
              className="col-span-3"
              required
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="deliveryDate" className="text-right">
              تاريخ الاستلام
            </Label>
            <div className="col-span-3">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-right font-normal",
                      !deliveryDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {deliveryDate ? (
                      format(deliveryDate, "PPP", { locale: ar })
                    ) : (
                      <span>اختر تاريخ</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={deliveryDate}
                    onSelect={(date) => setDeliveryDate(date || new Date())}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              ملاحظات
            </Label>
            <Input
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="col-span-3"
              placeholder="أي ملاحظات حول الاستلام"
            />
          </div>

          <DialogFooter className="flex justify-between sm:justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "جاري التأكيد..." : "تأكيد الاستلام"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

// Create a context to track API calls
const DebugContext = createContext<{
  apiCalls: { url: string; timestamp: number }[]
  addApiCall: (url: string) => void
  clearApiCalls: () => void
}>({
  apiCalls: [],
  addApiCall: () => {},
  clearApiCalls: () => {}
})

export const useDebug = () => useContext(DebugContext)

export function DebugProvider({ children }: { children: React.ReactNode }) {
  const [apiCalls, setApiCalls] = useState<{ url: string; timestamp: number }[]>([])

  const addApiCall = (url: string) => {
    setApiCalls(prev => [...prev, { url, timestamp: Date.now() }])
  }

  const clearApiCalls = () => {
    setApiCalls([])
  }

  // Log API calls to console when they change
  useEffect(() => {
    if (apiCalls.length > 0 && apiCalls.length % 10 === 0) {
      console.log(`API calls count: ${apiCalls.length}`)
      
      // Group by URL
      const grouped = apiCalls.reduce((acc, call) => {
        acc[call.url] = (acc[call.url] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      console.log('API calls by URL:', grouped)
      
      // Check for rapid calls (more than 5 calls to the same endpoint in 1 second)
      const now = Date.now()
      const recentCalls = apiCalls.filter(call => now - call.timestamp < 1000)
      
      if (recentCalls.length > 5) {
        console.warn('POTENTIAL INFINITE LOOP DETECTED: More than 5 API calls in the last second')
        console.warn('Recent calls:', recentCalls)
      }
    }
  }, [apiCalls])

  return (
    <DebugContext.Provider value={{ apiCalls, addApiCall, clearApiCalls }}>
      {children}
    </DebugContext.Provider>
  )
}

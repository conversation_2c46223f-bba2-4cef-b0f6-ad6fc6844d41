import express from "express"
import {
  getVotingOptionsByInitiative,
  createVotingOption,
  voteOnInitiative,
  getVoteResults,
} from "../controllers/vote.controller"
import { authenticate } from "../middleware/auth"
import { validateVotingOption } from "../middleware/validators/vote.validator"

const router = express.Router()

// Public routes
router.get("/initiative/:initiativeId", getVotingOptionsByInitiative)
router.get("/results/:initiativeId", getVoteResults)

// Protected routes
router.post("/options", authenticate, validateVotingOption, createVotingOption)
router.post("/:initiativeId", authenticate, voteOnInitiative)

export default router


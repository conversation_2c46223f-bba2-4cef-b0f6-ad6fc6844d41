import express from "express"
import { authenticate } from "../middleware/auth"
import {
  getAllBadges,
  getBadgeById,
  createBadge,
  updateBadge,
  deleteBadge,
  awardBadgeToUser,
  getUserBadges,
  getInitiativeBadges,
  getAllBadgeAwards
} from "../controllers/badge.controller"

const router = express.Router()

// Routes publiques
router.get("/", getAllBadges)
router.get("/awards", getAllBadgeAwards) // Route pour récupérer tous les badges attribués (pour le débogage)
router.get("/:id", getBadgeById)
router.get("/user/:userId", getUserBadges)

// Routes protégées
router.post("/", authenticate, createBadge)
router.put("/:id", authenticate, updateBadge)
router.delete("/:id", authenticate, deleteBadge)

export default router

import { Initiative, Milestone } from "../models";
import { logger } from "../utils/logger";

/**
 * Task to update all initiatives progress and status based on their milestones
 * This should be run periodically (e.g., daily) to ensure all initiatives have correct progress and status
 */
export const updateInitiativesProgress = async () => {
  try {
    logger.info("Starting initiative progress update task");
    
    // Get all initiatives
    const initiatives = await Initiative.find({});
    logger.info(`Found ${initiatives.length} initiatives to process`);

    let updatedCount = 0;
    let noChangeCount = 0;
    let errorCount = 0;

    // Process each initiative
    for (const initiative of initiatives) {
      try {
        // Get milestones for this initiative
        const milestones = await Milestone.find({ initiative: initiative._id });
        
        if (milestones.length === 0) {
          // Skip initiatives without milestones
          continue;
        }

        // Count completed milestones
        const completedMilestones = milestones.filter(m => m.isCompleted).length;

        // Calculate progress
        const progress = Math.round((completedMilestones / milestones.length) * 100);

        // Determine if update is needed
        let needsUpdate = false;
        let updateData: any = {};

        if (initiative.progress !== progress) {
          updateData.progress = progress;
          needsUpdate = true;
        }

        // Update status based on progress
        if (progress === 100 && initiative.status !== "completed") {
          updateData.status = "completed";
          needsUpdate = true;
        } else if (progress < 100 && progress > 0 && initiative.status === "completed") {
          updateData.status = "active";
          needsUpdate = true;
        }

        // Update initiative if needed
        if (needsUpdate) {
          await Initiative.findByIdAndUpdate(initiative._id, updateData);
          updatedCount++;
          logger.info(`Updated initiative ${initiative._id}: progress=${progress}, status=${updateData.status || initiative.status}`);
        } else {
          noChangeCount++;
        }
      } catch (err) {
        logger.error(`Error processing initiative ${initiative._id}:`, err);
        errorCount++;
      }
    }

    logger.info(`Task completed. Updated: ${updatedCount}, No change: ${noChangeCount}, Errors: ${errorCount}`);
    return { updatedCount, noChangeCount, errorCount };
  } catch (error) {
    logger.error("Error in initiative progress update task:", error);
    throw error;
  }
};

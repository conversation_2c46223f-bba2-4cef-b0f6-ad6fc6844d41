import express from "express"
import { authenticate } from "../middleware/auth"
import {
  getUserNotifications,
  mark<PERSON><PERSON><PERSON>,
  markAllAsRead,
  deleteNotification,
  getUnreadCount,
  deleteAllNotifications,
  deleteAllReadNotifications
} from "../controllers/notification.controller"
import { createTestNotifications, clearTestNotifications } from "../utils/seed-notifications"

const router = express.Router()

// All routes require authentication
router.use(authenticate)

router.get("/", getUserNotifications)
router.get("/unread-count", getUnreadCount)
router.patch("/:id/read", markAsRead)
router.patch("/read-all", markAllAsRead)
router.delete("/read-all", deleteAllReadNotifications)  // Changé de "/read" à "/read-all" pour éviter la confusion
router.delete("/:id", deleteNotification)
router.delete("/", deleteAllNotifications)

// Routes de test pour les notifications (uniquement en développement)
if (process.env.NODE_ENV !== 'production') {
  router.post("/test/create", async (req, res, next) => {
    try {
      const userId = req.user.id
      const count = req.body.count || 5

      const result = await createTestNotifications(userId, count)
      res.status(200).json(result)
    } catch (error) {
      next(error)
    }
  })

  router.delete("/test/clear", async (req, res, next) => {
    try {
      const userId = req.user.id
      const result = await clearTestNotifications(userId)
      res.status(200).json(result)
    } catch (error) {
      next(error)
    }
  })
}

export default router

import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Post, PostComment, Report } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"

// Get all posts
export const getAllPosts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Get posts
    const posts = await Post.find({ isHidden: false })
      .populate("author", "name username avatar")
      .populate("relatedInitiative", "title")
      .sort("-createdAt")
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Post.countDocuments({ isHidden: false })

    res.status(200).json({
      success: true,
      count: posts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      posts,
    })
  } catch (error) {
    next(error)
  }
}

// Get post by ID
export const getPostById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Find post
    const post = await Post.findOne({ _id: id, isHidden: false })
      .populate("author", "name username avatar")
      .populate("relatedInitiative", "title")

    if (!post) {
      return next(createError(404, "Post not found"))
    }

    res.status(200).json({
      success: true,
      post,
    })
  } catch (error) {
    next(error)
  }
}

// Create post
export const createPost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { content, images, relatedInitiative } = req.body
    const userId = req.user.id

    // Validate related initiative if provided
    if (relatedInitiative && !mongoose.Types.ObjectId.isValid(relatedInitiative)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Create post
    const post = new Post({
      author: userId,
      content,
      images: images || [],
      relatedInitiative: relatedInitiative || null,
    })

    await post.save()

    // Populate author details
    await post.populate("author", "name username avatar")

    // If related to an initiative, populate that too
    if (relatedInitiative) {
      await post.populate("relatedInitiative", "title")
    }

    res.status(201).json({
      success: true,
      message: "Post created successfully",
      post,
    })
  } catch (error) {
    next(error)
  }
}

// Update post
export const updatePost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { content, images } = req.body
    const userId = req.user.id

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Find post
    const post = await Post.findById(id)

    if (!post) {
      return next(createError(404, "Post not found"))
    }

    // Check if user is the author
    if (post.author.toString() !== userId && req.user.role !== "admin" && req.user.role !== "moderator") {
      return next(createError(403, "You are not authorized to update this post"))
    }

    // Update post
    if (content) post.content = content
    if (images) post.images = images

    await post.save()

    // Populate author details
    await post.populate("author", "name username avatar")

    // If related to an initiative, populate that too
    if (post.relatedInitiative) {
      await post.populate("relatedInitiative", "title")
    }

    res.status(200).json({
      success: true,
      message: "Post updated successfully",
      post,
    })
  } catch (error) {
    next(error)
  }
}

// Delete post
export const deletePost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Find post
    const post = await Post.findById(id)

    if (!post) {
      return next(createError(404, "Post not found"))
    }

    // Check if user is the author or admin/moderator
    if (post.author.toString() !== userId && req.user.role !== "admin" && req.user.role !== "moderator") {
      return next(createError(403, "You are not authorized to delete this post"))
    }

    // Delete all comments on the post
    await PostComment.deleteMany({ post: id })

    // Delete post
    await Post.findByIdAndDelete(id)

    res.status(200).json({
      success: true,
      message: "Post deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Like post
export const likePost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Find post
    const post = await Post.findById(id)

    if (!post) {
      return next(createError(404, "Post not found"))
    }

    // Check if user already liked the post
    if (post.likes.includes(userId)) {
      return next(createError(400, "You have already liked this post"))
    }

    // Add user to likes array and increment like count
    post.likes.push(userId)
    post.likeCount += 1
    await post.save()

    // Create notification for post author if it's not the same user
    if (post.author.toString() !== userId) {
      await createNotification({
        recipient: post.author,
        sender: userId,
        type: "like",
        content: "Someone liked your post",
        relatedPost: post._id,
        link: `/community`,
      })
    }

    res.status(200).json({
      success: true,
      message: "Post liked successfully",
      likeCount: post.likeCount,
    })
  } catch (error) {
    next(error)
  }
}

// Unlike post
export const unlikePost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Find post
    const post = await Post.findById(id)

    if (!post) {
      return next(createError(404, "Post not found"))
    }

    // Check if user has liked the post
    if (!post.likes.includes(userId)) {
      return next(createError(400, "You have not liked this post"))
    }

    // Remove user from likes array and decrement like count
    post.likes = post.likes.filter((id) => id.toString() !== userId)
    post.likeCount -= 1
    await post.save()

    res.status(200).json({
      success: true,
      message: "Post unliked successfully",
      likeCount: post.likeCount,
    })
  } catch (error) {
    next(error)
  }
}

// Get post comments
export const getPostComments = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Check if post exists
    const postExists = await Post.exists({ _id: id })
    if (!postExists) {
      return next(createError(404, "Post not found"))
    }

    // Get comments
    const comments = await PostComment.find({
      post: id,
      isHidden: false,
    })
      .populate("author", "name username avatar")
      .sort("-createdAt")

    res.status(200).json({
      success: true,
      count: comments.length,
      comments,
    })
  } catch (error) {
    next(error)
  }
}

// Create post comment
export const createPostComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { content } = req.body
    const userId = req.user.id

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Check if post exists
    const post = await Post.findById(id)
    if (!post) {
      return next(createError(404, "Post not found"))
    }

    // Create comment
    const comment = new PostComment({
      post: id,
      author: userId,
      content,
    })

    await comment.save()

    // Increment comment count on post
    post.commentCount += 1
    await post.save()

    // Populate author details
    await comment.populate("author", "name username avatar")

    // Create notification for post author if it's not the same user
    if (post.author.toString() !== userId) {
      await createNotification({
        recipient: post.author,
        sender: userId,
        type: "comment",
        content: "Someone commented on your post",
        relatedPost: post._id,
        link: `/community`,
      })
    }

    res.status(201).json({
      success: true,
      message: "Comment created successfully",
      comment,
    })
  } catch (error) {
    next(error)
  }
}

// Like post comment
export const likePostComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await PostComment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user already liked the comment
    if (comment.likes.includes(userId)) {
      return next(createError(400, "You have already liked this comment"))
    }

    // Add user to likes array and increment like count
    comment.likes.push(userId)
    comment.likeCount += 1
    await comment.save()

    // Create notification for comment author if it's not the same user
    if (comment.author.toString() !== userId) {
      await createNotification({
        recipient: comment.author,
        sender: userId,
        type: "like",
        content: "Someone liked your comment",
        relatedPost: comment.post,
        link: `/community`,
      })
    }

    res.status(200).json({
      success: true,
      message: "Comment liked successfully",
      likeCount: comment.likeCount,
    })
  } catch (error) {
    next(error)
  }
}

// Unlike post comment
export const unlikePostComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await PostComment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user has liked the comment
    if (!comment.likes.includes(userId)) {
      return next(createError(400, "You have not liked this comment"))
    }

    // Remove user from likes array and decrement like count
    comment.likes = comment.likes.filter((id) => id.toString() !== userId)
    comment.likeCount -= 1
    await comment.save()

    res.status(200).json({
      success: true,
      message: "Comment unliked successfully",
      likeCount: comment.likeCount,
    })
  } catch (error) {
    next(error)
  }
}

// Delete post comment
export const deletePostComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await PostComment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user is the author or admin/moderator
    if (comment.author.toString() !== userId && req.user.role !== "admin" && req.user.role !== "moderator") {
      return next(createError(403, "You are not authorized to delete this comment"))
    }

    // Get post to update comment count
    const post = await Post.findById(comment.post)

    // Delete comment
    await PostComment.findByIdAndDelete(id)

    // Decrement comment count on post if post exists
    if (post) {
      post.commentCount = Math.max(0, post.commentCount - 1)
      await post.save()
    }

    res.status(200).json({
      success: true,
      message: "Comment deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Report post
export const reportPost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { reason, description } = req.body
    const userId = req.user.id

    // Validate post ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid post ID"))
    }

    // Validate reason
    const validReasons = ["inappropriate", "spam", "offensive", "misleading", "other"]
    if (!validReasons.includes(reason)) {
      return next(createError(400, "Invalid reason"))
    }

    // Check if post exists
    const post = await Post.findById(id)
    if (!post) {
      return next(createError(404, "Post not found"))
    }

    // Check if user is reporting their own post
    if (post.author.toString() === userId) {
      return next(createError(400, "You cannot report your own post"))
    }

    // Create report in database
    const report = new Report({
      reporter: userId,
      type: "post",
      reason,
      description,
      relatedPost: id,
      status: "pending",
    })

    await report.save()

    // Mark post as reported
    post.isReported = true
    await post.save()

    res.status(200).json({
      success: true,
      message: "Post reported successfully",
    })
  } catch (error) {
    next(error)
  }
}


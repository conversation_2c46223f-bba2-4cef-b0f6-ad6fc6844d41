import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { SocialImpact } from "../models"
import { createError } from "../utils/error"

// Get all social impact categories with their impacts
export const getAllSocialImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get only active categories by default, unless specified otherwise
    const showInactive = req.query.showInactive === "true"
    const filter = showInactive ? {} : { isActive: true }

    // Get social impact categories
    const socialImpacts = await SocialImpact.find(filter).sort({ order: 1 })

    // Return social impact categories
    res.status(200).json({
      success: true,
      count: socialImpacts.length,
      socialImpacts,
    })
  } catch (error) {
    next(error)
  }
}

// Get social impact category by ID
export const getSocialImpactById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid social impact category ID"))
    }

    // Get social impact category
    const socialImpact = await SocialImpact.findById(id)

    // Check if social impact category exists
    if (!socialImpact) {
      return next(createError(404, "Social impact category not found"))
    }

    // Return social impact category
    res.status(200).json({
      success: true,
      socialImpact,
    })
  } catch (error) {
    next(error)
  }
}

// Create new social impact category
export const createSocialImpact = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Admin check is already done in the middleware
    // This function is only accessible to admins

    const { name, arabicName, description, arabicDescription, order, impacts } = req.body

    // Create social impact category
    const socialImpact = new SocialImpact({
      name,
      arabicName,
      description,
      arabicDescription,
      order,
      impacts: impacts || [],
    })

    // Save social impact category
    await socialImpact.save()

    // Return success response
    res.status(201).json({
      success: true,
      message: "Social impact category created successfully",
      socialImpact,
    })
  } catch (error) {
    next(error)
  }
}

// Update social impact category
export const updateSocialImpact = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Admin check is already done in the middleware
    // This function is only accessible to admins

    const { id } = req.params
    const updates = req.body

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid social impact category ID"))
    }

    // Get social impact category
    const socialImpact = await SocialImpact.findById(id)

    // Check if social impact category exists
    if (!socialImpact) {
      return next(createError(404, "Social impact category not found"))
    }

    // Update social impact category
    const updatedSocialImpact = await SocialImpact.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "Social impact category updated successfully",
      socialImpact: updatedSocialImpact,
    })
  } catch (error) {
    next(error)
  }
}

// Add impact to social impact category
export const addImpactToCategory = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Admin check is already done in the middleware
    // This function is only accessible to admins

    const { id } = req.params
    const { name, arabicName, description, arabicDescription } = req.body

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid social impact category ID"))
    }

    // Get social impact category
    const socialImpact = await SocialImpact.findById(id)

    // Check if social impact category exists
    if (!socialImpact) {
      return next(createError(404, "Social impact category not found"))
    }

    // Add impact to category
    socialImpact.impacts.push({
      name,
      arabicName,
      description,
      arabicDescription,
      isActive: true,
    })

    // Save social impact category
    await socialImpact.save()

    // Return success response
    res.status(200).json({
      success: true,
      message: "Impact added to category successfully",
      socialImpact,
    })
  } catch (error) {
    next(error)
  }
}

// Delete social impact category
export const deleteSocialImpact = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Admin check is already done in the middleware
    // This function is only accessible to admins

    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid social impact category ID"))
    }

    // Delete social impact category
    await SocialImpact.findByIdAndDelete(id)

    // Return success response
    res.status(200).json({
      success: true,
      message: "Social impact category deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed initial social impact categories and impacts
export const seedSocialImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is admin
    if (req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to seed social impact categories"))
    }

    // Check if social impact categories already exist
    const existingCount = await SocialImpact.countDocuments()
    if (existingCount > 0) {
      return next(createError(400, "Social impact categories already exist"))
    }

    // Define initial social impact categories and impacts
    const initialData = [
      {
        name: "Social Cohesion and Living Together",
        arabicName: "التماسك الاجتماعي والعيش المشترك",
        impacts: [
          {
            name: "Strengthening social bonds / interpersonal relationships",
            arabicName: "تعزيز الروابط الاجتماعية / العلاقات الشخصية",
          },
          {
            name: "Improving coexistence between different groups",
            arabicName: "تحسين التعايش بين مختلف المجموعات",
          },
          {
            name: "Reducing social isolation (elderly, isolated, etc.)",
            arabicName: "الحد من العزلة الاجتماعية (كبار السن، المعزولين، إلخ)",
          },
          {
            name: "Developing solidarity / community mutual aid",
            arabicName: "تطوير التضامن / المساعدة المتبادلة المجتمعية",
          },
          {
            name: "Creating / Strengthening spaces for meeting and dialogue",
            arabicName: "إنشاء / تعزيز مساحات للقاء والحوار",
          },
          {
            name: "Improving social climate in a neighborhood / community",
            arabicName: "تحسين المناخ الاجتماعي في حي / مجتمع",
          },
          {
            name: "Strengthening sense of community belonging",
            arabicName: "تعزيز الشعور بالانتماء للمجتمع",
          },
        ],
        order: 1,
      },
      // Les autres catégories seront ajoutées dans la fonction seedAdditionalSocialImpacts
    ]

    // Create social impact categories
    await SocialImpact.create(initialData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Initial social impact categories seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Seed additional social impact categories
export const seedAdditionalSocialImpacts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Admin check is already done in the middleware
    // This function is only accessible to admins

    // Define additional social impact categories and impacts
    const additionalData = [
      {
        name: "Inclusion, Equality and Rights",
        arabicName: "الشمول والمساواة والحقوق",
        impacts: [
          {
            name: "Reducing inequalities (social, economic, access to services...)",
            arabicName: "الحد من التفاوتات (الاجتماعية، الاقتصادية، الوصول إلى الخدمات...)",
          },
          {
            name: "Promoting inclusion of people with disabilities",
            arabicName: "تعزيز إدماج الأشخاص ذوي الإعاقة",
          },
          {
            name: "Promoting inclusion of minorities / vulnerable groups",
            arabicName: "تعزيز إدماج الأقليات / الفئات الضعيفة",
          },
          {
            name: "Fighting against discrimination (gender, origin, etc.)",
            arabicName: "مكافحة التمييز (الجنس، الأصل، إلخ)",
          },
          {
            name: "Improving access to fundamental rights (housing, health, education...)",
            arabicName: "تحسين الوصول إلى الحقوق الأساسية (السكن، الصحة، التعليم...)",
          },
          {
            name: "Promoting equal opportunities",
            arabicName: "تعزيز تكافؤ الفرص",
          },
          {
            name: "Integration of women / youth / marginalized groups",
            arabicName: "إدماج النساء / الشباب / الفئات المهمشة",
          },
        ],
        order: 2,
      },
      // Les autres catégories seront ajoutées dans d'autres fonctions
    ]

    // Create social impact categories
    await SocialImpact.create(additionalData)

    // Return success response
    res.status(201).json({
      success: true,
      message: "Additional social impact categories seeded successfully",
    })
  } catch (error) {
    next(error)
  }
}

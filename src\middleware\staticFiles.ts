import express from 'express';
import path from 'path';

// Middleware to add CORS and CORP headers to static files
const addCorsHeaders = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Add CORS headers
  const origin = req.headers.origin;
  if (origin && (origin === 'http://localhost:3000' || origin === 'http://localhost:3001' || origin === 'http://localhost:3002')) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    // Allow any origin in development mode
    res.header('Access-Control-Allow-Origin', '*');
  }
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With');

  // Add Cross-Origin-Resource-Policy header
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  // Add Cross-Origin-Embedder-Policy header
  res.header('Cross-Origin-Embedder-Policy', 'credentialless');

  next();
};

// Create a custom static middleware that adds CORS headers
const staticWithCors = (root: string) => {
  return [
    addCorsHeaders,
    express.static(root, {
      setHeaders: (res) => {
        res.set('Cross-Origin-Resource-Policy', 'cross-origin');
        res.set('Cross-Origin-Embedder-Policy', 'credentialless');
      }
    })
  ];
};

// Serve static files from the public directory
export const serveStaticFiles = (app: express.Application) => {
  // Serve files from the public directory with CORS headers
  app.use(staticWithCors(path.join(process.cwd(), 'public')));

  // Serve files from the uploads directory directly with CORS headers
  app.use('/uploads', staticWithCors(path.join(process.cwd(), 'public/uploads')));

  // Serve files from the uploads directory (for backward compatibility) with CORS headers
  const uploadDir = process.env.UPLOAD_DIR || 'uploads';
  app.use('/api/uploads', staticWithCors(path.join(process.cwd(), uploadDir)));
};

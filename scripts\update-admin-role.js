const { MongoClient } = require('mongodb');

async function updateAdminRole() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('initiatives_dz');
    const usersCollection = db.collection('users');
    
    // Find the admin user
    const adminUser = await usersCollection.findOne({ username: 'admin' });
    console.log('Admin user found:', adminUser);
    
    if (adminUser) {
      // Update the role to 'admin'
      const result = await usersCollection.updateOne(
        { username: 'admin' },
        { $set: { role: 'admin' } }
      );
      
      console.log('Update result:', result);
    } else {
      console.log('Admin user not found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

updateAdminRole();

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Image from "next/image"
import { But<PERSON> } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "../../components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "../../components/ui/dialog"
import { Switch } from "../../components/ui/switch"
import { Label } from "../../components/ui/label"
import { Alert, AlertDescription } from "../../components/ui/alert"
import { Skeleton } from "../../components/ui/skeleton"
import { toast } from "../../components/ui/use-toast"
import { Toaster } from "../../components/ui/toaster"
import { api } from "../../lib/api"
import { useAuth } from "../../components/auth-provider"
import { 
  AlertCircle, 
  Loader2, 
  Plus, 
  Pencil, 
  Trash2, 
  Upload,
  XCircle,
  Info
} from "lucide-react"
import { uploadFile } from "../../lib/fileUpload"

// Banner type definition
interface Banner {
  _id: string
  image: string
  mainText: string
  subText: string
  mainTextColor: string
  subTextColor: string
  order: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function AdminBannersPage() {
  const router = useRouter()
  const { isAuthenticated, isAdmin } = useAuth()

  // State for banners
  const [banners, setBanners] = useState<Banner[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  // State for banner form
  const [showBannerForm, setShowBannerForm] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [selectedBanner, setSelectedBanner] = useState<Banner | null>(null)
  const [formData, setFormData] = useState({
    image: "",
    mainText: "",
    subText: "",
    mainTextColor: "#FFFFFF",
    subTextColor: "#FFFFFF",
    order: 0,
    isActive: true,
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // State for delete confirmation
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [bannerToDelete, setBannerToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Check authentication
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    } else if (!isLoading && !isAdmin) {
      router.push("/")
    }
  }, [isAuthenticated, isAdmin, isLoading, router])

  // Fetch banners
  useEffect(() => {
    const fetchBanners = async () => {
      setIsLoading(true)
      setError("")

      try {
        const response = await api.get(`/api/banners`, true)

        if (response.success) {
          setBanners(response.banners || [])
        } else {
          setError(response.message || "Failed to fetch banners")
        }
      } catch (err: any) {
        console.error("Error fetching banners:", err)
        setError(err.message || "An error occurred while fetching banners")
      } finally {
        setIsLoading(false)
      }
    }

    if (isAuthenticated && isAdmin) {
      fetchBanners()
    }
  }, [isAuthenticated, isAdmin])

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    })
  }

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setImageFile(file)
      
      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle banner creation/update
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError("")

    try {
      // Upload image if a new one is selected
      let imageUrl = formData.image
      if (imageFile) {
        try {
          console.log('Uploading banner image...')
          const uploadData = await uploadFile("/api/public-upload", imageFile)
          
          if (uploadData && uploadData.file && uploadData.file.url) {
            imageUrl = uploadData.file.url
            console.log('Image uploaded successfully:', imageUrl)
          } else {
            throw new Error("Invalid response format from upload API")
          }
        } catch (error) {
          console.error("Error uploading image:", error)
          toast({
            title: "Image Upload Failed",
            description: error instanceof Error ? error.message : "Could not upload the image. Please try again.",
            variant: "destructive",
          })
          setIsSubmitting(false)
          return
        }
      }

      const bannerData = {
        ...formData,
        image: imageUrl,
      }

      let response
      if (isEditMode && selectedBanner) {
        response = await api.put(`/api/banners/${selectedBanner._id}`, bannerData)
      } else {
        response = await api.post("/api/banners", bannerData)
      }

      if (response.success) {
        toast({
          title: isEditMode ? "Banner Updated" : "Banner Created",
          description: isEditMode ? "Banner has been updated successfully." : "Banner has been created successfully.",
        })

        // Refresh banners list
        const bannersResponse = await api.get(`/api/banners`, true)
        if (bannersResponse.success) {
          setBanners(bannersResponse.banners || [])
        }

        // Reset form
        resetForm()
      } else {
        setError(response.message || `Failed to ${isEditMode ? "update" : "create"} banner`)
      }
    } catch (err: any) {
      console.error(`Error ${isEditMode ? "updating" : "creating"} banner:`, err)
      setError(err.message || `An error occurred while ${isEditMode ? "updating" : "creating"} the banner`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle banner deletion
  const handleDeleteBanner = async () => {
    if (!bannerToDelete) return

    setIsDeleting(true)

    try {
      const response = await api.delete(`/api/banners/${bannerToDelete}`)

      if (response.success) {
        toast({
          title: "Banner Deleted",
          description: "Banner has been deleted successfully.",
        })

        // Remove banner from list
        setBanners((prev) => prev.filter((banner) => banner._id !== bannerToDelete))
        setShowDeleteDialog(false)
      } else {
        setError(response.message || "Failed to delete banner")
      }
    } catch (err: any) {
      console.error("Error deleting banner:", err)
      setError(err.message || "An error occurred while deleting the banner")
    } finally {
      setIsDeleting(false)
      setBannerToDelete(null)
    }
  }

  // Handle banner status toggle
  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      const response = await api.put(`/api/banners/${id}/toggle`, {})

      if (response.success) {
        // Update banner in list
        setBanners((prev) =>
          prev.map((banner) =>
            banner._id === id ? { ...banner, isActive: !currentStatus } : banner
          )
        )

        toast({
          title: "Banner Status Updated",
          description: `Banner is now ${!currentStatus ? "active" : "inactive"}.`,
        })
      } else {
        setError(response.message || "Failed to update banner status")
      }
    } catch (err: any) {
      console.error("Error updating banner status:", err)
      setError(err.message || "An error occurred while updating the banner status")
    }
  }

  // Edit banner
  const handleEditBanner = (banner: Banner) => {
    setSelectedBanner(banner)
    setFormData({
      image: banner.image,
      mainText: banner.mainText,
      subText: banner.subText,
      mainTextColor: banner.mainTextColor,
      subTextColor: banner.subTextColor,
      order: banner.order,
      isActive: banner.isActive,
    })
    setImagePreview(banner.image)
    setIsEditMode(true)
    setShowBannerForm(true)
  }

  // Reset form
  const resetForm = () => {
    setFormData({
      image: "",
      mainText: "",
      subText: "",
      mainTextColor: "#FFFFFF",
      subTextColor: "#FFFFFF",
      order: 0,
      isActive: true,
    })
    setImageFile(null)
    setImagePreview("")
    setSelectedBanner(null)
    setIsEditMode(false)
    setShowBannerForm(false)
  }

  // Banner preview component
  const BannerPreview = () => (
    <div className="relative w-full h-[200px] rounded-md overflow-hidden mb-4">
      <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/40 z-10"></div>
      <img
        src={imagePreview || formData.image || "/placeholder.svg?height=200&width=800"}
        alt="Banner preview"
        className="w-full h-full object-cover"
      />
      <div className="absolute inset-0 flex flex-col items-center justify-center z-20 p-4">
        <h1 
          className="text-2xl md:text-3xl font-bold mb-2"
          style={{ color: formData.mainTextColor }}
        >
          {formData.mainText || "Main Text"}
        </h1>
        <p 
          className="text-sm md:text-base opacity-90"
          style={{ color: formData.subTextColor }}
        >
          {formData.subText || "Sub Text"}
        </p>
      </div>
    </div>
  )

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Banner Management</CardTitle>
            <CardDescription>Manage homepage banner carousel</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Banner Management</CardTitle>
            <CardDescription>Manage homepage banner carousel</CardDescription>
          </div>
          <Button onClick={() => setShowBannerForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Banner
          </Button>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Alert className="mb-6 bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-500" />
            <AlertDescription className="text-blue-700">
              Banners will be displayed in the homepage carousel. You can add multiple banners and they will rotate automatically.
            </AlertDescription>
          </Alert>

          {banners.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No banners found</p>
              <Button onClick={() => setShowBannerForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Banner
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {banners.map((banner) => (
                <Card key={banner._id} className="overflow-hidden">
                  <div className="relative h-48">
                    <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-transparent z-10"></div>
                    <Image
                      src={banner.image || "/placeholder.svg"}
                      alt={banner.mainText}
                      fill
                      style={{ objectFit: "cover" }}
                    />
                    <div className="absolute top-2 right-2 z-20">
                      <Switch
                        checked={banner.isActive}
                        onCheckedChange={() => handleToggleStatus(banner._id, banner.isActive)}
                      />
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1 truncate">{banner.mainText}</h3>
                    <p className="text-sm text-gray-500 mb-4 line-clamp-2">{banner.subText}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Order: {banner.order}</span>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditBanner(banner)}
                        >
                          <Pencil className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 border-red-600 hover:bg-red-50"
                          onClick={() => {
                            setBannerToDelete(banner._id)
                            setShowDeleteDialog(true)
                          }}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Banner Form Dialog */}
      <Dialog open={showBannerForm} onOpenChange={(open) => !open && resetForm()}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{isEditMode ? "Edit Banner" : "Add New Banner"}</DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update the banner details below."
                : "Fill in the details below to create a new banner."}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Banner Preview */}
            <div className="space-y-2">
              <Label>Banner Preview</Label>
              <BannerPreview />
            </div>

            {/* Image Upload */}
            <div className="space-y-2">
              <Label htmlFor="image">Banner Image</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {imagePreview ? (
                  <div className="mb-4">
                    <img
                      src={imagePreview || "/placeholder.svg"}
                      alt="Image Preview"
                      className="max-h-[200px] mx-auto rounded-lg"
                    />
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-4">
                    <Upload className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-500 mb-1">Drag and drop image here or click to select</p>
                    <p className="text-gray-400 text-sm">PNG, JPG, JPEG (Max: 5MB)</p>
                  </div>
                )}
                <input
                  type="file"
                  id="banner-image"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById("banner-image")?.click()}
                  className="mt-4"
                >
                  {imagePreview ? "Change Image" : "Select Image"}
                </Button>
              </div>
              {!imagePreview && !formData.image && !isEditMode && (
                <p className="text-sm text-red-500">Banner image is required</p>
              )}
            </div>

            {/* Main Text */}
            <div className="space-y-2">
              <Label htmlFor="mainText">Main Text</Label>
              <Input
                id="mainText"
                name="mainText"
                value={formData.mainText}
                onChange={handleInputChange}
                placeholder="Enter main text"
                required
              />
            </div>

            {/* Sub Text */}
            <div className="space-y-2">
              <Label htmlFor="subText">Sub Text</Label>
              <Input
                id="subText"
                name="subText"
                value={formData.subText}
                onChange={handleInputChange}
                placeholder="Enter sub text"
                required
              />
            </div>

            {/* Text Colors */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="mainTextColor">Main Text Color</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="mainTextColor"
                    name="mainTextColor"
                    type="color"
                    value={formData.mainTextColor}
                    onChange={handleInputChange}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    type="text"
                    value={formData.mainTextColor}
                    onChange={handleInputChange}
                    name="mainTextColor"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subTextColor">Sub Text Color</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="subTextColor"
                    name="subTextColor"
                    type="color"
                    value={formData.subTextColor}
                    onChange={handleInputChange}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    type="text"
                    value={formData.subTextColor}
                    onChange={handleInputChange}
                    name="subTextColor"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>
            </div>

            {/* Order and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="order">Display Order</Label>
                <Input
                  id="order"
                  name="order"
                  type="number"
                  value={formData.order}
                  onChange={handleInputChange}
                  min={0}
                />
                <p className="text-xs text-gray-500">Lower numbers appear first</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="isActive" className="block mb-2">Status</Label>
                <div className="flex items-center gap-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) =>
                      setFormData({ ...formData, isActive: checked })
                    }
                  />
                  <Label htmlFor="isActive">
                    {formData.isActive ? "Active" : "Inactive"}
                  </Label>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetForm()
                  setShowBannerForm(false)
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || (!imagePreview && !formData.image && !isEditMode)}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditMode ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    {isEditMode ? "Update Banner" : "Create Banner"}
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Banner</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this banner? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteBanner}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
  )
}

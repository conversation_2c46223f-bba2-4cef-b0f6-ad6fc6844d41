import { Schema } from "mongoose"

const categorySchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    arabicName: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    color: {
      type: String,
      default: "#0a8754",
    },
    icon: {
      type: String,
    },
    initiativeCount: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true },
)

export default categorySchema


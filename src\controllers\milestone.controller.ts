import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Milestone, Initiative } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { updateInitiativeProgress } from "../utils/initiative-progress"
import { ActivityService } from "../services/activity.service"

// Get milestones by initiative
export const getMilestonesByInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiativeExists = await Initiative.exists({ _id: initiativeId })
    if (!initiativeExists) {
      return next(createError(404, "Initiative not found"))
    }

    // Get milestones
    const milestones = await Milestone.find({ initiative: initiativeId }).sort("order")

    res.status(200).json({
      success: true,
      count: milestones.length,
      milestones,
    })
  } catch (error) {
    next(error)
  }
}

// Create milestone
export const createMilestone = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiative, title, description, targetDate, order } = req.body
    const userId = req.user.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiative)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiativeDoc = await Initiative.findById(initiative)
    if (!initiativeDoc) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author of the initiative or an admin
    if (initiativeDoc.author.toString() !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to add milestones to this initiative"))
    }

    // Create milestone
    const milestone = new Milestone({
      initiative,
      title,
      description,
      targetDate,
      order: order || (await Milestone.countDocuments({ initiative })) + 1,
    })

    await milestone.save()

    // Update initiative progress
    await updateInitiativeProgress(initiative)

    res.status(201).json({
      success: true,
      message: "Milestone created successfully",
      milestone,
    })
  } catch (error) {
    next(error)
  }
}

// Update milestone
export const updateMilestone = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { title, description, targetDate, isCompleted, completedDate, order } = req.body
    const userId = req.user.id

    // Validate milestone ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid milestone ID"))
    }

    // Find milestone
    const milestone = await Milestone.findById(id)

    if (!milestone) {
      return next(createError(404, "Milestone not found"))
    }

    // Get initiative
    const initiative = await Initiative.findById(milestone.initiative)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author of the initiative or an admin
    if (initiative.author.toString() !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to update this milestone"))
    }

    // Update fields
    if (title) milestone.title = title
    if (description !== undefined) milestone.description = description
    if (targetDate) milestone.targetDate = targetDate
    if (order) milestone.order = order

    // Handle completion status
    if (isCompleted !== undefined) {
      const previousStatus = milestone.isCompleted
      milestone.isCompleted = isCompleted

      // If marking as completed and wasn't completed before
      if (isCompleted && !previousStatus) {
        milestone.completedDate = completedDate || new Date()

        // Create notifications for supporters
        for (const supporterId of initiative.supporters) {
          // Skip notification for the initiative author
          if (supporterId.toString() === initiative.author.toString()) continue

          await createNotification({
            recipient: supporterId,
            sender: userId,
            type: "milestone",
            content: `Milestone completed for "${initiative.title}": ${milestone.title}`,
            relatedInitiative: initiative._id,
            link: `/initiatives/${initiative._id}`,
          })
        }
      }
      // If marking as incomplete and was completed before
      else if (!isCompleted && previousStatus) {
        milestone.completedDate = undefined
      }
    }

    await milestone.save()

    // Update initiative progress
    await updateInitiativeProgress(initiative._id.toString())

    // Enregistrer l'activité si le jalon a été marqué comme complété ou incomplet
    if (isCompleted !== undefined) {
      const action = isCompleted ? "complete" : "update";
      await ActivityService.initiativeActivity(
        userId,
        action,
        initiative._id.toString(),
        initiative.title,
        {
          milestoneTitle: milestone.title,
          milestoneId: milestone._id.toString(),
          milestoneDescription: milestone.description,
          milestoneOrder: milestone.order,
          isCompleted,
          completedDate: milestone.completedDate
        }
      );
    }

    res.status(200).json({
      success: true,
      message: "Milestone updated successfully",
      milestone,
    })
  } catch (error) {
    next(error)
  }
}

// Delete milestone
export const deleteMilestone = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate milestone ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid milestone ID"))
    }

    // Find milestone
    const milestone = await Milestone.findById(id)

    if (!milestone) {
      return next(createError(404, "Milestone not found"))
    }

    // Get initiative
    const initiative = await Initiative.findById(milestone.initiative)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author of the initiative or an admin
    if (initiative.author.toString() !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to delete this milestone"))
    }

    // Delete milestone
    await Milestone.findByIdAndDelete(id)

    // Reorder remaining milestones
    const remainingMilestones = await Milestone.find({
      initiative: milestone.initiative,
    }).sort("order")

    for (let i = 0; i < remainingMilestones.length; i++) {
      await Milestone.findByIdAndUpdate(remainingMilestones[i]._id, { order: i + 1 })
    }

    // Update initiative progress
    await updateInitiativeProgress(initiative._id.toString())

    res.status(200).json({
      success: true,
      message: "Milestone deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Get milestone by ID
export const getMilestoneById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate milestone ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid milestone ID"))
    }

    // Find milestone
    const milestone = await Milestone.findById(id)

    if (!milestone) {
      return next(createError(404, "Milestone not found"))
    }

    res.status(200).json({
      success: true,
      milestone,
    })
  } catch (error) {
    next(error)
  }
}


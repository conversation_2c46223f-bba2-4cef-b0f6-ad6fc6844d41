"use client"

import React, { useEffect, useState, useRef } from 'react'
import Link from 'next/link'
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone } from 'lucide-react'
import { api } from '@/lib/api'

type Settings = {
  siteName: string
  siteDescription: string
  contactEmail: string
  supportPhone: string
  socialLinks: {
    facebook: string
    twitter: string
    instagram: string
    linkedin: string
  }
}

const Footer = () => {
  const [settings, setSettings] = useState<Settings>({
    siteName: 'منصة المبادرات',
    siteDescription: 'منصة لإدارة ومتابعة المبادرات المجتمعية',
    contactEmail: '<EMAIL>',
    supportPhone: '+213 000 000 000',
    socialLinks: {
      facebook: '',
      twitter: '',
      instagram: '',
      linkedin: ''
    }
  })

  // Use a ref to track if settings have been fetched
  const settingsFetchedRef = useRef(false)

  useEffect(() => {
    // Only fetch settings once
    if (!settingsFetchedRef.current) {
      const fetchSettings = async () => {
        try {
          const response = await api.get('/api/settings/public', false)

          if (response && response.success && response.settings) {
            setSettings(response.settings)
          }
          // Mark settings as fetched
          settingsFetchedRef.current = true
        } catch (error) {
          console.error('Error fetching settings:', error)
        }
      }

      fetchSettings()
    }
  }, [])

  return (
    <footer className="bg-gray-900 text-white" dir="rtl">
      <div className="max-w-6xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-1">
            <h2 className="text-2xl font-bold mb-4">{settings.siteName}</h2>
            <p className="text-gray-400 mb-6">{settings.siteDescription}</p>
            <div className="flex space-x-4 space-x-reverse">
              {settings.socialLinks.facebook && (
                <a href={settings.socialLinks.facebook} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                  <Facebook size={20} />
                </a>
              )}
              {settings.socialLinks.twitter && (
                <a href={settings.socialLinks.twitter} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                  <Twitter size={20} />
                </a>
              )}
              {settings.socialLinks.instagram && (
                <a href={settings.socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                  <Instagram size={20} />
                </a>
              )}
              {settings.socialLinks.linkedin && (
                <a href={settings.socialLinks.linkedin} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
                  <Linkedin size={20} />
                </a>
              )}
            </div>
          </div>

          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4">روابط سريعة</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white">الرئيسية</Link>
              </li>
              <li>
                <Link href="/initiatives" className="text-gray-400 hover:text-white">المبادرات</Link>
              </li>
              <li>
                <Link href="/progress" className="text-gray-400 hover:text-white">متابعة التقدم</Link>
              </li>
              <li>
                <Link href="/community" className="text-gray-400 hover:text-white">المجتمع</Link>
              </li>
            </ul>
          </div>

          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4">معلومات</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-400 hover:text-white">من نحن</Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-400 hover:text-white">الشروط والأحكام</Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-400 hover:text-white">سياسة الخصوصية</Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white">اتصل بنا</Link>
              </li>
            </ul>
          </div>

          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4">اتصل بنا</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail size={18} className="ml-2 text-gray-400" />
                <a href={`mailto:${settings.contactEmail}`} className="text-gray-400 hover:text-white">
                  {settings.contactEmail}
                </a>
              </div>
              <div className="flex items-center">
                <Phone size={18} className="ml-2 text-gray-400" />
                <a href={`tel:${settings.supportPhone}`} className="text-gray-400 hover:text-white">
                  {settings.supportPhone}
                </a>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
          <p>© {new Date().getFullYear()} {settings.siteName}. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer

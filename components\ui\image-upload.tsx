"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { Button } from "./button"
import { Input } from "./input"
import { api } from "@/lib/api"
import { toast } from "@/components/ui/use-toast"

interface ImageUploadProps {
  value: string
  onChange: (url: string) => void
}

export function ImageUpload({ value, onChange }: ImageUploadProps) {
  const [isLoading, setIsLoading] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    try {
      setIsLoading(true)
      
      const formData = new FormData()
      formData.append("file", file)
      
      const response = await api.post("/api/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      })

      onChange(response.url)
      toast({
        title: "Succès",
        description: "L'image a été uploadée avec succès",
      })
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'upload",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }, [onChange])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".webp"]
    },
    maxFiles: 1,
    disabled: isLoading
  })

  return (
    <div className="space-y-4">
      {value ? (
        <div className="relative group">
          <img
            src={value}
            alt="Preview"
            className="w-full h-48 object-cover rounded-md"
          />
          <Button
            variant="destructive"
            size="sm"
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={() => onChange("")}
            disabled={isLoading}
          >
            Supprimer
          </Button>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer ${
            isDragActive ? "border-primary" : "border-muted"
          }`}
        >
          <input {...getInputProps()} />
          {isLoading ? (
            <p>Upload en cours...</p>
          ) : isDragActive ? (
            <p>Déposez l'image ici...</p>
          ) : (
            <p>
              Glissez-déposez une image ici, ou cliquez pour sélectionner
            </p>
          )}
        </div>
      )}
      {value && (
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Ou entrez une URL d'image"
        />
      )}
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Textarea } from "../ui/textarea"
import { Label } from "../ui/label"
import { Checkbox } from "../ui/checkbox"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "../ui/accordion"
import { Loader2 } from "lucide-react"
import { api } from "../../lib/api"

interface SocialImpactTabProps {
  formData: any
  updateFormData: (data: any) => void
  isSubmitting: boolean
}

interface ImpactItem {
  _id: string
  name: string
  arabicName: string
  description?: string
  arabicDescription?: string
  isActive: boolean
}

interface SocialImpactCategory {
  _id: string
  name: string
  arabicName: string
  description?: string
  arabicDescription?: string
  order: number
  isActive: boolean
  impacts: ImpactItem[]
}

export default function SocialImpactTab({ formData, updateFormData, isSubmitting }: SocialImpactTabProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [socialImpactCategories, setSocialImpactCategories] = useState<SocialImpactCategory[]>([])
  const [selectedImpacts, setSelectedImpacts] = useState<{ [key: string]: boolean }>({})

  // Fetch social impact categories when component mounts
  useEffect(() => {
    const fetchSocialImpacts = async () => {
      try {
        setIsLoading(true)
        const response = await api.get('/api/social-impacts')

        if (response && response.socialImpacts && Array.isArray(response.socialImpacts)) {
          setSocialImpactCategories(response.socialImpacts)

          // Initialize selected impacts from formData if available
          if (formData.selectedImpacts && Array.isArray(formData.selectedImpacts)) {
            const initialSelectedImpacts: { [key: string]: boolean } = {}
            formData.selectedImpacts.forEach((impact: any) => {
              if (impact._id) {
                initialSelectedImpacts[impact._id] = true
              }
            })
            setSelectedImpacts(initialSelectedImpacts)
          }
        } else {
          console.error('Unexpected social impacts response format:', response)
          setSocialImpactCategories([])
        }
      } catch (error) {
        console.error('Error fetching social impacts:', error)
        setError('Failed to load social impact categories. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchSocialImpacts()
  }, [formData.selectedImpacts])

  // Handle text field changes
  const handleTextChange = (field: string, value: string) => {
    updateFormData({ ...formData, [field]: value })
  }

  // Handle impact selection
  const handleImpactChange = (impact: ImpactItem, isChecked: boolean) => {
    // Update selected impacts state
    setSelectedImpacts((prev) => ({
      ...prev,
      [impact._id]: isChecked,
    }))

    // Update form data with selected impacts
    const updatedSelectedImpacts = isChecked
      ? [...(formData.selectedImpacts || []), impact]
      : (formData.selectedImpacts || []).filter((item: any) => item._id !== impact._id)

    updateFormData({
      ...formData,
      selectedImpacts: updatedSelectedImpacts,
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12" dir="rtl">
        <span className="ml-2">جاري تحميل بيانات التأثير الاجتماعي...</span>
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 text-red-500 text-right" dir="rtl">
        <p>{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-8" dir="rtl">
      <div className="space-y-4">
        <div className="text-right">
          <Label htmlFor="problem" className="text-lg font-medium text-right block">المشكلة / الفرصة</Label>
          <p className="text-sm text-gray-500 mb-2 text-right">ما هي المشكلة التي تسعى المبادرة لحلها أو الفرصة التي تستغلها؟</p>
          <Textarea
            id="problem"
            value={formData.problem || ""}
            onChange={(e) => handleTextChange("problem", e.target.value)}
            placeholder="اشرح المشكلة أو الفرصة بوضوح"
            className="min-h-[100px] text-right"
            dir="rtl"
            disabled={isSubmitting}
          />
        </div>

        <div className="text-right">
          <Label htmlFor="solution" className="text-lg font-medium text-right block">الحل</Label>
          <p className="text-sm text-gray-500 mb-2 text-right">ما هو الحل أو الإجراء المقترح؟</p>
          <Textarea
            id="solution"
            value={formData.solution || ""}
            onChange={(e) => handleTextChange("solution", e.target.value)}
            placeholder="اشرح الحل الذي تقترحه المبادرة"
            className="min-h-[100px] text-right"
            dir="rtl"
            disabled={isSubmitting}
          />
        </div>

        <div className="text-right">
          <Label htmlFor="beneficiaries" className="text-lg font-medium text-right block">المستفيدون</Label>
          <p className="text-sm text-gray-500 mb-2 text-right">من هم المستفيدون أو الجمهور المستهدف؟</p>
          <Textarea
            id="beneficiaries"
            value={formData.beneficiaries || ""}
            onChange={(e) => handleTextChange("beneficiaries", e.target.value)}
            placeholder="حدد الفئات المستفيدة من المبادرة"
            className="min-h-[100px] text-right"
            dir="rtl"
            disabled={isSubmitting}
          />
        </div>

        <div className="text-right">
          <Label htmlFor="quantitativeObjectives" className="text-lg font-medium text-right block">الأهداف الكمية</Label>
          <p className="text-sm text-gray-500 mb-2 text-right">ما هي الأرقام (القابلة للقياس) الملموسة التي تهدف المبادرة إلى تحقيقها؟</p>
          <Textarea
            id="quantitativeObjectives"
            value={formData.quantitativeObjectives || ""}
            onChange={(e) => handleTextChange("quantitativeObjectives", e.target.value)}
            placeholder="حدد الأهداف الكمية للمبادرة (أرقام ومؤشرات قابلة للقياس)"
            className="min-h-[100px] text-right"
            dir="rtl"
            disabled={isSubmitting}
          />
        </div>

        <div className="text-right">
          <Label htmlFor="qualitativeObjectives" className="text-lg font-medium text-right block">الأهداف النوعية</Label>
          <p className="text-sm text-gray-500 mb-2 text-right">ما هي طبيعة التغيير أو التحسين المستهدف؟ ما الذي سيكون مختلفًا نوعيًا بعد المبادرة؟</p>
          <Textarea
            id="qualitativeObjectives"
            value={formData.qualitativeObjectives || ""}
            onChange={(e) => handleTextChange("qualitativeObjectives", e.target.value)}
            placeholder="حدد الأهداف النوعية للمبادرة (التغييرات والتحسينات المتوقعة)"
            className="min-h-[100px] text-right"
            dir="rtl"
            disabled={isSubmitting}
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="text-right">
          <Label className="text-lg font-medium text-right block">التأثير الاجتماعي المتوقع</Label>
          <p className="text-sm text-gray-500 mb-4 text-right">
            ما هي التغييرات الإيجابية الأوسع نطاقًا التي تساهم المبادرة في إحداثها في المجتمع؟ اختر من القائمة أدناه.
          </p>

          <Accordion type="multiple" className="w-full border rounded-md text-right" dir="rtl">
            {socialImpactCategories.map((category) => (
              <AccordionItem value={category._id} key={category._id}>
                <AccordionTrigger className="px-4 hover:bg-gray-50 text-right">
                  {category.arabicName}
                </AccordionTrigger>
                <AccordionContent className="px-4 pt-2 pb-4">
                  <div className="space-y-3">
                    {category.impacts.map((impact) => (
                      <div key={impact._id} className="flex items-start space-x-reverse space-x-2 text-right">
                        <Checkbox
                          id={`impact-${impact._id}`}
                          checked={!!selectedImpacts[impact._id]}
                          onCheckedChange={(checked) => handleImpactChange(impact, checked === true)}
                          disabled={isSubmitting}
                        />
                        <div className="grid gap-1.5 leading-none mr-2">
                          <label
                            htmlFor={`impact-${impact._id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-right"
                          >
                            {impact.arabicName}
                          </label>
                          {impact.arabicDescription && (
                            <p className="text-xs text-gray-500 text-right">{impact.arabicDescription}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  )
}

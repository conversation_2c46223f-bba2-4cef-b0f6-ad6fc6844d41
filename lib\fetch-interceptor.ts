'use client'

import { useEffect } from 'react'

// Component that initializes the fetch interceptor
export default function FetchInterceptor() {
  useEffect(() => {
    // Store the original fetch function
    const originalFetch = window.fetch

    // Keep track of API calls
    let apiCallsCount: Record<string, number> = {}
    let lastWarningTime = 0
    const WARNING_INTERVAL = 5000 // 5 seconds

    // Override the fetch function
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      // Safely extract the URL string
      let urlString = ''
      try {
        if (typeof input === 'string') {
          urlString = input
        } else if (input instanceof URL) {
          urlString = input.href
        } else if (input instanceof Request) {
          urlString = input.url
        }
      } catch (e) {
        // If we can't extract the URL, just pass through to the original fetch
        return originalFetch.apply(window, [input, init])
      }

      // Only track API calls to our backend
      if (urlString && urlString.includes('/api/users/me')) {
        // Get the stack trace
        const stack = new Error().stack || ''

        // Count this API call
        apiCallsCount[urlString] = (apiCallsCount[urlString] || 0) + 1

        // Check if we're making too many calls to the same endpoint
        const now = Date.now()
        if (apiCallsCount[urlString] > 10 && now - lastWarningTime > WARNING_INTERVAL) {
          console.error(`⚠️ EXCESSIVE API CALLS DETECTED: ${apiCallsCount[urlString]} calls to ${urlString}`)
          console.error('Stack trace:', stack)

          // Reset the counter and update the warning time
          apiCallsCount = {}
          lastWarningTime = now

          // If we're making an extreme number of calls, throw an error to break the loop
          if (apiCallsCount[urlString] > 50) {
            throw new Error(`Infinite loop detected: ${apiCallsCount[urlString]} calls to ${urlString}. Request aborted.`)
          }
        }
      }

      // Call the original fetch function
      return originalFetch.apply(window, [input, init])
    }

    console.log('Fetch interceptor initialized')

    // Cleanup function to restore the original fetch
    return () => {
      window.fetch = originalFetch
      console.log('Fetch interceptor cleaned up')
    }
  }, []) // Empty dependency array ensures this runs only once

  // This component doesn't render anything
  return null
}

"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Bell } from "lucide-react"

export default function NotificationsPageSimple() {
  const [isLoading, setIsLoading] = useState(false)

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">الإشعارات</h1>
      </div>

      <Card>
        <CardContent className="py-12 text-center text-gray-500">
          <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg mb-4">لا توجد إشعارات حاليًا</p>
          <p className="text-sm text-gray-400 mb-6">
            سيتم إعلامك عندما تكون هناك تحديثات للمبادرات التي تدعمها، أو عندما يعلق شخص ما على مبادراتك.
          </p>
          <Button 
            variant="outline" 
            onClick={() => window.history.back()}
            className="mx-auto"
          >
            العودة
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { Input } from "../../../components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "../../../components/ui/tabs"
import { Badge } from "../../../components/ui/badge"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import { 
  Loader2, 
  AlertCircle, 
  Search, 
  Plus, 
  Filter, 
  CheckCircle2, 
  Clock, 
  Calendar,
  Users,
  Edit,
  Trash2,
  ArrowUpDown
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../../components/ui/dialog"
import { Checkbox } from "../../../components/ui/checkbox"
import { useAuth } from "../../../components/auth-provider"
import { api } from "../../../lib/api"
import { toast } from "../../../components/ui/use-toast"
import { Toaster } from "../../../components/ui/toaster"

interface Task {
  _id: string
  title: string
  description: string
  initiative: {
    _id: string
    title: string
  }
  milestone?: {
    _id: string
    title: string
  }
  assignedTo?: {
    _id: string
    name: string
    avatar: string
  }
  status: "pending" | "in-progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high"
  dueDate?: string
  completedDate?: string
  createdAt: string
  updatedAt: string
}

interface Initiative {
  _id: string
  title: string
}

interface Milestone {
  _id: string
  title: string
  initiative: string
}

export default function TasksDashboardPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [tasks, setTasks] = useState<Task[]>([])
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [initiativeFilter, setInitiativeFilter] = useState("all")
  const [sortBy, setSortBy] = useState("dueDate")
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [activeTab, setActiveTab] = useState("all")
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login")
      return
    }

    fetchTasks()
    fetchInitiatives()
  }, [isAuthenticated, router])

  useEffect(() => {
    if (tasks.length > 0) {
      applyFilters()
    }
  }, [tasks, searchQuery, statusFilter, priorityFilter, initiativeFilter, sortBy, activeTab])

  const fetchTasks = async () => {
    setIsLoading(true)
    try {
      if (!user) return

      // In a real implementation, you would have an API endpoint to fetch tasks
      // For now, we'll simulate it with a mock response
      const response = await api.get(`/api/tasks`, true)

      if (response.success) {
        setTasks(response.tasks || [])
      } else {
        setError("Failed to fetch tasks")
      }
    } catch (err: any) {
      console.error("Error fetching tasks:", err)
      setError(err.message || "An error occurred while fetching tasks")
    } finally {
      setIsLoading(false)
    }
  }

  const fetchInitiatives = async () => {
    try {
      if (!user) return

      const response = await api.get(`/api/initiatives/user/${user.id}?fields=_id,title`, true)

      if (response.success) {
        setInitiatives(response.initiatives || [])
      }
    } catch (err: any) {
      console.error("Error fetching initiatives:", err)
    }
  }

  const fetchMilestones = async (initiativeId: string) => {
    try {
      const response = await api.get(`/api/milestones/initiative/${initiativeId}?fields=_id,title`, true)

      if (response.success) {
        setMilestones(response.milestones || [])
      }
    } catch (err: any) {
      console.error("Error fetching milestones:", err)
    }
  }

  const applyFilters = () => {
    let filtered = [...tasks]

    // Apply tab filter
    if (activeTab !== "all") {
      filtered = filtered.filter(task => task.status === activeTab)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        task =>
          task.title.toLowerCase().includes(query) ||
          task.description.toLowerCase().includes(query) ||
          task.initiative.title.toLowerCase().includes(query)
      )
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(task => task.status === statusFilter)
    }

    // Apply priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter(task => task.priority === priorityFilter)
    }

    // Apply initiative filter
    if (initiativeFilter !== "all") {
      filtered = filtered.filter(task => task.initiative._id === initiativeFilter)
    }

    // Apply sorting
    switch (sortBy) {
      case "dueDate":
        filtered.sort((a, b) => {
          if (!a.dueDate) return 1
          if (!b.dueDate) return -1
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
        })
        break
      case "priority":
        const priorityOrder = { high: 0, medium: 1, low: 2 }
        filtered.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority])
        break
      case "status":
        const statusOrder = { "in-progress": 0, pending: 1, completed: 2, cancelled: 3 }
        filtered.sort((a, b) => statusOrder[a.status] - statusOrder[b.status])
        break
      case "newest":
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        break
      case "oldest":
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        break
      default:
        break
    }

    setFilteredTasks(filtered)
  }

  const handleDeleteTask = async () => {
    if (!taskToDelete) return

    setIsDeleting(true)
    try {
      const response = await api.delete(`/api/tasks/${taskToDelete}`, true)

      if (response.success) {
        // Remove from state
        setTasks(tasks.filter(t => t._id !== taskToDelete))
        toast({
          title: "Task Deleted",
          description: "The task has been successfully deleted.",
          variant: "default"
        })
      } else {
        throw new Error(response.message || "Failed to delete task")
      }
    } catch (err: any) {
      console.error("Error deleting task:", err)
      toast({
        title: "Error",
        description: err.message || "An error occurred while deleting the task",
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setTaskToDelete(null)
    }
  }

  const handleToggleTaskStatus = async (taskId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === "completed" ? "in-progress" : "completed"
      
      const response = await api.patch(`/api/tasks/${taskId}`, {
        status: newStatus,
        completedDate: newStatus === "completed" ? new Date().toISOString() : null
      }, true)

      if (response.success) {
        // Update task in state
        setTasks(tasks.map(task => 
          task._id === taskId 
            ? { 
                ...task, 
                status: newStatus, 
                completedDate: newStatus === "completed" ? new Date().toISOString() : undefined 
              } 
            : task
        ))
        
        toast({
          title: newStatus === "completed" ? "Task Completed" : "Task Reopened",
          description: newStatus === "completed" 
            ? "The task has been marked as completed." 
            : "The task has been reopened.",
          variant: "default"
        })
      } else {
        throw new Error(response.message || "Failed to update task status")
      }
    } catch (err: any) {
      console.error("Error updating task status:", err)
      toast({
        title: "Error",
        description: err.message || "An error occurred while updating the task",
        variant: "destructive"
      })
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return "غير محدد"
    
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">قيد الانتظار</Badge>
      case "in-progress":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">قيد التنفيذ</Badge>
      case "completed":
        return <Badge className="bg-green-100 text-green-800 border-green-200">مكتملة</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800 border-red-200">ملغاة</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-100 text-red-800 border-red-200">عالية</Badge>
      case "medium":
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">متوسطة</Badge>
      case "low":
        return <Badge className="bg-green-100 text-green-800 border-green-200">منخفضة</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  if (!isAuthenticated) {
    return null // Redirect handled in useEffect
  }

  // For demo purposes, let's create some mock tasks if none exist
  if (tasks.length === 0 && !isLoading && !error) {
    const mockTasks: Task[] = [
      {
        _id: "task1",
        title: "إعداد صفحة الويب للمبادرة",
        description: "إنشاء صفحة ويب للمبادرة تتضمن جميع المعلومات والصور",
        initiative: {
          _id: "init1",
          title: "مبادرة تنظيف الشاطئ"
        },
        milestone: {
          _id: "mile1",
          title: "إطلاق الموقع الإلكتروني"
        },
        status: "in-progress",
        priority: "high",
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: "task2",
        title: "التواصل مع المتطوعين",
        description: "إرسال رسائل للمتطوعين المحتملين للمشاركة في المبادرة",
        initiative: {
          _id: "init1",
          title: "مبادرة تنظيف الشاطئ"
        },
        status: "completed",
        priority: "medium",
        dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        completedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        _id: "task3",
        title: "شراء المعدات اللازمة",
        description: "شراء أكياس القمامة والقفازات والمعدات الأخرى اللازمة للمبادرة",
        initiative: {
          _id: "init1",
          title: "مبادرة تنظيف الشاطئ"
        },
        status: "pending",
        priority: "high",
        dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
    
    setTasks(mockTasks)
    setFilteredTasks(mockTasks)
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="max-w-7xl mx-auto p-4 md:p-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-[#0a8754]">إدارة المهام</h1>
            <p className="text-gray-600 mt-1">إدارة وتتبع المهام المرتبطة بمبادراتك</p>
          </div>
          <Link href="/dashboard/tasks/create">
            <Button className="bg-[#0a8754] hover:bg-[#097548]">
              <Plus className="ml-2 h-4 w-4" />
              إنشاء مهمة جديدة
            </Button>
          </Link>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="البحث عن مهمة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">قيد الانتظار</SelectItem>
                  <SelectItem value="in-progress">قيد التنفيذ</SelectItem>
                  <SelectItem value="completed">مكتملة</SelectItem>
                  <SelectItem value="cancelled">ملغاة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="الأولوية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأولويات</SelectItem>
                  <SelectItem value="high">عالية</SelectItem>
                  <SelectItem value="medium">متوسطة</SelectItem>
                  <SelectItem value="low">منخفضة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={initiativeFilter} onValueChange={setInitiativeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="المبادرة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المبادرات</SelectItem>
                  {initiatives.map((initiative) => (
                    <SelectItem key={initiative._id} value={initiative._id}>
                      {initiative.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="الترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dueDate">تاريخ الاستحقاق</SelectItem>
                  <SelectItem value="priority">الأولوية</SelectItem>
                  <SelectItem value="status">الحالة</SelectItem>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Tabs and Tasks Table */}
        <Card>
          <CardHeader className="pb-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 w-full">
                <TabsTrigger value="all">الكل</TabsTrigger>
                <TabsTrigger value="pending">قيد الانتظار</TabsTrigger>
                <TabsTrigger value="in-progress">قيد التنفيذ</TabsTrigger>
                <TabsTrigger value="completed">مكتملة</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent className="pt-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-[#0a8754]" />
                <span className="mr-2">جاري التحميل...</span>
              </div>
            ) : filteredTasks.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">لا توجد مهام تطابق معايير البحث</p>
                <Link href="/dashboard/tasks/create">
                  <Button className="bg-[#0a8754] hover:bg-[#097548]">
                    <Plus className="ml-2 h-4 w-4" />
                    إنشاء مهمة جديدة
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]"></TableHead>
                      <TableHead>المهمة</TableHead>
                      <TableHead>المبادرة</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>الأولوية</TableHead>
                      <TableHead>تاريخ الاستحقاق</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTasks.map((task) => (
                      <TableRow key={task._id}>
                        <TableCell>
                          <Checkbox
                            checked={task.status === "completed"}
                            onCheckedChange={() => handleToggleTaskStatus(task._id, task.status)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{task.title}</p>
                            <p className="text-sm text-gray-500 truncate max-w-[200px]">
                              {task.description}
                            </p>
                            {task.milestone && (
                              <Badge variant="outline" className="mt-1">
                                {task.milestone.title}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Link href={`/initiatives/${task.initiative._id}`} className="hover:text-green-600">
                            {task.initiative.title}
                          </Link>
                        </TableCell>
                        <TableCell>{getStatusBadge(task.status)}</TableCell>
                        <TableCell>{getPriorityBadge(task.priority)}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 text-gray-500 ml-1" />
                            <span className={`${
                              task.dueDate && new Date(task.dueDate) < new Date() && task.status !== "completed"
                                ? "text-red-600 font-medium"
                                : ""
                            }`}>
                              {formatDate(task.dueDate)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Link href={`/dashboard/tasks/${task._id}/edit`}>
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="icon" className="text-red-600">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>حذف المهمة</DialogTitle>
                                  <DialogDescription>
                                    هل أنت متأكد من رغبتك في حذف هذه المهمة؟ هذا الإجراء لا يمكن التراجع عنه.
                                  </DialogDescription>
                                </DialogHeader>
                                <DialogFooter>
                                  <Button variant="outline" onClick={() => setTaskToDelete(null)}>
                                    إلغاء
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    onClick={() => handleDeleteTask()}
                                    disabled={isDeleting}
                                  >
                                    {isDeleting ? (
                                      <>
                                        <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                                        جاري الحذف...
                                      </>
                                    ) : (
                                      "حذف"
                                    )}
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Toaster />
    </div>
  )
}

{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:server": "tsc --project tsconfig.server.json", "build:all": "npm run build && npm run build:server", "start": "next start", "start:server": "node dist/index.js", "start:prod": "node scripts/start-prod.js", "lint": "next lint", "server": "nodemon src/index.ts", "server:transpile": "ts-node --transpile-only --project tsconfig.backend.json src/index.ts", "server:simple": "node server.js", "setup-db": "node scripts/setup-db.js", "dev:all": "concurrently \"npm run dev\" \"npm run server\"", "dev:transpile": "concurrently \"npm run dev\" \"npm run server:transpile\"", "dev:simple": "concurrently \"npm run dev\" \"npm run server:simple\""}, "dependencies": {"@floating-ui/react-dom": "^2.1.2", "@hookform/resolvers": "latest", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@tanstack/react-query": "^5.71.10", "@tanstack/react-table": "^8.21.2", "autoprefixer": "^10.4.20", "bcryptjs": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cors": "latest", "crypto": "latest", "date-fns": "^2.30.0", "dotenv": "latest", "embla-carousel-react": "8.5.1", "express": "latest", "express-rate-limit": "latest", "express-validator": "^7.2.1", "fs": "latest", "helmet": "latest", "input-otp": "1.4.1", "joi": "latest", "jsonwebtoken": "latest", "lucide-react": "^0.454.0", "mongoose": "latest", "morgan": "latest", "multer": "^1.4.5-lts.2", "next": "^13.4.19", "next-themes": "^0.4.4", "nodemailer": "latest", "path": "latest", "react": "^18", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "latest"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "concurrently": "^8.2.2", "nodemon": "^3.1.0", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}
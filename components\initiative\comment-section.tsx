"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "../ui/button"
import { Textarea } from "../ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Separator } from "../ui/separator"
import { Alert, AlertDescription } from "../ui/alert"
import { AlertCircle, Loader2, ThumbsUp, Reply, MoreHorizontal, Flag } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../ui/dropdown-menu"

interface Comment {
  _id: string
  content: string
  author: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  createdAt: string
  likeCount: number
  isReply: boolean
  parentComment?: string
  replies?: Comment[]
}

interface CommentSectionProps {
  initiativeId: string
}

export default function CommentSection({ initiativeId }: CommentSectionProps) {
  const router = useRouter()
  const [comments, setComments] = useState<Comment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [commentText, setCommentText] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [replyTo, setReplyTo] = useState<string | null>(null)
  const [replyText, setReplyText] = useState("")

  // Get current user from localStorage or sessionStorage
  const getCurrentUser = () => {
    if (typeof window !== "undefined") {
      const userStr = localStorage.getItem("user") || sessionStorage.getItem("user")
      return userStr ? JSON.parse(userStr) : null
    }
    return null
  }

  const user = getCurrentUser()

  // Get token from localStorage or sessionStorage
  const getToken = () => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("accessToken") || sessionStorage.getItem("accessToken")
    }
    return null
  }

  useEffect(() => {
    const fetchComments = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/comments/initiative/${initiativeId}`,
        )

        if (!response.ok) {
          throw new Error("Failed to fetch comments")
        }

        const data = await response.json()
        setComments(data.comments || [])
      } catch (err: any) {
        setError(err.message || "An error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchComments()
  }, [initiativeId])

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      router.push("/auth/login")
      return
    }

    if (!commentText.trim()) return

    setIsSubmitting(true)

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/comments`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          initiative: initiativeId,
          content: commentText,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to post comment")
      }

      // Add new comment to the list
      setComments((prev) => [data.comment, ...prev])
      setCommentText("")
    } catch (err: any) {
      setError(err.message || "Failed to post comment")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmitReply = async (commentId: string) => {
    if (!user) {
      router.push("/auth/login")
      return
    }

    if (!replyText.trim()) return

    setIsSubmitting(true)

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/comments/${commentId}/reply`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            initiative: initiativeId,
            content: replyText,
          }),
        },
      )

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to post reply")
      }

      // Update comments with the new reply
      setComments((prev) => {
        return prev.map((comment) => {
          if (comment._id === commentId) {
            return {
              ...comment,
              replies: [...(comment.replies || []), data.comment],
            }
          }
          return comment
        })
      })

      setReplyText("")
      setReplyTo(null)
    } catch (err: any) {
      setError(err.message || "Failed to post reply")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleLikeComment = async (commentId: string) => {
    if (!user) {
      router.push("/auth/login")
      return
    }

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/comments/${commentId}/like`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        },
      )

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to like comment")
      }

      // Update comment like count
      setComments((prev) => {
        return prev.map((comment) => {
          if (comment._id === commentId) {
            return {
              ...comment,
              likeCount: data.likeCount,
            }
          }

          // Check if it's in replies
          if (comment.replies) {
            const updatedReplies = comment.replies.map((reply) => {
              if (reply._id === commentId) {
                return {
                  ...reply,
                  likeCount: data.likeCount,
                }
              }
              return reply
            })

            return {
              ...comment,
              replies: updatedReplies,
            }
          }

          return comment
        })
      })
    } catch (err: any) {
      setError(err.message || "Failed to like comment")
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-green-600" />
        <span className="ml-2">Loading comments...</span>
      </div>
    )
  }

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Comments</h2>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Comment Form */}
      <div className="mb-8">
        <form onSubmit={handleSubmitComment}>
          <div className="flex gap-4">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user?.avatar || "/placeholder.svg?height=40&width=40"} alt={user?.name || "User"} />
              <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <Textarea
                placeholder={user ? "Write a comment..." : "Please login to comment"}
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                disabled={!user || isSubmitting}
                className="mb-2 min-h-[100px]"
              />
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={!user || isSubmitting || !commentText.trim()}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Posting...
                    </>
                  ) : (
                    "Post Comment"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Comments List */}
      <div className="space-y-6">
        {comments.length === 0 ? (
          <p className="text-center text-gray-500 py-8">No comments yet. Be the first to comment!</p>
        ) : (
          comments.map((comment) => (
            <div key={comment._id} className="space-y-4">
              <div className="flex gap-4">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={comment.author.avatar} alt={comment.author.name} />
                  <AvatarFallback>{comment.author.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">{comment.author.name}</span>
                      <span className="text-sm text-gray-500 ml-2">@{comment.author.username}</span>
                      <span className="text-xs text-gray-400 ml-2">{formatDate(comment.createdAt)}</span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem className="text-red-600">
                          <Flag className="mr-2 h-4 w-4" />
                          Report
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <p className="mt-1">{comment.content}</p>
                  <div className="flex items-center gap-4 mt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-500 hover:text-green-600"
                      onClick={() => handleLikeComment(comment._id)}
                    >
                      <ThumbsUp className="mr-1 h-4 w-4" />
                      {comment.likeCount > 0 && comment.likeCount}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-500 hover:text-blue-600"
                      onClick={() => setReplyTo(replyTo === comment._id ? null : comment._id)}
                    >
                      <Reply className="mr-1 h-4 w-4" />
                      Reply
                    </Button>
                  </div>

                  {/* Reply Form */}
                  {replyTo === comment._id && (
                    <div className="mt-4 ml-6">
                      <div className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={user?.avatar || "/placeholder.svg?height=32&width=32"}
                            alt={user?.name || "User"}
                          />
                          <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <Textarea
                            placeholder="Write a reply..."
                            value={replyText}
                            onChange={(e) => setReplyText(e.target.value)}
                            disabled={!user || isSubmitting}
                            className="mb-2 min-h-[80px]"
                          />
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setReplyTo(null)
                                setReplyText("")
                              }}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              disabled={!user || isSubmitting || !replyText.trim()}
                              className="bg-green-600 hover:bg-green-700"
                              onClick={() => handleSubmitReply(comment._id)}
                            >
                              {isSubmitting ? (
                                <>
                                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                  Posting...
                                </>
                              ) : (
                                "Post Reply"
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-4 ml-6 space-y-4">
                      {comment.replies.map((reply) => (
                        <div key={reply._id} className="flex gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={reply.author.avatar} alt={reply.author.name} />
                            <AvatarFallback>{reply.author.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center">
                              <span className="font-medium">{reply.author.name}</span>
                              <span className="text-sm text-gray-500 ml-2">@{reply.author.username}</span>
                              <span className="text-xs text-gray-400 ml-2">{formatDate(reply.createdAt)}</span>
                            </div>
                            <p className="mt-1">{reply.content}</p>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-500 hover:text-green-600 mt-1"
                              onClick={() => handleLikeComment(reply._id)}
                            >
                              <ThumbsUp className="mr-1 h-3 w-3" />
                              {reply.likeCount > 0 && reply.likeCount}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <Separator />
            </div>
          ))
        )}
      </div>
    </div>
  )
}


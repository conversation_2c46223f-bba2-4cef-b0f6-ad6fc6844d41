"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { Button } from "../ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs"
import { Alert, AlertDescription } from "../ui/alert"
import {
  Loader2,
  AlertCircle,
  Plus,
  Package,
  DollarSign,
  Users,
  Wrench,
  HelpCircle,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  Ban
} from "lucide-react"
import { api } from "../../lib/api"
import { useAuth } from "../auth-provider"
import { toast } from "../ui/use-toast"
import { Toaster } from "../ui/toaster"
import ResourceOfferDialog from "./resource-offer-dialog"
import ResourceNeedDialog from "./resource-need-dialog"
import ResourceDeliveryDialog from "./resource-delivery-dialog"

interface ResourcesSectionProps {
  initiativeId: string
  isAuthor: boolean
  status: string
}

interface Resource {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  status: "requested" | "approved" | "rejected" | "delivered" | "canceled"
  requestedDate: string
  approvedDate?: string
  rejectedDate?: string
  deliveredDate?: string
  notes?: string
  provider: {
    _id: string
    name: string
    username: string
    avatar: string
    userType: string
  }
}

interface ResourceNeed {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  priority: "low" | "medium" | "high" | "critical"
  status: "open" | "in_progress" | "fulfilled" | "canceled"
  createdDate: string
  fulfilledDate?: string
  notes?: string
}

export default function ResourcesSection({ initiativeId, isAuthor, status }: ResourcesSectionProps) {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const isInstitutionalActor = user?.userType === 'company' || user?.userType === 'civil_society'

  const [resources, setResources] = useState<Resource[]>([])
  const [resourceNeeds, setResourceNeeds] = useState<ResourceNeed[]>([])
  const [resourceNeedsProgress, setResourceNeedsProgress] = useState<{[key: string]: {fulfilled: number, total: number}}>({})
  const [isLoadingResources, setIsLoadingResources] = useState(true)
  const [isLoadingNeeds, setIsLoadingNeeds] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("needs")
  const [showOfferDialog, setShowOfferDialog] = useState(false)
  const [showNeedDialog, setShowNeedDialog] = useState(false)
  const [showDeliveryDialog, setShowDeliveryDialog] = useState(false)
  const [selectedResourceNeed, setSelectedResourceNeed] = useState<ResourceNeed | null>(null)
  const [selectedResourceNeedForOffer, setSelectedResourceNeedForOffer] = useState<any>(null)
  const [selectedResourceForDelivery, setSelectedResourceForDelivery] = useState<Resource | null>(null)

  useEffect(() => {
    fetchResourceNeeds()
    fetchResources()
  }, [initiativeId])

  // Calculer le progrès des besoins en fonction des ressources offertes
  useEffect(() => {
    if (!isLoadingResources && !isLoadingNeeds) {
      const progress: {[key: string]: {fulfilled: number, total: number}} = {}

      // Initialiser le progrès pour chaque besoin
      resourceNeeds.forEach(need => {
        progress[need._id] = { fulfilled: 0, total: need.quantity }
      })

      // Calculer les quantités fournies pour chaque besoin
      resources.forEach(resource => {
        if (resource.relatedResourceNeed &&
            (resource.status === "requested" || resource.status === "approved" || resource.status === "delivered") &&
            progress[resource.relatedResourceNeed]) {
          progress[resource.relatedResourceNeed].fulfilled += resource.quantity
        }
      })

      setResourceNeedsProgress(progress)
    }
  }, [resources, resourceNeeds, isLoadingResources, isLoadingNeeds])

  const fetchResources = async () => {
    setIsLoadingResources(true)
    try {
      // Utiliser l'URL corrigée
      const response = await api.get(`/api/resources/initiative/${initiativeId}`, false)

      if (response.success) {
        // Filtrer pour ne pas afficher les offres annulées
        const filteredResources = (response.resources || []).filter(
          resource => resource.status !== "canceled"
        )
        setResources(filteredResources)
      } else {
        setError("Failed to fetch resources")
      }
    } catch (err: any) {
      console.error("Error fetching resources:", err)
      setError(err.message || "An error occurred while fetching resources")
    } finally {
      setIsLoadingResources(false)
    }
  }

  const fetchResourceNeeds = async () => {
    setIsLoadingNeeds(true)
    try {
      // Utiliser l'URL corrigée
      const response = await api.get(`/api/resource-needs/initiative/${initiativeId}`, false)

      if (response.success) {
        setResourceNeeds(response.resourceNeeds || [])
      } else {
        setError("Failed to fetch resource needs")
      }
    } catch (err: any) {
      console.error("Error fetching resource needs:", err)
      setError(err.message || "An error occurred while fetching resource needs")
    } finally {
      setIsLoadingNeeds(false)
    }
  }

  const handleResourceStatusUpdate = async (resourceId: string, newStatus: string) => {
    try {
      // Si on approuve une offre, on garde une référence à la ressource pour l'afficher dans la fenêtre de dialogue
      const resourceToUpdate = resources.find(r => r._id === resourceId);

      const response = await api.put(`/api/resources/${resourceId}/status`, {
        status: newStatus
      }, true)

      if (response.success) {
        toast({
          title: "تم تحديث حالة المورد",
          description: "تم تحديث حالة المورد بنجاح",
          variant: "default",
        })

        // Si l'offre est approuvée, ouvrir immédiatement la fenêtre de dialogue de réception
        if (newStatus === "approved" && resourceToUpdate) {
          // Mettre à jour les ressources d'abord pour avoir les données les plus récentes
          await fetchResources();

          // Trouver la ressource mise à jour
          const updatedResource = resources.find(r => r._id === resourceId);

          if (updatedResource) {
            // Si l'offre est liée à un besoin, mettre à jour l'état du besoin à "in_progress"
            if (updatedResource.relatedResourceNeed) {
              try {
                // Récupérer d'abord les détails du besoin pour les inclure dans la mise à jour
                const needResponse = await api.get(`/api/resource-needs/${updatedResource.relatedResourceNeed}`, true);
                if (needResponse.success && needResponse.resourceNeed) {
                  const need = needResponse.resourceNeed;
                  await api.put(`/api/resource-needs/${updatedResource.relatedResourceNeed}`, {
                    status: "in_progress",
                    type: need.type,
                    name: need.name,
                    description: need.description,
                    quantity: need.quantity,
                    unit: need.unit,
                    priority: need.priority
                  }, true);
                }

                // Rafraîchir les besoins pour refléter le changement d'état
                fetchResourceNeeds();
              } catch (error) {
                console.error("Erreur lors de la mise à jour de l'état du besoin:", error);
              }
            }

            // Ouvrir la fenêtre de dialogue de réception
            setSelectedResourceForDelivery(updatedResource);
            setShowDeliveryDialog(true);
          }
        } else {
          // Sinon, simplement rafraîchir les ressources
          fetchResources();
        }
      } else {
        throw new Error(response.message || "Failed to update resource status")
      }
    } catch (err: any) {
      console.error("Error updating resource status:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء تحديث حالة المورد",
        variant: "destructive",
      })
    }
  }

  const handleResourceCancel = async (resourceId: string) => {
    try {
      // Trouver la ressource à annuler pour obtenir son relatedResourceNeed
      const resourceToCancel = resources.find(r => r._id === resourceId);
      const needId = resourceToCancel?.relatedResourceNeed;
      const quantity = resourceToCancel?.quantity || 0;

      // Utiliser la route spécifique pour l'annulation des offres
      // Inclure le statut "canceled" pour satisfaire le middleware de validation
      const response = await api.put(`/api/resources/${resourceId}/cancel`, {
        status: "canceled"
      }, true)

      if (response.success) {
        toast({
          title: "تم إلغاء عرض المورد",
          description: "تم إلغاء عرض المورد بنجاح",
          variant: "default",
        })

        // Mettre à jour immédiatement le pourcentage et la barre de progression
        if (needId && resourceNeedsProgress[needId]) {
          // Créer une copie du state actuel
          const updatedProgress = { ...resourceNeedsProgress };

          // Soustraire la quantité de l'offre annulée
          updatedProgress[needId].fulfilled = Math.max(0, updatedProgress[needId].fulfilled - quantity);

          // Mettre à jour le state
          setResourceNeedsProgress(updatedProgress);
        }

        // Refresh resources
        fetchResources();
      } else {
        throw new Error(response.message || "Failed to cancel resource offer")
      }
    } catch (err: any) {
      console.error("Error canceling resource offer:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء إلغاء عرض المورد",
        variant: "destructive",
      })
    }
  }

  const handleResourceNeedUpdate = async (resourceNeedId: string, newStatus: string) => {
    try {
      // Utiliser l'URL corrigée
      const response = await api.put(`/api/resource-needs/${resourceNeedId}`, {
        status: newStatus
      }, true)

      if (response.success) {
        toast({
          title: "تم تحديث حالة الاحتياج",
          description: "تم تحديث حالة الاحتياج بنجاح",
          variant: "default",
        })

        // Refresh resource needs
        fetchResourceNeeds()
      } else {
        throw new Error(response.message || "Failed to update resource need status")
      }
    } catch (err: any) {
      console.error("Error updating resource need status:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء تحديث حالة الاحتياج",
        variant: "destructive",
      })
    }
  }

  const handleResourceNeedDelete = async (resourceNeedId: string) => {
    try {
      // Utiliser l'URL corrigée
      const response = await api.delete(`/api/resource-needs/${resourceNeedId}`, true)

      if (response.success) {
        toast({
          title: "تم حذف الاحتياج",
          description: "تم حذف الاحتياج بنجاح",
          variant: "default",
        })

        // Refresh resource needs
        fetchResourceNeeds()
      } else {
        throw new Error(response.message || "Failed to delete resource need")
      }
    } catch (err: any) {
      console.error("Error deleting resource need:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء حذف الاحتياج",
        variant: "destructive",
      })
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "مادي"
      case "financial":
        return "مالي"
      case "human":
        return "بشري"
      case "service":
        return "خدمة"
      default:
        return "آخر"
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "requested":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مطلوب</Badge>
      case "approved":
        return <Badge className="bg-green-100 text-green-800 border-green-200">موافق عليه</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 border-red-200">مرفوض</Badge>
      case "delivered":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">تم التسليم</Badge>
      case "canceled":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">ملغي</Badge>
      case "open":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مفتوح</Badge>
      case "in_progress":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">قيد التنفيذ</Badge>
      case "fulfilled":
        return <Badge className="bg-green-100 text-green-800 border-green-200">مكتمل</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "low":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">منخفضة</Badge>
      case "medium":
        return <Badge className="bg-green-100 text-green-800 border-green-200">متوسطة</Badge>
      case "high":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">عالية</Badge>
      case "critical":
        return <Badge className="bg-red-100 text-red-800 border-red-200">حرجة</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="space-y-6" dir="rtl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">الموارد</h2>
          <p className="text-gray-500">
            إدارة الموارد المطلوبة والمقدمة للمبادرة
          </p>
        </div>

        <div className="flex gap-2">
          {isAuthenticated && status === "active" && !isAuthor && (
            <Button
              className={isInstitutionalActor ? "bg-purple-600 hover:bg-purple-700" : "bg-green-600 hover:bg-green-700"}
              onClick={() => {
                setSelectedResourceNeedForOffer(null)
                setShowOfferDialog(true)
              }}
            >
              <Plus className="ml-2 h-4 w-4" />
              {isInstitutionalActor ? "تقديم دعم مؤسسي" : "تقديم مورد"}
            </Button>
          )}

          {isAuthenticated && status === "active" && isAuthor && (
            <>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => {
                  setSelectedResourceNeed(null)
                  setShowNeedDialog(true)
                }}
              >
                <Plus className="ml-2 h-4 w-4" />
                إضافة احتياج
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  setSelectedResourceNeedForOffer(null)
                  setShowOfferDialog(true)
                }}
              >
                <Plus className="ml-2 h-4 w-4" />
                إضافة مورد
              </Button>
            </>
          )}

          <Link href={`/initiatives/${initiativeId}/resources`}>
            <Button variant="outline">
              عرض الكل
            </Button>
          </Link>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" dir="rtl">
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="needs">الاحتياجات</TabsTrigger>
          <TabsTrigger value="offers">العروض المقدمة</TabsTrigger>
        </TabsList>

        <TabsContent value="needs" className="mt-6 text-right">
          {isLoadingNeeds ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري تحميل الاحتياجات...</span>
            </div>
          ) : resourceNeeds.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500 mb-4">لا توجد احتياجات محددة لهذه المبادرة حتى الآن.</p>
              {isAuthenticated && status === "active" && (
                isAuthor ? (
                  <Button
                    onClick={() => {
                      setSelectedResourceNeed(null)
                      setShowNeedDialog(true)
                    }}
                    variant="outline"
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة احتياج جديد
                  </Button>
                ) : isInstitutionalActor && (
                  <Button
                    onClick={() => {
                      setActiveTab("offers")
                      setSelectedResourceNeedForOffer(null)
                      setShowOfferDialog(true)
                    }}
                    variant="outline"
                    className="text-purple-600 border-purple-200 hover:bg-purple-50"
                  >
                    <Plus className="ml-2 h-4 w-4" />
                    تقديم دعم مؤسسي
                  </Button>
                )
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {resourceNeeds.map((need) => (
                <Card key={need._id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between">
                      <div className="flex items-center gap-2">
                        <div className="p-2 rounded-full bg-blue-100">
                          {getTypeIcon(need.type)}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{need.name}</CardTitle>
                          <CardDescription>{getTypeText(need.type)}</CardDescription>
                        </div>
                      </div>
                      {getStatusBadge(need.status)}
                    </div>
                  </CardHeader>

                  <CardContent className="pb-2">
                    <p className="text-sm text-gray-600 mb-3">{need.description}</p>

                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center gap-1 text-sm">
                        <span className="font-medium">الكمية:</span>
                        <span>{need.quantity} {need.unit}</span>
                      </div>
                      {getPriorityBadge(need.priority)}
                    </div>

                    {/* Barre de progression pour montrer le pourcentage de satisfaction du besoin */}
                    {resourceNeedsProgress[need._id] && (
                      <div className="mt-2 mb-3">
                        <div className="flex justify-between text-xs mb-1">
                          <span className="font-medium">تم تلبية: {resourceNeedsProgress[need._id].fulfilled} من {resourceNeedsProgress[need._id].total} {need.unit}</span>
                          <span className="font-medium">
                            {Math.min(100, Math.round((resourceNeedsProgress[need._id].fulfilled / resourceNeedsProgress[need._id].total) * 100))}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className={`h-2.5 rounded-full ${
                              resourceNeedsProgress[need._id].fulfilled >= resourceNeedsProgress[need._id].total
                                ? 'bg-green-600'
                                : resourceNeedsProgress[need._id].fulfilled > 0
                                  ? 'bg-amber-500'
                                  : 'bg-gray-300'
                            }`}
                            style={{
                              width: `${Math.min(100, Math.round((resourceNeedsProgress[need._id].fulfilled / resourceNeedsProgress[need._id].total) * 100))}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {isAuthenticated && !isAuthor && need.status !== "fulfilled" && need.status !== "canceled" && (
                      <Button
                        size="sm"
                        variant={isInstitutionalActor ? "default" : "outline"}
                        className={`w-full mt-2 ${isInstitutionalActor
                          ? "bg-purple-600 hover:bg-purple-700 text-white"
                          : "text-blue-600 border-blue-200 hover:bg-blue-50"}`}
                        onClick={() => {
                          console.log("Clicked on need:", need)
                          // Stocker l'objet complet du besoin au lieu de juste l'ID
                          setSelectedResourceNeedForOffer(need._id)
                          setShowOfferDialog(true)
                        }}
                      >
                        {isInstitutionalActor
                          ? <><Package className="ml-2 h-4 w-4" /> تقديم دعم مؤسسي لهذا الاحتياج</>
                          : <><Plus className="ml-2 h-4 w-4" /> تقديم مساهمة لهذا الاحتياج</>
                        }
                      </Button>
                    )}

                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <Clock className="h-4 w-4 ml-1" />
                      <span>تم الإنشاء في {formatDate(need.createdDate)}</span>
                    </div>

                    {need.fulfilledDate && (
                      <div className="flex items-center text-sm text-green-600">
                        <CheckCircle className="h-4 w-4 ml-1" />
                        <span>تم الاكتمال في {formatDate(need.fulfilledDate)}</span>
                      </div>
                    )}

                    {need.notes && (
                      <div className="mt-2 p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                        <p className="font-medium mb-1">ملاحظات:</p>
                        <p>{need.notes}</p>
                      </div>
                    )}
                  </CardContent>

                  {isAuthor && need.status !== "fulfilled" && need.status !== "canceled" && (
                    <CardFooter className="pt-2">
                      <div className="flex justify-between w-full">
                        <div className="flex gap-2">
                          {/* Suppression du bouton "قيد التنفيذ" car l'état est mis à jour automatiquement */}

                          {(need.status === "open" || need.status === "in_progress") && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-green-600 border-green-200 hover:bg-green-50"
                              onClick={() => handleResourceNeedUpdate(need._id, "fulfilled")}
                            >
                              تم الاكتمال
                            </Button>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-blue-600 border-blue-200 hover:bg-blue-50"
                            onClick={() => {
                              setSelectedResourceNeed(need)
                              setShowNeedDialog(true)
                            }}
                          >
                            تعديل
                          </Button>

                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 border-red-200 hover:bg-red-50"
                            onClick={() => handleResourceNeedDelete(need._id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </div>
                    </CardFooter>
                  )}
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="offers" className="mt-6 text-right">
          {isLoadingResources ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري تحميل الموارد...</span>
            </div>
          ) : resources.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500 mb-4">لا توجد موارد مقدمة لهذه المبادرة حتى الآن.</p>
              {isAuthenticated && status === "active" && (
                isAuthor ? (
                  <Button
                    onClick={() => {
                      setSelectedResourceNeedForOffer(null)
                      setShowOfferDialog(true)
                    }}
                    variant="outline"
                    className="text-green-600 border-green-200 hover:bg-green-50"
                  >
                    <Plus className="ml-2 h-4 w-4" />
                    إضافة مورد جديد
                  </Button>
                ) : (
                  <Button
                    onClick={() => {
                      setSelectedResourceNeedForOffer(null)
                      setShowOfferDialog(true)
                    }}
                    variant="outline"
                    className={isInstitutionalActor
                      ? "text-purple-600 border-purple-200 hover:bg-purple-50"
                      : "text-green-600 border-green-200 hover:bg-green-50"}
                  >
                    <Plus className="ml-2 h-4 w-4" />
                    {isInstitutionalActor ? "تقديم دعم مؤسسي" : "تقديم مورد جديد"}
                  </Button>
                )
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {resources.map((resource) => (
                <Card
                  key={resource._id}
                  className={`overflow-hidden ${resource.providerType === 'company' || resource.providerType === 'civilSociety' ? 'border-purple-200' : ''}`}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`p-2 rounded-full ${resource.providerType === 'company' || resource.providerType === 'civilSociety' ? 'bg-purple-100' : 'bg-green-100'}`}>
                          {getTypeIcon(resource.type)}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{resource.name}</CardTitle>
                          <CardDescription>
                            {getTypeText(resource.type)}
                            {(resource.providerType === 'company' || resource.providerType === 'civilSociety') && (
                              <Badge className="mr-2 bg-purple-100 text-purple-800 border-purple-200">
                                {resource.providerType === 'company' ? 'شركة' : 'فاعل مجتمع مدني'}
                              </Badge>
                            )}
                          </CardDescription>
                        </div>
                      </div>
                      {getStatusBadge(resource.status)}
                    </div>
                  </CardHeader>

                  <CardContent className="pb-2">
                    <p className="text-sm text-gray-600 mb-3">{resource.description}</p>

                    <div className="flex items-center gap-1 text-sm mb-2">
                      <span className="font-medium">الكمية:</span>
                      <span>{resource.quantity} {resource.unit}</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <Clock className="h-4 w-4 ml-1" />
                      <span>تم التقديم في {formatDate(resource.requestedDate)}</span>
                    </div>

                    {resource.approvedDate && (
                      <div className="flex items-center text-sm text-green-600 mb-1">
                        <CheckCircle className="h-4 w-4 ml-1" />
                        <span>تمت الموافقة في {formatDate(resource.approvedDate)}</span>
                      </div>
                    )}

                    {resource.rejectedDate && (
                      <div className="flex items-center text-sm text-red-600 mb-1">
                        <XCircle className="h-4 w-4 ml-1" />
                        <span>تم الرفض في {formatDate(resource.rejectedDate)}</span>
                      </div>
                    )}

                    {resource.deliveredDate && (
                      <div className="flex items-center text-sm text-purple-600 mb-1">
                        <Truck className="h-4 w-4 ml-1" />
                        <span>تم التسليم في {formatDate(resource.deliveredDate)}</span>
                        {resource.deliveredQuantity && resource.deliveredQuantity !== resource.quantity && (
                          <span className="mr-2 text-xs bg-purple-100 px-2 py-1 rounded">
                            الكمية المستلمة: {resource.deliveredQuantity} {resource.unit}
                          </span>
                        )}
                      </div>
                    )}

                    {resource.deliveryNotes && (
                      <div className="flex items-center text-sm text-purple-600 mb-1">
                        <span className="font-medium ml-1">ملاحظات الاستلام:</span>
                        <span>{resource.deliveryNotes}</span>
                      </div>
                    )}

                    <div className="mt-3 flex items-center gap-2">
                      <div className="text-sm font-medium">المقدم:</div>
                      {/* Masquer le nom du contributeur si l'offre est privée et que l'utilisateur n'est ni l'initiateur ni le contributeur */}
                      {(!resource.isPublic && !isAuthor && (!user || user.id !== resource.provider._id)) ? (
                        <div className="text-sm italic">مساهم خاص</div>
                      ) : (
                        <div className="text-sm">{resource.provider.name}</div>
                      )}
                      {resource.providerType && (
                        <div className="text-xs text-gray-500">
                          ({resource.providerType === 'company' ? 'شركة' :
                             resource.providerType === 'civilSociety' ? 'فاعل مجتمع مدني' :
                             resource.providerType === 'volunteer' ? 'متطوع' :
                             resource.providerType === 'proposer' ? 'مقترح مبادرات' :
                             resource.providerType === 'admin' ? 'مدير' : 'مستخدم'})
                        </div>
                      )}
                      {!resource.isPublic && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          مساهمة خاصة
                        </Badge>
                      )}
                    </div>

                    {/* Afficher les informations de contact uniquement pour l'initiateur ou le contributeur lui-même */}
                    {resource.contactPerson && (isAuthor || (user && user.id === resource.provider._id)) && (
                      <div className="mt-2 flex items-center gap-2">
                        <div className="text-sm font-medium">جهة الاتصال:</div>
                        <div className="text-sm">{resource.contactPerson}</div>
                        {resource.contactPhone && (
                          <div className="text-sm">{resource.contactPhone}</div>
                        )}
                        {resource.contactEmail && (
                          <div className="text-sm">{resource.contactEmail}</div>
                        )}
                      </div>
                    )}

                    {resource.expectedDeliveryDate && resource.status !== "delivered" && (
                      <div className="mt-2 flex items-center gap-2 text-purple-600">
                        <div className="text-sm font-medium">تاريخ التسليم المتوقع:</div>
                        <div className="text-sm">{new Date(resource.expectedDeliveryDate).toLocaleDateString("ar-DZ")}</div>
                      </div>
                    )}

                    {resource.notes && (
                      <div className="mt-2 p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                        <p className="font-medium mb-1">ملاحظات:</p>
                        <p>{resource.notes}</p>
                      </div>
                    )}
                  </CardContent>

                  {((isAuthor && resource.status === "requested") ||
                     (user && resource.provider._id === user.id &&
                      (resource.status === "requested" || resource.status === "approved"))) && (
                    <CardFooter className="pt-2">
                      <div className="flex justify-between w-full">
                        {isAuthor && resource.status === "requested" && (
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-green-600 border-green-200 hover:bg-green-50"
                              onClick={() => handleResourceStatusUpdate(resource._id, "approved")}
                            >
                              <CheckCircle className="ml-1 h-4 w-4" />
                              قبول
                            </Button>

                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-600 border-red-200 hover:bg-red-50"
                              onClick={() => handleResourceStatusUpdate(resource._id, "rejected")}
                            >
                              <XCircle className="ml-1 h-4 w-4" />
                              رفض
                            </Button>
                          </div>
                        )}

                        {isAuthor && resource.status === "approved" && (
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-purple-600 border-purple-200 hover:bg-purple-50"
                              onClick={() => {
                                setSelectedResourceForDelivery(resource)
                                setShowDeliveryDialog(true)
                              }}
                            >
                              <Truck className="ml-1 h-4 w-4" />
                              تأكيد الاستلام
                            </Button>
                            {resource.expectedDeliveryDate && new Date(resource.expectedDeliveryDate) < new Date() && (
                              <div className="flex items-center text-amber-600 text-xs">
                                <Clock className="ml-1 h-3 w-3" />
                                تأخر التسليم
                              </div>
                            )}
                          </div>
                        )}

                        {user && resource.provider._id === user.id &&
                         resource.status === "requested" && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 border-red-200 hover:bg-red-50"
                            onClick={() => handleResourceCancel(resource._id)}
                          >
                            <Ban className="ml-1 h-4 w-4" />
                            إلغاء العرض
                          </Button>
                        )}
                      </div>
                    </CardFooter>
                  )}
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Resource Offer Dialog */}
      <ResourceOfferDialog
        open={showOfferDialog}
        onOpenChange={setShowOfferDialog}
        initiativeId={initiativeId}
        resourceNeedId={selectedResourceNeedForOffer || undefined}
        onSuccess={(newResource) => {
          // Mettre à jour immédiatement le pourcentage et la barre de progression
          if (newResource && newResource.relatedResourceNeed) {
            // Créer une copie du state actuel
            const updatedProgress = { ...resourceNeedsProgress }

            // Trouver le besoin correspondant
            const needId = newResource.relatedResourceNeed
            if (updatedProgress[needId]) {
              // Ajouter la quantité de la nouvelle offre
              updatedProgress[needId].fulfilled += newResource.quantity

              // Mettre à jour le state
              setResourceNeedsProgress(updatedProgress)

              console.log("Mise à jour immédiate du progrès:", {
                needId,
                quantité: newResource.quantity,
                nouveauTotal: updatedProgress[needId].fulfilled,
                pourcentage: Math.round((updatedProgress[needId].fulfilled / updatedProgress[needId].total) * 100)
              });
            }
          }

          // Rafraîchir les données
          fetchResources()
          fetchResourceNeeds()
          setShowOfferDialog(false)
          setSelectedResourceNeedForOffer(null)
        }}
      />

      {/* Resource Need Dialog */}
      <ResourceNeedDialog
        open={showNeedDialog}
        onOpenChange={setShowNeedDialog}
        initiativeId={initiativeId}
        resourceNeed={selectedResourceNeed}
        onSuccess={() => {
          fetchResourceNeeds()
          setShowNeedDialog(false)
          setSelectedResourceNeed(null)
        }}
      />

      {/* Resource Delivery Dialog */}
      {selectedResourceForDelivery && (
        <ResourceDeliveryDialog
          open={showDeliveryDialog}
          onOpenChange={setShowDeliveryDialog}
          resource={selectedResourceForDelivery}
          onSuccess={() => {
            fetchResources()
            setShowDeliveryDialog(false)
            setSelectedResourceForDelivery(null)
          }}
        />
      )}

      <Toaster />
    </div>
  )
}

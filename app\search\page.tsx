"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, SearchIcon, ThumbsUp, MessageSquare, MapPin } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  location: string
  mainImage: string
  supportCount: number
  commentCount: number
  status: string
  createdAt: string
}

interface User {
  _id: string
  name: string
  username: string
  avatar: string
  bio?: string
  location?: string
}

export default function SearchPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const query = searchParams.get("q") || ""
  const type = searchParams.get("type") || "initiatives"

  const [searchQuery, setSearchQuery] = useState(query)
  const [searchType, setSearchType] = useState(type)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  // Results
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [totalInitiatives, setTotalInitiatives] = useState(0)
  const [totalUsers, setTotalUsers] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Filters
  const [categoryFilter, setCategoryFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [sortBy, setSortBy] = useState("newest")

  useEffect(() => {
    if (query) {
      performSearch()
    }

    // Update state when URL params change
    setSearchQuery(query)
    setSearchType(type)
  }, [query, type])

  const performSearch = async () => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    setError("")

    try {
      if (searchType === "initiatives") {
        await searchInitiatives()
      } else if (searchType === "users") {
        await searchUsers()
      }
    } catch (err: any) {
      setError(err.message || "An error occurred during search")
    } finally {
      setIsLoading(false)
    }
  }

  const searchInitiatives = async () => {
    const params = new URLSearchParams({
      q: searchQuery,
      page: currentPage.toString(),
      limit: "9",
    })

    if (categoryFilter) params.append("category", categoryFilter)
    if (statusFilter) params.append("status", statusFilter)
    if (sortBy) params.append("sort", sortBy)

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/initiatives/search?${params.toString()}`,
    )

    if (!response.ok) {
      throw new Error("Failed to search initiatives")
    }

    const data = await response.json()
    setInitiatives(data.initiatives || [])
    setTotalInitiatives(data.total || 0)
    setTotalPages(data.totalPages || 1)
  }

  const searchUsers = async () => {
    const params = new URLSearchParams({
      q: searchQuery,
      page: currentPage.toString(),
      limit: "9",
    })

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/users/search?${params.toString()}`,
    )

    if (!response.ok) {
      throw new Error("Failed to search users")
    }

    const data = await response.json()
    setUsers(data.users || [])
    setTotalUsers(data.total || 0)
    setTotalPages(data.totalPages || 1)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()

    // Update URL with search params
    const params = new URLSearchParams()
    if (searchQuery) params.set("q", searchQuery)
    params.set("type", searchType)

    router.push(`/search?${params.toString()}`)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)

    // Scroll to top
    window.scrollTo({ top: 0, behavior: "smooth" })

    // Perform search with new page
    performSearch()
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <h1 className="text-3xl font-bold mb-8">بحث</h1>

      <div className="mb-8">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <SearchIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="ابحث عن مبادرات، مستخدمين..."
              className="pr-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Select value={searchType} onValueChange={setSearchType}>
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="البحث في" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="initiatives">المبادرات</SelectItem>
              <SelectItem value="users">المستخدمين</SelectItem>
            </SelectContent>
          </Select>
          <Button type="submit" className="bg-green-600 hover:bg-green-700">
            <SearchIcon className="ml-2 h-4 w-4" />
            بحث
          </Button>
        </form>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error.replace("An error occurred during search", "حدث خطأ أثناء البحث").replace("Failed to search initiatives", "فشل البحث عن المبادرات").replace("Failed to search users", "فشل البحث عن المستخدمين")}</AlertDescription>
        </Alert>
      )}

      <Tabs
        value={searchType}
        onValueChange={(value) => {
          setSearchType(value)
          const params = new URLSearchParams(searchParams.toString())
          params.set("type", value)
          router.push(`/search?${params.toString()}`)
        }}
      >
        <TabsList className="mb-6">
          <TabsTrigger value="initiatives">المبادرات</TabsTrigger>
          <TabsTrigger value="users">المستخدمين</TabsTrigger>
        </TabsList>

        <TabsContent value="initiatives">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري البحث عن المبادرات...</span>
            </div>
          ) : !query ? (
            <div className="text-center py-12 text-gray-500">
              <p>أدخل مصطلح البحث للعثور على المبادرات</p>
            </div>
          ) : initiatives.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <p>لم يتم العثور على مبادرات لـ "{query}"</p>
              <Link href="/initiatives">
                <Button className="mt-4 bg-green-600 hover:bg-green-700">تصفح جميع المبادرات</Button>
              </Link>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-6">
                <p className="text-gray-600">
                  تم العثور على {totalInitiatives} مبادرة{totalInitiatives !== 1 ? "" : ""} لـ "{query}"
                </p>
                <div className="flex items-center gap-2">
                  <Select
                    value={sortBy}
                    onValueChange={(value) => {
                      setSortBy(value)
                      setCurrentPage(1)
                      performSearch()
                    }}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="ترتيب حسب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">الأحدث</SelectItem>
                      <SelectItem value="oldest">الأقدم</SelectItem>
                      <SelectItem value="most_supported">الأكثر دعماً</SelectItem>
                      <SelectItem value="most_commented">الأكثر تعليقاً</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {initiatives.map((initiative) => (
                  <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="h-[160px] overflow-hidden">
                      <Image
                        src={initiative.mainImage || "/placeholder.svg"}
                        alt={initiative.title}
                        width={400}
                        height={160}
                        className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                      />
                    </div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
                        <Badge
                          variant="outline"
                          className={
                            initiative.status === "active"
                              ? "text-green-600 border-green-200 bg-green-50"
                              : initiative.status === "completed"
                                ? "text-blue-600 border-blue-200 bg-blue-50"
                                : "text-gray-600 border-gray-200 bg-gray-50"
                          }
                        >
                          {initiative.status === "active"
                            ? "نشطة"
                            : initiative.status === "completed"
                              ? "مكتملة"
                              : initiative.status === "pending"
                                ? "قيد الانتظار"
                                : initiative.status}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg mt-2 line-clamp-1">
                        <Link
                          href={`/initiatives/${initiative._id}`}
                          className="hover:text-green-600 transition-colors"
                        >
                          {initiative.title}
                        </Link>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 line-clamp-2 text-sm mb-4">{initiative.shortDescription}</p>
                      <div className="flex justify-between text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          {initiative.supportCount}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {initiative.commentCount}
                        </span>
                        <span>{formatDate(initiative.createdAt)}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="users">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري البحث عن المستخدمين...</span>
            </div>
          ) : !query ? (
            <div className="text-center py-12 text-gray-500">
              <p>أدخل مصطلح البحث للعثور على المستخدمين</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <p>لم يتم العثور على مستخدمين لـ "{query}"</p>
            </div>
          ) : (
            <>
              <div className="mb-6">
                <p className="text-gray-600">
                  تم العثور على {totalUsers} مستخدم{totalUsers !== 1 ? "" : ""} لـ "{query}"
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {users.map((user) => (
                  <Card key={user._id}>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-4 mb-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={user.avatar} alt={user.name} />
                          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-lg font-bold">{user.name}</h3>
                          <p className="text-gray-500">@{user.username}</p>
                        </div>
                      </div>

                      {user.bio && <p className="text-gray-600 mb-4 line-clamp-2">{user.bio}</p>}

                      {user.location && (
                        <div className="flex items-center gap-1 text-sm text-gray-500 mb-4">
                          <MapPin className="h-4 w-4 ml-1" />
                          <span>{user.location}</span>
                        </div>
                      )}

                      <Link href={`/users/${user._id}`}>
                        <Button className="w-full bg-green-600 hover:bg-green-700">عرض الملف الشخصي</Button>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      السابق
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      التالي
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}


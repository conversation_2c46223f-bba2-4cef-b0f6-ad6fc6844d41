{user?.userType === 'proposer' && (
  <TabsContent value="proposer">
    <Card>
      <CardHeader className="card-header">
        <CardTitle>أضف معلومات عن مهاراتك ومؤهلاتك كمقترح مبادرات</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 card-content">
        <div className="space-y-2">
          <Label htmlFor="qualifications">المؤهلات</Label>
          <Input
            id="qualifications"
            value={userSettings.qualifications.join(', ')}
            onChange={(e) => handleArrayInput(e, 'qualifications')}
            placeholder="أدخل مؤهلاتك مفصولة بفواصل"
            dir="rtl"
          />
          <p className="text-xs text-gray-500">مثال: بكالوريوس هندسة، ماجستير إدارة أعمال</p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="skills" className="text-lg font-medium">المهارات ومستوياتها</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>أضف مهاراتك المختلفة وحدد مستوى إتقانك لكل منها. هذا سيساعد في تقديم مبادرات أكثر فعالية.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-4">
            <p className="text-sm text-gray-600 mb-2">
              تم تصنيف المهارات إلى 7 فئات رئيسية لمساعدتك في تنظيم مهاراتك بشكل أفضل. اختر الفئة المناسبة لكل مهارة وحدد مستوى إتقانك لها.
            </p>
          </div>

          <Accordion type="multiple" className="w-full mb-4" dir="rtl">
            {SKILL_CATEGORIES.map((category) => (
              <AccordionItem key={category.id} value={category.id}>
                <AccordionTrigger className="text-right accordion-trigger">
                  <div className="flex items-center gap-2">
                    <span>{category.icon}</span>
                    <span>{category.arabicName}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="accordion-content">
                  <div className="p-2 space-y-2">
                    <p className="text-sm text-gray-600 mb-2">{category.arabicDescription}</p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {SKILL_EXAMPLES_AR[category.id].map((example, i) => (
                        <Button
                          key={i}
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Vérifier si cette compétence existe déjà
                            const exists = userSettings.skills.some(
                              skill => skill.name === example && skill.category === category.id
                            );

                            if (!exists) {
                              setUserSettings({
                                ...userSettings,
                                skills: [
                                  ...userSettings.skills,
                                  {
                                    name: example,
                                    category: category.id,
                                    level: 'intermediate'
                                  }
                                ]
                              });
                            } else {
                              toast({
                                title: "تنبيه",
                                description: "هذه المهارة موجودة بالفعل في قائمتك",
                                variant: "default"
                              });
                            }
                          }}
                        >
                          {example}
                        </Button>
                      ))}
                    </div>

                    <div className="flex items-center gap-2">
                      <Input
                        placeholder={`أضف مهارة ${category.arabicName} جديدة...`}
                        className="flex-1"
                        id={`new-skill-${category.id}`}
                        dir="rtl"
                      />
                      <Button
                        variant="outline"
                        onClick={() => {
                          const input = document.getElementById(`new-skill-${category.id}`) as HTMLInputElement;
                          const skillName = input.value.trim();

                          if (skillName) {
                            // Vérifier si cette compétence existe déjà
                            const exists = userSettings.skills.some(
                              skill => skill.name === skillName && skill.category === category.id
                            );

                            if (!exists) {
                              setUserSettings({
                                ...userSettings,
                                skills: [
                                  ...userSettings.skills,
                                  {
                                    name: skillName,
                                    category: category.id,
                                    level: 'intermediate'
                                  }
                                ]
                              });
                              input.value = '';
                            } else {
                              toast({
                                title: "تنبيه",
                                description: "هذه المهارة موجودة بالفعل في قائمتك",
                                variant: "default"
                              });
                            }
                          }
                        }}
                      >
                        إضافة
                      </Button>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className="bg-gray-50 p-4 rounded-md mb-4">
            <h3 className="font-medium mb-2">مهاراتك الحالية</h3>
            {userSettings.skills && userSettings.skills.length > 0 ? (
              <div className="space-y-3">
                {userSettings.skills.map((skill, index) => {
                  const category = SKILL_CATEGORIES.find(c => c.id === skill.category);
                  return (
                    <div key={index} className="flex items-center gap-2 bg-white p-2 rounded border">
                      <div className="flex-none w-8 h-8 flex items-center justify-center rounded-full bg-gray-100">
                        {category ? category.icon : '🔧'}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{skill.name}</div>
                        <div className="text-xs text-gray-500">
                          {category ? category.arabicName : 'مهارة عامة'}
                        </div>
                      </div>
                      <Select
                        value={skill.level}
                        onValueChange={(value) => {
                          const newSkills = [...userSettings.skills];
                          newSkills[index] = { ...newSkills[index], level: value };
                          setUserSettings({ ...userSettings, skills: newSkills });
                        }}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="المستوى" />
                        </SelectTrigger>
                        <SelectContent>
                          {SKILL_LEVELS.map(level => (
                            <SelectItem key={level.id} value={level.id}>
                              {level.arabicName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          const newSkills = [...userSettings.skills];
                          newSkills.splice(index, 1);
                          setUserSettings({ ...userSettings, skills: newSkills });
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-sm text-gray-500">لم تضف أي مهارات بعد</p>
            )}
          </div>

          <p className="text-xs text-gray-500">أضف مهاراتك مع تحديد مستوى إتقانك لكل منها. هذا سيساعد في تقديم مبادرات أكثر فعالية وتحقيق أهدافك.</p>
        </div>
      </CardContent>
    </Card>
  </TabsContent>
)}

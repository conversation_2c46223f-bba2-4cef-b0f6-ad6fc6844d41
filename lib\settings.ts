import { api } from './api'

export interface SiteSettings {
  siteName: string
  siteDescription: string
  contactEmail: string
  supportPhone: string
  maintenanceMode: boolean
  termsAndConditions: string
  privacyPolicy: string
  aboutUs: string
  socialLinks: {
    facebook: string
    twitter: string
    instagram: string
    linkedin: string
  }
  address?: string
}

// Default settings in case API fails
export const defaultSettings: SiteSettings = {
  siteName: "منصة المبادرات",
  siteDescription: "منصة لإدارة ومتابعة المبادرات المجتمعية",
  contactEmail: "<EMAIL>",
  supportPhone: "+213 000 000 000",
  maintenanceMode: false,
  termsAndConditions: "",
  privacyPolicy: "",
  aboutUs: "",
  address: "الجزائر العاصمة، الجزائر",
  socialLinks: {
    facebook: "",
    twitter: "",
    instagram: "",
    linkedin: ""
  }
}

/**
 * Get public site settings
 */
export const getPublicSettings = async (): Promise<SiteSettings> => {
  try {
    const response = await api.get('/api/settings/public', false)
    
    if (response && response.success && response.settings) {
      // Add address field if it doesn't exist in the API response
      if (!response.settings.address) {
        response.settings.address = "الجزائر العاصمة، الجزائر"
      }
      
      return response.settings
    }
    
    return defaultSettings
  } catch (error) {
    console.error('Error fetching public settings:', error)
    return defaultSettings
  }
}

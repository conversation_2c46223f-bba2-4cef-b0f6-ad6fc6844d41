import express from "express"
import { authenticate } from "../middleware/auth"
import {
  getResources,
  getInitiativeResources,
  getUserResources,
  getResourceById,
  offerResource,
  updateResourceStatus,
  cancelResourceOffer,
} from "../controllers/resource.controller"
import {
  validateResource,
  validateResourceStatusUpdate,
} from "../middleware/validators/resource.validator"

const router = express.Router()

// Resource routes
router.get("/", authenticate, getResources)
router.get("/initiative/:initiativeId", getInitiativeResources)
router.get("/user/:userId", getUserResources)
router.post("/initiative/:initiativeId", authenticate, validateResource, offerResource)
router.put("/:id/status", authenticate, validateResourceStatusUpdate, updateResourceStatus)
router.put("/:id/cancel", authenticate, validateResourceStatusUpdate, cancelResourceOffer)
router.get("/:id", getResourceById)

export default router

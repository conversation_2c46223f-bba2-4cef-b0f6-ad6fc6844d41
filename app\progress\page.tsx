"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CheckCircle2, Clock, AlertTriangle, Search, Filter, ChevronRight, ChevronLeft, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { api } from "@/lib/api"

// Define types for initiatives and milestones
type Milestone = {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  date: string;
  completedDate?: string | null;
};

type Initiative = {
  id: string;
  title: string;
  category: string;
  location: string;
  startDate: string;
  endDate: string;
  progress: number;
  status: string;
  milestones: Milestone[];
};

export default function ProgressPage() {
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [selectedInitiative, setSelectedInitiative] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [activeTab, setActiveTab] = useState('all')
  
  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0,
    limit: 10,
    hasNextPage: false,
    hasPrevPage: false
  })

  // Function to fetch initiatives with filters and pagination
  const fetchInitiatives = async (page = 1) => {
    setIsLoading(true)
    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString()
      })
      
      // Add filters
      if (searchTerm) queryParams.append('search', searchTerm)
      if (statusFilter !== 'all') queryParams.append('status', statusFilter)
      
      // Use the API utility for consistent handling
      const response = await api.get(`/api/progress?${queryParams.toString()}`, false)
      
      if (!response || !response.success) {
        throw new Error('Failed to fetch progress data')
      }
      
      setInitiatives(response.initiatives || [])
      
      // Update pagination information
      if (response.pagination) {
        setPagination({
          currentPage: response.pagination.currentPage,
          totalPages: response.pagination.totalPages,
          total: response.pagination.total,
          limit: response.pagination.limit,
          hasNextPage: response.pagination.hasNextPage,
          hasPrevPage: response.pagination.hasPrevPage
        })
      }
      
      // Select the first initiative by default if available and none is selected
      if (response.initiatives && response.initiatives.length > 0 && !selectedInitiative) {
        setSelectedInitiative(response.initiatives[0].id)
      } else if (response.initiatives && response.initiatives.length > 0 && selectedInitiative) {
        // Check if the selected initiative is still in the results
        const stillExists = response.initiatives.some(initiative => initiative.id === selectedInitiative)
        if (!stillExists) {
          setSelectedInitiative(response.initiatives[0].id)
        }
      }
      
      setError(null)
    } catch (err) {
      console.error('Error fetching initiatives:', err)
      setError('An error occurred while fetching data')
    } finally {
      setIsLoading(false)
    }
  }

  // Effect to fetch initiatives when filters change
  useEffect(() => {
    // Reset to page 1 when filters change
    setPagination(prev => ({ ...prev, currentPage: 1 }))
    fetchInitiatives(1)
  }, [searchTerm, statusFilter, activeTab])
  
  // Effect to fetch initiatives when page changes
  useEffect(() => {
    if (pagination.currentPage > 1) {
      fetchInitiatives(pagination.currentPage)
    }
  }, [pagination.currentPage])

  const getInitiativeById = (id: string) => {
    return initiatives.find((initiative) => initiative.id === id)
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="bg-[#0a8754] text-white py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">متابعة تقدم المبادرات</h1>
          <p className="text-lg opacity-90 mb-8">
            تابع تقدم المبادرات التي تم اعتمادها، واطلع على مراحل التنفيذ والإنجازات
          </p>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="ابحث عن مبادرة..."
                className="pl-10 bg-white text-black border-0 h-12"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-[180px] bg-white text-black border-0 h-12">
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المبادرات</SelectItem>
                <SelectItem value="active">الجارية</SelectItem>
                <SelectItem value="completed">المكتملة</SelectItem>
              </SelectContent>
            </Select>
            <Button className="h-12 bg-[#d9364c] hover:bg-[#c02e42]">
              <Filter size={18} className="mr-2" />
              تصفية
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 md:p-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-8">
          <TabsList className="bg-white">
            <TabsTrigger value="all">جميع المبادرات</TabsTrigger>
            <TabsTrigger value="active">الجارية</TabsTrigger>
            <TabsTrigger value="completed">المكتملة</TabsTrigger>
            <TabsTrigger value="my">مبادراتي</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <h2 className="text-xl font-bold mb-4">المبادرات</h2>
              {isLoading ? (
                <div className="flex items-center justify-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                  <span className="ml-2">جاري التحميل...</span>
                </div>
              ) : error ? (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ) : initiatives.length === 0 ? (
                <div className="text-center py-10">
                  <p className="text-gray-500">لا توجد مبادرات مطابقة للبحث</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {initiatives.map((initiative) => (
                    <div
                      key={initiative.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedInitiative === initiative.id ? "bg-[#0a8754] text-white" : "bg-gray-50 hover:bg-gray-100"
                      }`}
                      onClick={() => setSelectedInitiative(initiative.id)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3
                          className={`font-medium ${selectedInitiative === initiative.id ? "text-white" : "text-[#0a8754]"}`}
                        >
                          {initiative.title}
                        </h3>
                        <Badge
                          className={`${
                            initiative.status === "مكتملة"
                              ? "bg-blue-500"
                              : initiative.progress > 75
                                ? "bg-green-500"
                                : initiative.progress > 25
                                  ? "bg-yellow-500"
                                  : "bg-orange-500"
                          }`}
                        >
                          {initiative.status === "مكتملة" ? "مكتملة" : `${initiative.progress}%`}
                        </Badge>
                      </div>
                      <div
                        className={`text-sm ${selectedInitiative === initiative.id ? "text-white opacity-90" : "text-gray-500"}`}
                      >
                        {initiative.category} | {initiative.location}
                      </div>
                      <Progress
                        value={initiative.progress}
                        className={`h-2 mt-2 ${selectedInitiative === initiative.id ? "bg-white bg-opacity-30" : "bg-gray-200"}`}
                        indicatorClassName={selectedInitiative === initiative.id ? "bg-white" : "bg-[#0a8754]"}
                      />
                    </div>
                  ))}
                </div>
              )}
              
              {/* Pagination controls */}
              {pagination.totalPages > 1 && (
                <div className="flex justify-center mt-4 gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
                    disabled={!pagination.hasPrevPage || isLoading}
                  >
                    <ChevronRight className="h-4 w-4 ml-1" />
                    السابق
                  </Button>
                  <div className="flex items-center px-2">
                    <span className="text-xs">
                      {pagination.currentPage} / {pagination.totalPages}
                    </span>
                  </div>
                  <Button 
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
                    disabled={!pagination.hasNextPage || isLoading}
                  >
                    التالي
                    <ChevronLeft className="h-4 w-4 mr-1" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="lg:col-span-2">
            {isLoading ? (
              <div className="bg-white rounded-lg shadow-md p-6 flex items-center justify-center min-h-[60vh]">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="ml-2">جاري التحميل...</span>
              </div>
            ) : selectedInitiative ? (
              <div className="bg-white rounded-lg shadow-md p-6">
                {(() => {
                  const initiative = getInitiativeById(selectedInitiative)
                  if (!initiative) return null

                  return (
                    <>
                      <div className="flex justify-between items-start mb-6">
                        <div>
                          <h2 className="text-2xl font-bold text-[#0a8754] mb-2">{initiative.title}</h2>
                          <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                            <span>{initiative.category}</span>
                            <span>•</span>
                            <span>{initiative.location}</span>
                          </div>
                        </div>
                        <Badge className={`${initiative.status === "مكتملة" ? "bg-blue-500" : "bg-green-500"}`}>
                          {initiative.status}
                        </Badge>
                      </div>

                      <div className="mb-8">
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">التقدم الإجمالي</span>
                          <span className="text-sm font-medium">{initiative.progress}%</span>
                        </div>
                        <Progress
                          value={initiative.progress}
                          className="h-3 bg-gray-200"
                          indicatorClassName="bg-[#0a8754]"
                        />
                      </div>

                      <div className="mb-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-semibold">مراحل التنفيذ</h3>
                          <div className="text-sm text-gray-500">
                            <span>{initiative.startDate}</span>
                            <span> - </span>
                            <span>{initiative.endDate}</span>
                          </div>
                        </div>

                        <div className="space-y-4">
                          {initiative.milestones.map((milestone, index) => (
                            <Card
                              key={milestone.id}
                              className={`border-r-4 ${
                                milestone.completed
                                  ? "border-green-500"
                                  : index === initiative.milestones.findIndex((m) => !m.completed)
                                    ? "border-yellow-500"
                                    : "border-gray-300"
                              }`}
                            >
                              <CardHeader className="p-4 pb-2">
                                <div className="flex justify-between items-start">
                                  <CardTitle className="text-base flex items-center gap-2">
                                    {milestone.completed ? (
                                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                                    ) : index === initiative.milestones.findIndex((m) => !m.completed) ? (
                                      <Clock className="h-5 w-5 text-yellow-500" />
                                    ) : (
                                      <AlertTriangle className="h-5 w-5 text-gray-400" />
                                    )}
                                    {milestone.title}
                                  </CardTitle>
                                  <Badge
                                    variant="outline"
                                    className={`${
                                      milestone.completed
                                        ? "text-green-600 border-green-200 bg-green-50"
                                        : index === initiative.milestones.findIndex((m) => !m.completed)
                                          ? "text-yellow-600 border-yellow-200 bg-yellow-50"
                                          : "text-gray-600 border-gray-200 bg-gray-50"
                                    }`}
                                  >
                                    {milestone.date}
                                  </Badge>
                                </div>
                              </CardHeader>
                              <CardContent className="p-4 pt-2">
                                <CardDescription className="text-gray-600">
                                  {milestone.description || (
                                    milestone.completed
                                      ? "تم إنجاز هذه المرحلة بنجاح"
                                      : index === initiative.milestones.findIndex((m) => !m.completed)
                                        ? "هذه المرحلة قيد التنفيذ حالياً"
                                        : "هذه المرحلة في انتظار البدء"
                                  )}
                                </CardDescription>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Link href={`/initiatives/${initiative.id}`}>
                          <Button className="flex items-center gap-2 bg-[#0a8754] hover:bg-[#097548]">
                            الذهاب إلى صفحة المبادرة
                            <ChevronRight size={16} />
                          </Button>
                        </Link>
                      </div>
                    </>
                  )
                })()}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <div className="py-12">
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">اختر مبادرة من القائمة</h3>
                  <p className="text-gray-500">يرجى اختيار مبادرة من القائمة على اليمين لعرض تفاصيل تقدمها</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

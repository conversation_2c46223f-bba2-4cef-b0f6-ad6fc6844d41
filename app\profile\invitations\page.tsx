"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Loader2, CheckCircle, XCircle, Clock } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { api } from "@/lib/api"
import { useToast } from "@/components/ui/use-toast"

interface Invitation {
  _id: string
  initiative: {
    _id: string
    title: string
    shortDescription: string
    mainImage: string
  }
  sender: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  message: string
  status: "pending" | "accepted" | "declined"
  createdAt: string
}

export default function InvitationsPage() {
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login")
      return
    }

    fetchInvitations()
  }, [isAuthenticated, router])

  const fetchInvitations = async () => {
    setIsLoading(true)
    try {
      const response = await api.get("/api/invitations")
      if (response.success) {
        setInvitations(response.invitations || [])
      }
    } catch (error) {
      console.error("Error fetching invitations:", error)
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء جلب الدعوات",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAccept = async (id: string) => {
    try {
      const response = await api.put(`/api/invitations/${id}/accept`, {})
      if (response.success) {
        toast({
          title: "تم قبول الدعوة",
          description: "تمت إضافتك كمتطوع في المبادرة",
          variant: "default",
        })
        fetchInvitations()
      }
    } catch (error) {
      console.error("Error accepting invitation:", error)
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء قبول الدعوة",
        variant: "destructive",
      })
    }
  }

  const handleDecline = async (id: string) => {
    try {
      const response = await api.put(`/api/invitations/${id}/decline`, {})
      if (response.success) {
        toast({
          title: "تم رفض الدعوة",
          description: "تم رفض الدعوة بنجاح",
          variant: "default",
        })
        fetchInvitations()
      }
    } catch (error) {
      console.error("Error declining invitation:", error)
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء رفض الدعوة",
        variant: "destructive",
      })
    }
  }

  const filteredInvitations = invitations.filter(invitation => {
    if (activeTab === "all") return true
    if (activeTab === "pending") return invitation.status === "pending"
    if (activeTab === "accepted") return invitation.status === "accepted"
    if (activeTab === "declined") return invitation.status === "declined"
    return true
  })

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري تحميل الدعوات...</span>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <h1 className="text-3xl font-bold mb-6 text-right">دعوات التطوع</h1>

      <Tabs defaultValue="all" className="mb-8" onValueChange={setActiveTab} dir="rtl">
        <TabsList className="mb-4 w-full flex justify-start">
          <TabsTrigger value="all">الكل</TabsTrigger>
          <TabsTrigger value="pending">قيد الانتظار</TabsTrigger>
          <TabsTrigger value="accepted">مقبولة</TabsTrigger>
          <TabsTrigger value="declined">مرفوضة</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {filteredInvitations.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-gray-500">لا توجد دعوات {activeTab === "pending" ? "قيد الانتظار" : activeTab === "accepted" ? "مقبولة" : activeTab === "declined" ? "مرفوضة" : ""}</p>
                <p className="text-sm text-gray-400 mt-2">ستظهر الدعوات هنا عندما يدعوك منشئو المبادرات للانضمام كمتطوع.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredInvitations.map((invitation) => (
                <Card key={invitation._id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge
                        className={
                          invitation.status === "pending"
                            ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                            : invitation.status === "accepted"
                            ? "bg-green-100 text-green-800 border-green-200"
                            : "bg-red-100 text-red-800 border-red-200"
                        }
                      >
                        {invitation.status === "pending" ? (
                          <>قيد الانتظار <Clock className="h-3 w-3 mr-1 ml-1" /></>
                        ) : invitation.status === "accepted" ? (
                          <>مقبولة <CheckCircle className="h-3 w-3 mr-1 ml-1" /></>
                        ) : (
                          <>مرفوضة <XCircle className="h-3 w-3 mr-1 ml-1" /></>
                        )}
                      </Badge>
                      <div className="flex items-center gap-2">
                        <div>
                          <CardTitle className="text-lg text-right">{invitation.sender.name}</CardTitle>
                          <CardDescription className="text-right">@{invitation.sender.username}</CardDescription>
                        </div>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={invitation.sender.avatar} alt={invitation.sender.name} />
                          <AvatarFallback>{invitation.sender.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col md:flex-row-reverse gap-4">
                      <div className="md:w-1/4">
                        <img
                          src={invitation.initiative.mainImage || "/placeholder.svg"}
                          alt={invitation.initiative.title}
                          className="w-full h-32 object-cover rounded-md"
                        />
                      </div>
                      <div className="md:w-3/4 text-right">
                        <h3 className="text-xl font-semibold mb-2">{invitation.initiative.title}</h3>
                        <p className="text-gray-600 mb-4">{invitation.initiative.shortDescription}</p>
                        <div className="bg-gray-50 p-3 rounded-md border border-gray-100">
                          <p className="text-gray-700 text-right">{invitation.message || "دعوة للانضمام كمتطوع في هذه المبادرة"}</p>
                        </div>
                        <p className="text-xs text-gray-500 mt-2 text-right">تاريخ الدعوة: {formatDate(invitation.createdAt)}</p>
                      </div>
                    </div>
                  </CardContent>
                  {invitation.status === "pending" && (
                    <CardFooter className="flex justify-start gap-2 pt-0">
                      <Button
                        className="bg-green-600 hover:bg-green-700"
                        onClick={() => handleAccept(invitation._id)}
                      >
                        قبول
                      </Button>
                      <Button
                        variant="outline"
                        className="border-red-200 text-red-600 hover:bg-red-50"
                        onClick={() => handleDecline(invitation._id)}
                      >
                        رفض
                      </Button>
                    </CardFooter>
                  )}
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

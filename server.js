require('dotenv').config();
const jwt = require('jsonwebtoken');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const mongoose = require('mongoose');
const path = require('path');

// Create Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet()); // Security headers
const corsOptions = {
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Handle preflight requests
app.options('*', cors(corsOptions)); 
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Parse URL-encoded bodies
app.use(morgan('dev')); // Logging

// Serve static files from the uploads directory
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
app.use('/api/uploads', express.static(path.join(__dirname, uploadDir)));

// Health check route
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'API is running' });
});

// Simple auth routes
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // For demo purposes, accept any login with valid format
  if (email && password && password.length >= 6) {
    const demoToken = jwt.sign(
      { id: 'demo-user-123', role: 'admin' }, 
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '1h' }
    );
    
    res.status(200).json({
      success: true,
      user: {
        id: 'demo-user-123',
        name: 'Demo User',
        email: email,
        username: email.split('@')[0],
        role: 'admin',
        avatar: '/placeholder.svg?height=100&width=100'
      },
      accessToken: demoToken
    });

    // Store demo token in memory
    console.log('[DEMO] Generated token:', demoToken.substring(0, 15) + '...');
  } else {
    res.status(401).json({
      success: false,
      error: {
        message: 'Invalid credentials',
        statusCode: 401
      }
    });
  }
});

// Import banner routes
const bannerRoutes = require('./src/routes/banner.routes');

// Mount banner routes
app.use('/api/admin/banners', bannerRoutes);

app.post('/api/auth/register', (req, res) => {
  res.status(201).json({
    success: true,
    message: 'Registration successful. Please check your email for verification.'
  });
});

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz')
  .then(() => {
    console.log('Connected to MongoDB');
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    console.log('Starting server without database connection...');
    
    // Start the server anyway
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT} (without database)`);
    });
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  // Don't crash the server, but log the error
});

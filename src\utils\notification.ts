import { Notification, User } from "../models"
import { sendNotificationEmail } from "./email"

interface NotificationData {
  recipient: string
  sender?: string
  type: string
  content: string
  relatedInitiative?: string
  relatedComment?: string
  relatedPost?: string
  link?: string
}

// Create notification
export const createNotification = async (data: NotificationData) => {
  try {
    // Create notification
    const notification = new Notification({
      recipient: data.recipient,
      sender: data.sender,
      type: data.type,
      content: data.content,
      relatedInitiative: data.relatedInitiative,
      relatedComment: data.relatedComment,
      relatedPost: data.relatedPost,
      link: data.link,
    })

    // Save notification
    await notification.save()

    // Get recipient user
    const recipient = await User.findById(data.recipient)
    if (recipient && recipient.email) {
      // Check user preferences before sending email notifications
      let shouldSendEmail = recipient.settings?.emailNotifications !== false;

      // Check specific notification type preferences
      if (data.type === 'initiative_update' && recipient.settings?.initiativeUpdates === false) {
        shouldSendEmail = false;
      } else if ((data.type === 'comment' || data.type === 'reply') && recipient.settings?.commentReplies === false) {
        shouldSendEmail = false;
      } else if (data.type === 'support' && recipient.settings?.supportNotifications === false) {
        shouldSendEmail = false;
      }

      // Send email if user preferences allow it
      if (shouldSendEmail) {
        try {
          await sendNotificationEmail(
            recipient.email,
            `New ${data.type} notification - Initiatives.dz`,
            data.content,
            data.link,
          )
        } catch (error) {
          console.error("Error sending notification email:", error)
        }
      }
    }

    return notification
  } catch (error) {
    console.error("Error creating notification:", error)
    throw error
  }
}


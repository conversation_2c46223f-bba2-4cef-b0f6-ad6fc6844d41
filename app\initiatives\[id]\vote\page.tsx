"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, CheckCircle2, ArrowLeft, Calendar } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface VotingOption {
  _id: string
  title: string
  description: string
  voteCount: number
}

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  author: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  location: string
  mainImage: string
  votingEndDate?: string
  isVotingEnabled: boolean
  votingOptions: VotingOption[]
}

export default function VotePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params

  const [initiative, setInitiative] = useState<Initiative | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedOption, setSelectedOption] = useState<string | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [isVoting, setIsVoting] = useState(false)
  const [hasVoted, setHasVoted] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [totalVotes, setTotalVotes] = useState(0)

  // Get current user from localStorage or sessionStorage
  const getCurrentUser = () => {
    if (typeof window !== "undefined") {
      const userStr = localStorage.getItem("user") || sessionStorage.getItem("user")
      return userStr ? JSON.parse(userStr) : null
    }
    return null
  }

  const user = getCurrentUser()

  // Get token from localStorage or sessionStorage
  const getToken = () => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("accessToken") || sessionStorage.getItem("accessToken")
    }
    return null
  }

  useEffect(() => {
    const fetchInitiative = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/initiatives/${id}`,
        )

        if (!response.ok) {
          throw new Error("Failed to fetch initiative")
        }

        const data = await response.json()

        if (!data.initiative.isVotingEnabled) {
          // Redirect if voting is not enabled
          router.push(`/initiatives/${id}`)
          return
        }

        setInitiative(data.initiative)

        // Calculate total votes
        const total = data.initiative.votingOptions.reduce(
          (sum: number, option: VotingOption) => sum + option.voteCount,
          0,
        )
        setTotalVotes(total)

        // Check if user has already voted
        if (user) {
          const token = getToken()
          if (token) {
            const voteResponse = await fetch(
              `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/votes/check/${id}`,
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              },
            )

            if (voteResponse.ok) {
              const voteData = await voteResponse.json()
              if (voteData.hasVoted) {
                setHasVoted(true)
                setSelectedOption(voteData.optionId)
              }
            }
          }
        }
      } catch (err: any) {
        setError(err.message || "An error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchInitiative()
  }, [id, router, user])

  const handleVote = () => {
    if (!user) {
      router.push("/auth/login")
      return
    }

    if (selectedOption) {
      setShowConfirmDialog(true)
    }
  }

  const confirmVote = async () => {
    if (!selectedOption) return

    setIsVoting(true)

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/votes/${id}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          optionId: selectedOption,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to submit vote")
      }

      // Update initiative with new vote counts
      if (initiative) {
        const updatedOptions = initiative.votingOptions.map((option) => {
          if (option._id === selectedOption) {
            return { ...option, voteCount: option.voteCount + 1 }
          }
          return option
        })

        setInitiative({
          ...initiative,
          votingOptions: updatedOptions,
        })

        setTotalVotes(totalVotes + 1)
      }

      setHasVoted(true)
      setShowConfirmDialog(false)
      setShowSuccessDialog(true)
    } catch (err: any) {
      setError(err.message || "Failed to submit vote")
    } finally {
      setIsVoting(false)
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"

    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading voting page...</span>
      </div>
    )
  }

  if (error || !initiative) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "Initiative not found or voting is not enabled"}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Link href="/initiatives">
            <Button variant="outline">Back to Initiatives</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href={`/initiatives/${id}`} className="flex items-center text-green-600 hover:text-green-800">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Initiative
        </Link>
      </div>

      <div className="mb-8">
        <div className="relative w-full h-[200px] rounded-lg overflow-hidden mb-6">
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
            <h1 className="text-3xl font-bold text-white">Vote on Initiative</h1>
          </div>
          <Image
            src={initiative.mainImage || "/placeholder.svg"}
            alt={initiative.title}
            fill
            style={{ objectFit: "cover" }}
          />
        </div>

        <Card className="mb-6">
          <CardContent className="pt-6">
            <h2 className="text-2xl font-bold mb-2">{initiative.title}</h2>
            <div className="flex flex-wrap items-center gap-2 mb-4">
              <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
              <Badge variant="outline">{initiative.location}</Badge>
              {initiative.votingEndDate && (
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Voting ends: {formatDate(initiative.votingEndDate)}</span>
                </div>
              )}
            </div>
            <p className="text-gray-700 mb-4">{initiative.shortDescription}</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <h2 className="text-xl font-bold mb-4">Cast Your Vote</h2>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {hasVoted ? (
            <Card className="bg-green-50 border-green-200 mb-6">
              <CardHeader className="pb-2">
                <CardTitle className="text-green-700 flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5" />
                  You have already voted
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-green-600">
                  Thank you for participating in this vote. You can see the current results on the right.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {initiative.votingOptions.map((option) => (
                <Card
                  key={option._id}
                  className={`cursor-pointer transition-all ${
                    selectedOption === option._id ? "border-green-600 bg-green-50" : "hover:border-gray-300"
                  }`}
                  onClick={() => setSelectedOption(option._id)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {selectedOption === option._id && <div className="h-4 w-4 rounded-full bg-green-600"></div>}
                      {option.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-700">{option.description}</CardDescription>
                  </CardContent>
                </Card>
              ))}

              <div className="flex justify-end pt-4">
                <Button
                  onClick={handleVote}
                  disabled={!selectedOption || hasVoted}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Confirm Vote
                </Button>
              </div>
            </div>
          )}
        </div>

        <div>
          <h2 className="text-xl font-bold mb-4">Current Results</h2>
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {initiative.votingOptions.map((option) => (
                  <div key={option._id} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>{option.title}</span>
                      <span>
                        {option.voteCount} votes (
                        {totalVotes > 0 ? Math.round((option.voteCount / totalVotes) * 100) : 0}%)
                      </span>
                    </div>
                    <Progress
                      value={totalVotes > 0 ? (option.voteCount / totalVotes) * 100 : 0}
                      className="h-2 bg-gray-200"
                    />
                  </div>
                ))}

                <div className="pt-4 text-center text-sm text-gray-500">Total votes: {totalVotes}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Confirm Vote Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Your Vote</DialogTitle>
            <DialogDescription>
              Are you sure you want to vote? You cannot change your vote after confirmation.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="font-medium">Your choice:</p>
            <p className="mt-1">{initiative.votingOptions.find((o) => o._id === selectedOption)?.title}</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button onClick={confirmVote} className="bg-green-600 hover:bg-green-700" disabled={isVoting}>
              {isVoting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Confirm Vote"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-green-700">
              <CheckCircle2 className="h-5 w-5" />
              Vote Submitted Successfully
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Thank you for participating in this vote. Your input helps shape the future of this initiative.</p>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowSuccessDialog(false)} className="bg-green-600 hover:bg-green-700">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


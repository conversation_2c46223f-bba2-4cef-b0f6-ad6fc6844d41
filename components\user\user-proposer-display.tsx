"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Lightbulb, Target, Puzzle } from "lucide-react"
import UserSkillsDisplay from "./user-skills-display"

interface UserProposerDisplayProps {
  ideaDescription?: string
  objectives?: string
  needs?: string
  qualifications?: string[]
  skills?: {
    name: string
    category: 'cognitive' | 'technical' | 'interpersonal' | 'organizational' | 'digital' | 'linguistic' | 'transversal'
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  }[]
  interests?: string[]
}

export default function UserProposerDisplay({
  ideaDescription,
  objectives,
  needs,
  qualifications = [],
  skills = [],
  interests = []
}: UserProposerDisplayProps) {
  // Si aucune information d'initiateur d'idées n'est disponible
  if (!ideaDescription && !objectives && !needs && !skills.length && !qualifications.length && !interests.length) {
    // Ne rien afficher, car nous affichons déjà la bio dans le composant parent
    return null;
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Description des idées */}
      {ideaDescription && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 p-3 rounded-full">
                <Lightbulb className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium">وصف عام للأفكار</h3>
            </div>
            <p className="text-gray-700">{ideaDescription}</p>
          </CardContent>
        </Card>
      )}

      {/* Objectifs */}
      {objectives && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 p-3 rounded-full">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium">الأهداف العامة</h3>
            </div>
            <p className="text-gray-700">{objectives}</p>
          </CardContent>
        </Card>
      )}

      {/* Besoins */}
      {needs && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center gap-3">
              <div className="bg-purple-100 p-3 rounded-full">
                <Puzzle className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-medium">الاحتياجات</h3>
            </div>
            <p className="text-gray-700">{needs}</p>
          </CardContent>
        </Card>
      )}

      {/* Compétences et qualifications */}
      {(skills.length > 0 || qualifications.length > 0 || interests.length > 0) && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-4">المهارات والمؤهلات</h3>
          <UserSkillsDisplay
            skills={skills}
            qualifications={qualifications}
            interests={interests}
            userType="proposer"
          />
        </div>
      )}
    </div>
  )
}

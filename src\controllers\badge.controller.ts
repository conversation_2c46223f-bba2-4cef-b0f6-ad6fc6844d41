import { Request, Response, NextFunction } from "express"
import { <PERSON><PERSON>, <PERSON>geA<PERSON>, User, Initiative } from "../models"
import { createError } from "../utils/error"
import { asyncHandler } from "../utils/error"
import mongoose from "mongoose"
import { createNotification } from "../utils/notification"
import { ActivityService } from "../services/activity.service"

/**
 * Get all badges
 * @route GET /api/badges
 * @access Public
 */
export const getAllBadges = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { category } = req.query

  const query: any = {}

  if (category) {
    query.category = category
  }

  const badges = await Badge.find(query).sort({ level: 1, name: 1 })

  res.status(200).json({
    success: true,
    badges
  })
})

/**
 * Get badge by ID
 * @route GET /api/badges/:id
 * @access Public
 */
export const getBadgeById = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid badge ID"))
  }

  const badge = await Badge.findById(id)

  if (!badge) {
    return next(createError(404, "Badge not found"))
  }

  res.status(200).json({
    success: true,
    badge
  })
})

/**
 * Create a new badge
 * @route POST /api/badges
 * @access Private (Admin)
 */
export const createBadge = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const {
    name,
    arabicName,
    description,
    arabicDescription,
    icon,
    color,
    criteria,
    category,
    level
  } = req.body

  // Vérifier si l'utilisateur a les permissions nécessaires
  // Si l'utilisateur est admin ou a la permission spécifique
  const isAdmin = req.user.role === 'admin';
  const hasPermission = isAdmin || (req.user.permissions && req.user.permissions.includes('badges:create'));

  if (!hasPermission) {
    return next(createError(403, "You don't have permission to create badges"))
  }

  // Créer le badge
  const badge = await Badge.create({
    name,
    arabicName,
    description,
    arabicDescription,
    icon,
    color,
    criteria,
    category,
    level: level || 1
  })

  res.status(201).json({
    success: true,
    badge
  })
})

/**
 * Update a badge
 * @route PUT /api/badges/:id
 * @access Private (Admin)
 */
export const updateBadge = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const {
    name,
    arabicName,
    description,
    arabicDescription,
    icon,
    color,
    criteria,
    category,
    level
  } = req.body

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid badge ID"))
  }

  // Vérifier si l'utilisateur a les permissions nécessaires
  // Si l'utilisateur est admin ou a la permission spécifique
  const isAdmin = req.user.role === 'admin';
  const hasPermission = isAdmin || (req.user.permissions && req.user.permissions.includes('badges:edit'));

  if (!hasPermission) {
    return next(createError(403, "You don't have permission to update badges"))
  }

  // Trouver et mettre à jour le badge
  const badge = await Badge.findByIdAndUpdate(
    id,
    {
      name,
      arabicName,
      description,
      arabicDescription,
      icon,
      color,
      criteria,
      category,
      level
    },
    { new: true, runValidators: true }
  )

  if (!badge) {
    return next(createError(404, "Badge not found"))
  }

  res.status(200).json({
    success: true,
    badge
  })
})

/**
 * Delete a badge
 * @route DELETE /api/badges/:id
 * @access Private (Admin)
 */
export const deleteBadge = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid badge ID"))
  }

  // Vérifier si l'utilisateur a les permissions nécessaires
  // Si l'utilisateur est admin ou a la permission spécifique
  const isAdmin = req.user.role === 'admin';
  const hasPermission = isAdmin || (req.user.permissions && req.user.permissions.includes('badges:delete'));

  if (!hasPermission) {
    return next(createError(403, "You don't have permission to delete badges"))
  }

  // Vérifier si le badge est utilisé
  const badgeAwardsCount = await BadgeAward.countDocuments({ badge: id })
  if (badgeAwardsCount > 0) {
    return next(createError(400, "Cannot delete badge because it has been awarded to users"))
  }

  // Supprimer le badge
  const badge = await Badge.findByIdAndDelete(id)

  if (!badge) {
    return next(createError(404, "Badge not found"))
  }

  res.status(200).json({
    success: true,
    message: "Badge deleted successfully"
  })
})

/**
 * Award a badge to a user
 * @route POST /api/initiatives/:initiativeId/volunteers/:userId/award-badge
 * @access Private (Initiative Author)
 */
export const awardBadgeToUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  // Récupérer les paramètres de l'URL (initiativeId peut être dans id ou initiativeId)
  const initiativeId = req.params.initiativeId || req.params.id
  const userId = req.params.userId || req.params.volunteerId
  const { badgeId, reason } = req.body

  console.log(`Awarding badge to user. Initiative ID: ${initiativeId}, User ID: ${userId}, Badge ID: ${badgeId}`)

  try {
    // Vérifier si l'initiative existe
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      console.error(`Initiative with ID ${initiativeId} not found`)
      return next(createError(404, "Initiative not found"))
    }

    // Vérifier si l'utilisateur est l'auteur de l'initiative
    if (initiative.author.toString() !== req.user.id) {
      console.error(`User ${req.user.id} is not the author of initiative ${initiativeId}`)
      return next(createError(403, "Only the initiative author can award badges"))
    }

    // Vérifier si l'utilisateur existe
    const user = await User.findById(userId)
    if (!user) {
      console.error(`User with ID ${userId} not found`)
      return next(createError(404, "User not found"))
    }

    // Vérifier si l'utilisateur est un volontaire de l'initiative
    if (!initiative.currentVolunteers.includes(userId)) {
      console.error(`User ${userId} is not a volunteer for initiative ${initiativeId}`)
      return next(createError(400, "User is not a volunteer for this initiative"))
    }

    // Vérifier si le badge existe
    const badge = await Badge.findById(badgeId)
    if (!badge) {
      console.error(`Badge with ID ${badgeId} not found`)
      return next(createError(404, "Badge not found"))
    }

    // Vérifier si le badge a déjà été attribué à cet utilisateur pour cette initiative
    const existingAward = await BadgeAward.findOne({
      badge: badgeId,
      user: userId,
      initiative: initiativeId
    })

    if (existingAward) {
      console.error(`Badge ${badgeId} already awarded to user ${userId} for initiative ${initiativeId}`)

      // Renvoyer une réponse 200 avec un message d'erreur au lieu d'une erreur 400
      // Cela permet à l'interface utilisateur de gérer l'erreur plus facilement
      return res.status(200).json({
        success: false,
        message: "Badge already awarded to this user for this initiative",
        alreadyAwarded: true
      })
    }

    console.log(`Creating badge award for user ${userId} in initiative ${initiativeId}`)

    // Créer l'attribution de badge
    const badgeAward = await BadgeAward.create({
      badge: badgeId,
      user: userId,
      initiative: initiativeId,
      awardedBy: req.user.id,
      reason: reason || badge.criteria,
      awardedAt: new Date()
    })

    console.log(`Badge award created: ${badgeAward._id}`)

    // Ajouter le badge à l'utilisateur s'il ne l'a pas déjà
    if (!user.badges || !user.badges.includes(badgeId)) {
      await User.findByIdAndUpdate(userId, { $push: { badges: badgeId } })
      console.log(`Badge ${badgeId} added to user ${userId}`)
    }

    // Créer une notification pour l'utilisateur
    try {
      await createNotification({
        recipient: userId,
        sender: req.user.id,
        type: "badge_awarded",
        content: `تم منحك شارة "${badge.arabicName}" من مبادرة "${initiative.title}"`,
        relatedInitiative: initiativeId,
        link: `/profile`,
      })
      console.log(`Notification created for user ${userId}`)
    } catch (notificationError) {
      console.error("Error creating notification:", notificationError)
      // Ne pas échouer si la notification échoue
    }

    // Enregistrer l'activité pour l'utilisateur qui reçoit le badge
    try {
      await ActivityService.badgeActivity(
        userId,
        "earn",
        badge.arabicName || badge.name,
        badge.arabicDescription || badge.description,
        req.user.id,
        initiativeId,
        initiative.title
      )
      console.log(`Activity recorded for badge recipient ${userId}`)
    } catch (activityError) {
      console.error("Error recording badge earn activity:", activityError)
    }

    // Enregistrer l'activité pour l'utilisateur qui attribue le badge
    try {
      await ActivityService.badgeActivity(
        req.user.id,
        "award",
        badge.arabicName || badge.name,
        badge.arabicDescription || badge.description,
        userId,
        initiativeId,
        initiative.title
      )
      console.log(`Activity recorded for badge awarder ${req.user.id}`)
    } catch (activityError) {
      console.error("Error recording badge award activity:", activityError)
    }

    res.status(200).json({
      success: true,
      badgeAward
    })
  } catch (error) {
    console.error("Error awarding badge:", error)
    return next(createError(500, "Error awarding badge"))
  }
})

/**
 * Get badges awarded to a user
 * @route GET /api/users/:userId/badges
 * @access Public
 */
export const getUserBadges = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { userId } = req.params

  try {
    // Vérifier si l'utilisateur existe
    const user = await User.findById(userId)

    // Même si l'utilisateur n'existe pas, nous renvoyons un tableau vide au lieu d'une erreur
    // Cela permet à l'interface utilisateur de continuer à fonctionner
    if (!user) {
      console.warn(`User with ID ${userId} not found, returning empty badges array`)
      return res.status(200).json({
        success: true,
        badgeAwards: []
      })
    }

    // Récupérer les badges de l'utilisateur avec les détails
    const badgeAwards = await BadgeAward.find({ user: userId })
      .populate('badge')
      .populate('initiative', 'title')
      .populate('awardedBy', 'name username avatar')
      .sort({ awardedAt: -1 })

    res.status(200).json({
      success: true,
      badgeAwards
    })
  } catch (error) {
    console.error("Error fetching user badges:", error)
    // En cas d'erreur, nous renvoyons également un tableau vide
    return res.status(200).json({
      success: true,
      badgeAwards: []
    })
  }
})

/**
 * Get badges awarded for an initiative
 * @route GET /api/initiatives/:initiativeId/badges
 * @access Public
 */
export const getInitiativeBadges = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiativeId } = req.params

  try {
    // Vérifier si l'initiative existe
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      console.warn(`Initiative with ID ${initiativeId} not found, returning empty badges array`)
      return res.status(200).json({
        success: true,
        badgeAwards: []
      })
    }

    // Récupérer les badges attribués pour cette initiative
    const badgeAwards = await BadgeAward.find({ initiative: initiativeId })
      .populate('badge')
      .populate('user', 'name username avatar')
      .populate('awardedBy', 'name username avatar')
      .sort({ awardedAt: -1 })

    res.status(200).json({
      success: true,
      badgeAwards
    })
  } catch (error) {
    console.error("Error fetching initiative badges:", error)
    return next(createError(500, "Error fetching initiative badges"))
  }
})

/**
 * Get badges awarded to a volunteer for a specific initiative
 * @route GET /api/initiatives/:initiativeId/volunteers/:volunteerId/badges
 * @access Public
 */
export const getVolunteerBadgesForInitiative = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { initiativeId, volunteerId } = req.params

  try {
    console.log(`Fetching badges for volunteer ${volunteerId} in initiative ${initiativeId}`)

    // Vérifier si les IDs sont valides
    if (!mongoose.Types.ObjectId.isValid(initiativeId) || !mongoose.Types.ObjectId.isValid(volunteerId)) {
      console.error(`Invalid IDs: initiativeId=${initiativeId}, volunteerId=${volunteerId}`)
      return res.status(200).json({
        success: true,
        badgeAwards: [],
        badgeIds: []
      })
    }

    // Récupérer les badges attribués à ce volontaire pour cette initiative
    const badgeAwards = await BadgeAward.find({
      initiative: initiativeId,
      user: volunteerId
    })
      .populate('badge')
      .sort({ awardedAt: -1 })

    console.log(`Found ${badgeAwards.length} badges for volunteer ${volunteerId} in initiative ${initiativeId}`)

    // Afficher les détails des badges trouvés
    if (badgeAwards.length > 0) {
      console.log("Badge awards details:", JSON.stringify(badgeAwards, null, 2))
    }

    // Extraire les IDs des badges
    const badgeIds = badgeAwards.map(award => award.badge._id)

    res.status(200).json({
      success: true,
      badgeAwards,
      badgeIds
    })
  } catch (error) {
    console.error("Error fetching volunteer badges for initiative:", error)
    // Renvoyer un tableau vide en cas d'erreur pour ne pas perturber l'interface utilisateur
    return res.status(200).json({
      success: true,
      badgeAwards: [],
      badgeIds: []
    })
  }
})

/**
 * Get all badge awards (for debugging)
 * @route GET /api/badges/awards
 * @access Public
 */
export const getAllBadgeAwards = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Récupérer tous les badges attribués
    const badgeAwards = await BadgeAward.find()
      .populate('badge')
      .populate('user', 'name username')
      .populate('initiative', 'title')
      .sort({ awardedAt: -1 })

    console.log(`Found ${badgeAwards.length} badge awards in total`)

    res.status(200).json({
      success: true,
      badgeAwards
    })
  } catch (error) {
    console.error("Error fetching all badge awards:", error)
    return next(createError(500, "Error fetching all badge awards"))
  }
})

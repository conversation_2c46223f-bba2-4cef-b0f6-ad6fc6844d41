"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Building, Briefcase, MapPin, Globe, Phone, Mail, Link as LinkIcon } from "lucide-react"

// Liste des secteurs d'activité
const INDUSTRY_SECTORS = [
  { id: "technology", name: "تكنولوجيا المعلومات" },
  { id: "education", name: "التعليم والتدريب" },
  { id: "health", name: "الصحة والرعاية الطبية" },
  { id: "finance", name: "المالية والمصرفية" },
  { id: "manufacturing", name: "التصنيع" },
  { id: "retail", name: "التجزئة والتجارة" },
  { id: "construction", name: "البناء والإنشاءات" },
  { id: "agriculture", name: "الزراعة" },
  { id: "energy", name: "الطاقة" },
  { id: "transportation", name: "النقل والمواصلات" },
  { id: "media", name: "الإعلام والاتصالات" },
  { id: "tourism", name: "السياحة والضيافة" },
  { id: "consulting", name: "الاستشارات" },
  { id: "nonprofit", name: "المنظمات غير الربحية" },
  { id: "government", name: "القطاع الحكومي" },
  { id: "other", name: "أخرى" }
]

// Liste des types de ressources
const RESOURCE_TYPES = [
  { id: "financial", name: "دعم مالي", icon: "💰" },
  { id: "space", name: "مساحات عمل", icon: "🏢" },
  { id: "equipment", name: "معدات وتجهيزات", icon: "🔧" },
  { id: "expertise", name: "خبرات ومهارات", icon: "🧠" },
  { id: "mentorship", name: "إرشاد وتوجيه", icon: "👨‍🏫" },
  { id: "networking", name: "شبكة علاقات", icon: "🔗" },
  { id: "marketing", name: "تسويق وترويج", icon: "📢" },
  { id: "logistics", name: "خدمات لوجستية", icon: "🚚" },
  { id: "technology", name: "حلول تقنية", icon: "💻" },
  { id: "training", name: "تدريب", icon: "📚" }
]

interface UserCompanyDisplayProps {
  companyName?: string
  industry?: string
  customIndustry?: string
  companyDescription?: string
  employeeCount?: string
  foundingYear?: string
  contactPhone?: string
  contactEmail?: string
  website?: string
  address?: string
  services?: string[]
  resources?: string[]
  customResources?: string[]
  supportDescription?: string
}

export default function UserCompanyDisplay({
  companyName,
  industry,
  customIndustry,
  companyDescription,
  employeeCount,
  foundingYear,
  contactPhone,
  contactEmail,
  website,
  address,
  services = [],
  resources = [],
  customResources = [],
  supportDescription
}: UserCompanyDisplayProps) {
  // Si aucune information d'entreprise n'est disponible
  if (!companyName && !industry && !services.length && !resources.length) {
    return (
      <Card>
        <CardContent className="py-8 text-center text-gray-500">
          <p>لم يتم إضافة معلومات الشركة بعد.</p>
        </CardContent>
      </Card>
    )
  }

  // Obtenir le nom du secteur d'activité
  const getIndustryName = (industryId?: string) => {
    if (!industryId) return ''
    const found = INDUSTRY_SECTORS.find(sector => sector.id === industryId)
    return found ? found.name : (customIndustry || industryId)
  }

  // Obtenir les informations sur les ressources
  const getResourceInfo = (resourceId: string) => {
    return RESOURCE_TYPES.find(resource => resource.id === resourceId) || {
      id: resourceId,
      name: resourceId,
      icon: "📦"
    }
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Informations de base */}
      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center gap-3">
            <div className="bg-green-100 p-3">
              <Building className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold">{companyName}</h3>
              {industry && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Briefcase className="h-4 w-4" />
                  <span>{getIndustryName(industry)}</span>
                </div>
              )}
            </div>
          </div>

          {companyDescription && (
            <div className="mt-4 text-gray-700">
              <p>{companyDescription}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            {employeeCount && (
              <div className="flex items-center gap-2 text-gray-600">
                <Badge variant="outline">عدد الموظفين</Badge>
                <span>{employeeCount}</span>
              </div>
            )}

            {foundingYear && (
              <div className="flex items-center gap-2 text-gray-600">
                <Badge variant="outline">سنة التأسيس</Badge>
                <span>{foundingYear}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Informations de contact */}
      {(contactPhone || contactEmail || website || address) && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <h3 className="text-lg font-medium">معلومات الاتصال</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {contactPhone && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span>{contactPhone}</span>
                </div>
              )}

              {contactEmail && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span>{contactEmail}</span>
                </div>
              )}

              {website && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Globe className="h-4 w-4" />
                  <a href={website.startsWith('http') ? website : `https://${website}`}
                     target="_blank"
                     rel="noopener noreferrer"
                     className="text-green-600 hover:underline">
                    {website}
                  </a>
                </div>
              )}

              {address && (
                <div className="flex items-center gap-2 text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{address}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Services et ressources */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Services */}
        {services && services.length > 0 && (
          <Card>
            <CardContent className="p-6 space-y-4">
              <h3 className="text-lg font-medium">الخدمات المقدمة</h3>
              <div className="flex flex-wrap gap-2">
                {services.map((service, index) => (
                  <Badge key={index} variant="outline" className="bg-blue-50 text-blue-800">
                    {service}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Ressources */}
        {(resources.length > 0 || customResources.length > 0) && (
          <Card>
            <CardContent className="p-6 space-y-4">
              <h3 className="text-lg font-medium">الموارد المتاحة</h3>
              <div className="flex flex-wrap gap-2">
                {resources.map((resourceId, index) => {
                  const resource = getResourceInfo(resourceId)
                  return (
                    <Badge key={index} variant="outline" className="bg-green-50 text-green-800">
                      <span className="mr-1">{resource.icon}</span> {resource.name}
                    </Badge>
                  )
                })}

                {customResources.map((resource, index) => (
                  <Badge key={`custom-${index}`} variant="outline" className="bg-green-50 text-green-800">
                    <span className="mr-1">📦</span> {resource}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Description du support */}
      {supportDescription && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <h3 className="text-lg font-medium">وصف الدعم الذي يمكن تقديمه</h3>
            <p className="text-gray-700">{supportDescription}</p>
          </CardContent>
        </Card>
      )}

      {/* Informations supplémentaires */}
      <Card>
        <CardContent className="p-6 space-y-4">
          <h3 className="text-lg font-medium">معلومات إضافية</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {customIndustry && (
              <div className="space-y-1">
                <p className="font-medium">مجال عمل مخصص</p>
                <p className="text-gray-700">{customIndustry}</p>
              </div>
            )}

            {customResources && customResources.length > 0 && (
              <div className="space-y-1">
                <p className="font-medium">موارد مخصصة</p>
                <div className="flex flex-wrap gap-2">
                  {customResources.map((resource, index) => (
                    <Badge key={`custom-${index}`} variant="outline" className="bg-green-50 text-green-800">
                      <span className="mr-1">📦</span> {resource}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { Alert, AlertDescription } from "../ui/alert"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { 
  Loader2, 
  AlertCircle, 
  Plus, 
  Package, 
  DollarSign, 
  Users, 
  Wrench,
  HelpCircle,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  Ban,
  Search,
  Filter,
  ArrowUpDown
} from "lucide-react"
import { Input } from "../ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"
import ResourceOfferDialog from "./resource-offer-dialog"

interface ResourceOffersListProps {
  initiativeId: string
  isAuthor: boolean
  status: string
  isDetailPage?: boolean
}

interface Resource {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  status: "requested" | "approved" | "rejected" | "delivered" | "canceled"
  requestedDate: string
  approvedDate?: string
  rejectedDate?: string
  deliveredDate?: string
  notes?: string
  provider: {
    _id: string
    name: string
    username: string
    avatar: string
    userType: string
  }
}

export default function ResourceOffersList({ initiativeId, isAuthor, status, isDetailPage = false }: ResourceOffersListProps) {
  const [resources, setResources] = useState<Resource[]>([])
  const [filteredResources, setFilteredResources] = useState<Resource[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showOfferDialog, setShowOfferDialog] = useState(false)
  
  // Filters
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("date")
  
  useEffect(() => {
    fetchResources()
  }, [initiativeId])
  
  useEffect(() => {
    applyFilters()
  }, [resources, searchQuery, typeFilter, statusFilter, sortBy])
  
  const fetchResources = async () => {
    setIsLoading(true)
    try {
      const response = await api.get(`/api/resources/initiative/${initiativeId}`, false)
      
      if (response.success) {
        setResources(response.resources || [])
      } else {
        setError("Failed to fetch resources")
      }
    } catch (err: any) {
      console.error("Error fetching resources:", err)
      setError(err.message || "An error occurred while fetching resources")
    } finally {
      setIsLoading(false)
    }
  }
  
  const applyFilters = () => {
    let filtered = [...resources]
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(resource => 
        resource.name.toLowerCase().includes(query) || 
        resource.description.toLowerCase().includes(query) ||
        resource.provider.name.toLowerCase().includes(query) ||
        (resource.notes && resource.notes.toLowerCase().includes(query))
      )
    }
    
    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter(resource => resource.type === typeFilter)
    }
    
    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(resource => resource.status === statusFilter)
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "date":
          return new Date(b.requestedDate).getTime() - new Date(a.requestedDate).getTime()
        case "name":
          return a.name.localeCompare(b.name)
        case "quantity":
          return b.quantity - a.quantity
        case "provider":
          return a.provider.name.localeCompare(b.provider.name)
        default:
          return 0
      }
    })
    
    setFilteredResources(filtered)
  }
  
  const handleResourceStatusUpdate = async (resourceId: string, newStatus: string) => {
    try {
      const response = await api.put(`/api/resources/${resourceId}/status`, {
        status: newStatus
      }, true)
      
      if (response.success) {
        toast({
          title: "تم تحديث حالة المورد",
          description: "تم تحديث حالة المورد بنجاح",
          variant: "default",
        })
        
        // Refresh resources
        fetchResources()
      } else {
        throw new Error(response.message || "Failed to update resource status")
      }
    } catch (err: any) {
      console.error("Error updating resource status:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء تحديث حالة المورد",
        variant: "destructive",
      })
    }
  }
  
  const handleResourceCancel = async (resourceId: string) => {
    try {
      const response = await api.put(`/api/resources/${resourceId}/cancel`, {}, true)
      
      if (response.success) {
        toast({
          title: "تم إلغاء عرض المورد",
          description: "تم إلغاء عرض المورد بنجاح",
          variant: "default",
        })
        
        // Refresh resources
        fetchResources()
      } else {
        throw new Error(response.message || "Failed to cancel resource offer")
      }
    } catch (err: any) {
      console.error("Error canceling resource offer:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء إلغاء عرض المورد",
        variant: "destructive",
      })
    }
  }
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }
  
  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "مادي"
      case "financial":
        return "مالي"
      case "human":
        return "بشري"
      case "service":
        return "خدمة"
      default:
        return "آخر"
    }
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "requested":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مطلوب</Badge>
      case "approved":
        return <Badge className="bg-green-100 text-green-800 border-green-200">موافق عليه</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 border-red-200">مرفوض</Badge>
      case "delivered":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">تم التسليم</Badge>
      case "canceled":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">ملغي</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }
  
  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {/* Header with Add Button */}
      {isDetailPage && (
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">عروض الموارد</h2>
          
          {!isAuthor && status === "active" && (
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={() => setShowOfferDialog(true)}
            >
              <Plus className="ml-2 h-4 w-4" />
              تقديم مورد
            </Button>
          )}
        </div>
      )}
      
      {/* Filters */}
      {isDetailPage && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="البحث عن مورد..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع المورد" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="material">مادي</SelectItem>
                  <SelectItem value="financial">مالي</SelectItem>
                  <SelectItem value="human">بشري</SelectItem>
                  <SelectItem value="service">خدمة</SelectItem>
                  <SelectItem value="other">آخر</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="requested">مطلوب</SelectItem>
                  <SelectItem value="approved">موافق عليه</SelectItem>
                  <SelectItem value="rejected">مرفوض</SelectItem>
                  <SelectItem value="delivered">تم التسليم</SelectItem>
                  <SelectItem value="canceled">ملغي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex justify-end mt-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="ترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">تاريخ التقديم</SelectItem>
                  <SelectItem value="name">الاسم</SelectItem>
                  <SelectItem value="quantity">الكمية</SelectItem>
                  <SelectItem value="provider">المقدم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Resource Offers List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-green-600" />
          <span className="mr-2">جاري تحميل الموارد...</span>
        </div>
      ) : filteredResources.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500 mb-4">لا توجد موارد مقدمة لهذه المبادرة حتى الآن.</p>
          {!isAuthor && status === "active" && (
            <Button 
              onClick={() => setShowOfferDialog(true)} 
              variant="outline" 
              className="text-green-600 border-green-200 hover:bg-green-50"
            >
              <Plus className="ml-2 h-4 w-4" />
              تقديم مورد جديد
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredResources.map((resource) => (
            <Card key={resource._id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-2 rounded-full bg-green-100">
                      {getTypeIcon(resource.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{resource.name}</CardTitle>
                      <div className="text-sm text-gray-500">{getTypeText(resource.type)}</div>
                    </div>
                  </div>
                  {getStatusBadge(resource.status)}
                </div>
              </CardHeader>
              
              <CardContent className="pb-2">
                <p className="text-sm text-gray-600 mb-3">{resource.description}</p>
                
                <div className="flex items-center gap-1 text-sm mb-2">
                  <span className="font-medium">الكمية:</span>
                  <span>{resource.quantity} {resource.unit}</span>
                </div>
                
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <Clock className="h-4 w-4 ml-1" />
                  <span>تم التقديم في {formatDate(resource.requestedDate)}</span>
                </div>
                
                {resource.approvedDate && (
                  <div className="flex items-center text-sm text-green-600 mb-1">
                    <CheckCircle className="h-4 w-4 ml-1" />
                    <span>تمت الموافقة في {formatDate(resource.approvedDate)}</span>
                  </div>
                )}
                
                {resource.rejectedDate && (
                  <div className="flex items-center text-sm text-red-600 mb-1">
                    <XCircle className="h-4 w-4 ml-1" />
                    <span>تم الرفض في {formatDate(resource.rejectedDate)}</span>
                  </div>
                )}
                
                {resource.deliveredDate && (
                  <div className="flex items-center text-sm text-purple-600 mb-1">
                    <Truck className="h-4 w-4 ml-1" />
                    <span>تم التسليم في {formatDate(resource.deliveredDate)}</span>
                  </div>
                )}
                
                <div className="mt-3 flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={resource.provider.avatar || "/placeholder.svg?height=24&width=24"} alt={resource.provider.name} />
                    <AvatarFallback>{resource.provider.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="text-sm">{resource.provider.name}</div>
                </div>
                
                {resource.notes && (
                  <div className="mt-2 p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                    <p className="font-medium mb-1">ملاحظات:</p>
                    <p>{resource.notes}</p>
                  </div>
                )}
              </CardContent>
              
              {((isAuthor && resource.status === "requested") || 
                 (resource.provider._id === (window as any).userId && 
                  (resource.status === "requested" || resource.status === "approved"))) && (
                <CardFooter className="pt-2">
                  <div className="flex justify-between w-full">
                    {isAuthor && resource.status === "requested" && (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-green-600 border-green-200 hover:bg-green-50"
                          onClick={() => handleResourceStatusUpdate(resource._id, "approved")}
                        >
                          <CheckCircle className="ml-1 h-4 w-4" />
                          قبول
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 border-red-200 hover:bg-red-50"
                          onClick={() => handleResourceStatusUpdate(resource._id, "rejected")}
                        >
                          <XCircle className="ml-1 h-4 w-4" />
                          رفض
                        </Button>
                      </div>
                    )}
                    
                    {isAuthor && resource.status === "approved" && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-purple-600 border-purple-200 hover:bg-purple-50"
                        onClick={() => handleResourceStatusUpdate(resource._id, "delivered")}
                      >
                        <Truck className="ml-1 h-4 w-4" />
                        تم التسليم
                      </Button>
                    )}
                    
                    {resource.provider._id === (window as any).userId && 
                     (resource.status === "requested" || resource.status === "approved") && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                        onClick={() => handleResourceCancel(resource._id)}
                      >
                        <Ban className="ml-1 h-4 w-4" />
                        إلغاء العرض
                      </Button>
                    )}
                  </div>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      )}
      
      {/* Resource Offer Dialog */}
      <ResourceOfferDialog
        open={showOfferDialog}
        onOpenChange={setShowOfferDialog}
        initiativeId={initiativeId}
        onSuccess={() => {
          fetchResources()
          setShowOfferDialog(false)
        }}
      />
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "../ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "../ui/tabs"
import { Alert, AlertDescription } from "../ui/alert"
import {
  Loader2,
  AlertCircle,
  UserPlus,
  UserMinus,
  Award,
  Users,
  CheckCircle2,
  Clock,
  Calendar,
  Star,
  Briefcase,
  LogOut,
  Plus,
  X
} from "lucide-react"
import VolunteerSelectorDialog from "./volunteer-selector-dialog"
import VolunteerBadges from "../../app/initiatives/[id]/components/VolunteerBadges"
import {
  Dialog,
  DialogContent,
  Di<PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "../ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { Input } from "../ui/input"
import { Textarea } from "../ui/textarea"
import { useAuth } from "../auth-provider"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"
import { Toaster } from "../ui/toaster"

interface User {
  _id: string
  name: string
  username: string
  email: string
  avatar: string
  bio?: string
  userType: string
  skills?: string[]
}

interface Volunteer {
  _id: string
  name: string
  username: string
  avatar: string
  bio?: string
  location?: string
  userType: string
  skills?: string[]
  qualifications?: string[]
}

interface VolunteerData {
  user: string
  role: string
  skills: string[]
  availability: string
  message: string
  joinedAt: string
  leftAt?: string
  status: string
  contributions: {
    description: string
    points: number
    date: string
  }[]
  points: number
}

interface VolunteersSectionProps {
  initiativeId: string
  isAuthor: boolean
  status: string
  requiredVolunteers?: number
}

export default function VolunteersSection({ initiativeId, isAuthor, status, requiredVolunteers }: VolunteersSectionProps) {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [volunteers, setVolunteers] = useState<Volunteer[]>([])
  const [volunteersData, setVolunteersData] = useState<Record<string, VolunteerData>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isJoining, setIsJoining] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)
  const [showJoinForm, setShowJoinForm] = useState(false)
  const [showPointsForm, setShowPointsForm] = useState(false)
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [showBadgeDialog, setShowBadgeDialog] = useState(false)
  const [selectedVolunteer, setSelectedVolunteer] = useState<string | null>(null)
  const [selectedVolunteerName, setSelectedVolunteerName] = useState<string>("")
  const [totalVolunteers, setTotalVolunteers] = useState(0)
  const [isUserVolunteer, setIsUserVolunteer] = useState(false)
  const [activeTab, setActiveTab] = useState("active")

  const [joinFormData, setJoinFormData] = useState({
    role: "general",
    skills: "",
    availability: "flexible",
    message: "",
  })

  const [inviteFormData, setInviteFormData] = useState({
    email: "",
    role: "general",
    message: "",
  })

  const [showVolunteerSelector, setShowVolunteerSelector] = useState(false)
  const [selectedUserToInvite, setSelectedUserToInvite] = useState<User | null>(null)

  const [pointsFormData, setPointsFormData] = useState({
    points: 0,
    description: "",
  })

  useEffect(() => {
    if (initiativeId) {
      fetchVolunteers()
    }
  }, [initiativeId])

  useEffect(() => {
    if (user && volunteers.length > 0) {
      const isVolunteer = volunteers.some(volunteer => volunteer._id === user.id)
      setIsUserVolunteer(isVolunteer)
    }
  }, [user, volunteers, isAuthenticated, status, isAuthor])

  const fetchVolunteers = async () => {
    setIsLoading(true)
    try {
      const response = await api.get(`/api/initiatives/${initiativeId}/volunteers`, false)

      if (response.success) {
        setVolunteers(response.volunteers || [])
        setTotalVolunteers(response.totalVolunteers || 0)

        // Fetch volunteer data for each volunteer if author
        if (isAuthor && response.volunteers && response.volunteers.length > 0) {
          const volunteerDataMap: Record<string, VolunteerData> = {}

          for (const volunteer of response.volunteers) {
            try {
              const dataResponse = await api.get(
                `/api/initiatives/${initiativeId}/volunteers/${volunteer._id}/contributions`,
                true
              )

              if (dataResponse.success && dataResponse.volunteerData) {
                volunteerDataMap[volunteer._id] = dataResponse.volunteerData
              }
            } catch (err) {
              console.error(`Error fetching data for volunteer ${volunteer._id}:`, err)
            }
          }

          setVolunteersData(volunteerDataMap)
        }
      } else {
        setError("Failed to fetch volunteers")
      }
    } catch (err: any) {
      console.error("Error fetching volunteers:", err)
      setError(err.message || "An error occurred while fetching volunteers")
    } finally {
      setIsLoading(false)
    }
  }

  const handleJoinInitiative = async () => {
    if (!isAuthenticated) {
      router.push("/auth/login")
      return
    }

    setIsJoining(true)
    try {
      const { role, availability, message } = joinFormData
      const skills = joinFormData.skills.split(',').map(skill => skill.trim()).filter(Boolean)

      const response = await api.post(`/api/initiatives/${initiativeId}/join`, {
        role,
        skills,
        availability,
        message,
      }, true)

      if (response.success) {
        toast({
          title: "تم الانضمام بنجاح",
          description: "لقد انضممت إلى هذه المبادرة كمتطوع",
          variant: "default",
        })

        // Refresh volunteers list
        fetchVolunteers()
        setShowJoinForm(false)

        // Reset form
        setJoinFormData({
          role: "general",
          skills: "",
          availability: "flexible",
          message: "",
        })
      } else {
        throw new Error(response.message || "Failed to join initiative")
      }
    } catch (err: any) {
      console.error("Error joining initiative:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء محاولة الانضمام إلى المبادرة",
        variant: "destructive",
      })
    } finally {
      setIsJoining(false)
    }
  }

  const handleLeaveInitiative = async () => {
    if (!isAuthenticated) {
      return
    }

    setIsLeaving(true)
    try {
      const response = await api.delete(`/api/initiatives/${initiativeId}/join`, true)

      if (response.success) {
        toast({
          title: "تم المغادرة بنجاح",
          description: "لقد غادرت هذه المبادرة",
          variant: "default",
        })

        // Refresh volunteers list
        fetchVolunteers()
      } else {
        throw new Error(response.message || "Failed to leave initiative")
      }
    } catch (err: any) {
      console.error("Error leaving initiative:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء محاولة مغادرة المبادرة",
        variant: "destructive",
      })
    } finally {
      setIsLeaving(false)
    }
  }

  const handleAddPoints = async () => {
    if (!selectedVolunteer) return

    try {
      const response = await api.post(
        `/api/initiatives/${initiativeId}/volunteers/${selectedVolunteer}/points`,
        pointsFormData,
        true
      )

      if (response.success) {
        toast({
          title: "تمت إضافة النقاط بنجاح",
          description: `تمت إضافة ${pointsFormData.points} نقطة للمتطوع`,
          variant: "default",
        })

        // Refresh volunteer data
        fetchVolunteers()
        setShowPointsForm(false)

        // Reset form
        setPointsFormData({
          points: 0,
          description: "",
        })
      } else {
        throw new Error(response.message || "Failed to add points")
      }
    } catch (err: any) {
      console.error("Error adding points:", err)
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء محاولة إضافة النقاط",
        variant: "destructive",
      })
    }
  }

  const handleJoinFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setJoinFormData(prev => ({ ...prev, [name]: value }))
  }

  const handlePointsFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setPointsFormData(prev => ({
      ...prev,
      [name]: name === "points" ? Number(value) : value
    }))
  }

  const handleInviteFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setInviteFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleInviteVolunteer = async () => {
    if (!isAuthenticated) {
      return
    }

    try {
      const { role, message } = inviteFormData
      let email = inviteFormData.email

      // Si un utilisateur a été sélectionné via le sélecteur, utiliser son email
      if (selectedUserToInvite) {
        email = selectedUserToInvite.email
      }

      if (!email.trim()) {
        toast({
          title: "خطأ",
          description: "يرجى إدخال البريد الإلكتروني للمتطوع أو اختيار مستخدم",
          variant: "destructive",
        })
        return
      }

      const response = await api.post(`/api/initiatives/${initiativeId}/invite`, {
        email,
        role,
        message,
      }, true)

      if (response.success) {
        toast({
          title: "تمت الدعوة بنجاح",
          description: "تم إرسال دعوة للمتطوع بنجاح",
          variant: "default",
        })

        // Reset form and close dialog
        setInviteFormData({
          email: "",
          role: "general",
          message: "",
        })
        setSelectedUserToInvite(null)
        setShowInviteForm(false)
      } else {
        throw new Error(response.message || "Failed to invite volunteer")
      }
    } catch (err: any) {
      console.error("Error inviting volunteer:", err)

      // Vérifier si l'erreur est liée à une invitation déjà envoyée
      if (err.message && (
          err.message.includes("تم إرسال دعوة بالفعل لهذا المستخدم") ||
          err.message.includes("already been sent") ||
          err.message.includes("already invited")
        )) {
        toast({
          title: "تنبيه",
          description: "تم إرسال دعوة بالفعل لهذا المستخدم",
          variant: "warning",
        })
      } else if (err.message && err.message.includes("هذا المستخدم متطوع بالفعل في هذه المبادرة")) {
        toast({
          title: "تنبيه",
          description: "هذا المستخدم متطوع بالفعل في هذه المبادرة",
          variant: "warning",
        })
      } else {
        toast({
          title: "خطأ",
          description: err.message || "حدث خطأ أثناء محاولة دعوة المتطوع",
          variant: "destructive",
        })
      }
    }
  }

  const handleUserSelect = (user: User) => {
    setSelectedUserToInvite(user)
    setInviteFormData(prev => ({
      ...prev,
      email: user.email
    }))
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "leader":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">قائد</Badge>
      case "coordinator":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">منسق</Badge>
      case "specialist":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">متخصص</Badge>
      case "general":
      default:
        return <Badge className="bg-green-100 text-green-800 border-green-200">متطوع عام</Badge>
    }
  }

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case "flexible":
        return "مرن"
      case "weekends":
        return "عطلة نهاية الأسبوع"
      case "evenings":
        return "المساء"
      case "fulltime":
        return "دوام كامل"
      default:
        return availability
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const filteredVolunteers = volunteers.filter(volunteer => {
    if (activeTab === "all") return true

    const volunteerData = volunteersData[volunteer._id]
    if (!volunteerData) return activeTab === "active"

    return volunteerData.status === activeTab
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold">المتطوعون</h2>
            <Link href={`/initiatives/${initiativeId}/volunteers`}>
              <Button variant="outline" size="sm">
                عرض الكل
              </Button>
            </Link>
          </div>
          <p className="text-gray-500">
            {totalVolunteers} متطوع
            {requiredVolunteers ? ` من أصل ${requiredVolunteers} مطلوب` : ""}
          </p>
        </div>

        {isAuthenticated && status === "active" && (
          <div>
            {isAuthor ? (
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => setShowInviteForm(true)}
              >
                <UserPlus className="ml-2 h-4 w-4" />
                دعوة متطوع
              </Button>
            ) : (
              <>
                {isUserVolunteer ? (
                  <Button
                    variant="outline"
                    className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                    onClick={handleLeaveInitiative}
                    disabled={isLeaving}
                  >
                    {isLeaving ? (
                      <>
                        <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                        جاري المعالجة...
                      </>
                    ) : (
                      <>
                        <UserMinus className="ml-2 h-4 w-4" />
                        مغادرة المبادرة
                      </>
                    )}
                  </Button>
                ) : (
                  <Button
                    className="bg-green-600 hover:bg-green-700"
                    onClick={() => setShowJoinForm(true)}
                  >
                    <UserPlus className="ml-2 h-4 w-4" />
                    انضم كمتطوع
                  </Button>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Join Initiative Dialog */}
      <Dialog open={showJoinForm} onOpenChange={setShowJoinForm}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>انضم إلى المبادرة كمتطوع</DialogTitle>
            <DialogDescription>
              أكمل النموذج التالي للانضمام إلى هذه المبادرة كمتطوع. سيتم إخطار منشئ المبادرة بطلبك.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="role" className="font-medium">
                الدور
              </label>
              <Select
                name="role"
                value={joinFormData.role}
                onValueChange={(value) => setJoinFormData(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر دورك" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">متطوع عام</SelectItem>
                  <SelectItem value="specialist">متخصص</SelectItem>
                  <SelectItem value="coordinator">منسق</SelectItem>
                  <SelectItem value="leader">قائد فريق</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="skills" className="font-medium">
                المهارات
              </label>
              <Input
                id="skills"
                name="skills"
                placeholder="أدخل مهاراتك مفصولة بفواصل"
                value={joinFormData.skills}
                onChange={handleJoinFormChange}
              />
              <p className="text-xs text-gray-500">مثال: تصميم، برمجة، تسويق</p>
            </div>

            <div className="space-y-2">
              <label htmlFor="availability" className="font-medium">
                التوفر
              </label>
              <Select
                name="availability"
                value={joinFormData.availability}
                onValueChange={(value) => setJoinFormData(prev => ({ ...prev, availability: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر توفرك" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="flexible">مرن</SelectItem>
                  <SelectItem value="weekends">عطلة نهاية الأسبوع</SelectItem>
                  <SelectItem value="evenings">المساء</SelectItem>
                  <SelectItem value="fulltime">دوام كامل</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="message" className="font-medium">
                رسالة
              </label>
              <Textarea
                id="message"
                name="message"
                placeholder="أخبرنا لماذا تريد الانضمام إلى هذه المبادرة"
                value={joinFormData.message}
                onChange={handleJoinFormChange}
                className="min-h-[100px]"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowJoinForm(false)}>
              إلغاء
            </Button>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handleJoinInitiative}
              disabled={isJoining}
            >
              {isJoining ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري الانضمام...
                </>
              ) : (
                "انضم الآن"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Invite Volunteer Dialog */}
      <Dialog open={showInviteForm} onOpenChange={setShowInviteForm}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>دعوة متطوع</DialogTitle>
            <DialogDescription>
              أدخل البريد الإلكتروني للشخص الذي ترغب في دعوته للانضمام إلى مبادرتك كمتطوع أو اختر مستخدم من القائمة.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex justify-between items-center mb-2">
              <label htmlFor="email" className="font-medium">
                البريد الإلكتروني
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowVolunteerSelector(true)}
              >
                <UserPlus className="ml-1 h-4 w-4" />
                اختر من القائمة
              </Button>
            </div>

            <Input
              id="email"
              name="email"
              type="email"
              placeholder="أدخل البريد الإلكتروني للمتطوع"
              value={inviteFormData.email}
              onChange={handleInviteFormChange}
              required
            />

            {selectedUserToInvite && (
              <div className="flex items-center gap-2 p-2 bg-muted/30 rounded-md">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={selectedUserToInvite.avatar || "/placeholder.svg?height=32&width=32"} alt={selectedUserToInvite.name} />
                  <AvatarFallback>{selectedUserToInvite.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-sm font-medium">{selectedUserToInvite.name}</p>
                  <p className="text-xs text-muted-foreground">@{selectedUserToInvite.username}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    setSelectedUserToInvite(null)
                    setInviteFormData(prev => ({ ...prev, email: "" }))
                  }}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">إزالة</span>
                </Button>
              </div>
            )}

            <div className="space-y-2">
              <label htmlFor="role" className="font-medium">
                الدور
              </label>
              <Select
                name="role"
                value={inviteFormData.role}
                onValueChange={(value) => setInviteFormData(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر دور المتطوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">متطوع عام</SelectItem>
                  <SelectItem value="specialist">متخصص</SelectItem>
                  <SelectItem value="coordinator">منسق</SelectItem>
                  <SelectItem value="leader">قائد فريق</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="message" className="font-medium">
                رسالة الدعوة
              </label>
              <Textarea
                id="message"
                name="message"
                placeholder="أضف رسالة شخصية للمتطوع"
                value={inviteFormData.message}
                onChange={handleInviteFormChange}
                className="min-h-[100px]"
              />
              <div className="text-sm text-muted-foreground">
                <p>نصائح لكتابة رسالة دعوة فعالة:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>قدم نفسك ومبادرتك بشكل موجز</li>
                  <li>اشرح لماذا تعتقد أن مهارات المتطوع ستكون مفيدة</li>
                  <li>حدد ما هو متوقع من المتطوع (الوقت، المهام، إلخ)</li>
                  <li>اذكر الفوائد التي سيحصل عليها المتطوع من المشاركة</li>
                </ul>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteForm(false)}>
              إلغاء
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={handleInviteVolunteer}
              disabled={!inviteFormData.email.trim()}
            >
              إرسال الدعوة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Volunteer Selector Dialog */}
      <VolunteerSelectorDialog
        open={showVolunteerSelector}
        onOpenChange={setShowVolunteerSelector}
        initiativeId={initiativeId}
        onSelect={handleUserSelect}
      />

      {/* Add Points Dialog */}
      <Dialog open={showPointsForm} onOpenChange={setShowPointsForm}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>إضافة نقاط للمتطوع</DialogTitle>
            <DialogDescription>
              أضف نقاط للمتطوع مقابل مساهمته في المبادرة.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="points" className="font-medium">
                النقاط
              </label>
              <Input
                id="points"
                name="points"
                type="number"
                min="1"
                placeholder="عدد النقاط"
                value={pointsFormData.points || ""}
                onChange={handlePointsFormChange}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="font-medium">
                وصف المساهمة
              </label>
              <Textarea
                id="description"
                name="description"
                placeholder="وصف المساهمة التي قدمها المتطوع"
                value={pointsFormData.description}
                onChange={handlePointsFormChange}
                className="min-h-[100px]"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPointsForm(false)}>
              إلغاء
            </Button>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handleAddPoints}
              disabled={!pointsFormData.points || !pointsFormData.description}
            >
              إضافة النقاط
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Volunteers List */}
      {isAuthor && volunteers.length > 0 && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="active">نشط</TabsTrigger>
            <TabsTrigger value="left">غادر</TabsTrigger>
            <TabsTrigger value="all">الكل</TabsTrigger>
          </TabsList>
        </Tabs>
      )}

      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-green-600" />
            <span className="mr-2">جاري تحميل المتطوعين...</span>
          </div>
        ) : volunteers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p>لا يوجد متطوعون لهذه المبادرة حتى الآن.</p>
            {status === "active" && !isAuthor && isAuthenticated && (
              <Button
                onClick={() => setShowJoinForm(true)}
                variant="link"
                className="text-green-600 mt-2"
              >
                كن أول متطوع
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredVolunteers.map((volunteer) => {
              const volunteerData = volunteersData[volunteer._id]

              return (
                <Card key={volunteer._id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={volunteer.avatar || "/placeholder.svg?height=40&width=40"} alt={volunteer.name} />
                          <AvatarFallback>{volunteer.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg">{volunteer.name}</CardTitle>
                          <CardDescription>@{volunteer.username}</CardDescription>
                        </div>
                      </div>

                      {volunteerData && getRoleBadge(volunteerData.role)}
                    </div>
                  </CardHeader>

                  <CardContent className="pb-2">
                    {volunteer.bio && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{volunteer.bio}</p>
                    )}

                    {volunteerData && (
                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar className="h-4 w-4 ml-1" />
                          <span>انضم في {formatDate(volunteerData.joinedAt)}</span>
                        </div>

                        {volunteerData.skills && volunteerData.skills.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {volunteerData.skills.map((skill, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        )}

                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 ml-1" />
                          <span>التوفر: {getAvailabilityText(volunteerData.availability)}</span>
                        </div>

                        {volunteerData.points > 0 && (
                          <div className="flex items-center text-sm font-medium text-amber-600">
                            <Star className="h-4 w-4 ml-1" />
                            <span>{volunteerData.points} نقطة</span>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>

                  <CardFooter className="pt-2">
                    <div className="flex justify-between w-full">
                      <Link href={`/users/${volunteer._id}`}>
                        <Button variant="outline" size="sm">
                          عرض الملف
                        </Button>
                      </Link>

                      {isAuthor && (
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-amber-600 border-amber-200 hover:bg-amber-50"
                            onClick={() => {
                              setSelectedVolunteer(volunteer._id)
                              setShowPointsForm(true)
                            }}
                          >
                            <Star className="h-4 w-4 ml-1" />
                            إضافة نقاط
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-purple-600 border-purple-200 hover:bg-purple-50"
                            onClick={() => {
                              setSelectedVolunteer(volunteer._id);
                              setSelectedVolunteerName(volunteer.name);
                              setShowBadgeDialog(true);
                            }}
                          >
                            <Award className="h-4 w-4 ml-1" />
                            منح شارة
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardFooter>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Badge Dialog */}
      <Dialog open={showBadgeDialog} onOpenChange={setShowBadgeDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>شارات المتطوع {selectedVolunteerName}</DialogTitle>
            <DialogDescription>
              عرض وإدارة الشارات التي حصل عليها المتطوع في هذه المبادرة
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {selectedVolunteer && (
              <>
                <VolunteerBadges
                  initiativeId={initiativeId}
                  volunteerId={selectedVolunteer}
                  volunteerName={selectedVolunteerName}
                  isAuthor={isAuthor}
                />
                {/* Debug info */}
                {/* Informations de base */}
                <div className="text-xs text-gray-400 mt-2">
                  Initiative ID: {initiativeId}<br />
                  Volunteer ID: {selectedVolunteer}<br />
                  Volunteer Name: {selectedVolunteerName}<br />
                  Is Author: {isAuthor ? "Yes" : "No"}
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
  )
}

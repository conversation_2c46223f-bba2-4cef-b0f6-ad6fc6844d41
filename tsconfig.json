{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": false, "outDir": "./dist", "sourceMap": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["*"]}}, "include": ["src/**/*", "app/**/*", "components/**/*", "lib/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist"]}
import mongoose from "mongoose";
import dotenv from "dotenv";
import { User, Role } from "../models";
import { logger } from "../utils/logger";

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/initiatives_dz");
    logger.info("MongoDB connected");
  } catch (error) {
    logger.error("MongoDB connection error:", error);
    process.exit(1);
  }
};

// Migrate users to new role system
const migrateUsers = async () => {
  try {
    logger.info("Starting user migration to new role system...");
    
    // Get roles
    const adminRole = await Role.findOne({ code: "admin" });
    const moderatorRole = await Role.findOne({ code: "moderator" });
    const userRole = await Role.findOne({ code: "user" });
    
    if (!adminRole || !moderatorRole || !userRole) {
      logger.error("Required roles not found. Please run init-permissions.ts first.");
      return;
    }
    
    logger.info(`Found roles: admin (${adminRole._id}), moderator (${moderatorRole._id}), user (${userRole._id})`);
    
    // Get all users
    const users = await User.find({});
    logger.info(`Found ${users.length} users to migrate`);
    
    let adminCount = 0;
    let moderatorCount = 0;
    let userCount = 0;
    let errorCount = 0;
    
    // Process each user
    for (const user of users) {
      try {
        // Skip users that already have a role object ID
        if (user.role && mongoose.Types.ObjectId.isValid(user.role.toString())) {
          logger.info(`User ${user._id} (${user.username}) already has a role object ID, skipping`);
          continue;
        }
        
        // Map string role to role object ID
        let roleId;
        if (user.role === "admin") {
          roleId = adminRole._id;
          adminCount++;
        } else if (user.role === "moderator") {
          roleId = moderatorRole._id;
          moderatorCount++;
        } else {
          roleId = userRole._id;
          userCount++;
        }
        
        // Update user
        await User.findByIdAndUpdate(user._id, { role: roleId });
        logger.info(`Updated user ${user._id} (${user.username}) with role ${roleId}`);
      } catch (err) {
        logger.error(`Error processing user ${user._id}:`, err);
        errorCount++;
      }
    }
    
    logger.info("\n--- Migration Summary ---");
    logger.info(`Total users processed: ${users.length}`);
    logger.info(`Admin users: ${adminCount}`);
    logger.info(`Moderator users: ${moderatorCount}`);
    logger.info(`Regular users: ${userCount}`);
    logger.info(`Errors: ${errorCount}`);
    
  } catch (error) {
    logger.error("Error migrating users:", error);
  }
};

// Main function
const main = async () => {
  try {
    await connectDB();
    await migrateUsers();
  } catch (error) {
    logger.error("Error:", error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    logger.info("MongoDB connection closed");
  }
};

// Run the script
main();

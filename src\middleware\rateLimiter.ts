import rateLimit from "express-rate-limit"

// Create rate limiter middleware
export const createRateLimiter = (options = {}) => {
  // Désactiver complètement la limitation en développement
  if (process.env.NODE_ENV === 'development') {
    return (req, res, next) => next(); // Passer directement au middleware suivant
  }

  return rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'), // Default: 1 minute
    max: parseInt(process.env.RATE_LIMIT_MAX || '1000'), // Default: Limit each IP to 1000 requests per windowMs
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    message: {
      success: false,
      error: {
        message: "Too many requests, please try again later.",
        statusCode: 429,
      },
    },
    ...options,
  })
}

// Create auth rate limiter middleware (more strict)
export const createAuthRateLimiter = () => {
  // Désactiver complètement la limitation en développement
  if (process.env.NODE_ENV === 'development') {
    return (req, res, next) => next(); // Passer directement au middleware suivant
  }

  // En production, utiliser des limites strictes
  return createRateLimiter({
    windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || '3600000'), // 1 hour in prod
    max: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '10'), // 10 requests in prod
    message: {
      success: false,
      error: {
        message: "Too many authentication attempts, please try again later.",
        statusCode: 429,
      },
    },
  })
}


import { Request, Response, NextFunction } from "express"
import { Resource, ResourceNeed, Initiative } from "../models"
import mongoose from "mongoose"
import { createError } from "../utils/error"

/**
 * Get resource statistics for the platform
 * @route GET /api/stats/resources
 * @access Public
 */
export const getResourceStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get counts by type
    const resourcesByType = await Resource.aggregate([
      { $group: { _id: "$type", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get counts by status
    const resourcesByStatus = await Resource.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get resource needs by type
    const needsByType = await ResourceNeed.aggregate([
      { $group: { _id: "$type", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get resource needs by priority
    const needsByPriority = await ResourceNeed.aggregate([
      { $group: { _id: "$priority", count: { $sum: 1 } } },
      { $sort: { _id: -1 } } // Sort by priority (critical, high, medium, low)
    ])

    // Get resource needs by status
    const needsByStatus = await ResourceNeed.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get top 5 initiatives with most resources
    const topInitiativesByResources = await Resource.aggregate([
      { $group: { _id: "$initiative", count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "initiatives",
          localField: "_id",
          foreignField: "_id",
          as: "initiative"
        }
      },
      { $unwind: "$initiative" },
      {
        $project: {
          _id: 0,
          initiativeId: "$initiative._id",
          title: "$initiative.title",
          count: 1
        }
      }
    ])

    // Get top 5 initiatives with most resource needs
    const topInitiativesByNeeds = await ResourceNeed.aggregate([
      { $group: { _id: "$initiative", count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "initiatives",
          localField: "_id",
          foreignField: "_id",
          as: "initiative"
        }
      },
      { $unwind: "$initiative" },
      {
        $project: {
          _id: 0,
          initiativeId: "$initiative._id",
          title: "$initiative.title",
          count: 1
        }
      }
    ])

    // Get monthly resource counts for the last 6 months
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)

    const resourcesByMonth = await Resource.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ])

    // Format the monthly data
    const monthlyData = resourcesByMonth.map(item => ({
      month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
      count: item.count
    }))

    // Return all statistics
    return res.status(200).json({
      success: true,
      statistics: {
        resourcesByType: resourcesByType.map(item => ({
          type: item._id,
          count: item.count
        })),
        resourcesByStatus: resourcesByStatus.map(item => ({
          status: item._id,
          count: item.count
        })),
        needsByType: needsByType.map(item => ({
          type: item._id,
          count: item.count
        })),
        needsByPriority: needsByPriority.map(item => ({
          priority: item._id,
          count: item.count
        })),
        needsByStatus: needsByStatus.map(item => ({
          status: item._id,
          count: item.count
        })),
        topInitiativesByResources,
        topInitiativesByNeeds,
        monthlyData
      }
    })
  } catch (error) {
    console.error("Error fetching resource statistics:", error)
    return next(createError(500, "Failed to fetch resource statistics"))
  }
}

/**
 * Get resource statistics for a specific initiative
 * @route GET /api/stats/resources/initiative/:initiativeId
 * @access Public
 */
export const getInitiativeResourceStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Get resource counts by type
    const resourcesByType = await Resource.aggregate([
      { $match: { initiative: new mongoose.Types.ObjectId(initiativeId) } },
      { $group: { _id: "$type", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get resource counts by status
    const resourcesByStatus = await Resource.aggregate([
      { $match: { initiative: new mongoose.Types.ObjectId(initiativeId) } },
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get resource need counts by type
    const needsByType = await ResourceNeed.aggregate([
      { $match: { initiative: new mongoose.Types.ObjectId(initiativeId) } },
      { $group: { _id: "$type", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get resource need counts by priority
    const needsByPriority = await ResourceNeed.aggregate([
      { $match: { initiative: new mongoose.Types.ObjectId(initiativeId) } },
      { $group: { _id: "$priority", count: { $sum: 1 } } },
      { $sort: { _id: -1 } } // Sort by priority (critical, high, medium, low)
    ])

    // Get resource need counts by status
    const needsByStatus = await ResourceNeed.aggregate([
      { $match: { initiative: new mongoose.Types.ObjectId(initiativeId) } },
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])

    // Get fulfillment rate
    const totalNeeds = await ResourceNeed.countDocuments({ initiative: initiativeId })
    const fulfilledNeeds = await ResourceNeed.countDocuments({
      initiative: initiativeId,
      status: "fulfilled"
    })
    const fulfillmentRate = totalNeeds > 0 ? Math.round((fulfilledNeeds / totalNeeds) * 100) : 0

    // Get delivery rate
    const totalResources = await Resource.countDocuments({ initiative: initiativeId })
    const deliveredResources = await Resource.countDocuments({
      initiative: initiativeId,
      status: "delivered"
    })
    const deliveryRate = totalResources > 0 ? Math.round((deliveredResources / totalResources) * 100) : 0

    // Return all statistics
    return res.status(200).json({
      success: true,
      statistics: {
        resourcesByType: resourcesByType.map(item => ({
          type: item._id,
          count: item.count
        })),
        resourcesByStatus: resourcesByStatus.map(item => ({
          status: item._id,
          count: item.count
        })),
        needsByType: needsByType.map(item => ({
          type: item._id,
          count: item.count
        })),
        needsByPriority: needsByPriority.map(item => ({
          priority: item._id,
          count: item.count
        })),
        needsByStatus: needsByStatus.map(item => ({
          status: item._id,
          count: item.count
        })),
        summary: {
          totalNeeds,
          fulfilledNeeds,
          fulfillmentRate,
          totalResources,
          deliveredResources,
          deliveryRate
        }
      }
    })
  } catch (error) {
    console.error("Error fetching initiative resource statistics:", error)
    return next(createError(500, "Failed to fetch initiative resource statistics"))
  }
}

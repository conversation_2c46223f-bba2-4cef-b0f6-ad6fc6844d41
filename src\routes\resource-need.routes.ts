import express from "express"
import { authenticate } from "../middleware/auth"
import {
  getResourceNeeds,
  getInitiativeResourceNeeds,
  createResourceNeed,
  updateResourceNeed,
  deleteResourceNeed,
  getResourceNeedById,
} from "../controllers/resource.controller"
import {
  validateResourceNeed,
  validateResourceNeedStatusUpdate
} from "../middleware/validators/resource.validator"

const router = express.Router()

// Resource needs routes
router.get("/", getResourceNeeds)
router.get("/initiative/:initiativeId", getInitiativeResourceNeeds)
router.get("/:id", getResourceNeedById) // Nouvelle route pour récupérer un besoin par ID
router.post("/initiative/:initiativeId", authenticate, validateResourceNeed, createResourceNeed)
router.put("/:id", authenticate, validateResourceNeed, updateResourceNeed)
router.put("/:id/status", authenticate, validateResourceNeedStatusUpdate, updateResourceNeed)
router.delete("/:id", authenticate, deleteResourceNeed)

export default router

const { MongoClient } = require('mongodb');

// Données des impacts sociaux
const socialImpactCategories = [
  {
    name: "Social Cohesion and Living Together",
    arabicName: "التماسك الاجتماعي والعيش المشترك",
    description: "Impacts related to social bonds and community cohesion",
    arabicDescription: "التأثيرات المتعلقة بالروابط الاجتماعية وتماسك المجتمع",
    order: 1,
    isActive: true,
    impacts: [
      {
        name: "Strengthening social bonds / interpersonal relationships",
        arabicName: "تعزيز الروابط الاجتماعية / العلاقات الشخصية",
        isActive: true,
      },
      {
        name: "Improving coexistence between different groups",
        arabicName: "تحسين التعايش بين مختلف المجموعات",
        isActive: true,
      },
      {
        name: "Reducing social isolation (elderly, isolated, etc.)",
        arabicName: "الحد من العزلة الاجتماعية (كبار السن، المعزولين، إلخ)",
        isActive: true,
      },
      {
        name: "Developing solidarity / community mutual aid",
        arabicName: "تطوير التضامن / المساعدة المتبادلة المجتمعية",
        isActive: true,
      },
      {
        name: "Creating / Strengthening spaces for meeting and dialogue",
        arabicName: "إنشاء / تعزيز مساحات للقاء والحوار",
        isActive: true,
      },
      {
        name: "Improving social climate in a neighborhood / community",
        arabicName: "تحسين المناخ الاجتماعي في حي / مجتمع",
        isActive: true,
      },
      {
        name: "Strengthening sense of community belonging",
        arabicName: "تعزيز الشعور بالانتماء للمجتمع",
        isActive: true,
      },
    ],
  },
  {
    name: "Inclusion, Equality and Rights",
    arabicName: "الشمول والمساواة والحقوق",
    description: "Impacts related to reducing inequalities and promoting inclusion",
    arabicDescription: "التأثيرات المتعلقة بالحد من التفاوتات وتعزيز الشمول",
    order: 2,
    isActive: true,
    impacts: [
      {
        name: "Reducing inequalities (social, economic, access to services...)",
        arabicName: "الحد من التفاوتات (الاجتماعية، الاقتصادية، الوصول إلى الخدمات...)",
        isActive: true,
      },
      {
        name: "Promoting inclusion of people with disabilities",
        arabicName: "تعزيز إدماج الأشخاص ذوي الإعاقة",
        isActive: true,
      },
      {
        name: "Promoting inclusion of minorities / vulnerable groups",
        arabicName: "تعزيز إدماج الأقليات / الفئات الضعيفة",
        isActive: true,
      },
      {
        name: "Fighting against discrimination (gender, origin, etc.)",
        arabicName: "مكافحة التمييز (الجنس، الأصل، إلخ)",
        isActive: true,
      },
      {
        name: "Improving access to fundamental rights (housing, health, education...)",
        arabicName: "تحسين الوصول إلى الحقوق الأساسية (السكن، الصحة، التعليم...)",
        isActive: true,
      },
      {
        name: "Promoting equal opportunities",
        arabicName: "تعزيز تكافؤ الفرص",
        isActive: true,
      },
      {
        name: "Integration of women / youth / marginalized groups",
        arabicName: "إدماج النساء / الشباب / الفئات المهمشة",
        isActive: true,
      },
    ],
  },
  {
    name: "Education, Awareness and Skills",
    arabicName: "التعليم والتوعية والمهارات",
    description: "Impacts related to education, awareness and skill development",
    arabicDescription: "التأثيرات المتعلقة بالتعليم والتوعية وتنمية المهارات",
    order: 3,
    isActive: true,
    impacts: [
      {
        name: "Improving access to education / knowledge",
        arabicName: "تحسين الوصول إلى التعليم / المعرفة",
        isActive: true,
      },
      {
        name: "Developing skills (personal, professional, civic)",
        arabicName: "تطوير المهارات (الشخصية، المهنية، المدنية)",
        isActive: true,
      },
      {
        name: "Increasing awareness on a social / environmental issue",
        arabicName: "زيادة الوعي حول قضية اجتماعية / بيئية",
        isActive: true,
      },
      {
        name: "Changing mentalities / behaviors on a given subject",
        arabicName: "تغيير العقليات / السلوكيات حول موضوع معين",
        isActive: true,
      },
      {
        name: "Prevention (health, delinquency, school dropout...)",
        arabicName: "الوقاية (الصحة، الجنوح، التسرب المدرسي...)",
        isActive: true,
      },
      {
        name: "Promoting critical thinking and active citizenship",
        arabicName: "تعزيز التفكير النقدي والمواطنة الفعالة",
        isActive: true,
      },
    ],
  },
  {
    name: "Environment and Living Environment",
    arabicName: "البيئة وبيئة المعيشة",
    description: "Impacts related to environmental protection and improvement of living conditions",
    arabicDescription: "التأثيرات المتعلقة بحماية البيئة وتحسين ظروف المعيشة",
    order: 4,
    isActive: true,
    impacts: [
      {
        name: "Protection / Restoration of the environment (biodiversity, water, air, soil)",
        arabicName: "حماية / استعادة البيئة (التنوع البيولوجي، الماء، الهواء، التربة)",
        isActive: true,
      },
      {
        name: "Beautification / Improvement of living environment (green spaces, cleanliness...)",
        arabicName: "تجميل / تحسين بيئة المعيشة (المساحات الخضراء، النظافة...)",
        isActive: true,
      },
      {
        name: "Promotion of sustainable / eco-responsible practices (recycling, energy saving, soft mobility...)",
        arabicName: "تعزيز الممارسات المستدامة / المسؤولة بيئياً (إعادة التدوير، توفير الطاقة، التنقل اللطيف...)",
        isActive: true,
      },
      {
        name: "Awareness of ecology / sustainable development",
        arabicName: "التوعية بالبيئة / التنمية المستدامة",
        isActive: true,
      },
      {
        name: "Reduction of pollution / nuisances",
        arabicName: "الحد من التلوث / الإزعاج",
        isActive: true,
      },
    ],
  },
  {
    name: "Local Economy and Employment",
    arabicName: "الاقتصاد المحلي والتوظيف",
    description: "Impacts related to local economic development and employment",
    arabicDescription: "التأثيرات المتعلقة بالتنمية الاقتصادية المحلية والتوظيف",
    order: 5,
    isActive: true,
    impacts: [
      {
        name: "Supporting local economy / short circuits",
        arabicName: "دعم الاقتصاد المحلي / الدوائر القصيرة",
        isActive: true,
      },
      {
        name: "Creating jobs / income-generating activities",
        arabicName: "خلق فرص عمل / أنشطة مدرة للدخل",
        isActive: true,
      },
      {
        name: "Improving employability / Professional insertion",
        arabicName: "تحسين القابلية للتوظيف / الإدماج المهني",
        isActive: true,
      },
      {
        name: "Fighting against economic precariousness / poverty",
        arabicName: "مكافحة الهشاشة الاقتصادية / الفقر",
        isActive: true,
      },
      {
        name: "Promoting social entrepreneurship",
        arabicName: "تعزيز ريادة الأعمال الاجتماعية",
        isActive: true,
      },
    ],
  },
  {
    name: "Health and Well-being",
    arabicName: "الصحة والرفاهية",
    description: "Impacts related to health and well-being",
    arabicDescription: "التأثيرات المتعلقة بالصحة والرفاهية",
    order: 6,
    isActive: true,
    impacts: [
      {
        name: "Improving access to healthcare / health prevention",
        arabicName: "تحسين الوصول إلى الرعاية الصحية / الوقاية الصحية",
        isActive: true,
      },
      {
        name: "Promoting physical and mental health",
        arabicName: "تعزيز الصحة البدنية والعقلية",
        isActive: true,
      },
      {
        name: "Improving general well-being of individuals / community",
        arabicName: "تحسين الرفاهية العامة للأفراد / المجتمع",
        isActive: true,
      },
      {
        name: "Supporting sick people / their caregivers",
        arabicName: "دعم المرضى / مقدمي الرعاية لهم",
        isActive: true,
      },
    ],
  },
  {
    name: "Citizenship, Democracy and Engagement",
    arabicName: "المواطنة والديمقراطية والمشاركة",
    description: "Impacts related to civic participation and democratic processes",
    arabicDescription: "التأثيرات المتعلقة بالمشاركة المدنية والعمليات الديمقراطية",
    order: 7,
    isActive: true,
    impacts: [
      {
        name: "Strengthening citizen participation / volunteer engagement",
        arabicName: "تعزيز المشاركة المدنية / المشاركة التطوعية",
        isActive: true,
      },
      {
        name: "Improving dialogue between citizens and institutions",
        arabicName: "تحسين الحوار بين المواطنين والمؤسسات",
        isActive: true,
      },
      {
        name: "Promoting local / participatory democracy",
        arabicName: "تعزيز الديمقراطية المحلية / التشاركية",
        isActive: true,
      },
      {
        name: "Developing culture of debate and deliberation",
        arabicName: "تطوير ثقافة النقاش والمداولة",
        isActive: true,
      },
    ],
  },
  {
    name: "Culture and Heritage",
    arabicName: "الثقافة والتراث",
    description: "Impacts related to culture and heritage",
    arabicDescription: "التأثيرات المتعلقة بالثقافة والتراث",
    order: 8,
    isActive: true,
    impacts: [
      {
        name: "Improving access to culture / artistic practices",
        arabicName: "تحسين الوصول إلى الثقافة / الممارسات الفنية",
        isActive: true,
      },
      {
        name: "Enhancing local heritage (material or immaterial)",
        arabicName: "تعزيز التراث المحلي (المادي أو غير المادي)",
        isActive: true,
      },
      {
        name: "Promoting cultural diversity",
        arabicName: "تعزيز التنوع الثقافي",
        isActive: true,
      },
      {
        name: "Creating cultural / artistic events",
        arabicName: "إنشاء فعاليات ثقافية / فنية",
        isActive: true,
      },
    ],
  },
];

// Fonction pour initialiser les données
async function seedSocialImpacts() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db('initiatives_dz');
    const collection = db.collection('socialimpacts');

    // Vérifier si des données existent déjà
    const existingCount = await collection.countDocuments();
    if (existingCount > 0) {
      console.log(`${existingCount} social impact categories already exist. Skipping seeding.`);
      console.log('If you want to reseed, please drop the collection first.');
      return;
    }

    // Insérer les données
    const result = await collection.insertMany(socialImpactCategories);
    console.log(`${result.insertedCount} social impact categories inserted successfully`);

  } catch (error) {
    console.error('Error seeding social impacts:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Exécuter la fonction
seedSocialImpacts();

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, ThumbsUp, MessageSquare } from "lucide-react"
import { api } from "@/lib/api"

interface Category {
  _id: string
  name: string
  arabicName: string
  description?: string
  color: string
  icon?: string
  initiativeCount: number
}

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  location: string
  mainImage: string
  supportCount: number
  commentCount: number
  status: string
  createdAt: string
}

export default function CategoriesPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [totalInitiatives, setTotalInitiatives] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    if (selectedCategory) {
      fetchInitiativesByCategory(selectedCategory._id, currentPage)
    }
  }, [selectedCategory, currentPage])

  const fetchCategories = async () => {
    try {
      setIsLoading(true)

      const response = await api.get("/api/categories", false)
      setCategories(response.categories || [])

      // Select first category by default
      if (response.categories && response.categories.length > 0) {
        setSelectedCategory(response.categories[0])
      }
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const fetchInitiativesByCategory = async (categoryId: string, page: number) => {
    try {
      setIsLoading(true)

      const response = await api.get(`/api/initiatives/category/${categoryId}?page=${page}&limit=9`, false)
      setInitiatives(response.initiatives || [])
      setTotalInitiatives(response.total || 0)
      setTotalPages(response.totalPages || 1)
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)

    // Scroll to top
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">تصفح حسب الفئات</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h2 className="text-xl font-bold mb-4">الفئات</h2>
          <div className="space-y-2">
            {categories.map((category) => (
              <div
                key={category._id}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedCategory?._id === category._id ? "bg-green-600 text-white" : "bg-gray-50 hover:bg-gray-100"
                }`}
                onClick={() => handleCategorySelect(category)}
              >
                <div className="flex items-center justify-between">
                  <div className="font-medium">{category.arabicName}</div>
                  <Badge
                    variant={selectedCategory?._id === category._id ? "outline" : "default"}
                    className={selectedCategory?._id === category._id ? "border-white text-white" : ""}
                  >
                    {category.initiativeCount}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="md:col-span-3">
          {selectedCategory ? (
            <>
              <div className="mb-6">
                <h2 className="text-2xl font-bold" style={{ color: selectedCategory.color }}>
                  {selectedCategory.arabicName}
                </h2>
                {selectedCategory.description && <p className="text-gray-600 mt-2">{selectedCategory.description}</p>}
                <div className="mt-2 text-gray-500">{totalInitiatives} مبادرة في هذه الفئة</div>
              </div>

              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                  <span className="ml-2">جاري تحميل المبادرات...</span>
                </div>
              ) : initiatives.length === 0 ? (
                <Card>
                  <CardContent className="py-12 text-center text-gray-500">
                    <p className="text-lg">لا توجد مبادرات في هذه الفئة</p>
                    <Link href="/initiatives/create">
                      <Button className="mt-4 bg-green-600 hover:bg-green-700">إنشاء مبادرة جديدة</Button>
                    </Link>
                  </CardContent>
                </Card>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {initiatives.map((initiative) => (
                      <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                        <div className="h-[160px] overflow-hidden">
                          <Image
                            src={initiative.mainImage || "/placeholder.svg"}
                            alt={initiative.title}
                            width={400}
                            height={160}
                            className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                          />
                        </div>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <Badge style={{ backgroundColor: initiative.category.color }}>
                              {initiative.category.arabicName}
                            </Badge>
                            <Badge
                              variant="outline"
                              className={
                                initiative.status === "active"
                                  ? "text-green-600 border-green-200 bg-green-50"
                                  : initiative.status === "completed"
                                    ? "text-blue-600 border-blue-200 bg-blue-50"
                                    : "text-gray-600 border-gray-200 bg-gray-50"
                              }
                            >
                              {initiative.status === "active"
                                ? "نشطة"
                                : initiative.status === "completed"
                                  ? "مكتملة"
                                  : initiative.status}
                            </Badge>
                          </div>
                          <CardTitle className="text-lg mt-2 line-clamp-1">
                            <Link
                              href={`/initiatives/${initiative._id}`}
                              className="hover:text-green-600 transition-colors"
                            >
                              {initiative.title}
                            </Link>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-gray-600 line-clamp-2 text-sm mb-4">{initiative.shortDescription}</p>
                          <div className="flex justify-between text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <ThumbsUp className="h-3 w-3" />
                              {initiative.supportCount}
                            </span>
                            <span className="flex items-center gap-1">
                              <MessageSquare className="h-3 w-3" />
                              {initiative.commentCount}
                            </span>
                            <span>{formatDate(initiative.createdAt)}</span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-8">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                        >
                          السابق
                        </Button>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        ))}
                        <Button
                          variant="outline"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                        >
                          التالي
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </>
          ) : (
            <div className="flex items-center justify-center py-12">
              <p className="text-gray-500">يرجى اختيار فئة من القائمة</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}


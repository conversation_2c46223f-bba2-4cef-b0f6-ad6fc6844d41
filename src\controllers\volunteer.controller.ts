import { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Initiative, User, Invitation } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { ActivityService } from "../services/activity.service"

/**
 * Join an initiative as a volunteer
 * @route POST /api/initiatives/:id/join
 * @access Private
 */
export const joinInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id
    const { role, skills, availability, message } = req.body

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if initiative is active
    if (initiative.status !== "active") {
      return next(createError(400, "You can only join active initiatives"))
    }

    // Check if user is already a volunteer
    if (initiative.currentVolunteers.includes(userId)) {
      return next(createError(400, "You are already a volunteer for this initiative"))
    }

    // Check if initiative has reached the required number of volunteers
    if (
      initiative.requiredVolunteers &&
      initiative.currentVolunteers.length >= initiative.requiredVolunteers
    ) {
      return next(createError(400, "This initiative has reached its volunteer capacity"))
    }

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Create volunteer application data
    const volunteerData = {
      user: userId,
      role: role || "general",
      skills: skills || [],
      availability: availability || "flexible",
      message: message || "",
      joinedAt: new Date(),
      status: "active",
      contributions: [],
      points: 0
    }

    // Add user to currentVolunteers
    initiative.currentVolunteers.push(userId)

    // Store volunteer data in a new field if it doesn't exist
    if (!initiative.volunteersData) {
      initiative.volunteersData = []
    }

    initiative.volunteersData.push(volunteerData)

    await initiative.save()

    // Create notification for initiative author
    if (initiative.author.toString() !== userId) {
      await createNotification({
        recipient: initiative.author,
        sender: userId,
        type: "volunteer_join",
        content: `${user.name} has joined your initiative "${initiative.title}" as a volunteer`,
        relatedInitiative: initiative._id,
        link: `/initiatives/${initiative._id}/volunteers`,
      })
    }

    // Enregistrer l'activité
    await ActivityService.volunteerActivity(
      userId,
      "join",
      initiative._id.toString(),
      initiative.title,
      undefined,
      {
        role: volunteerData.role,
        skills: volunteerData.skills,
        availability: volunteerData.availability,
        joinDate: volunteerData.joinedAt
      }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "You have successfully joined this initiative as a volunteer",
      volunteersCount: initiative.currentVolunteers.length,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Leave an initiative as a volunteer
 * @route DELETE /api/initiatives/:id/join
 * @access Private
 */
export const leaveInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is a volunteer
    if (!initiative.currentVolunteers.includes(userId)) {
      return next(createError(400, "You are not a volunteer for this initiative"))
    }

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Remove user from currentVolunteers
    initiative.currentVolunteers = initiative.currentVolunteers.filter(
      (volunteerId) => volunteerId.toString() !== userId
    )

    // Update volunteer status in volunteersData
    if (initiative.volunteersData) {
      initiative.volunteersData = initiative.volunteersData.map(volunteer => {
        if (volunteer.user.toString() === userId) {
          return {
            ...volunteer,
            status: "left",
            leftAt: new Date()
          }
        }
        return volunteer
      })
    }

    await initiative.save()

    // Create notification for initiative author
    if (initiative.author.toString() !== userId) {
      await createNotification({
        recipient: initiative.author,
        sender: userId,
        type: "volunteer_leave",
        content: `${user.name} has left your initiative "${initiative.title}" as a volunteer`,
        relatedInitiative: initiative._id,
        link: `/initiatives/${initiative._id}/volunteers`,
      })
    }

    // Trouver les données du volontaire
    let volunteerData = null;
    if (initiative.volunteersData) {
      volunteerData = initiative.volunteersData.find(vol => vol.user.toString() === userId);
    }

    // Enregistrer l'activité
    await ActivityService.volunteerActivity(
      userId,
      "leave",
      initiative._id.toString(),
      initiative.title,
      undefined,
      {
        role: volunteerData?.role || "general",
        joinDate: volunteerData?.joinedAt,
        leftDate: new Date(),
        duration: volunteerData?.joinedAt ?
          Math.round((new Date().getTime() - new Date(volunteerData.joinedAt).getTime()) / (1000 * 60 * 60 * 24)) + " يوم" :
          undefined
      }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "You have successfully left this initiative",
      volunteersCount: initiative.currentVolunteers.length,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get volunteers for an initiative
 * @route GET /api/initiatives/:id/volunteers
 * @access Public
 */
export const getInitiativeVolunteers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Get volunteers
    const volunteers = await User.find({
      _id: { $in: initiative.currentVolunteers },
    })
      .select("_id name username avatar bio location userType skills qualifications")
      .skip(skip)
      .limit(limit)

    // Get total count
    const totalVolunteers = initiative.currentVolunteers.length

    // Return volunteers
    res.status(200).json({
      success: true,
      volunteers,
      totalVolunteers,
      page,
      totalPages: Math.ceil(totalVolunteers / limit),
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Add contribution points to a volunteer
 * @route POST /api/initiatives/:id/volunteers/:volunteerId/points
 * @access Private (Initiative Author Only)
 */
export const addVolunteerPoints = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id, volunteerId } = req.params
    const userId = req.user.id
    const { points, description } = req.body

    // Validate points
    if (!points || points <= 0) {
      return next(createError(400, "Points must be a positive number"))
    }

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Validate volunteer ID
    if (!mongoose.Types.ObjectId.isValid(volunteerId)) {
      return next(createError(400, "Invalid volunteer ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the initiative author
    if (initiative.author.toString() !== userId) {
      return next(createError(403, "Only the initiative author can add points to volunteers"))
    }

    // Check if volunteer is part of the initiative
    if (!initiative.currentVolunteers.includes(volunteerId)) {
      return next(createError(400, "This user is not a volunteer for this initiative"))
    }

    // Find volunteer
    const volunteer = await User.findById(volunteerId)
    if (!volunteer) {
      return next(createError(404, "Volunteer not found"))
    }

    // Add contribution to volunteersData
    if (initiative.volunteersData) {
      initiative.volunteersData = initiative.volunteersData.map(vol => {
        if (vol.user.toString() === volunteerId) {
          const newContribution = {
            description: description || "Contribution to the initiative",
            points,
            date: new Date()
          }

          return {
            ...vol,
            contributions: [...vol.contributions, newContribution],
            points: vol.points + points
          }
        }
        return vol
      })
    }

    await initiative.save()

    // Create notification for volunteer
    await createNotification({
      recipient: volunteerId,
      sender: userId,
      type: "volunteer_points",
      content: `You have received ${points} points for your contribution to "${initiative.title}"`,
      relatedInitiative: initiative._id,
      link: `/initiatives/${initiative._id}`,
    })

    // Return success response
    res.status(200).json({
      success: true,
      message: `Successfully added ${points} points to ${volunteer.name}`,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get volunteer contributions for an initiative
 * @route GET /api/initiatives/:id/volunteers/:volunteerId/contributions
 * @access Private (Initiative Author or Volunteer)
 */
export const getVolunteerContributions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id, volunteerId } = req.params
    const userId = req.user.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Validate volunteer ID
    if (!mongoose.Types.ObjectId.isValid(volunteerId)) {
      return next(createError(400, "Invalid volunteer ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the initiative author or the volunteer
    if (initiative.author.toString() !== userId && volunteerId !== userId) {
      return next(createError(403, "You don't have permission to view these contributions"))
    }

    // Check if volunteer is part of the initiative
    if (!initiative.currentVolunteers.includes(volunteerId)) {
      return next(createError(400, "This user is not a volunteer for this initiative"))
    }

    // Get volunteer data
    let volunteerData = null
    if (initiative.volunteersData) {
      volunteerData = initiative.volunteersData.find(vol => vol.user.toString() === volunteerId)
    }

    if (!volunteerData) {
      return next(createError(404, "Volunteer data not found"))
    }

    // Return volunteer contributions
    res.status(200).json({
      success: true,
      volunteerData,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Invite a volunteer to an initiative
 * @route POST /api/initiatives/:id/invite
 * @access Private (Initiative Author Only)
 */
export const inviteVolunteer = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id
    const { email, role, message } = req.body

    // Validate email
    if (!email || !email.trim()) {
      return next(createError(400, "Email is required"))
    }

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the initiative author
    if (initiative.author.toString() !== userId) {
      return next(createError(403, "Only the initiative author can invite volunteers"))
    }

    // Find the author (current user)
    const author = await User.findById(userId)
    if (!author) {
      return next(createError(404, "Author not found"))
    }

    // Check if user with this email already exists
    const existingUser = await User.findOne({ email })

    if (existingUser) {
      // Check if user is already a volunteer
      if (initiative.currentVolunteers.includes(existingUser._id)) {
        return next(createError(400, "هذا المستخدم متطوع بالفعل في هذه المبادرة"))
      }

      // Check if there's already a pending invitation
      const existingInvitation = await Invitation.findOne({
        initiative: id,
        recipient: email,
        status: "pending"
      })

      if (existingInvitation) {
        return next(createError(400, "تم إرسال دعوة بالفعل لهذا المستخدم"))
      }

      // Create a new invitation
      const invitation = new Invitation({
        initiative: id,
        sender: userId,
        recipient: email,
        role: role || "general",
        message: message || "",
        status: "pending"
      })

      await invitation.save()

      // Create notification for the invited user
      await createNotification({
        recipient: existingUser._id,
        sender: userId,
        type: "volunteer_invite",
        content: `قام ${author.name} بدعوتك للانضمام إلى مبادرة "${initiative.title}" كمتطوع`,
        relatedInitiative: initiative._id,
        link: `/profile`,
      })
    } else {
      // User doesn't exist yet, still create an invitation that they'll see when they register
      const invitation = new Invitation({
        initiative: id,
        sender: userId,
        recipient: email,
        role: role || "general",
        message: message || "",
        status: "pending"
      })

      await invitation.save()
    }

    // Enregistrer l'activité
    await ActivityService.volunteerActivity(
      userId,
      "invite",
      initiative._id.toString(),
      initiative.title,
      existingUser ? existingUser._id.toString() : undefined,
      {
        invitedEmail: email,
        invitedUserName: existingUser ? existingUser.name : undefined,
        role: role || "general",
        message: message || "",
        invitationDate: new Date()
      }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: `Invitation sent to ${email}`,
    })
  } catch (error) {
    next(error)
  }
}

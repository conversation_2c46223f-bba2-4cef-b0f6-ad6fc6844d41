const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define the schemas
const impactItemSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    arabicName: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    arabicDescription: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

const socialImpactSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    arabicName: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    arabicDescription: {
      type: String,
      trim: true,
    },
    order: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    impacts: [impactItemSchema],
  },
  {
    timestamps: true,
  }
);

// Create the model
const SocialImpact = mongoose.model('SocialImpact', socialImpactSchema);

// Define the initial data
const initialData = [
  {
    name: "Social Cohesion and Living Together",
    arabicName: "التماسك الاجتماعي والعيش المشترك",
    description: "Impacts related to social cohesion and community living",
    arabicDescription: "التأثيرات المتعلقة بالتماسك الاجتماعي والعيش المجتمعي",
    order: 1,
    isActive: true,
    impacts: [
      {
        name: "Strengthening social bonds / interpersonal relationships",
        arabicName: "تعزيز الروابط الاجتماعية / العلاقات الشخصية",
        isActive: true,
      },
      {
        name: "Improving coexistence between different groups",
        arabicName: "تحسين التعايش بين مختلف المجموعات",
        isActive: true,
      },
      {
        name: "Reducing social isolation (elderly, isolated, etc.)",
        arabicName: "الحد من العزلة الاجتماعية (كبار السن، المعزولين، إلخ)",
        isActive: true,
      },
      {
        name: "Developing solidarity / community mutual aid",
        arabicName: "تطوير التضامن / المساعدة المتبادلة المجتمعية",
        isActive: true,
      },
      {
        name: "Creating / Strengthening spaces for meeting and dialogue",
        arabicName: "إنشاء / تعزيز مساحات للقاء والحوار",
        isActive: true,
      },
    ],
  },
  {
    name: "Inclusion, Equality and Rights",
    arabicName: "الشمول والمساواة والحقوق",
    description: "Impacts related to inclusion, equality and rights",
    arabicDescription: "التأثيرات المتعلقة بالشمول والمساواة والحقوق",
    order: 2,
    isActive: true,
    impacts: [
      {
        name: "Reducing inequalities (social, economic, access to services...)",
        arabicName: "الحد من التفاوتات (الاجتماعية، الاقتصادية، الوصول إلى الخدمات...)",
        isActive: true,
      },
      {
        name: "Promoting inclusion of people with disabilities",
        arabicName: "تعزيز إدماج الأشخاص ذوي الإعاقة",
        isActive: true,
      },
      {
        name: "Promoting inclusion of minorities / vulnerable groups",
        arabicName: "تعزيز إدماج الأقليات / الفئات الضعيفة",
        isActive: true,
      },
      {
        name: "Fighting against discrimination (gender, origin, etc.)",
        arabicName: "مكافحة التمييز (الجنس، الأصل، إلخ)",
        isActive: true,
      },
    ],
  },
  {
    name: "Education and Skills Development",
    arabicName: "التعليم وتنمية المهارات",
    description: "Impacts related to education and skills development",
    arabicDescription: "التأثيرات المتعلقة بالتعليم وتنمية المهارات",
    order: 3,
    isActive: true,
    impacts: [
      {
        name: "Improving access to education / training",
        arabicName: "تحسين الوصول إلى التعليم / التدريب",
        isActive: true,
      },
      {
        name: "Developing technical / professional skills",
        arabicName: "تطوير المهارات التقنية / المهنية",
        isActive: true,
      },
      {
        name: "Developing soft skills / life skills",
        arabicName: "تطوير المهارات الناعمة / مهارات الحياة",
        isActive: true,
      },
      {
        name: "Promoting digital literacy",
        arabicName: "تعزيز المعرفة الرقمية",
        isActive: true,
      },
    ],
  },
  {
    name: "Health and Well-being",
    arabicName: "الصحة والرفاهية",
    description: "Impacts related to health and well-being",
    arabicDescription: "التأثيرات المتعلقة بالصحة والرفاهية",
    order: 4,
    isActive: true,
    impacts: [
      {
        name: "Improving physical health",
        arabicName: "تحسين الصحة البدنية",
        isActive: true,
      },
      {
        name: "Improving mental health / psychological well-being",
        arabicName: "تحسين الصحة النفسية / الرفاهية النفسية",
        isActive: true,
      },
      {
        name: "Improving access to healthcare",
        arabicName: "تحسين الوصول إلى الرعاية الصحية",
        isActive: true,
      },
      {
        name: "Promoting healthy lifestyles",
        arabicName: "تعزيز أنماط الحياة الصحية",
        isActive: true,
      },
    ],
  },
  {
    name: "Environment and Sustainability",
    arabicName: "البيئة والاستدامة",
    description: "Impacts related to environment and sustainability",
    arabicDescription: "التأثيرات المتعلقة بالبيئة والاستدامة",
    order: 5,
    isActive: true,
    impacts: [
      {
        name: "Reducing environmental footprint / pollution",
        arabicName: "تقليل البصمة البيئية / التلوث",
        isActive: true,
      },
      {
        name: "Preserving biodiversity / natural resources",
        arabicName: "الحفاظ على التنوع البيولوجي / الموارد الطبيعية",
        isActive: true,
      },
      {
        name: "Promoting sustainable consumption / production",
        arabicName: "تعزيز الاستهلاك المستدام / الإنتاج",
        isActive: true,
      },
      {
        name: "Raising awareness about environmental issues",
        arabicName: "زيادة الوعي بالقضايا البيئية",
        isActive: true,
      },
    ],
  },
];

// Function to seed the data
async function seedSocialImpacts() {
  try {
    // Check if data already exists and delete it
    const count = await SocialImpact.countDocuments();
    if (count > 0) {
      console.log(`Found ${count} existing social impact categories. Deleting them...`);
      await SocialImpact.deleteMany({});
      console.log('Existing social impact categories deleted.');
    }

    // Insert the data
    await SocialImpact.insertMany(initialData);
    console.log('Social impact categories seeded successfully!');
  } catch (error) {
    console.error('Error seeding social impact categories:', error);
  } finally {
    // Close the connection
    mongoose.connection.close();
  }
}

// Run the seed function
seedSocialImpacts();

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Users, FileText, Flag, Loader2, CheckCircle2, XCircle, ArrowUpRight } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
// Removed AdminLayout import as it's handled by app/admin/layout.tsx
import { api } from "@/lib/api"

export default function AdminDashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [stats, setStats] = useState({
    totalUsers: 0,
    newUsersToday: 0,
    totalInitiatives: 0,
    pendingInitiatives: 0,
    totalReports: 0,
    pendingReports: 0,
    totalCategories: 0,
  })
  const [pendingInitiatives, setPendingInitiatives] = useState([])
  const [recentUsers, setRecentUsers] = useState([])
  const [pendingReports, setPendingReports] = useState([])

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)

        // Fetch dashboard stats
        try {
          const statsResponse = await api.get("/api/admin/stats")
          setStats(statsResponse.stats)
        } catch (statsError) {
          console.error('Error fetching stats:', statsError)
          // Use default stats
          setStats({
            totalUsers: 0,
            newUsersToday: 0,
            totalInitiatives: 0,
            pendingInitiatives: 0,
            totalReports: 0,
            pendingReports: 0,
            totalCategories: 0
          })
        }

        // Fetch pending initiatives
        try {
          const initiativesResponse = await api.get("/api/admin/initiatives/pending?limit=5")
          setPendingInitiatives(initiativesResponse.initiatives || [])
        } catch (initiativesError) {
          console.error('Error fetching pending initiatives:', initiativesError)
          setPendingInitiatives([])
        }

        // Fetch recent users
        try {
          const usersResponse = await api.get("/api/admin/users/recent?limit=5")
          setRecentUsers(usersResponse.users || [])
        } catch (usersError) {
          console.error('Error fetching recent users:', usersError)
          setRecentUsers([])
        }

        // Fetch pending reports
        try {
          const reportsResponse = await api.get("/api/admin/reports/pending?limit=5")
          setPendingReports(reportsResponse.reports || [])
        } catch (reportsError) {
          console.error('Error fetching pending reports:', reportsError)
          setPendingReports([])
        }
      } catch (err) {
        setError(err.message || "Failed to load dashboard data")
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري تحميل البيانات...</span>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">لوحة التحكم</h1>
        <p className="text-sm text-muted-foreground">مرحباً بك في لوحة تحكم المشرف</p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">+{stats.newUsersToday} اليوم</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المبادرات</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalInitiatives}</div>
            <p className="text-xs text-muted-foreground">{stats.pendingInitiatives} بانتظار الموافقة</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">البلاغات</CardTitle>
            <Flag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalReports}</div>
            <p className="text-xs text-muted-foreground">{stats.pendingReports} بانتظار المراجعة</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">التصنيفات</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCategories}</div>
            <p className="text-xs text-muted-foreground">تصنيفات المبادرات</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="initiatives" className="space-y-4">
        <TabsList>
          <TabsTrigger value="initiatives">المبادرات المعلقة</TabsTrigger>
          <TabsTrigger value="users">المستخدمين الجدد</TabsTrigger>
          <TabsTrigger value="reports">البلاغات المعلقة</TabsTrigger>
        </TabsList>

        <TabsContent value="initiatives" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>المبادرات بانتظار الموافقة</CardTitle>
              <CardDescription>مبادرات بحاجة إلى مراجعة وموافقة</CardDescription>
            </CardHeader>
            <CardContent>
              {pendingInitiatives.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">لا توجد مبادرات بانتظار الموافقة</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>العنوان</TableHead>
                      <TableHead>المستخدم</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead className="text-left">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingInitiatives.map((initiative) => (
                      <TableRow key={initiative._id}>
                        <TableCell className="font-medium">
                          <Link href={`/initiatives/${initiative._id}`} className="hover:underline">
                            {initiative.title}
                          </Link>
                        </TableCell>
                        <TableCell>{initiative.author.name}</TableCell>
                        <TableCell>{formatDate(initiative.createdAt)}</TableCell>
                        <TableCell className="text-left">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-green-600 border-green-600 hover:bg-green-50"
                            >
                              <CheckCircle2 className="ml-1 h-4 w-4" />
                              قبول
                            </Button>
                            <Button variant="outline" size="sm" className="text-red-600 border-red-600 hover:bg-red-50">
                              <XCircle className="ml-1 h-4 w-4" />
                              رفض
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              <div className="mt-4 text-left">
                <Link href="/admin/initiatives">
                  <Button variant="outline" size="sm" className="gap-1">
                    عرض جميع المبادرات
                    <ArrowUpRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>المستخدمين الجدد</CardTitle>
              <CardDescription>المستخدمين الذين انضموا مؤخراً</CardDescription>
            </CardHeader>
            <CardContent>
              {recentUsers.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">لا يوجد مستخدمين جدد</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الاسم</TableHead>
                      <TableHead>البريد الإلكتروني</TableHead>
                      <TableHead>تاريخ التسجيل</TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentUsers.map((user) => (
                      <TableRow key={user._id}>
                        <TableCell className="font-medium">
                          <Link href={`/users/${user._id}`} className="hover:underline">
                            {user.name}
                          </Link>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{formatDate(user.joinDate)}</TableCell>
                        <TableCell>
                          {user.isVerified ? (
                            <Badge className="bg-green-600">مفعل</Badge>
                          ) : (
                            <Badge variant="outline" className="text-yellow-600">
                              غير مفعل
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              <div className="mt-4 text-left">
                <Link href="/admin/users">
                  <Button variant="outline" size="sm" className="gap-1">
                    إدارة المستخدمين
                    <ArrowUpRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>البلاغات المعلقة</CardTitle>
              <CardDescription>البلاغات التي تحتاج إلى مراجعة</CardDescription>
            </CardHeader>
            <CardContent>
              {pendingReports.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">لا توجد بلاغات معلقة</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>النوع</TableHead>
                      <TableHead>السبب</TableHead>
                      <TableHead>المبلغ</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead className="text-left">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingReports.map((report) => (
                      <TableRow key={report._id}>
                        <TableCell>
                          <Badge>
                            {report.type === "initiative"
                              ? "مبادرة"
                              : report.type === "comment"
                                ? "تعليق"
                                : report.type === "user"
                                  ? "مستخدم"
                                  : report.type}
                          </Badge>
                        </TableCell>
                        <TableCell>{report.reason}</TableCell>
                        <TableCell>{report.reporter.name}</TableCell>
                        <TableCell>{formatDate(report.createdAt)}</TableCell>
                        <TableCell className="text-left">
                          <Link href={`/admin/reports/${report._id}`}>
                            <Button variant="outline" size="sm">
                              مراجعة
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              <div className="mt-4 text-left">
                <Link href="/admin/reports">
                  <Button variant="outline" size="sm" className="gap-1">
                    عرض جميع البلاغات
                    <ArrowUpRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
  )
}


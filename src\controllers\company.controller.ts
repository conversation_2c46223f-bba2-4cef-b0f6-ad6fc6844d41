import { Request, Response, NextFunction } from "express"
import { User } from "../models"
import { createError } from "../utils/error"

/**
 * Get all companies with pagination and filtering
 * @route GET /api/companies
 * @access Public
 */
export const getCompanies = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 12
    const skip = (page - 1) * limit

    // Build filter object
    const filter: any = { userType: "company" }

    // Industry filter
    if (req.query.industry && req.query.industry !== "all") {
      filter.industry = req.query.industry
    }

    // Resource filter
    if (req.query.resource && req.query.resource !== "all") {
      filter.resources = req.query.resource
    }

    // Search filter
    if (req.query.search) {
      const searchTerm = req.query.search as string
      filter.$or = [
        { name: { $regex: searchTerm, $options: "i" } },
        { companyName: { $regex: searchTerm, $options: "i" } },
        { industry: { $regex: searchTerm, $options: "i" } },
        { services: { $regex: searchTerm, $options: "i" } }
      ]
    }

    // Get total count
    const total = await User.countDocuments(filter)

    // Get companies
    const companies = await User.find(filter)
      .select("name username avatar bio location companyName industry services resources companyDescription")
      .sort(req.query.sort as string || "-createdAt")
      .skip(skip)
      .limit(limit)

    res.status(200).json({
      success: true,
      companies,
      pagination: {
        total,
        count: companies.length,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Search companies
 * @route GET /api/companies/search
 * @access Public
 */
export const searchCompanies = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { q } = req.query
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 12
    const skip = (page - 1) * limit

    if (!q) {
      return next(createError(400, "Search query is required"))
    }

    // Search companies
    const query = {
      userType: "company",
      $or: [
        { name: { $regex: q as string, $options: "i" } },
        { companyName: { $regex: q as string, $options: "i" } },
        { industry: { $regex: q as string, $options: "i" } },
        { services: { $regex: q as string, $options: "i" } }
      ]
    }

    const companies = await User.find(query)
      .select("name username avatar bio location companyName industry services resources companyDescription")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)

    // Count total companies
    const total = await User.countDocuments(query)

    res.status(200).json({
      success: true,
      companies,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    })
  } catch (error) {
    next(error)
  }
}

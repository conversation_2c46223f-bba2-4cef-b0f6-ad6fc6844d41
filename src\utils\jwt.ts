import jwt from 'jsonwebtoken';

// JWT Secret keys
export const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_change_this_in_production';
export const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your_jwt_refresh_secret_key_change_this_in_production';

// JWT Expiration times
export const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
export const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// Interface for decoded token
export interface DecodedToken {
  id: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Generate access token
 * @param id User ID
 * @param role User role
 * @returns JWT token
 */
export const generateAccessToken = (id: string, role: string): string => {
  return jwt.sign({ id, role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

/**
 * Generate refresh token
 * @param id User ID
 * @returns JWT refresh token
 */
export const generateRefreshToken = (id: string): string => {
  return jwt.sign({ id }, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });
};

/**
 * Verify JWT token
 * @param token JWT token
 * @returns Decoded token or null if invalid
 */
export const verifyToken = (token: string): DecodedToken | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as DecodedToken;
  } catch (error) {
    return null;
  }
};

/**
 * Verify refresh token
 * @param token JWT refresh token
 * @returns Decoded token or null if invalid
 */
export const verifyRefreshToken = (token: string): { id: string } | null => {
  try {
    return jwt.verify(token, JWT_REFRESH_SECRET) as { id: string };
  } catch (error) {
    return null;
  }
};

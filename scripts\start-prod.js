const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Check if .env file exists
if (!fs.existsSync(path.join(process.cwd(), '.env'))) {
  console.error('Error: .env file not found. Please create one based on .env.example');
  process.exit(1);
}

// Check if .env.local file exists
if (!fs.existsSync(path.join(process.cwd(), '.env.local'))) {
  console.error('Error: .env.local file not found. Please create one based on .env.local.example');
  process.exit(1);
}

// Build Next.js app
console.log('Building Next.js application...');
const buildProcess = spawn('npm', ['run', 'build'], { stdio: 'inherit' });

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error('Error: Next.js build failed');
    process.exit(1);
  }

  console.log('Next.js build completed successfully');

  // Start the backend server
  console.log('Starting backend server...');
  const serverProcess = spawn('node', ['dist/index.js'], { stdio: 'inherit' });

  serverProcess.on('close', (code) => {
    console.error(`Backend server exited with code ${code}`);
    process.exit(code);
  });

  // Start the frontend server
  console.log('Starting frontend server...');
  const frontendProcess = spawn('npm', ['run', 'start'], { stdio: 'inherit' });

  frontendProcess.on('close', (code) => {
    console.error(`Frontend server exited with code ${code}`);
    process.exit(code);
  });

  // Handle process termination
  const handleTermination = () => {
    console.log('Shutting down servers...');
    serverProcess.kill();
    frontendProcess.kill();
    process.exit(0);
  };

  process.on('SIGINT', handleTermination);
  process.on('SIGTERM', handleTermination);
});

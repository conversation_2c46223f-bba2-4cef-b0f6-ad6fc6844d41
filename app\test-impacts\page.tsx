"use client"

import { useState, useEffect } from "react"

export default function TestImpactsPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching data from API...')
        const response = await fetch('http://localhost:5000/api/social-impacts')
        console.log('Response status:', response.status)
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`)
        }
        
        const jsonData = await response.json()
        console.log('Raw API response:', jsonData)
        
        setData(jsonData)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return <div>Loading...</div>
  }

  if (error) {
    return <div>Error: {error}</div>
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Social Impacts Test Page</h1>
      
      <div className="mb-4">
        <h2 className="text-xl font-semibold">Raw API Response:</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-[400px]">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
      
      {data && data.socialImpacts && (
        <div>
          <h2 className="text-xl font-semibold mb-2">Social Impact Categories ({data.socialImpacts.length}):</h2>
          <ul className="space-y-4">
            {data.socialImpacts.map((category: any) => (
              <li key={category._id} className="border p-4 rounded">
                <h3 className="font-bold">{category.arabicName} ({category.name})</h3>
                <p>Order: {category.order}</p>
                <p>Active: {category.isActive ? 'Yes' : 'No'}</p>
                <p>Impacts: {category.impacts.length}</p>
                
                {category.impacts.length > 0 && (
                  <ul className="mt-2 pl-4 space-y-2">
                    {category.impacts.map((impact: any) => (
                      <li key={impact._id} className="border-l-2 pl-2">
                        {impact.arabicName} ({impact.name})
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Search, Building, Loader2, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import CompanyCard from "@/components/company/company-card"
import { api } from "@/lib/api"

// Liste des secteurs d'activité
const INDUSTRY_SECTORS = [
  { id: "all", name: "جميع القطاعات" },
  { id: "technology", name: "تكنولوجيا المعلومات" },
  { id: "education", name: "التعليم والتدريب" },
  { id: "health", name: "الصحة والرعاية الطبية" },
  { id: "finance", name: "المالية والمصرفية" },
  { id: "manufacturing", name: "التصنيع" },
  { id: "retail", name: "التجزئة والتجارة" },
  { id: "construction", name: "البناء والإنشاءات" },
  { id: "agriculture", name: "الزراعة" },
  { id: "energy", name: "الطاقة" },
  { id: "transportation", name: "النقل والمواصلات" },
  { id: "media", name: "الإعلام والاتصالات" },
  { id: "tourism", name: "السياحة والضيافة" },
  { id: "consulting", name: "الاستشارات" },
  { id: "nonprofit", name: "المنظمات غير الربحية" },
  { id: "government", name: "القطاع الحكومي" },
  { id: "other", name: "أخرى" }
]

// Liste des types de ressources
const RESOURCE_TYPES = [
  { id: "all", name: "جميع الموارد" },
  { id: "financial", name: "دعم مالي", icon: "💰" },
  { id: "space", name: "مساحات عمل", icon: "🏢" },
  { id: "equipment", name: "معدات وتجهيزات", icon: "🔧" },
  { id: "expertise", name: "خبرات ومهارات", icon: "🧠" },
  { id: "mentorship", name: "إرشاد وتوجيه", icon: "👨‍🏫" },
  { id: "networking", name: "شبكة علاقات", icon: "🔗" },
  { id: "marketing", name: "تسويق وترويج", icon: "📢" },
  { id: "logistics", name: "خدمات لوجستية", icon: "🚚" },
  { id: "technology", name: "حلول تقنية", icon: "💻" },
  { id: "training", name: "تدريب", icon: "📚" }
]

interface Company {
  _id: string
  name: string
  username: string
  avatar?: string
  bio?: string
  location?: string
  companyName?: string
  industry?: string
  services?: string[]
  resources?: string[]
  companyDescription?: string
}

export default function CompaniesPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [companies, setCompanies] = useState<Company[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "")
  const [industryFilter, setIndustryFilter] = useState(searchParams.get("industry") || "all")
  const [resourceFilter, setResourceFilter] = useState(searchParams.get("resource") || "all")
  const [sortOrder, setSortOrder] = useState(searchParams.get("sort") || "newest")

  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: Number(searchParams.get("page")) || 1,
    totalPages: 1,
    total: 0,
    limit: 12,
    hasNextPage: false,
    hasPrevPage: false
  })

  // Effect to fetch companies when filters change
  useEffect(() => {
    fetchCompanies()
  }, [pagination.currentPage, industryFilter, resourceFilter, sortOrder, searchTerm])

  // Function to fetch companies with filters and pagination
  const fetchCompanies = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.limit.toString()
      })

      // Add filters
      if (searchTerm) queryParams.append("search", searchTerm)
      if (industryFilter !== "all") queryParams.append("industry", industryFilter)
      if (resourceFilter !== "all") queryParams.append("resource", resourceFilter)

      // Add sort
      if (sortOrder === "newest") {
        queryParams.append("sort", "-createdAt")
      } else if (sortOrder === "oldest") {
        queryParams.append("sort", "createdAt")
      } else if (sortOrder === "alphabetical") {
        queryParams.append("sort", "companyName")
      }

      // Fetch companies using the API helper
      console.log(`Fetching companies with query: /api/users?userType=company&${queryParams.toString()}`);
      const data = await api.get(`/api/users?userType=company&${queryParams.toString()}`)

      if (data.success) {
        setCompanies(data.users || [])
        setPagination({
          currentPage: data.pagination.currentPage,
          totalPages: data.pagination.totalPages,
          total: data.pagination.total,
          limit: data.pagination.limit,
          hasNextPage: data.pagination.hasNextPage,
          hasPrevPage: data.pagination.hasPrevPage
        })
      } else {
        throw new Error(data.error || "Failed to fetch companies")
      }
    } catch (err: any) {
      setError(err.message || "حدث خطأ أثناء جلب بيانات الشركات")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, currentPage: 1 }))

    // Update URL with search params
    updateUrlParams()

    fetchCompanies()
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }))

    // Update URL with page param
    const params = new URLSearchParams(searchParams.toString())
    params.set("page", page.toString())
    router.push(`/companies?${params.toString()}`)

    // Scroll to top
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const updateUrlParams = () => {
    const params = new URLSearchParams()

    if (searchTerm) params.set("search", searchTerm)
    if (industryFilter !== "all") params.set("industry", industryFilter)
    if (resourceFilter !== "all") params.set("resource", resourceFilter)
    if (sortOrder !== "newest") params.set("sort", sortOrder)
    if (pagination.currentPage > 1) params.set("page", pagination.currentPage.toString())

    router.push(`/companies?${params.toString()}`)
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="bg-[#0a8754] text-white py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">الشركات الداعمة</h1>
          <p className="text-lg opacity-90 mb-8">
            تعرف على الشركات والمؤسسات التي تدعم المبادرات المجتمعية وتساهم في تنميتها
          </p>

          <form onSubmit={handleSearch} className="max-w-xl mx-auto">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="ابحث عن شركة..."
                className="pr-10 bg-white text-black border-0 h-12 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button type="submit" className="absolute left-1 top-1/2 transform -translate-y-1/2 bg-green-700 hover:bg-green-800 h-10">
                بحث
              </Button>
            </div>
          </form>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col md:flex-row justify-between items-start gap-6 mb-8">
          <div className="w-full md:w-64 space-y-4">
            <Card>
              <CardContent className="p-4">
                <h2 className="text-lg font-bold mb-4 flex items-center">
                  <Building className="ml-2 h-5 w-5" />
                  تصفية النتائج
                </h2>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium block mb-2">القطاع</label>
                    <Select value={industryFilter} onValueChange={(value) => {
                      setIndustryFilter(value)
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر القطاع" />
                      </SelectTrigger>
                      <SelectContent>
                        {INDUSTRY_SECTORS.map((sector) => (
                          <SelectItem key={sector.id} value={sector.id}>
                            {sector.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium block mb-2">نوع الدعم</label>
                    <Select value={resourceFilter} onValueChange={(value) => {
                      setResourceFilter(value)
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع الدعم" />
                      </SelectTrigger>
                      <SelectContent>
                        {RESOURCE_TYPES.map((resource) => (
                          <SelectItem key={resource.id} value={resource.id}>
                            {resource.icon && <span className="ml-1">{resource.icon}</span>} {resource.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium block mb-2">الترتيب</label>
                    <Select value={sortOrder} onValueChange={(value) => {
                      setSortOrder(value)
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الترتيب" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newest">الأحدث</SelectItem>
                        <SelectItem value="oldest">الأقدم</SelectItem>
                        <SelectItem value="alphabetical">أبجدي</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex-1">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="mr-2">جاري تحميل الشركات...</span>
              </div>
            ) : companies.length === 0 ? (
              <div className="text-center py-12 bg-white rounded-lg shadow-sm">
                <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-bold mb-2">لا توجد شركات</h3>
                <p className="text-gray-500 mb-4">
                  لم يتم العثور على شركات تطابق معايير البحث الخاصة بك
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setIndustryFilter("all")
                    setResourceFilter("all")
                    setSortOrder("newest")
                    setPagination(prev => ({ ...prev, currentPage: 1 }))
                    router.push("/companies")
                  }}
                >
                  إعادة ضبط الفلاتر
                </Button>
              </div>
            ) : (
              <>
                <div className="mb-4 flex justify-between items-center">
                  <p className="text-gray-600">
                    تم العثور على {pagination.total} شركة
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6 mb-8">
                  {companies.map((company) => (
                    <CompanyCard key={company._id} company={company} />
                  ))}
                </div>

                {pagination.totalPages > 1 && (
                  <Pagination className="mt-8">
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(pagination.currentPage - 1)}
                          className={!pagination.hasPrevPage ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                        .filter(page =>
                          page === 1 ||
                          page === pagination.totalPages ||
                          (page >= pagination.currentPage - 1 && page <= pagination.currentPage + 1)
                        )
                        .map((page, i, array) => (
                          <React.Fragment key={page}>
                            {i > 0 && array[i - 1] !== page - 1 && (
                              <PaginationItem>
                                <span className="px-2">...</span>
                              </PaginationItem>
                            )}
                            <PaginationItem>
                              <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={pagination.currentPage === page}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          </React.Fragment>
                        ))
                      }

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(pagination.currentPage + 1)}
                          className={!pagination.hasNextPage ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { But<PERSON> } from "../../../../components/ui/button"

export default function MinimalTestPage() {
  const [count, setCount] = useState(0)

  const handleClick = () => {
    setCount(count + 1)
    console.log("But<PERSON> clicked, count:", count + 1)
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Minimal Test Page</h1>
      <p className="mb-4">Count: {count}</p>
      <Button onClick={handleClick}>Click Me</Button>
    </div>
  )
}

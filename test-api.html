<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div>
        <button id="testHealth">Test Health Endpoint</button>
        <button id="testInitiatives">Test Initiatives Endpoint</button>
        <button id="testInitiativeDetails">Test Initiative Details</button>
    </div>
    
    <h2>Results:</h2>
    <div id="results"></div>
    
    <script>
        // Function to add result to the page
        function addResult(title, data, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            
            const titleElement = document.createElement('h3');
            titleElement.textContent = title;
            resultDiv.appendChild(titleElement);
            
            const contentElement = document.createElement('pre');
            contentElement.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
            resultDiv.appendChild(contentElement);
            
            resultsDiv.prepend(resultDiv);
        }
        
        // Test health endpoint
        document.getElementById('testHealth').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                addResult('Health Endpoint', data);
            } catch (error) {
                addResult('Health Endpoint Error', error.message, true);
            }
        });
        
        // Test initiatives endpoint
        document.getElementById('testInitiatives').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:5000/api/initiatives');
                const data = await response.json();
                addResult('Initiatives Endpoint', {
                    status: response.status,
                    count: data.count,
                    initiatives: data.initiatives
                });
            } catch (error) {
                addResult('Initiatives Endpoint Error', error.message, true);
            }
        });
        
        // Test initiative details endpoint
        document.getElementById('testInitiativeDetails').addEventListener('click', async () => {
            try {
                // First get all initiatives to get an ID
                const listResponse = await fetch('http://localhost:5000/api/initiatives');
                const listData = await listResponse.json();
                
                if (!listData.initiatives || listData.initiatives.length === 0) {
                    addResult('Initiative Details Error', 'No initiatives found to test details', true);
                    return;
                }
                
                const initiativeId = listData.initiatives[0]._id;
                const response = await fetch(`http://localhost:5000/api/initiatives/${initiativeId}`);
                const data = await response.json();
                
                addResult('Initiative Details Endpoint', {
                    id: initiativeId,
                    status: response.status,
                    initiative: data.initiative
                });
            } catch (error) {
                addResult('Initiative Details Endpoint Error', error.message, true);
            }
        });
    </script>
</body>
</html>

import express from "express";
import { getPlatformStats } from "../controllers/stats.controller";
import { getResourceStats, getInitiativeResourceStats } from "../controllers/resource-stats.controller";

const router = express.Router();

// Get platform statistics
router.get("/", getPlatformStats);

// Get resource statistics
router.get("/resources", getResourceStats);

// Get resource statistics for a specific initiative
router.get("/resources/initiative/:initiativeId", getInitiativeResourceStats);

export default router;

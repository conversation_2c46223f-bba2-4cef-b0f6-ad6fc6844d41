// Helper to check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Function to refresh the token
const refreshToken = async (): Promise<string | null> => {
  if (!isBrowser) return null;

  try {
    console.log('Attempting to refresh token...');

    // Essayer de récupérer l'ID de l'utilisateur à partir du token expiré
    let userId = null;
    const oldToken = localStorage.getItem('accessToken');
    if (oldToken) {
      try {
        // Décoder le token sans vérifier la signature
        const base64Url = oldToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        const payload = JSON.parse(jsonPayload);
        userId = payload.id;
        console.log('Extracted user ID from expired token:', userId);
      } catch (e) {
        console.error('Failed to extract user ID from token:', e);
      }
    }

    // Préparer le corps de la requête avec l'ID de l'utilisateur si disponible
    const requestBody: any = {};
    if (userId) {
      requestBody.userId = userId;
    }

    const response = await fetch('http://localhost:5000/api/auth/refresh-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody),
      credentials: 'include',
      mode: 'cors'
    });

    if (!response.ok) {
      console.error('Failed to refresh token:', response.status, response.statusText);
      try {
        const errorText = await response.text();
        console.error('Error response:', errorText);
      } catch (e) {
        console.error('Could not read error response');
      }
      // Clear invalid tokens
      localStorage.removeItem('accessToken');
      sessionStorage.removeItem('accessToken');
      return null;
    }

    const data = await response.json();
    if (data.success && data.accessToken) {
      console.log('Token refreshed successfully');
      localStorage.setItem('accessToken', data.accessToken);
      return data.accessToken;
    } else {
      console.error('Token refresh response did not contain a new token');
      return null;
    }
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
};

// Function to handle API requests with token refresh
const makeAuthenticatedRequest = async (
  url: string,
  method: string,
  data?: any,
  withAuth: boolean = true
): Promise<any> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  if (withAuth && isBrowser) {
    let token = localStorage.getItem('accessToken');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  const requestOptions: RequestInit = {
    method,
    headers,
    mode: 'cors',
    credentials: 'include'
  };

  if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
    requestOptions.body = JSON.stringify(data);
  }

  try {
    console.log(`Making ${method} request to: http://localhost:5000${url}`);
    let response = await fetch(`http://localhost:5000${url}`, requestOptions);
    console.log(`Response status: ${response.status}`);

    // If we get a 401 (Unauthorized) and withAuth is true, try to refresh the token
    if (response.status === 401 && withAuth) {
      console.log('Received 401, attempting to refresh token...');
      const newToken = await refreshToken();

      if (newToken) {
        // Update the Authorization header with the new token
        headers['Authorization'] = `Bearer ${newToken}`;
        requestOptions.headers = headers;

        // Retry the request with the new token
        console.log(`Retrying ${method} request with new token...`);
        response = await fetch(`http://localhost:5000${url}`, requestOptions);
        console.log(`Retry response status: ${response.status}`);
      }
    }

    if (!response.ok) {
      let errorMessage = `Failed to ${method.toLowerCase()} data`;
      try {
        const errorText = await response.text();
        console.error('Server error response:', errorText);

        // Essayer de parser le JSON de l'erreur
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.error && errorJson.error.message) {
            // Utiliser le message d'erreur du serveur
            errorMessage = errorJson.error.message;
          } else {
            errorMessage = `${errorMessage}: ${response.status} ${response.statusText}. ${errorText}`;
          }
        } catch (jsonError) {
          // Si ce n'est pas du JSON, utiliser le texte brut
          errorMessage = `${errorMessage}: ${response.status} ${response.statusText}. ${errorText}`;
        }
      } catch (e) {
        console.error('Could not parse error response:', e);
      }
      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

export const api = {
  get: async (url: string, withAuth: boolean = true) => {
    return makeAuthenticatedRequest(url, 'GET', undefined, withAuth);
  },

  post: async (url: string, data: any, withAuth: boolean = true) => {
    return makeAuthenticatedRequest(url, 'POST', data, withAuth);
  },

  put: async (url: string, data: any, withAuth: boolean = true) => {
    // Log the data being sent for debugging
    console.log('Request data size:', JSON.stringify(data).length, 'bytes');
    return makeAuthenticatedRequest(url, 'PUT', data, withAuth);
  },

  delete: async (url: string, withAuth: boolean = true) => {
    return makeAuthenticatedRequest(url, 'DELETE', undefined, withAuth);
  },

  patch: async (url: string, data: any, withAuth: boolean = true) => {
    return makeAuthenticatedRequest(url, 'PATCH', data, withAuth);
  },

  // Add a method to explicitly refresh the token
  refreshToken: async () => {
    return refreshToken();
  }
};

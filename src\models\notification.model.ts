import { Schema } from "mongoose"

const notificationSchema = new Schema(
  {
    recipient: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    sender: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    type: {
      type: String,
      enum: [
        "support",
        "comment",
        "reply",
        "mention",
        "update",
        "milestone",
        "vote",
        "system",
        "resource_offer",
        "resource_status_update",
        "resource_canceled",
        "resource_need_response",
        "resource_need_fulfilled",
        "volunteer_invite",
        "volunteer_join",
        "volunteer_decline",
        "volunteer_points",
        "badge_awarded"
      ],
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    relatedInitiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
    },
    relatedComment: {
      type: Schema.Types.ObjectId,
      ref: "Comment",
    },
    relatedPost: {
      type: Schema.Types.ObjectId,
      ref: "Post",
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    link: {
      type: String,
    },
  },
  { timestamps: true },
)

export default notificationSchema


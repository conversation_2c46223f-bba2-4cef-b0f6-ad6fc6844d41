import express from "express"
import { authenticate, adminOnly } from "../middleware/auth"
import {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryInitiatives
} from "../controllers/category.controller"

const router = express.Router()

// Public routes
router.get("/", getCategories)
router.get("/:id", getCategoryById)
router.get("/:id/initiatives", getCategoryInitiatives)

// Admin routes
router.post("/", authenticate, adminOnly, createCategory)
router.put("/:id", authenticate, adminOnly, updateCategory)
router.delete("/:id", authenticate, adminOnly, deleteCategory)

export default router

"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "../../../../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../../../components/ui/card"
import { Award, Plus } from "lucide-react"
import { api } from "../../../../lib/api"
import { toast } from "../../../../components/ui/use-toast"
import BadgeDisplay from "../../../../components/badges/BadgeDisplay"
import AwardBadgeDialog from "../../../../components/initiative/AwardBadgeDialog"

interface VolunteerBadgesProps {
  initiativeId: string
  volunteerId: string
  volunteerName: string
  isAuthor: boolean
}

export default function VolunteerBadges({
  initiativeId,
  volunteerId,
  volunteerName,
  isAuthor
}: VolunteerBadgesProps) {
  const [badges, setBadges] = useState<{
    _id: string;
    badge: {
      _id: string;
      name: string;
      arabicName?: string;
      description?: string;
      arabicDescription?: string;
      icon?: string;
      color?: string;
      category?: string;
      level?: number;
    };
    user: string;
    initiative: {
      _id: string;
      title: string;
    };
    awardedBy: {
      _id: string;
      name: string;
    };
    reason?: string;
    awardedAt: string;
  }[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAwardDialog, setShowAwardDialog] = useState(false)

  // Fonction pour récupérer tous les badges et filtrer
  const fetchBadges = async () => {
    try {
      setIsLoading(true)

      // Récupérer tous les badges attribués
      const response = await api.get('/api/badges/awards', false)

      if (response.success && response.badgeAwards) {
        // Filtrer les badges pour ce volontaire et cette initiative
        const filteredBadges = response.badgeAwards.filter((award: any) => {
          const userIdMatch = award.user &&
            (award.user._id === volunteerId || award.user.id === volunteerId)

          const initiativeIdMatch = award.initiative &&
            (award.initiative._id === initiativeId || award.initiative.id === initiativeId)

          return userIdMatch && initiativeIdMatch
        })

        setBadges(filteredBadges)
      } else {
        setBadges([])
      }
    } catch (error) {
      setBadges([])
    } finally {
      setIsLoading(false)
    }
  }

  // Charger les badges au montage du composant
  useEffect(() => {
    if (volunteerId && initiativeId) {
      fetchBadges()
    } else {
      setIsLoading(false)
    }
  }, [volunteerId, initiativeId])

  const handleAwardBadge = () => {
    setShowAwardDialog(true)
  }

  const handleBadgeAwarded = () => {
    fetchBadges()
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">الشارات</CardTitle>
            <CardDescription>الشارات التي حصل عليها المتطوع في هذه المبادرة</CardDescription>
          </div>
          {isAuthor && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={handleAwardBadge}
            >
              <Plus className="h-4 w-4" />
              منح شارة
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : badges.length === 0 ? (
          <div className="text-center py-6">
            <Award className="h-12 w-12 mx-auto text-gray-300 mb-2" />
            <p className="text-gray-500">لم يحصل المتطوع على أي شارات في هذه المبادرة بعد</p>
            {isAuthor && (
              <Button
                variant="outline"
                className="mt-4"
                onClick={handleAwardBadge}
              >
                منح أول شارة
              </Button>
            )}
          </div>
        ) : (
          <BadgeDisplay badges={badges} />
        )}
      </CardContent>

      <AwardBadgeDialog
        isOpen={showAwardDialog}
        onClose={() => setShowAwardDialog(false)}
        initiativeId={initiativeId}
        volunteerId={volunteerId}
        volunteerName={volunteerName}
        onBadgeAwarded={handleBadgeAwarded}
      />
    </Card>
  )
}

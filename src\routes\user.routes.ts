import express from "express"
import { authenticate, adminOnly } from "../middleware/auth"
import { User } from "../models"
import {
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  getUserProfile,
  updateUserProfile,
  getUserInitiatives,
  getUserSupportedInitiatives,
  deleteAccount
} from "../controllers/user.controller"
import { searchUsers } from "../controllers/search.controller"
import { getUserBadges } from "../controllers/badge.controller"
import { getUserActivities } from "../controllers/activity.controller"
import {
  uploadAvatar,
  updateAvatar
} from "../controllers/avatar.controller"
import {
  getUserSettings,
  updateUserSettings
} from "../controllers/user-settings.controller"

const router = express.Router()

// Current user routes
router.get("/me", authenticate, async (req, res, next) => {
  try {
    // Get full user data from database
    const user = await User.findById(req.user.id)
      .select("-password -refreshToken -resetPasswordToken -resetPasswordExpires")

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: "User not found",
          statusCode: 404
        }
      })
    }

    // Préparer la réponse en fonction du type d'utilisateur
    const userData: any = user.toObject();

    console.log("GET /api/users/me - User data before sending:", {
      id: userData._id,
      userType: userData.userType,
      hasSkills: !!userData.skills,
      skillsLength: userData.skills ? userData.skills.length : 0,
      skills: userData.skills
    });

    // Ajouter les champs spécifiques au type d'utilisateur
    if (userData.userType === 'volunteer') {
      // S'assurer que les champs pertinents pour les volontaires sont inclus
      userData.qualifications = userData.qualifications || [];
      userData.skills = userData.skills || [];
      userData.interests = userData.interests || [];
      userData.availability = userData.availability || "";
    } else if (userData.userType === 'proposer') {
      // S'assurer que les champs pertinents pour les proposeurs sont inclus
      userData.qualifications = userData.qualifications || [];
      userData.skills = userData.skills || [];
      userData.interests = userData.interests || [];
      userData.ideaDescription = userData.ideaDescription || "";
      userData.objectives = userData.objectives || "";
      userData.needs = userData.needs || "";
    } else if (userData.userType === 'company') {
      // S'assurer que les champs pertinents pour les entreprises sont inclus
      userData.companyName = userData.companyName || "";
      userData.industry = userData.industry || "";
      userData.services = userData.services || [];
      userData.resources = userData.resources || [];
    }

    // Return the user data
    res.status(200).json({
      success: true,
      user: userData
    })
  } catch (error) {
    next(error)
  }
})

router.get("/me/settings", authenticate, (req, res, next) => {
  // Redirect to user settings endpoint
  req.params.userId = req.user.id
  getUserSettings(req, res, next)
})

// Update current user profile
router.put("/profile", authenticate, async (req, res, next) => {
  try {
    const { name, username, email, bio, location, avatar } = req.body

    // Check if username or email is already taken
    if (username || email) {
      const existingUser = await User.findOne({
        $and: [
          { _id: { $ne: req.user.id } },
          { $or: [{ username }, { email }] }
        ]
      })

      if (existingUser) {
        if (username && existingUser.username === username) {
          return res.status(400).json({
            success: false,
            error: {
              message: "Username is already taken",
              statusCode: 400
            }
          })
        }
        if (email && existingUser.email === email) {
          return res.status(400).json({
            success: false,
            error: {
              message: "Email is already in use",
              statusCode: 400
            }
          })
        }
      }
    }

    // Update user profile
    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      {
        $set: {
          name,
          username,
          email,
          bio,
          location,
          avatar
        }
      },
      { new: true }
    ).select("-password -refreshToken -resetPasswordToken -resetPasswordExpires")

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        error: {
          message: "User not found",
          statusCode: 404
        }
      })
    }

    // Return updated user data
    res.status(200).json({
      success: true,
      user: updatedUser
    })
  } catch (error) {
    next(error)
  }
})

// Public routes
router.get("/", getUsers)
router.get("/search", searchUsers)
router.get("/:id", getUserById)
router.get("/:id/initiatives", getUserInitiatives)
router.get("/:id/supported", getUserSupportedInitiatives)
router.get("/:id/supported-initiatives", getUserSupportedInitiatives)
router.get("/:id/badges", getUserBadges)
router.get("/:id/activities", getUserActivities)

// Protected routes
router.get("/profile", authenticate, getUserProfile)
router.put("/profile", authenticate, updateUserProfile)
router.put("/:id", authenticate, adminOnly, updateUser)
router.delete("/:id", authenticate, adminOnly, deleteUser)

// User settings
router.get("/:userId/settings", authenticate, getUserSettings)
router.put("/:userId/settings", authenticate, updateUserSettings)

// Avatar upload
router.post("/:userId/avatar", authenticate, uploadAvatar, updateAvatar)

// Update user skills directly
router.post("/:userId/update-skills", authenticate, async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { skills } = req.body;

    // Vérifier si l'utilisateur est autorisé à mettre à jour ces données
    if (req.user.id !== userId && req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "Vous n'êtes pas autorisé à mettre à jour ces données"
      });
    }

    // Vérifier que les compétences sont correctement formatées
    if (!skills || !Array.isArray(skills)) {
      return res.status(400).json({
        success: false,
        message: "Les compétences doivent être un tableau"
      });
    }

    // Vérifier que chaque compétence a les propriétés requises
    const validSkills = skills.map(skill => {
      if (!skill.category) {
        return {
          ...skill,
          category: 'technical' // Catégorie par défaut
        };
      }
      if (!skill.level) {
        return {
          ...skill,
          level: 'intermediate' // Niveau par défaut
        };
      }
      return skill;
    });

    console.log("Mise à jour des compétences pour l'utilisateur:", userId);
    console.log("Compétences à mettre à jour:", validSkills);

    // Mettre à jour uniquement les compétences de l'utilisateur
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: { skills: validSkills } },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: "Utilisateur non trouvé"
      });
    }

    console.log("Compétences mises à jour avec succès");
    console.log("Nouvelles compétences:", updatedUser.skills);

    res.status(200).json({
      success: true,
      message: "Compétences mises à jour avec succès",
      skills: updatedUser.skills
    });
  } catch (error) {
    console.error("Erreur lors de la mise à jour des compétences:", error);
    next(error);
  }
});

// Delete account
router.post("/delete-account", authenticate, deleteAccount)

export default router

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "../ui/dialog"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { Textarea } from "../ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { Award, Loader2 } from "lucide-react"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"

interface Badge {
  _id: string
  name: string
  arabicName: string
  description: string
  arabicDescription: string
  icon: string
  color: string
  category: string
  level: number
}

interface AwardBadgeDialogProps {
  isOpen: boolean
  onClose: () => void
  initiativeId: string
  volunteerId: string
  volunteerName: string
  onBadgeAwarded: () => void
}

export default function AwardBadgeDialog({
  isOpen,
  onClose,
  initiativeId,
  volunteerId,
  volunteerName,
  onBadgeAwarded
}: AwardBadgeDialogProps) {
  const [badges, setBadges] = useState<Badge[]>([])
  const [awardedBadges, setAwardedBadges] = useState<string[]>([]) // IDs des badges déjà attribués
  const [selectedBadge, setSelectedBadge] = useState<string>("")
  const [reason, setReason] = useState<string>("")
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isFetchingBadges, setIsFetchingBadges] = useState<boolean>(false)

  // Vérifier et nettoyer les IDs
  const cleanInitiativeId = initiativeId?.toString().trim();
  const cleanVolunteerId = volunteerId?.toString().trim();

  // Ajouter un log pour déboguer
  useEffect(() => {
    console.log("AwardBadgeDialog component props:", {
      isOpen,
      initiativeId,
      cleanInitiativeId,
      volunteerId,
      cleanVolunteerId,
      volunteerName
    });
  }, [isOpen, initiativeId, cleanInitiativeId, volunteerId, cleanVolunteerId, volunteerName]);

  useEffect(() => {
    if (isOpen && cleanInitiativeId && cleanVolunteerId) {
      fetchBadges()
      fetchAwardedBadges()

      // Ajouter un log pour déboguer
      console.log(`AwardBadgeDialog: isOpen=${isOpen}, initiativeId=${cleanInitiativeId}, volunteerId=${cleanVolunteerId}`)
    }
  }, [isOpen, cleanInitiativeId, cleanVolunteerId])

  const fetchBadges = async () => {
    try {
      setIsFetchingBadges(true)
      const response = await api.get("/api/badges", false)
      if (response.success) {
        setBadges(response.badges || [])
      } else {
        toast({
          title: "خطأ",
          description: "فشل في جلب الشارات المتاحة",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error fetching badges:", error)
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء جلب الشارات المتاحة",
        variant: "destructive"
      })
    } finally {
      setIsFetchingBadges(false)
    }
  }

  const fetchAwardedBadges = async () => {
    try {
      console.log(`Fetching awarded badges for initiative ${cleanInitiativeId} and volunteer ${cleanVolunteerId}`)

      // Utiliser la nouvelle API pour récupérer les badges déjà attribués à ce volontaire pour cette initiative
      const response = await api.get(`/api/initiatives/${cleanInitiativeId}/volunteers/${cleanVolunteerId}/badges`, false)
      console.log("API response for volunteer badges:", response)

      if (response.success && response.badgeIds) {
        console.log("Badges déjà attribués:", response.badgeIds)
        setAwardedBadges(response.badgeIds)
      } else {
        console.log("No badge IDs found in response or response not successful")
        setAwardedBadges([])
      }
    } catch (error) {
      console.error("Error fetching awarded badges:", error)
      // Ne pas afficher de toast pour ne pas perturber l'expérience utilisateur
      setAwardedBadges([])
    }
  }

  const handleAwardBadge = async () => {
    if (!selectedBadge) {
      toast({
        title: "تنبيه",
        description: "يرجى اختيار شارة لمنحها",
        variant: "destructive"
      })
      return
    }

    // Vérifier si le badge a déjà été attribué
    if (awardedBadges.includes(selectedBadge)) {
      toast({
        title: "تنبيه",
        description: "تم منح هذه الشارة مسبقاً لهذا المتطوع في هذه المبادرة",
        variant: "destructive"
      })
      return
    }

    console.log(`Awarding badge to volunteer. Initiative ID: ${cleanInitiativeId}, Volunteer ID: ${cleanVolunteerId}, Badge ID: ${selectedBadge}`)

    if (!cleanVolunteerId) {
      console.error("Cannot award badge: volunteerId is undefined or empty")
      toast({
        title: "خطأ",
        description: "معرف المتطوع غير محدد",
        variant: "destructive"
      })
      return
    }

    if (!cleanInitiativeId) {
      console.error("Cannot award badge: initiativeId is undefined or empty")
      toast({
        title: "خطأ",
        description: "معرف المبادرة غير محدد",
        variant: "destructive"
      })
      return
    }

    try {
      setIsLoading(true)

      // Construire l'URL de l'API
      const apiUrl = `/api/initiatives/${cleanInitiativeId}/volunteers/${cleanVolunteerId}/award-badge`
      console.log(`Sending request to: ${apiUrl}`)

      const response = await api.post(
        apiUrl,
        {
          badgeId: selectedBadge,
          reason: reason || "منح شارة تقديرية للمتطوع"
        },
        true
      )

      console.log(`Award badge response:`, response)

      if (response.success) {
        toast({
          title: "تم بنجاح",
          description: "تم منح الشارة بنجاح",
          variant: "default"
        })
        onBadgeAwarded()
        handleClose()
      } else {
        console.error("Failed to award badge:", response.message)
        toast({
          title: "خطأ",
          description: response.message || "فشل في منح الشارة",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error("Error awarding badge:", error)

      // Vérifier si l'erreur est due à un badge déjà attribué
      if (error.message && error.message.includes("Badge already awarded")) {
        toast({
          title: "تنبيه",
          description: "تم منح هذه الشارة مسبقاً لهذا المتطوع في هذه المبادرة",
          variant: "warning"
        })

        // Ajouter le badge à la liste des badges déjà attribués
        if (selectedBadge && !awardedBadges.includes(selectedBadge)) {
          setAwardedBadges(prev => [...prev, selectedBadge])
        }

        // Fermer la boîte de dialogue
        handleClose()
      } else {
        // Autre type d'erreur
        toast({
          title: "خطأ",
          description: error.message || "حدث خطأ أثناء منح الشارة",
          variant: "destructive"
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setSelectedBadge("")
    setReason("")
    onClose()
  }

  const getCategoryText = (category: string) => {
    if (!category) return "غير محدد";

    switch (category) {
      case "participation": return "المشاركة"
      case "achievement": return "الإنجاز"
      case "contribution": return "المساهمة"
      case "special": return "خاص"
      case "skill": return "المهارة"
      default: return category
    }
  }

  const getBadgeLevelText = (level: number | undefined) => {
    if (level === undefined || level === null) return "غير محدد";

    switch (level) {
      case 1: return "المستوى الأول"
      case 2: return "المستوى الثاني"
      case 3: return "المستوى الثالث"
      case 4: return "المستوى الرابع"
      case 5: return "المستوى الخامس"
      default: return `المستوى ${level}`
    }
  }

  const selectedBadgeData = badges.find(badge => badge._id === selectedBadge)

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>منح شارة للمتطوع</DialogTitle>
          <DialogDescription>
            منح شارة تقديرية للمتطوع {volunteerName} لمساهمته في المبادرة
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="badge">اختر الشارة</Label>
            {isFetchingBadges ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : (
              <Select value={selectedBadge} onValueChange={setSelectedBadge}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر شارة" />
                </SelectTrigger>
                <SelectContent>
                  {badges.map((badge) => {
                    const isAwarded = awardedBadges.includes(badge._id);
                    return (
                      <SelectItem
                        key={badge._id}
                        value={badge._id}
                        disabled={isAwarded}
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: badge?.color || "#888888" }}
                          ></div>
                          <span>{badge?.arabicName || badge?.name || "شارة"}</span>
                          <span className="text-xs text-gray-500">
                            ({getCategoryText(badge?.category || "")} - {getBadgeLevelText(badge?.level)})
                            {isAwarded && " - تم منحها مسبقاً"}
                          </span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            )}
          </div>

          {selectedBadgeData && (
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="flex items-center gap-3 mb-2">
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{
                    backgroundColor: `${selectedBadgeData?.color || "#888888"}20`,
                    border: `2px solid ${selectedBadgeData?.color || "#888888"}`
                  }}
                >
                  <Award style={{ color: selectedBadgeData?.color || "#888888" }} className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium">{selectedBadgeData?.arabicName || selectedBadgeData?.name || "شارة"}</h4>
                  <p className="text-xs text-gray-500">
                    {getCategoryText(selectedBadgeData?.category || "")} - {getBadgeLevelText(selectedBadgeData?.level)}
                  </p>
                </div>
              </div>
              <p className="text-sm text-gray-600">{selectedBadgeData?.arabicDescription || selectedBadgeData?.description || ""}</p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="reason">سبب منح الشارة</Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="اذكر سبب منح هذه الشارة للمتطوع"
              className="min-h-[100px]"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            إلغاء
          </Button>
          <Button onClick={handleAwardBadge} disabled={isLoading || !selectedBadge}>
            {isLoading ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                جاري المنح...
              </>
            ) : (
              <>
                <Award className="ml-2 h-4 w-4" />
                منح الشارة
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

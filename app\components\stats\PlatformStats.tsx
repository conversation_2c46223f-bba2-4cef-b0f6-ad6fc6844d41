"use client"

import { useState, useEffect } from "react"
import { api } from "@/lib/api"
import { Skeleton } from "@/components/ui/skeleton"

interface Statistic {
  key: string
  value: string
  label: string
}

export function PlatformStats() {
  const [stats, setStats] = useState<Statistic[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true)
        const response = await api.get("/api/stats", false) // false = no authentication required

        if (response.success && response.statistics) {
          setStats(response.statistics)
        } else {
          setError(response.message || "Failed to fetch statistics")
          // Use fallback data if API fails
          setStats([
            { key: "initiatives", value: "100+", label: "مبادرة مقترحة" },
            { key: "implemented", value: "25+", label: "مبادرة منفذة" },
            { key: "users", value: "500+", label: "مشارك نشط" },
            { key: "locations", value: "48", label: "ولاية مستفيدة" },
          ])
        }
      } catch (err) {
        console.error("Error fetching statistics:", err)
        setError("An error occurred while fetching statistics")
        // Use fallback data if API fails
        setStats([
          { key: "initiatives", value: "100+", label: "مبادرة مقترحة" },
          { key: "implemented", value: "25+", label: "مبادرة منفذة" },
          { key: "users", value: "500+", label: "مشارك نشط" },
          { key: "locations", value: "48", label: "ولاية مستفيدة" },
        ])
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (isLoading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <Skeleton className="h-12 w-24 mx-auto mb-2" />
            <Skeleton className="h-6 w-32 mx-auto" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
      {stats.map((stat) => (
        <div key={stat.key} className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
          <div className="text-4xl font-bold mb-2">{stat.value}</div>
          <p className="text-lg opacity-90">{stat.label}</p>
        </div>
      ))}
    </div>
  )
}

import mongoose from "mongoose";
import { connectToDatabase } from "./connection";
import { Initiative, Category, User } from "../models";

// Sample data for seeding
const categories = [
  {
    name: "Environment",
    arabicName: "بيئة",
    description: "Initiatives related to environmental protection and sustainability",
    color: "#4CAF50",
    icon: "leaf"
  },
  {
    name: "Education",
    arabicName: "تعليم",
    description: "Initiatives focused on education and learning",
    color: "#2196F3",
    icon: "book"
  },
  {
    name: "Health",
    arabicName: "صحة",
    description: "Initiatives related to health and wellness",
    color: "#F44336",
    icon: "heart"
  },
  {
    name: "Culture",
    arabicName: "ثقافة",
    description: "Initiatives promoting cultural activities and heritage",
    color: "#9C27B0",
    icon: "music"
  },
  {
    name: "Technology",
    arabicName: "تكنولوجيا",
    description: "Initiatives focused on technology and innovation",
    color: "#3F51B5",
    icon: "cpu"
  }
];

const users = [
  {
    name: "Admin User",
    email: "<EMAIL>",
    username: "admin",
    password: "$2a$10$ij4H1fLtQdvPnEyoWAoHAOCQuz3uMMGn9uHpkMGUcqTNQQEj8GgmC", // Admin123!
    role: "admin",
    isVerified: true,
    avatar: "https://ui-avatars.com/api/?name=Admin+User&background=0D8ABC&color=fff",
    bio: "Administrator of the Initiatives DZ platform",
    location: "Algiers, Algeria",
    joinDate: new Date("2023-01-01")
  },
  {
    name: "Regular User",
    email: "<EMAIL>",
    username: "user",
    password: "$2a$10$ij4H1fLtQdvPnEyoWAoHAOCQuz3uMMGn9uHpkMGUcqTNQQEj8GgmC", // Admin123!
    role: "user",
    isVerified: true,
    avatar: "https://ui-avatars.com/api/?name=Regular+User&background=0D8ABC&color=fff",
    bio: "Regular user of the Initiatives DZ platform",
    location: "Oran, Algeria",
    joinDate: new Date("2023-01-15")
  }
];

const initiatives = [
  {
    title: "حملة تشجير الأحياء السكنية",
    shortDescription: "مبادرة لزراعة الأشجار في الأحياء السكنية لتحسين البيئة المحلية وزيادة المساحات الخضراء.",
    fullDescription: "تهدف هذه المبادرة إلى تحسين البيئة المحلية في الأحياء السكنية من خلال زراعة الأشجار وزيادة المساحات الخضراء. سنقوم بتنظيم حملات تطوعية لزراعة الأشجار في مختلف الأحياء، وتوعية السكان بأهمية الحفاظ على البيئة والمساحات الخضراء.",
    location: "الجزائر العاصمة",
    wilaya: "الجزائر",
    mainImage: "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80",
    images: [],
    status: "active",
    goal: 500,
    supportCount: 124,
    commentCount: 45,
    viewCount: 320,
    progress: 60,
    isVotingEnabled: false,
    tags: ["تشجير", "بيئة", "تطوع"]
  },
  {
    title: "مكتبات متنقلة للمناطق النائية",
    shortDescription: "إنشاء مكتبات متنقلة للوصول إلى المناطق النائية وتوفير الكتب والموارد التعليمية للأطفال.",
    fullDescription: "تهدف هذه المبادرة إلى إنشاء مكتبات متنقلة تصل إلى المناطق النائية والمحرومة، وتوفير الكتب والموارد التعليمية للأطفال والشباب. سنقوم بجمع التبرعات لشراء الكتب وتجهيز المكتبات المتنقلة، وتنظيم زيارات دورية للمناطق المستهدفة.",
    location: "قسنطينة",
    wilaya: "قسنطينة",
    mainImage: "https://images.unsplash.com/photo-1521587760476-6c12a4b040da?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    images: [],
    status: "active",
    goal: 300,
    supportCount: 98,
    commentCount: 32,
    viewCount: 245,
    progress: 45,
    isVotingEnabled: true,
    tags: ["تعليم", "كتب", "مناطق نائية"]
  }
];

/**
 * Seed the database with initial data
 */
async function seedDatabase() {
  try {
    console.log("Connecting to database...");
    await connectToDatabase();

    // Clear existing data
    console.log("Clearing existing data...");
    await Category.deleteMany({});
    await User.deleteMany({});
    await Initiative.deleteMany({});

    // Insert categories
    console.log("Inserting categories...");
    const createdCategories = await Category.insertMany(categories);
    console.log(`${createdCategories.length} categories inserted`);

    // Insert users
    console.log("Inserting users...");
    const createdUsers = await User.insertMany(users);
    console.log(`${createdUsers.length} users inserted`);

    // Insert initiatives with references to categories and users
    console.log("Inserting initiatives...");
    const environmentCategory = createdCategories.find(c => c.name === "Environment");
    const educationCategory = createdCategories.find(c => c.name === "Education");
    const adminUser = createdUsers.find(u => u.role === "admin");

    if (environmentCategory && educationCategory && adminUser) {
      const initiativesToInsert = [
        {
          ...initiatives[0],
          category: environmentCategory._id,
          author: adminUser._id,
          createdAt: new Date("2023-03-15"),
          updatedAt: new Date("2023-03-15")
        },
        {
          ...initiatives[1],
          category: educationCategory._id,
          author: adminUser._id,
          createdAt: new Date("2023-03-10"),
          updatedAt: new Date("2023-03-10")
        }
      ];

      const createdInitiatives = await Initiative.insertMany(initiativesToInsert);
      console.log(`${createdInitiatives.length} initiatives inserted`);
    } else {
      console.error("Could not find required categories or users for initiatives");
    }

    console.log("Database seeded successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log("Database connection closed");
  }
}

// Run the seeder
seedDatabase();

import mongoose, { Schema, Document } from "mongoose"

export interface IInvitation extends Document {
  initiative: mongoose.Types.ObjectId
  sender: mongoose.Types.ObjectId
  recipient: string // Email du destinataire
  role: string
  message: string
  status: "pending" | "accepted" | "declined"
  createdAt: Date
  respondedAt?: Date
}

const InvitationSchema: Schema = new Schema(
  {
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    sender: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    recipient: {
      type: String,
      required: true,
    },
    role: {
      type: String,
      enum: ["general", "specialist", "coordinator", "leader"],
      default: "general",
    },
    message: {
      type: String,
      default: "",
    },
    status: {
      type: String,
      enum: ["pending", "accepted", "declined"],
      default: "pending",
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    respondedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
)

export default mongoose.model<IInvitation>("Invitation", InvitationSchema)

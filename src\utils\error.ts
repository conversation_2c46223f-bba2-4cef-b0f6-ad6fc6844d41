/**
 * Custom error class for API errors
 */
export class AppError extends Error {
  statusCode: number
  isOperational: boolean

  constructor(statusCode: number, message: string, isOperational = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational

    Error.captureStackTrace(this, this.constructor)
  }
}

/**
 * Create an API error
 * @param statusCode HTTP status code
 * @param message Error message
 * @param isOperational Whether the error is operational (expected) or programming
 * @returns AppError instance
 */
export const createError = (statusCode: number, message: string, isOperational = true) => {
  return new AppError(statusCode, message, isOperational)
}

/**
 * Handle errors in async functions
 * @param fn Async function to wrap
 * @returns Express middleware function
 */
export const asyncHandler = (fn: Function) => {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

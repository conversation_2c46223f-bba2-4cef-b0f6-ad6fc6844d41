import { Schema } from "mongoose"
import { impactItemSchema } from "./social-impact.model"

const initiativeSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
      minlength: 10,
      maxlength: 100,
    },
    shortDescription: {
      type: String,
      required: true,
      minlength: 20,
      maxlength: 200,
    },
    fullDescription: {
      type: String,
      required: true,
      minlength: 100,
    },
    // Nouveaux champs structurants
    problem: {
      type: String,
      trim: true,
    },
    solution: {
      type: String,
      trim: true,
    },
    beneficiaries: {
      type: String,
      trim: true,
    },
    quantitativeObjectives: {
      type: String,
      trim: true,
    },
    qualitativeObjectives: {
      type: String,
      trim: true,
    },
    socialImpacts: [
      {
        category: {
          type: Schema.Types.ObjectId,
          ref: "SocialImpact",
        },
        impacts: [String], // Stockage des IDs sous forme de chaînes pour éviter les problèmes de référence
      }
    ],
    // Stockage direct des impacts sélectionnés pour faciliter l'affichage
    selectedImpacts: [impactItemSchema],
    category: {
      type: Schema.Types.ObjectId,
      ref: "Category",
      required: true,
    },
    author: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    location: {
      type: String,
      required: true,
    },
    wilaya: {
      type: String,
    },
    images: [
      {
        type: String,
      },
    ],
    mainImage: {
      type: String,
      default: "/placeholder.svg?height=600&width=1200",
    },
    status: {
      type: String,
      enum: ["draft", "pending", "active", "completed", "rejected"],
      default: "pending",
    },
    goal: {
      type: Number,
      required: true,
      min: 1,
    },
    supporters: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    supportCount: {
      type: Number,
      default: 0,
    },
    commentCount: {
      type: Number,
      default: 0,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
    votingEndDate: {
      type: Date,
    },
    isVotingEnabled: {
      type: Boolean,
      default: false,
    },
    votingOptions: [
      {
        type: Schema.Types.ObjectId,
        ref: "VotingOption",
      },
    ],
    progress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100,
    },
    tags: [
      {
        type: String,
      },
    ],
    budget: {
      type: Number,
    },
    requiredVolunteers: {
      type: Number,
    },
    currentVolunteers: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    volunteersData: [
      {
        user: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
        role: {
          type: String,
          enum: ["general", "coordinator", "specialist", "leader"],
          default: "general",
        },
        skills: [String],
        availability: {
          type: String,
          enum: ["flexible", "weekends", "evenings", "fulltime"],
          default: "flexible",
        },
        message: String,
        joinedAt: Date,
        leftAt: Date,
        status: {
          type: String,
          enum: ["pending", "active", "left", "removed"],
          default: "active",
        },
        contributions: [
          {
            description: String,
            points: Number,
            date: Date,
          },
        ],
        points: {
          type: Number,
          default: 0,
        },
      },
    ],
    isPromoted: {
      type: Boolean,
      default: false,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
)

// Virtual for initiative URL
initiativeSchema.virtual("url").get(function () {
  return `/initiatives/${this._id}`
})

// Virtual for voting URL
initiativeSchema.virtual("votingUrl").get(function () {
  return `/initiatives/vote/${this._id}`
})

// Virtual for comments
initiativeSchema.virtual("comments", {
  ref: "Comment",
  localField: "_id",
  foreignField: "initiative",
  options: { sort: { createdAt: -1 } },
})

// Virtual for updates
initiativeSchema.virtual("updates", {
  ref: "Update",
  localField: "_id",
  foreignField: "initiative",
  options: { sort: { createdAt: -1 } },
})

// Virtual for milestones
initiativeSchema.virtual("milestones", {
  ref: "Milestone",
  localField: "_id",
  foreignField: "initiative",
  options: { sort: { order: 1 } },
})

// Virtual for resource needs
initiativeSchema.virtual("resourceNeeds", {
  ref: "ResourceNeed",
  localField: "_id",
  foreignField: "initiative",
  options: { sort: { priority: -1, createdAt: -1 } },
})

// Virtual for resources
initiativeSchema.virtual("resources", {
  ref: "Resource",
  localField: "_id",
  foreignField: "initiative",
  options: { sort: { createdAt: -1 } },
})

// Index for search
initiativeSchema.index({
  title: "text",
  shortDescription: "text",
  fullDescription: "text",
  location: "text",
})

export default initiativeSchema


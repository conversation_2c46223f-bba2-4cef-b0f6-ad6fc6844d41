import express from "express"
import {
  getAllPosts,
  getPostById,
  createPost,
  updatePost,
  deletePost,
  likePost,
  unlikePost,
  getPostComments,
  createPostComment,
  likePostComment,
  unlikePostComment,
  deletePostComment,
  reportPost,
} from "../controllers/community.controller"
import { authenticate } from "../middleware/auth"
import { validatePost, validatePostComment } from "../middleware/validators/community.validator"

const router = express.Router()

// Public routes
router.get("/", async (req, res) => {
  try {
    // Import models
    const User = require("../models").User
    const Initiative = require("../models").Initiative
    const Comment = require("../models").Comment
    const Support = require("../models").Support

    // Parse pagination parameters
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Get search term if any
    const searchTerm = req.query.search as string || ''

    // Get tab if any
    const tab = req.query.tab as string || 'feed'

    // Build user filter
    const userFilter: any = {}
    if (searchTerm) {
      userFilter.$or = [
        { name: { $regex: searchTerm, $options: 'i' } },
        { username: { $regex: searchTerm, $options: 'i' } }
      ]
    }

    // Get total count of users for pagination
    const totalUsers = await User.countDocuments(userFilter)

    // Get active users (users with initiatives or comments)
    const users = await User.find(userFilter)
      .sort('-createdAt')
      .skip(tab === 'members' ? skip : 0)
      .limit(tab === 'members' ? limit : 10)

    // Process users data
    const membersData = users.map(user => {
      return {
        id: user._id,
        name: user.name,
        username: `@${user.username}`,
        avatar: user.avatar || '/placeholder.svg?height=100&width=100',
        location: user.location || 'الجزائر',
        joinDate: new Date(user.createdAt).toLocaleDateString('ar-DZ', {
          year: 'numeric',
          month: 'long',
        }),
        bio: user.bio || 'عضو في مجتمع المبادرات',
        initiatives: 0,
        contributions: 0,
        badges: ['عضو نشط'],
        recentActivity: [],
      }
    })

    // Build initiative filter
    const initiativeFilter: any = { status: 'active' }
    if (searchTerm) {
      initiativeFilter.$or = [
        { title: { $regex: searchTerm, $options: 'i' } },
        { shortDescription: { $regex: searchTerm, $options: 'i' } }
      ]
    }

    // Get total count of initiatives for pagination
    const totalInitiatives = await Initiative.countDocuments(initiativeFilter)

    // Get popular initiatives
    const popularInitiatives = await Initiative.find(initiativeFilter)
      .sort('-supportCount -commentCount')
      .populate('author', 'name username avatar')
      .populate('category', 'name arabicName')
      .skip(tab === 'initiatives' ? skip : 0)
      .limit(tab === 'initiatives' ? limit : 3)

    // Format popular initiatives
    const formattedInitiatives = popularInitiatives.map(initiative => ({
      id: initiative._id,
      title: initiative.title,
      category: initiative.category?.arabicName || initiative.category?.name || 'عام',
      location: initiative.location || 'الجزائر',
      supportCount: initiative.supportCount || 0,
      commentCount: initiative.commentCount || 0,
      description: initiative.shortDescription || '',
      image: initiative.mainImage || '/placeholder.svg?height=300&width=500',
    }))

    // Get recent posts (combining initiatives and comments)
    const recentPosts = []

    // Get recent initiatives for posts
    const recentInitiatives = await Initiative.find()
      .sort('-createdAt')
      .populate('author', 'name username avatar')
      .limit(5)

    for (const initiative of recentInitiatives) {
      recentPosts.push({
        id: `initiative-${initiative._id}`,
        author: {
          id: initiative.author?._id || 'unknown',
          name: initiative.author?.name || 'مستخدم',
          username: `@${initiative.author?.username || 'user'}`,
          avatar: initiative.author?.avatar || '/placeholder.svg?height=100&width=100',
        },
        date: 'منذ أيام قليلة',
        content: `أطلقت مبادرة جديدة: ${initiative.title}\n\n${initiative.shortDescription || ''}`,
        likes: initiative.supportCount || 0,
        comments: initiative.commentCount || 0,
        image: initiative.mainImage || null,
      })
    }

    // Get recent comments
    const recentComments = await Comment.find({ isReply: false })
      .sort('-createdAt')
      .populate('author', 'name username avatar')
      .populate('initiative', 'title')
      .limit(5)

    for (const comment of recentComments) {
      if (comment.initiative) {
        recentPosts.push({
          id: `comment-${comment._id}`,
          author: {
            id: comment.author?._id || 'unknown',
            name: comment.author?.name || 'مستخدم',
            username: `@${comment.author?.username || 'user'}`,
            avatar: comment.author?.avatar || '/placeholder.svg?height=100&width=100',
          },
          date: 'منذ أيام قليلة',
          content: `علقت على مبادرة "${comment.initiative.title}":\n\n${comment.content}`,
          likes: comment.likes || 0,
          comments: comment.replies?.length || 0,
          image: null,
        })
      }
    }

    // Sort posts by date (newest first)
    recentPosts.sort((a, b) => 0) // Simple sort to avoid errors

    // Calculate pagination metadata
    const userPages = Math.ceil(totalUsers / limit)
    const initiativePages = Math.ceil(totalInitiatives / limit)
    const postPages = Math.ceil(recentPosts.length / limit)

    return res.json({
      success: true,
      members: membersData,
      posts: recentPosts,
      initiatives: formattedInitiatives,
      pagination: {
        members: {
          total: totalUsers,
          count: membersData.length,
          totalPages: userPages,
          currentPage: page,
          limit,
          hasNextPage: page < userPages,
          hasPrevPage: page > 1
        },
        posts: {
          total: recentPosts.length,
          count: recentPosts.length,
          totalPages: postPages,
          currentPage: page,
          limit,
          hasNextPage: page < postPages,
          hasPrevPage: page > 1
        },
        initiatives: {
          total: totalInitiatives,
          count: formattedInitiatives.length,
          totalPages: initiativePages,
          currentPage: page,
          limit,
          hasNextPage: page < initiativePages,
          hasPrevPage: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Error fetching community data:', error)
    return res.status(500).json(
      { success: false, error: 'Failed to fetch community data' }
    )
  }
})

router.get("/posts", getAllPosts)
router.get("/posts/:id", getPostById)
router.get("/posts/:id/comments", getPostComments)

// Protected routes
router.post("/posts", authenticate, validatePost, createPost)
router.put("/posts/:id", authenticate, validatePost, updatePost)
router.delete("/posts/:id", authenticate, deletePost)
router.post("/posts/:id/like", authenticate, likePost)
router.delete("/posts/:id/like", authenticate, unlikePost)
router.post("/posts/:id/comments", authenticate, validatePostComment, createPostComment)
router.post("/posts/:id/comments/:commentId/like", authenticate, likePostComment)
router.delete("/posts/:id/comments/:commentId/like", authenticate, unlikePostComment)
router.delete("/posts/:id/comments/:commentId", authenticate, deletePostComment)
router.post("/posts/:id/report", authenticate, reportPost)

export default router

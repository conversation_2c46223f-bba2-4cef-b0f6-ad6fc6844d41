import mongoose from "mongoose";
import dotenv from "dotenv";
import { Role, Permission } from "../models";
import { logger } from "../utils/logger";

// Load environment variables
dotenv.config();

// Default permissions by category
const defaultPermissions = {
  initiatives: [
    { name: "Create Initiative", description: "Can create new initiatives", code: "initiatives:create" },
    { name: "Edit Own Initiative", description: "Can edit own initiatives", code: "initiatives:edit_own" },
    { name: "Edit Any Initiative", description: "Can edit any initiative", code: "initiatives:edit_any" },
    { name: "Delete Own Initiative", description: "Can delete own initiatives", code: "initiatives:delete_own" },
    { name: "Delete Any Initiative", description: "Can delete any initiative", code: "initiatives:delete_any" },
    { name: "View Initiatives", description: "Can view initiatives", code: "initiatives:view" },
    { name: "Support Initiatives", description: "Can support initiatives", code: "initiatives:support" },
    { name: "Vote on Initiatives", description: "Can vote on initiatives", code: "initiatives:vote" },
    { name: "Approve Initiatives", description: "Can approve pending initiatives", code: "initiatives:approve" },
    { name: "Reject Initiatives", description: "Can reject pending initiatives", code: "initiatives:reject" },
  ],
  comments: [
    { name: "Create Comment", description: "Can create comments", code: "comments:create" },
    { name: "Edit Own Comment", description: "Can edit own comments", code: "comments:edit_own" },
    { name: "Edit Any Comment", description: "Can edit any comment", code: "comments:edit_any" },
    { name: "Delete Own Comment", description: "Can delete own comments", code: "comments:delete_own" },
    { name: "Delete Any Comment", description: "Can delete any comment", code: "comments:delete_any" },
  ],
  users: [
    { name: "View Users", description: "Can view user profiles", code: "users:view" },
    { name: "Edit Own Profile", description: "Can edit own profile", code: "users:edit_own" },
    { name: "Edit Any Profile", description: "Can edit any user profile", code: "users:edit_any" },
    { name: "Block Users", description: "Can block users", code: "users:block" },
    { name: "Unblock Users", description: "Can unblock users", code: "users:unblock" },
    { name: "Verify Users", description: "Can verify users", code: "users:verify" },
    { name: "Change User Role", description: "Can change user roles", code: "users:change_role" },
  ],
  reports: [
    { name: "Create Report", description: "Can create reports", code: "reports:create" },
    { name: "View Reports", description: "Can view reports", code: "reports:view" },
    { name: "Resolve Reports", description: "Can resolve reports", code: "reports:resolve" },
    { name: "Reject Reports", description: "Can reject reports", code: "reports:reject" },
  ],
  admin: [
    { name: "Access Admin Panel", description: "Can access admin panel", code: "admin:access" },
    { name: "Manage Categories", description: "Can manage categories", code: "admin:manage_categories" },
    { name: "Manage Banners", description: "Can manage banners", code: "admin:manage_banners" },
    { name: "View Statistics", description: "Can view platform statistics", code: "admin:view_statistics" },
    { name: "Manage Roles", description: "Can manage roles and permissions", code: "admin:manage_roles" },
    { name: "Manage Settings", description: "Can manage platform settings", code: "admin:manage_settings" },
  ],
  system: [
    { name: "Super Admin", description: "Has all permissions", code: "system:super_admin" },
  ],
};

// Default roles
const defaultRoles = [
  {
    name: "Administrator",
    description: "Full access to all features",
    code: "admin",
    isDefault: false,
    isSystem: true,
    permissions: ["system:super_admin"], // Will be replaced with actual permission IDs
  },
  {
    name: "Moderator",
    description: "Can moderate content and users",
    code: "moderator",
    isDefault: false,
    isSystem: true,
    permissions: [
      // Initiatives
      "initiatives:view", "initiatives:edit_any", "initiatives:delete_any", "initiatives:approve", "initiatives:reject",
      // Comments
      "comments:edit_any", "comments:delete_any",
      // Users
      "users:view", "users:block", "users:unblock", "users:verify",
      // Reports
      "reports:view", "reports:resolve", "reports:reject",
      // Admin
      "admin:access", "admin:view_statistics",
    ],
  },
  {
    name: "User",
    description: "Standard user with basic permissions",
    code: "user",
    isDefault: true,
    isSystem: true,
    permissions: [
      // Initiatives
      "initiatives:view", "initiatives:create", "initiatives:edit_own", "initiatives:delete_own", "initiatives:support", "initiatives:vote",
      // Comments
      "comments:create", "comments:edit_own", "comments:delete_own",
      // Users
      "users:view", "users:edit_own",
      // Reports
      "reports:create",
    ],
  },
];

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/initiatives_dz");
    logger.info("MongoDB connected");
  } catch (error) {
    logger.error("MongoDB connection error:", error);
    process.exit(1);
  }
};

// Initialize permissions
const initPermissions = async () => {
  try {
    logger.info("Initializing permissions...");
    
    // Create permissions by category
    const permissionMap = new Map();
    
    for (const [category, permissions] of Object.entries(defaultPermissions)) {
      for (const permissionData of permissions) {
        // Check if permission already exists
        let permission = await Permission.findOne({ code: permissionData.code });
        
        if (!permission) {
          // Create new permission
          permission = await Permission.create({
            ...permissionData,
            category,
          });
          logger.info(`Created permission: ${permission.name} (${permission.code})`);
        } else {
          logger.info(`Permission already exists: ${permission.name} (${permission.code})`);
        }
        
        // Store permission ID by code for role creation
        permissionMap.set(permission.code, permission._id);
      }
    }
    
    // Create roles
    for (const roleData of defaultRoles) {
      // Check if role already exists
      let role = await Role.findOne({ code: roleData.code });
      
      if (!role) {
        // Map permission codes to IDs
        const permissionIds = roleData.permissions.map(code => permissionMap.get(code));
        
        // Create new role
        role = await Role.create({
          name: roleData.name,
          description: roleData.description,
          code: roleData.code,
          permissions: permissionIds,
          isDefault: roleData.isDefault,
          isSystem: roleData.isSystem,
        });
        logger.info(`Created role: ${role.name} (${role.code})`);
      } else {
        logger.info(`Role already exists: ${role.name} (${role.code})`);
      }
    }
    
    logger.info("Permissions and roles initialized successfully");
  } catch (error) {
    logger.error("Error initializing permissions:", error);
  }
};

// Main function
const main = async () => {
  try {
    await connectDB();
    await initPermissions();
  } catch (error) {
    logger.error("Error:", error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    logger.info("MongoDB connection closed");
  }
};

// Run the script
main();

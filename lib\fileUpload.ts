import { getToken } from "./auth"

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"

/**
 * Upload a file to the API
 * @param endpoint API endpoint
 * @param file File to upload
 * @param fieldName Field name for the file (default: 'file')
 * @returns Promise with the response data
 */
export const uploadFile = async (endpoint: string, file: File, fieldName: string = 'file') => {
  const url = `${API_URL}${endpoint}`

  // Create form data
  const formData = new FormData()
  formData.append(fieldName, file)

  // Get authentication token
  const token = getToken()
  if (!token) {
    throw new Error("Authentication required")
  }

  // Create request options - Note: We can't set Content-Type for FormData
  // as the browser needs to set it with the correct boundary
  const options: RequestInit = {
    method: 'POST',
    body: formData,
    // Create headers object without type assertion
    credentials: 'include',
    mode: 'cors',
    cache: 'no-cache'
  }

  // Add authorization header separately
  // This avoids the type error with FormData
  const headers = new Headers()
  headers.append('Authorization', `Bearer ${token}`)
  options.headers = headers

  try {
    console.log(`Uploading file to ${endpoint}`)
    const response = await fetch(url, options)

    // Check if response is empty
    const text = await response.text()

    // If empty response, return empty object
    if (!text) {
      return {}
    }

    try {
      // Try to parse as JSON
      const data = JSON.parse(text)

      if (!response.ok) {
        console.error('Upload error:', data)
        throw new Error(data.error?.message || data.message || "File upload failed")
      }

      return data
    } catch (error) {
      console.error('Error parsing JSON response:', error)
      console.error('Response text:', text)

      if (!response.ok) {
        throw new Error(`Upload failed with status ${response.status}`)
      }

      // Return empty object if JSON parsing fails
      return {}
    }
  } catch (error: any) {
    console.error('File upload error:', error)
    throw new Error(`File upload error: ${error.message}`)
  }
}

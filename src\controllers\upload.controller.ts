import type { Request, Response, NextFunction } from "express"
import multer from "multer"
import path from "path"
import fs from "fs"
import { v4 as uuidv4 } from "uuid"
import { createError } from "../utils/error"

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Use public/uploads to make files accessible via static middleware
    const uploadDir = "public/uploads"

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }

    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`
    cb(null, uniqueName)
  },
})

// Configure upload limits
const limits = {
  fileSize: Number.parseInt(process.env.MAX_FILE_SIZE || "5242880"), // 5MB default
}

// File filter
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept images and documents
  const allowedMimeTypes = [
    // Images
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    // Documents
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    // Others as needed
  ]

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error("Invalid file type. Only images and documents are allowed.") as any)
  }
}

// Initialize multer
const upload = multer({
  storage,
  limits,
  fileFilter,
})

// Upload single file
export const uploadFile = (req: Request, res: Response, next: NextFunction) => {
  const singleUpload = upload.single("file")

  singleUpload(req, res, (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === "LIMIT_FILE_SIZE") {
          return next(createError(400, `File too large. Maximum size is ${limits.fileSize / (1024 * 1024)}MB`))
        }
        return next(createError(400, err.message))
      }
      return next(createError(400, err.message))
    }

    if (!req.file) {
      return next(createError(400, "No file uploaded"))
    }

    // Generate file URL
    const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get("host")}`
    // Create URL path that works with the static middleware
    const fileUrl = `${baseUrl}/uploads/${req.file.filename}`

    res.status(200).json({
      success: true,
      file: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path,
        url: fileUrl,
      },
    })
  })
}

// Upload multiple files
export const uploadFiles = (req: Request, res: Response, next: NextFunction) => {
  const multiUpload = upload.array("files", 10) // Max 10 files

  multiUpload(req, res, (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === "LIMIT_FILE_SIZE") {
          return next(createError(400, `File too large. Maximum size is ${limits.fileSize / (1024 * 1024)}MB`))
        } else if (err.code === "LIMIT_UNEXPECTED_FILE") {
          return next(createError(400, "Too many files. Maximum is 10 files"))
        }
        return next(createError(400, err.message))
      }
      return next(createError(400, err.message))
    }

    if (!req.files || (Array.isArray(req.files) && req.files.length === 0)) {
      return next(createError(400, "No files uploaded"))
    }

    // Generate file URLs
    const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get("host")}`
    const files = Array.isArray(req.files)
      ? req.files.map((file) => ({
          filename: file.filename,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          url: `${baseUrl}/uploads/${file.filename}`,
        }))
      : []

    res.status(200).json({
      success: true,
      files,
    })
  })
}

// Delete file
export const deleteFile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename } = req.params

    if (!filename) {
      return next(createError(400, "Filename is required"))
    }

    const uploadDir = "public/uploads"
    const filePath = path.join(uploadDir, filename)

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return next(createError(404, "File not found"))
    }

    // Delete file
    fs.unlinkSync(filePath)

    res.status(200).json({
      success: true,
      message: "File deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}


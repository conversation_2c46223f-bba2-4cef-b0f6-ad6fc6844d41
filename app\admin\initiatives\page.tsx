"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, Search, CheckCircle2, XCircle } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
// Removed AdminLayout import as it's handled by app/admin/layout.tsx
import { api } from "@/lib/api"
import { getCurrentUser } from "@/lib/auth"

interface Initiative {
  _id: string
  title: string
  author: {
    _id: string
    name: string
    username: string
  }
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  status: string
  supportCount: number
  commentCount: number
  createdAt: string
}

export default function AdminInitiativesPage() {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [totalInitiatives, setTotalInitiatives] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Filters
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("newest")

  // Action dialogs
  const [selectedInitiative, setSelectedInitiative] = useState<string | null>(null)
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // Get current user
  const user = getCurrentUser()
  // Flag to prevent multiple API calls
  const [isInitialLoad, setIsInitialLoad] = useState(true)

  // First useEffect: Check if user is admin (runs only once)
  useEffect(() => {
    // Check if user is admin
    const isAdmin = user && (
      // Cas 1: rôle est une chaîne exacte 'admin'
      (typeof user.role === 'string' && user.role === 'admin') ||
      // Cas 2: rôle est un objet avec code === 'admin'
      (typeof user.role === 'object' && user.role && user.role.code === 'admin') ||
      // Cas 3: rôle est une chaîne contenant 'admin'
      (typeof user.role === 'string' && user.role.includes('admin')) ||
      // Cas 4: username est 'admin' (pour le développement)
      user.username === 'admin'
    )

    if (!isAdmin) {
      console.log('[Admin Initiatives] User is not admin:', user)
      router.push("/")
      return
    }

    console.log('[Admin Initiatives] User is admin')
    // Mark initial check as complete
    setIsInitialLoad(false)
  }, [router, user])

  // Second useEffect: Fetch initiatives when filters change (runs after admin check)
  useEffect(() => {
    // Skip on initial load (wait for admin check)
    if (isInitialLoad) return

    console.log('[Admin Initiatives] Fetching initiatives with filters:', { currentPage, statusFilter, sortBy })
    fetchInitiatives()
  }, [currentPage, statusFilter, sortBy, isInitialLoad])

  // Throttle API calls
  const [lastFetchTime, setLastFetchTime] = useState(0)
  const THROTTLE_INTERVAL = 500 // ms

  const fetchInitiatives = async () => {
    try {
      // Prevent excessive API calls
      const now = Date.now()
      if (now - lastFetchTime < THROTTLE_INTERVAL) {
        console.log('[Admin Initiatives] Throttling API call, too frequent')
        return
      }
      setLastFetchTime(now)

      setIsLoading(true)

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        sort: sortBy,
      })

      if (statusFilter !== "all") {
        params.append("status", statusFilter)
      }

      if (searchQuery) {
        params.append("q", searchQuery)
      }

      console.log(`[Admin Initiatives] Fetching data with params: ${params.toString()}`)

      // Fetch initiatives
      const response = await api.get(`/api/admin/initiatives?${params.toString()}`)
      console.log('[Admin Initiatives] API response received:', response)

      setInitiatives(response.initiatives || [])
      if (response.pagination) {
        setTotalInitiatives(response.pagination.total || 0)
        setTotalPages(response.pagination.totalPages || 1)
      }
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchInitiatives()
  }

  const handleApproveInitiative = async () => {
    if (!selectedInitiative) return

    setIsProcessing(true)

    try {
      await api.put(`/api/admin/initiatives/${selectedInitiative}/approve`, {})

      // Update initiative in the list
      setInitiatives((prev) =>
        prev.map((initiative) =>
          initiative._id === selectedInitiative ? { ...initiative, status: "active" } : initiative,
        ),
      )

      setShowApproveDialog(false)
    } catch (err: any) {
      setError(err.message || "Failed to approve initiative")
    } finally {
      setIsProcessing(false)
      setSelectedInitiative(null)
    }
  }

  const handleRejectInitiative = async () => {
    if (!selectedInitiative) return

    setIsProcessing(true)

    try {
      await api.put(`/api/admin/initiatives/${selectedInitiative}/reject`, {})

      // Update initiative in the list
      setInitiatives((prev) =>
        prev.map((initiative) =>
          initiative._id === selectedInitiative ? { ...initiative, status: "rejected" } : initiative,
        ),
      )

      setShowRejectDialog(false)
    } catch (err: any) {
      setError(err.message || "Failed to reject initiative")
    } finally {
      setIsProcessing(false)
      setSelectedInitiative(null)
    }
  }

  const handleDeleteInitiative = async () => {
    if (!selectedInitiative) return

    setIsProcessing(true)

    try {
      await api.delete(`/api/initiatives/${selectedInitiative}`)

      // Remove the initiative from the list
      setInitiatives((prev) => prev.filter((initiative) => initiative._id !== selectedInitiative))

      // Update total count
      setTotalInitiatives((prev) => prev - 1)

      // Recalculate total pages
      const newTotalPages = Math.ceil((totalInitiatives - 1) / 10)
      setTotalPages(newTotalPages > 0 ? newTotalPages : 1)

      // If current page is now greater than total pages, go to last page
      if (currentPage > newTotalPages && newTotalPages > 0) {
        setCurrentPage(newTotalPages)
      }

      setShowDeleteDialog(false)
    } catch (err: any) {
      setError(err.message || "Failed to delete initiative")
    } finally {
      setIsProcessing(false)
      setSelectedInitiative(null)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-600">نشطة</Badge>
      case "completed":
        return <Badge className="bg-blue-600">مكتملة</Badge>
      case "pending":
        return <Badge className="bg-yellow-600">قيد المراجعة</Badge>
      case "rejected":
        return <Badge className="bg-red-600">مرفوضة</Badge>
      default:
        return <Badge className="bg-gray-600">{status}</Badge>
    }
  }

  if (isLoading && initiatives.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading initiatives...</span>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">إدارة المبادرات</h1>

        <Link href="/admin/dashboard">
          <Button variant="outline">العودة إلى لوحة التحكم</Button>
        </Link>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>تصفية المبادرات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <form onSubmit={handleSearch} className="flex-1 flex gap-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="البحث عن مبادرة..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button type="submit" className="bg-green-600 hover:bg-green-700">
                بحث
              </Button>
            </form>

            <div className="flex gap-2">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">قيد المراجعة</SelectItem>
                  <SelectItem value="active">نشطة</SelectItem>
                  <SelectItem value="completed">مكتملة</SelectItem>
                  <SelectItem value="rejected">مرفوضة</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={sortBy}
                onValueChange={(value) => {
                  setSortBy(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="الترتيب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                  <SelectItem value="most_supported">الأكثر دعماً</SelectItem>
                  <SelectItem value="most_commented">الأكثر تعليقاً</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-0">
          {initiatives.length === 0 ? (
            <div className="py-12 text-center text-gray-500">
              <p className="text-lg">لا توجد مبادرات مطابقة للمعايير المحددة</p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>العنوان</TableHead>
                    <TableHead>الكاتب</TableHead>
                    <TableHead>الفئة</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>الدعم</TableHead>
                    <TableHead>التعليقات</TableHead>
                    <TableHead>تاريخ الإنشاء</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {initiatives.map((initiative) => (
                    <TableRow key={initiative._id}>
                      <TableCell className="font-medium">
                        <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600 hover:underline">
                          {initiative.title}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <Link href={`/users/${initiative.author._id}`} className="hover:text-green-600 hover:underline">
                          {initiative.author.name}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <Badge style={{ backgroundColor: initiative.category.color }}>
                          {initiative.category.arabicName}
                        </Badge>
                      </TableCell>
                      <TableCell>{getStatusBadge(initiative.status)}</TableCell>
                      <TableCell>{initiative.supportCount}</TableCell>
                      <TableCell>{initiative.commentCount}</TableCell>
                      <TableCell>{formatDate(initiative.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Link href={`/initiatives/${initiative._id}`}>
                            <Button variant="outline" size="sm">
                              عرض
                            </Button>
                          </Link>

                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-600 hover:bg-red-50"
                            onClick={() => {
                              setSelectedInitiative(initiative._id)
                              setShowDeleteDialog(true)
                            }}
                          >
                            حذف
                          </Button>

                          {initiative.status === "pending" && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-green-600 border-green-600 hover:bg-green-50"
                                onClick={() => {
                                  setSelectedInitiative(initiative._id)
                                  setShowApproveDialog(true)
                                }}
                              >
                                قبول
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 border-red-600 hover:bg-red-50"
                                onClick={() => {
                                  setSelectedInitiative(initiative._id)
                                  setShowRejectDialog(true)
                                }}
                              >
                                رفض
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center py-4">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      السابق
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      التالي
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Approve Initiative Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>قبول المبادرة</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في قبول هذه المبادرة؟ سيتم نشرها وستكون مرئية لجميع المستخدمين.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
              إلغاء
            </Button>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handleApproveInitiative}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  قبول
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Initiative Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>رفض المبادرة</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في رفض هذه المبادرة؟ سيتم إخطار الكاتب.</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleRejectInitiative} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  رفض
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Initiative Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف المبادرة</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف هذه المبادرة؟ هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteInitiative}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري الحذف...
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  حذف
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
  )
}


"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { LayoutDashboard, Users, FileText, Tag, Flag, Settings, Menu, LogOut, Home, Image, Shield, Package, Award, Heart } from "lucide-react"
import { getCurrentUser, hasRole, clearUserData } from "@/lib/auth"

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter()

  // Utiliser un état pour gérer l'affichage
  const [loading, setLoading] = useState(true)
  const [authorized, setAuthorized] = useState(false)

  // Vérifier l'authentification côté client uniquement
  useEffect(() => {
    // Fonction pour vérifier l'authentification
    const checkAuth = () => {
      const user = getCurrentUser()
      console.log('[Admin Layout] User:', user)

      if (!user) {
        console.log('[Admin Layout] No user found')
        // Redirection forcée si non authentifié
        window.location.href = "/auth/login?redirect=/admin/dashboard"
        return false
      }

      // Vérifier si l'utilisateur a le rôle admin
      console.log('[Admin Layout] User role:', user.role, typeof user.role)
      console.log('[Admin Layout] Full user object:', JSON.stringify(user))

      // Vérification plus permissive pour le rôle admin
      // Cas 1: rôle est une chaîne exacte 'admin'
      if (typeof user.role === 'string' && user.role === 'admin') {
        console.log('[Admin Layout] User has string role "admin"')
        return true
      }

      // Cas 2: rôle est un objet avec code === 'admin'
      if (typeof user.role === 'object' && user.role && user.role.code === 'admin') {
        console.log('[Admin Layout] User has object role with code "admin"')
        return true
      }

      // Cas 3: rôle est une chaîne contenant 'admin'
      if (typeof user.role === 'string' && user.role.includes('admin')) {
        console.log('[Admin Layout] User has string role containing "admin"')
        return true
      }

      // Cas 4: username est 'admin' (pour le développement)
      if (user.username === 'admin') {
        console.log('[Admin Layout] User has username "admin"')
        return true
      }

      console.log('[Admin Layout] User is not admin')
      // Redirection forcée si non admin
      window.location.href = "/"
      return false
    }

    // Vérifier l'authentification et mettre à jour l'état
    const isAuth = checkAuth()
    setAuthorized(isAuth)
    setLoading(false)

    // Vérifier périodiquement
    const interval = setInterval(() => {
      const isStillAuth = checkAuth()
      setAuthorized(isStillAuth)
    }, 5000) // Vérifier toutes les 5 secondes

    // Écouter les événements de stockage pour détecter la déconnexion dans d'autres onglets
    const handleStorageChange = (e: StorageEvent) => {
      if ((e.key === 'user' || e.key === 'accessToken') && !e.newValue) {
        // Si l'utilisateur s'est déconnecté dans un autre onglet
        window.location.href = "/auth/login?redirect=/admin/dashboard"
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Nettoyer les écouteurs et intervalles lors du démontage
    return () => {
      clearInterval(interval)
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Afficher un écran de chargement pendant la vérification
  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#0a8754] mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">جاري التحميل...</h1>
          <p className="text-gray-600 mb-6">يرجى الانتظار بينما نتحقق من صلاحياتك.</p>
        </div>
      </div>
    )
  }

  // Afficher un message d'erreur si non autorisé
  if (!authorized) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">غير مصرح بالوصول</h1>
          <p className="text-gray-600 mb-6">يجب أن تكون مسجل الدخول كمشرف للوصول إلى هذه الصفحة.</p>
          <div className="flex flex-col space-y-2">
            <Button onClick={() => window.location.href = "/auth/login?redirect=/admin/dashboard"} className="w-full">
              تسجيل الدخول
            </Button>
            <Button variant="outline" onClick={() => window.location.href = "/"} className="w-full">
              العودة للصفحة الرئيسية
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="pr-0">
            <MobileNav />
          </SheetContent>
        </Sheet>
        <div className="flex items-center gap-2">
          <Link href="/admin/dashboard" className="flex items-center gap-2 font-semibold">
            <FileText className="h-6 w-6" />
            <span className="hidden md:inline-block">لوحة الإدارة</span>
          </Link>
        </div>
        <div className="flex flex-1 items-center gap-4 md:gap-2 md:ml-auto">
          <Link href="/" className="ml-auto">
            <Button variant="outline" size="sm" className="gap-1">
              <Home className="h-4 w-4" />
              <span>العودة للموقع</span>
            </Button>
          </Link>
        </div>
      </header>
      <div className="flex-1 flex flex-col md:grid md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
        <aside className="hidden md:block border-l">
          <ScrollArea className="h-[calc(100vh-64px)] py-6 pl-6 pr-2">
            <DesktopNav />
          </ScrollArea>
        </aside>
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          {children}
        </main>
      </div>
    </div>
  )
}

function DesktopNav() {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-1 py-2">
        <h2 className="text-lg font-semibold tracking-tight">الإدارة</h2>
        <p className="text-sm text-muted-foreground">إدارة المنصة والمستخدمين والمحتوى</p>
      </div>
      <Separator />
      <nav className="grid gap-1 px-2 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2">
        <NavLink href="/admin/dashboard" icon={<LayoutDashboard className="h-4 w-4" />} label="لوحة التحكم" />
        <NavLink href="/admin/users" icon={<Users className="h-4 w-4" />} label="المستخدمين" />
        <NavLink href="/admin/initiatives" icon={<FileText className="h-4 w-4" />} label="المبادرات" />
        <NavLink href="/admin/banners" icon={<Image className="h-4 w-4" />} label="البانرات" />
        <NavLink href="/admin/categories" icon={<Tag className="h-4 w-4" />} label="التصنيفات" />
        <NavLink href="/admin/resources" icon={<Package className="h-4 w-4" />} label="الموارد" />
        <NavLink href="/admin/badges" icon={<Award className="h-4 w-4" />} label="الشارات" />
        <NavLink href="/admin/social-impacts" icon={<Heart className="h-4 w-4" />} label="التأثيرات الاجتماعية" />
        <NavLink href="/admin/reports" icon={<Flag className="h-4 w-4" />} label="البلاغات" />
        <NavLink href="/admin/roles" icon={<Shield className="h-4 w-4" />} label="الأدوار والصلاحيات" />
        <NavLink href="/admin/settings" icon={<Settings className="h-4 w-4" />} label="الإعدادات" />
      </nav>
    </div>
  )
}

function MobileNav() {
  const router = useRouter()

  const handleLogout = () => {
    clearUserData()
    window.location.href = '/'
  }

  return (
    <div className="flex flex-col gap-2 pr-4">
      <div className="flex flex-col gap-1 py-2">
        <h2 className="text-lg font-semibold tracking-tight">الإدارة</h2>
        <p className="text-sm text-muted-foreground">إدارة المنصة والمستخدمين والمحتوى</p>
      </div>
      <Separator />
      <nav className="grid gap-1">
        <NavLink href="/admin/dashboard" icon={<LayoutDashboard className="h-4 w-4" />} label="لوحة التحكم" />
        <NavLink href="/admin/users" icon={<Users className="h-4 w-4" />} label="المستخدمين" />
        <NavLink href="/admin/initiatives" icon={<FileText className="h-4 w-4" />} label="المبادرات" />
        <NavLink href="/admin/banners" icon={<Image className="h-4 w-4" />} label="البانرات" />
        <NavLink href="/admin/categories" icon={<Tag className="h-4 w-4" />} label="التصنيفات" />
        <NavLink href="/admin/resources" icon={<Package className="h-4 w-4" />} label="الموارد" />
        <NavLink href="/admin/badges" icon={<Award className="h-4 w-4" />} label="الشارات" />
        <NavLink href="/admin/social-impacts" icon={<Heart className="h-4 w-4" />} label="التأثيرات الاجتماعية" />
        <NavLink href="/admin/reports" icon={<Flag className="h-4 w-4" />} label="البلاغات" />
        <NavLink href="/admin/roles" icon={<Shield className="h-4 w-4" />} label="الأدوار والصلاحيات" />
        <NavLink href="/admin/settings" icon={<Settings className="h-4 w-4" />} label="الإعدادات" />
      </nav>
      <Separator className="mt-auto" />
      <Button
        variant="ghost"
        className="justify-start gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
        onClick={handleLogout}
      >
        <LogOut className="h-4 w-4" />
        <span>تسجيل الخروج</span>
      </Button>
    </div>
  )
}

interface NavLinkProps {
  href: string
  icon: React.ReactNode
  label: string
}

function NavLink({ href, icon, label }: NavLinkProps) {
  return (
    <Link href={href}>
      <Button variant="ghost" className="w-full justify-start gap-2">
        {icon}
        <span>{label}</span>
      </Button>
    </Link>
  )
}


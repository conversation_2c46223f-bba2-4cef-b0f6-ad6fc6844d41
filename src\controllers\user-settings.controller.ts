import { Request, Response, NextFunction } from "express"
import { User } from "../models"
import { createError } from "../utils/error"
import { asyncHand<PERSON> } from "../utils/error"

/**
 * Get user settings
 * @route GET /api/users/:id/settings
 * @access Private
 */
export const getUserSettings = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params

    // Check if user is authorized to access these settings
    if (req.user && req.user.id !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to access these settings"))
    }

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Get settings from user document
    // If settings field doesn't exist, return default settings
    const defaultSettings = {
      notifications: {
        email: true,
        browser: true,
        initiatives: true,
        comments: true,
        mentions: true
      },
      privacy: {
        showEmail: false,
        showLocation: true,
        showInitiatives: true
      },
      language: "ar",
      theme: "light"
    };

    // Préparer la réponse en fonction du type d'utilisateur
    const responseSettings: any = {
      ...(user.settings || defaultSettings),
      // Include these fields from user document
      name: user.name,
      email: user.email,
      username: user.username,
      bio: user.bio || "",
      location: user.location || "",
      website: user.website || "",
      avatar: user.avatar || ""
    };

    // Ajouter les champs spécifiques au type d'utilisateur
    if (user.userType === 'volunteer') {
      responseSettings.qualifications = user.qualifications || [];
      responseSettings.skills = user.skills || [];
      responseSettings.interests = user.interests || [];
      responseSettings.availability = user.availability || "";
    } else if (user.userType === 'proposer') {
      responseSettings.ideaDescription = user.ideaDescription || "";
      responseSettings.objectives = user.objectives || "";
      responseSettings.needs = user.needs || "";
    } else if (user.userType === 'company') {
      responseSettings.companyName = user.companyName || "";
      responseSettings.industry = user.industry || "";
      responseSettings.customIndustry = user.customIndustry || "";
      responseSettings.companyDescription = user.companyDescription || "";
      responseSettings.employeeCount = user.employeeCount || "";
      responseSettings.foundingYear = user.foundingYear || "";
      responseSettings.contactPhone = user.contactPhone || "";
      responseSettings.contactEmail = user.contactEmail || "";
      responseSettings.website = user.website || "";
      responseSettings.address = user.address || "";
      responseSettings.services = user.services || [];
      responseSettings.resources = user.resources || [];
      responseSettings.customResources = user.customResources || [];
      responseSettings.supportDescription = user.supportDescription || "";
      responseSettings.socialLinks = user.socialLinks || {};
    } else if (user.userType === 'civilSociety') {
      responseSettings.organizationName = user.organizationName || "";
      responseSettings.organizationType = user.organizationType || "";
      responseSettings.activitySector = user.activitySector || "";
      responseSettings.scope = user.scope || "";
      responseSettings.approvalNumber = user.approvalNumber || "";
      responseSettings.memberCount = user.memberCount || "";
      responseSettings.foundingYear = user.foundingYear || "";
      responseSettings.organizationDescription = user.organizationDescription || "";
      responseSettings.contactPhone = user.contactPhone || "";
      responseSettings.contactPhone2 = user.contactPhone2 || "";
      responseSettings.contactEmail = user.contactEmail || "";
      responseSettings.website = user.website || "";
      responseSettings.address = user.address || "";
      responseSettings.services = user.services || [];
      responseSettings.resources = user.resources || [];
      responseSettings.customResources = user.customResources || [];
      responseSettings.supportDescription = user.supportDescription || "";
      responseSettings.mainContact = user.mainContact || { name: "", phone: "", email: "" };
    }

    res.status(200).json({
      success: true,
      settings: responseSettings
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Update user settings
 * @route PUT /api/users/:id/settings
 * @access Private
 */
export const updateUserSettings = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params;
    const {
      name, username, bio, location, website,
      notifications, privacy, language, theme,
      // Champs spécifiques pour les volontaires
      qualifications, skills, interests, availability,
      // Champs spécifiques pour les proposeurs d'idées
      ideaDescription, objectives, needs,
      // Champs spécifiques pour les entreprises
      companyName, industry, customIndustry, companyDescription, employeeCount,
      foundingYear, contactPhone, contactEmail, address, services, resources,
      customResources, supportDescription, socialLinks,
      // Champs spécifiques pour les acteurs de la société civile
      organizationName, organizationType, activitySector, scope, approvalNumber,
      memberCount, organizationDescription, contactPhone2, mainContact
    } = req.body;

    // Check if user is authorized to update these settings
    if (req.user && req.user.id !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to update these settings"));
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, "User not found"));
    }

    // Update user fields
    if (name) user.name = name;
    if (username) user.username = username;
    if (bio !== undefined) user.bio = bio;
    if (location !== undefined) user.location = location;
    if (website !== undefined) user.website = website;

    // Update settings fields
    if (!user.settings) {
      user.settings = {};
    }

    if (notifications) {
      user.settings.notifications = {
        ...user.settings.notifications,
        ...notifications
      };
    }

    if (privacy) {
      user.settings.privacy = {
        ...user.settings.privacy,
        ...privacy
      };
    }

    if (language) {
      user.settings.language = language;
    }

    if (theme) {
      user.settings.theme = theme;
    }

    // Mettre à jour les champs spécifiques au type d'utilisateur
    if (user.userType === 'volunteer') {
      // Champs spécifiques pour les volontaires
      if (qualifications !== undefined) {
        console.log("Updating qualifications:", qualifications);
        user.qualifications = qualifications;
      }

      // Vérifier et mettre à jour les compétences
      if (skills !== undefined) {
        console.log("Received skills:", JSON.stringify(skills, null, 2));

        // Vérifier que chaque compétence a une catégorie et un niveau
        const validSkills = skills.map(skill => {
          let validSkill = { ...skill };

          // Si c'est une chaîne, la convertir en objet
          if (typeof skill === 'string') {
            validSkill = {
              name: skill,
              category: 'technical',
              level: 'intermediate'
            };
          } else {
            // S'assurer que les propriétés requises sont présentes
            if (!validSkill.category) {
              validSkill.category = 'technical';
            }
            if (!validSkill.level) {
              validSkill.level = 'intermediate';
            }
          }

          return validSkill;
        });

        console.log("Saving skills to user:", JSON.stringify(validSkills, null, 2));
        user.skills = validSkills;

        // Assurez-vous que les compétences sont correctement formatées pour MongoDB
        if (user.skills && Array.isArray(user.skills)) {
          user.skills = user.skills
            .filter(skill => {
              // Filtrer les compétences vides ou non valides
              if (typeof skill === 'string') {
                return skill.trim() !== '';
              }
              return skill && (skill.name && skill.name.trim() !== '' && skill.name !== 'Unnamed skill');
            })
            .map(skill => {
              // Si c'est déjà un objet avec les bonnes propriétés, le retourner tel quel
              if (skill && typeof skill === 'object' && 'name' in skill && 'category' in skill && 'level' in skill) {
                // Vérifier que le nom est valide
                if (skill.name.trim() === '' || skill.name === 'Unnamed skill') {
                  return null;
                }
                return skill;
              }

              // Si c'est une chaîne, la convertir en objet
              if (typeof skill === 'string') {
                return {
                  name: skill.trim(),
                  category: 'technical',
                  level: 'intermediate'
                };
              }

              // Sinon, créer un nouvel objet avec les propriétés requises
              if (!skill.name || skill.name.trim() === '' || skill.name === 'Unnamed skill') {
                return null;
              }

              return {
                name: skill.name.trim(),
                category: skill.category || 'technical',
                level: skill.level || 'intermediate'
              };
            })
            .filter(Boolean); // Filtrer les valeurs null
        }

        console.log("Final skills array for volunteer:", JSON.stringify(user.skills, null, 2));
      }

      if (interests !== undefined) {
        console.log("Updating interests:", interests);
        user.interests = interests;
      }

      if (availability !== undefined) {
        console.log("Updating availability:", availability);
        user.availability = availability;
      }
    } else if (user.userType === 'proposer') {
      // Champs spécifiques pour les proposeurs d'idées
      console.log("Updating proposer fields");
      console.log("User type:", user.userType);

      if (qualifications !== undefined) {
        console.log("Updating qualifications for proposer:", qualifications);
        user.qualifications = qualifications;
      }

      if (skills !== undefined) {
        console.log("Updating skills for proposer:", JSON.stringify(skills, null, 2));

        // Vérifier que chaque compétence a une catégorie et un niveau
        const validSkills = skills.map(skill => {
          let validSkill = { ...skill };

          // Si c'est une chaîne, la convertir en objet
          if (typeof skill === 'string') {
            validSkill = {
              name: skill,
              category: 'technical',
              level: 'intermediate'
            };
          } else {
            // S'assurer que les propriétés requises sont présentes
            if (!validSkill.category) {
              validSkill.category = 'technical';
            }
            if (!validSkill.level) {
              validSkill.level = 'intermediate';
            }
          }

          return validSkill;
        });

        console.log("Formatted skills for proposer:", JSON.stringify(validSkills, null, 2));
        user.skills = validSkills;

        // Assurez-vous que les compétences sont correctement formatées pour MongoDB
        if (user.skills && Array.isArray(user.skills)) {
          user.skills = user.skills
            .filter(skill => {
              // Filtrer les compétences vides ou non valides
              if (typeof skill === 'string') {
                return skill.trim() !== '';
              }
              return skill && (skill.name && skill.name.trim() !== '' && skill.name !== 'Unnamed skill');
            })
            .map(skill => {
              // Si c'est déjà un objet avec les bonnes propriétés, le retourner tel quel
              if (skill && typeof skill === 'object' && 'name' in skill && 'category' in skill && 'level' in skill) {
                // Vérifier que le nom est valide
                if (skill.name.trim() === '' || skill.name === 'Unnamed skill') {
                  return null;
                }
                return skill;
              }

              // Si c'est une chaîne, la convertir en objet
              if (typeof skill === 'string') {
                return {
                  name: skill.trim(),
                  category: 'technical',
                  level: 'intermediate'
                };
              }

              // Sinon, créer un nouvel objet avec les propriétés requises
              if (!skill.name || skill.name.trim() === '' || skill.name === 'Unnamed skill') {
                return null;
              }

              return {
                name: skill.name.trim(),
                category: skill.category || 'technical',
                level: skill.level || 'intermediate'
              };
            })
            .filter(Boolean); // Filtrer les valeurs null
        }

        console.log("Final skills array for proposer:", JSON.stringify(user.skills, null, 2));
      }

      if (interests !== undefined) {
        console.log("Updating interests for proposer:", interests);
        user.interests = interests;
      }

      if (ideaDescription !== undefined) {
        console.log("Updating ideaDescription:", ideaDescription);
        user.ideaDescription = ideaDescription;
      }

      if (objectives !== undefined) {
        console.log("Updating objectives:", objectives);
        user.objectives = objectives;
      }

      if (needs !== undefined) {
        console.log("Updating needs:", needs);
        user.needs = needs;
      }
    } else if (user.userType === 'company') {
      // Champs spécifiques pour les entreprises
      if (companyName !== undefined) user.companyName = companyName;
      if (industry !== undefined) user.industry = industry;
      if (customIndustry !== undefined) user.customIndustry = customIndustry;
      if (companyDescription !== undefined) user.companyDescription = companyDescription;
      if (employeeCount !== undefined) user.employeeCount = employeeCount;
      if (foundingYear !== undefined) user.foundingYear = foundingYear;
      if (contactPhone !== undefined) user.contactPhone = contactPhone;
      if (contactEmail !== undefined) user.contactEmail = contactEmail;
      if (address !== undefined) user.address = address;
      if (services !== undefined) user.services = services;
      if (resources !== undefined) user.resources = resources;
      if (customResources !== undefined) user.customResources = customResources;
      if (supportDescription !== undefined) user.supportDescription = supportDescription;
      if (socialLinks !== undefined) user.socialLinks = socialLinks;
    } else if (user.userType === 'civilSociety') {
      // Champs spécifiques pour les acteurs de la société civile
      if (organizationName !== undefined) user.organizationName = organizationName;
      if (organizationType !== undefined) user.organizationType = organizationType;
      if (activitySector !== undefined) user.activitySector = activitySector;
      if (scope !== undefined) user.scope = scope;
      if (approvalNumber !== undefined) user.approvalNumber = approvalNumber;
      if (memberCount !== undefined) user.memberCount = memberCount;
      if (foundingYear !== undefined) user.foundingYear = foundingYear;
      if (organizationDescription !== undefined) user.organizationDescription = organizationDescription;
      if (contactPhone !== undefined) user.contactPhone = contactPhone;
      if (contactPhone2 !== undefined) user.contactPhone2 = contactPhone2;
      if (contactEmail !== undefined) user.contactEmail = contactEmail;
      if (website !== undefined) user.website = website;
      if (address !== undefined) user.address = address;
      if (services !== undefined) user.services = services;
      if (resources !== undefined) user.resources = resources;
      if (customResources !== undefined) user.customResources = customResources;
      if (supportDescription !== undefined) user.supportDescription = supportDescription;
      if (mainContact !== undefined) user.mainContact = mainContact;
    }

    // Log user object before saving
    console.log("User object before saving:", {
      id: user._id,
      userType: user.userType,
      hasSkills: !!user.skills,
      skillsLength: user.skills ? user.skills.length : 0,
      skills: user.skills
    });

    // Préparer les données à mettre à jour
    const updateData: any = {
      name: user.name,
      username: user.username,
      bio: user.bio,
      location: user.location,
      website: user.website,
      settings: user.settings
    };

    // Ajouter les champs spécifiques au type d'utilisateur
    if (user.userType === 'volunteer') {
      updateData.qualifications = user.qualifications;
      updateData.skills = user.skills;
      updateData.interests = user.interests;
      updateData.availability = user.availability;
    } else if (user.userType === 'proposer') {
      updateData.qualifications = user.qualifications;
      updateData.skills = user.skills;
      updateData.interests = user.interests;
      updateData.ideaDescription = user.ideaDescription;
      updateData.objectives = user.objectives;
      updateData.needs = user.needs;
    } else if (user.userType === 'company') {
      updateData.companyName = user.companyName;
      updateData.industry = user.industry;
      updateData.customIndustry = user.customIndustry;
      updateData.companyDescription = user.companyDescription;
      updateData.employeeCount = user.employeeCount;
      updateData.foundingYear = user.foundingYear;
      updateData.contactPhone = user.contactPhone;
      updateData.contactEmail = user.contactEmail;
      updateData.address = user.address;
      updateData.services = user.services;
      updateData.resources = user.resources;
      updateData.customResources = user.customResources;
      updateData.supportDescription = user.supportDescription;
      updateData.socialLinks = user.socialLinks;
    } else if (user.userType === 'civilSociety') {
      updateData.organizationName = user.organizationName;
      updateData.organizationType = user.organizationType;
      updateData.activitySector = user.activitySector;
      updateData.scope = user.scope;
      updateData.approvalNumber = user.approvalNumber;
      updateData.memberCount = user.memberCount;
      updateData.foundingYear = user.foundingYear;
      updateData.organizationDescription = user.organizationDescription;
      updateData.contactPhone = user.contactPhone;
      updateData.contactPhone2 = user.contactPhone2;
      updateData.contactEmail = user.contactEmail;
      updateData.website = user.website;
      updateData.address = user.address;
      updateData.services = user.services;
      updateData.resources = user.resources;
      updateData.customResources = user.customResources;
      updateData.supportDescription = user.supportDescription;
      updateData.mainContact = user.mainContact;
    }

    console.log("Update data:", JSON.stringify(updateData, null, 2));

    // Assurez-vous que les compétences sont correctement incluses dans les données à mettre à jour
    console.log("Skills before update:", user.skills);

    // Assurez-vous que les compétences sont correctement incluses dans les données à mettre à jour
    if (skills !== undefined && Array.isArray(skills)) {
      console.log("Ensuring skills are included in updateData");

      // Créer un nouveau tableau de compétences valides
      const validatedSkills = skills
        .filter(skill => {
          // Filtrer les compétences vides ou non valides
          if (typeof skill === 'string') {
            return skill.trim() !== '';
          }
          return skill && (skill.name && skill.name.trim() !== '' && skill.name !== 'Unnamed skill');
        })
        .map(skill => {
          // Si c'est une chaîne, la convertir en objet
          if (typeof skill === 'string') {
            return {
              name: skill.trim(),
              category: 'technical',
              level: 'intermediate'
            };
          }

          // Si c'est un objet, s'assurer qu'il a les propriétés requises
          const validSkill = { ...skill };

          // S'assurer que le nom est valide (non vide et pas "Unnamed skill")
          if (!validSkill.name || validSkill.name.trim() === '' || validSkill.name === 'Unnamed skill') {
            // Si le nom n'est pas valide, ne pas inclure cette compétence
            return null;
          }

          // S'assurer que les autres propriétés sont valides
          if (!validSkill.category) validSkill.category = 'technical';
          if (!validSkill.level) validSkill.level = 'intermediate';

          return {
            ...validSkill,
            name: validSkill.name.trim() // S'assurer que le nom est trimé
          };
        })
        .filter(Boolean); // Filtrer les valeurs null

      console.log("Skills after validation:", JSON.stringify(validatedSkills, null, 2));

      // Mettre à jour les compétences de l'utilisateur
      user.skills = validatedSkills;

      // Ajouter les compétences validées aux données à mettre à jour
      updateData.skills = validatedSkills;

      console.log("Skills added to updateData:", JSON.stringify(updateData.skills, null, 2));
    }

    console.log("Final update data:", JSON.stringify(updateData, null, 2));

    // Save user using findByIdAndUpdate
    try {
      // Utiliser directement les données de l'utilisateur pour la mise à jour
      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        { $set: updateData },
        { new: true, runValidators: true }
      );

      console.log("User saved successfully with findByIdAndUpdate");
      console.log("Updated user:", {
        id: updatedUser._id,
        userType: updatedUser.userType,
        hasSkills: !!updatedUser.skills,
        skillsLength: updatedUser.skills ? updatedUser.skills.length : 0,
        skills: updatedUser.skills
      });

      // Vérifier si les compétences ont été correctement sauvegardées
      if (skills !== undefined && (!updatedUser.skills || updatedUser.skills.length === 0)) {
        console.log("Skills not saved properly with findByIdAndUpdate, trying direct save method");

        // Essayer de sauvegarder directement avec save()
        try {
          // Mettre à jour l'utilisateur avec les données mises à jour
          Object.assign(user, updateData);

          // Sauvegarder l'utilisateur
          await user.save();

          console.log("User saved successfully with direct save method");
          console.log("User after direct save:", {
            id: user._id,
            userType: user.userType,
            hasSkills: !!user.skills,
            skillsLength: user.skills ? user.skills.length : 0,
            skills: user.skills
          });
        } catch (saveError) {
          console.error("Error saving user with direct save method:", saveError);
          // Continuer avec les données de findByIdAndUpdate
          Object.assign(user, updatedUser);
        }
      } else {
        // Mettre à jour l'objet user avec les données mises à jour
        Object.assign(user, updatedUser);
      }
    } catch (error) {
      console.error("Error saving user:", error);

      // Essayer de sauvegarder directement avec save() en cas d'échec de findByIdAndUpdate
      try {
        console.log("Trying direct save method as fallback");

        // Mettre à jour l'utilisateur avec les données mises à jour
        Object.assign(user, updateData);

        // Sauvegarder l'utilisateur
        await user.save();

        console.log("User saved successfully with direct save method (fallback)");
      } catch (saveError) {
        console.error("Error saving user with direct save method (fallback):", saveError);
        throw error; // Lancer l'erreur originale
      }
    }

    // Préparer la réponse en fonction du type d'utilisateur
    const responseSettings: any = {
      name: user.name,
      email: user.email,
      username: user.username,
      bio: user.bio || "",
      location: user.location || "",
      website: user.website || "",
      avatar: user.avatar || "",
      notifications: user.settings.notifications,
      privacy: user.settings.privacy,
      language: user.settings.language,
      theme: user.settings.theme,
      // Ajouter les champs spécifiques au type d'utilisateur
      userType: user.userType,
      // Champs communs à tous les types d'utilisateurs
      qualifications: user.qualifications || [],
      skills: user.skills || [],
      interests: user.interests || []
    };

    console.log("Response settings:", JSON.stringify(responseSettings, null, 2));

    // Ajouter les champs spécifiques au type d'utilisateur
    if (user.userType === 'volunteer') {
      responseSettings.qualifications = user.qualifications || [];
      responseSettings.skills = user.skills || [];
      responseSettings.interests = user.interests || [];
      responseSettings.availability = user.availability || "";
    } else if (user.userType === 'proposer') {
      responseSettings.ideaDescription = user.ideaDescription || "";
      responseSettings.objectives = user.objectives || "";
      responseSettings.needs = user.needs || "";
    } else if (user.userType === 'company') {
      responseSettings.companyName = user.companyName || "";
      responseSettings.industry = user.industry || "";
      responseSettings.customIndustry = user.customIndustry || "";
      responseSettings.companyDescription = user.companyDescription || "";
      responseSettings.employeeCount = user.employeeCount || "";
      responseSettings.foundingYear = user.foundingYear || "";
      responseSettings.contactPhone = user.contactPhone || "";
      responseSettings.contactEmail = user.contactEmail || "";
      responseSettings.website = user.website || "";
      responseSettings.address = user.address || "";
      responseSettings.services = user.services || [];
      responseSettings.resources = user.resources || [];
      responseSettings.customResources = user.customResources || [];
      responseSettings.supportDescription = user.supportDescription || "";
      responseSettings.socialLinks = user.socialLinks || {};
    } else if (user.userType === 'civilSociety') {
      responseSettings.organizationName = user.organizationName || "";
      responseSettings.organizationType = user.organizationType || "";
      responseSettings.activitySector = user.activitySector || "";
      responseSettings.scope = user.scope || "";
      responseSettings.approvalNumber = user.approvalNumber || "";
      responseSettings.memberCount = user.memberCount || "";
      responseSettings.foundingYear = user.foundingYear || "";
      responseSettings.organizationDescription = user.organizationDescription || "";
      responseSettings.contactPhone = user.contactPhone || "";
      responseSettings.contactPhone2 = user.contactPhone2 || "";
      responseSettings.contactEmail = user.contactEmail || "";
      responseSettings.website = user.website || "";
      responseSettings.address = user.address || "";
      responseSettings.services = user.services || [];
      responseSettings.resources = user.resources || [];
      responseSettings.customResources = user.customResources || [];
      responseSettings.supportDescription = user.supportDescription || "";
      responseSettings.mainContact = user.mainContact || { name: "", phone: "", email: "" };
    }

    res.status(200).json({
      success: true,
      message: "Settings updated successfully",
      settings: responseSettings
    });
  } catch (error) {
    next(error);
  }
});

"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { api } from "@/lib/api"
import { toast } from "@/components/ui/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Loader2 } from "lucide-react"

// Schéma de validation pour le formulaire
const badgeSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit contenir au moins 2 caractères" }),
  arabicName: z.string().min(2, { message: "Le nom arabe doit contenir au moins 2 caractères" }),
  description: z.string().min(10, { message: "La description doit contenir au moins 10 caractères" }),
  arabicDescription: z.string().min(10, { message: "La description arabe doit contenir au moins 10 caractères" }),
  icon: z.string().min(1, { message: "L'icône est requise" }),
  color: z.string().min(1, { message: "La couleur est requise" }),
  criteria: z.string().min(5, { message: "Les critères doivent contenir au moins 5 caractères" }),
  category: z.string().min(1, { message: "La catégorie est requise" }),
  level: z.coerce.number().min(1, { message: "Le niveau doit être au moins 1" }).max(5, { message: "Le niveau ne peut pas dépasser 5" })
})

type BadgeFormValues = z.infer<typeof badgeSchema>

interface BadgeFormProps {
  badge?: any
  onSuccess: () => void
  onCancel?: () => void
}

export default function BadgeForm({ badge, onSuccess, onCancel }: BadgeFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const isEditing = !!badge

  // Initialiser le formulaire avec les valeurs par défaut ou les valeurs du badge existant
  const form = useForm<BadgeFormValues>({
    resolver: zodResolver(badgeSchema),
    defaultValues: {
      name: badge?.name || "",
      arabicName: badge?.arabicName || "",
      description: badge?.description || "",
      arabicDescription: badge?.arabicDescription || "",
      icon: badge?.icon || "award",
      color: badge?.color || "#4CAF50",
      criteria: badge?.criteria || "",
      category: badge?.category || "participation",
      level: badge?.level || 1
    }
  })

  const onSubmit = async (data: BadgeFormValues) => {
    try {
      setIsLoading(true)

      let response
      if (isEditing) {
        // Mettre à jour un badge existant
        response = await api.put(`/api/badges/${badge._id}`, data, true)
      } else {
        // Créer un nouveau badge
        response = await api.post("/api/badges", data, true)
      }

      if (response.success) {
        toast({
          title: isEditing ? "Badge mis à jour" : "Badge créé",
          description: isEditing ? "Le badge a été mis à jour avec succès" : "Le badge a été créé avec succès",
          variant: "default"
        })
        onSuccess()
      } else {
        toast({
          title: "Erreur",
          description: response.message || "Une erreur s'est produite",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error("Error submitting badge form:", error)
      toast({
        title: "Erreur",
        description: error.message || "Une erreur s'est produite lors de la soumission du formulaire",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Liste des catégories de badges
  const categories = [
    { value: "participation", label: "المشاركة (Participation)" },
    { value: "achievement", label: "الإنجاز (Achievement)" },
    { value: "contribution", label: "المساهمة (Contribution)" },
    { value: "skill", label: "المهارة (Skill)" },
    { value: "special", label: "خاص (Special)" }
  ]

  // Liste des icônes disponibles
  const icons = [
    { value: "award", label: "جائزة (Award)" },
    { value: "trophy", label: "كأس (Trophy)" },
    { value: "star", label: "نجمة (Star)" },
    { value: "heart", label: "قلب (Heart)" },
    { value: "gift", label: "هدية (Gift)" },
    { value: "users", label: "مستخدمون (Users)" },
    { value: "tool", label: "أداة (Tool)" },
    { value: "lightbulb", label: "مصباح (Lightbulb)" }
  ]

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Nom */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom (en anglais)</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: Participation Badge" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Nom arabe */}
          <FormField
            control={form.control}
            name="arabicName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الاسم (بالعربية)</FormLabel>
                <FormControl>
                  <Input placeholder="مثال: شارة المشاركة" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (en anglais)</FormLabel>
                <FormControl>
                  <Textarea placeholder="Ex: Awarded for active participation in initiatives" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description arabe */}
          <FormField
            control={form.control}
            name="arabicDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الوصف (بالعربية)</FormLabel>
                <FormControl>
                  <Textarea placeholder="مثال: تمنح للمشاركة النشطة في المبادرات" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Icône */}
          <FormField
            control={form.control}
            name="icon"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الأيقونة (Icon)</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر أيقونة" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {icons.map((icon) => (
                      <SelectItem key={icon.value} value={icon.value}>
                        {icon.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Couleur */}
          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem>
                <FormLabel>اللون (Color)</FormLabel>
                <div className="flex items-center gap-2">
                  <FormControl>
                    <Input type="text" placeholder="#4CAF50" {...field} />
                  </FormControl>
                  <Input
                    type="color"
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                    className="w-12 h-10 p-1"
                  />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Critères */}
          <FormField
            control={form.control}
            name="criteria"
            render={({ field }) => (
              <FormItem>
                <FormLabel>معايير المنح (Criteria)</FormLabel>
                <FormControl>
                  <Textarea placeholder="مثال: المشاركة النشطة في المبادرة" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Catégorie */}
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>الفئة (Category)</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر فئة" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Niveau */}
          <FormField
            control={form.control}
            name="level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>المستوى (Level)</FormLabel>
                <FormControl>
                  <Input type="number" min={1} max={5} {...field} />
                </FormControl>
                <FormDescription>
                  قيمة بين 1 و 5، حيث 1 هو المستوى الأساسي و 5 هو المستوى الأعلى
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel || (() => {})} disabled={isLoading}>
            إلغاء
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                {isEditing ? "جاري التحديث..." : "جاري الإنشاء..."}
              </>
            ) : (
              <>{isEditing ? "تحديث الشارة" : "إنشاء الشارة"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Users, Heart, Target, Lightbulb, Handshake, Mail, Phone, MapPin } from "lucide-react"
import { getPublicSettings, type SiteSettings, defaultSettings } from "@/lib/settings"

// Mock data for team members
const teamMembers = [
  {
    name: "محمد أمين",
    role: "المؤسس والمدير التنفيذي",
    bio: "خبير في مجال التنمية المجتمعية مع أكثر من 10 سنوات من الخبرة في إدارة المشاريع الاجتماعية.",
    avatar: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "سارة مصطفى",
    role: "مديرة العمليات",
    bio: "متخصصة في إدارة المشاريع وتطوير المنظمات غير الربحية، حاصلة على ماجستير في التنمية المستدامة.",
    avatar: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "أحمد خالد",
    role: "مدير التكنولوجيا",
    bio: "مهندس برمجيات مع خبرة واسعة في تطوير المنصات الرقمية وحلول التكنولوجيا للمشاريع الاجتماعية.",
    avatar: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "ليلى بن عمر",
    role: "مديرة التواصل والشراكات",
    bio: "خبيرة في العلاقات العامة والتواصل المؤسسي، عملت سابقاً في العديد من المنظمات الدولية.",
    avatar: "/placeholder.svg?height=200&width=200",
  },
]

// Mock data for partners
const partners = [
  { name: "وزارة البيئة", logo: "/placeholder.svg?height=100&width=200" },
  { name: "وزارة التعليم", logo: "/placeholder.svg?height=100&width=200" },
  { name: "بلدية الجزائر", logo: "/placeholder.svg?height=100&width=200" },
  { name: "جامعة الجزائر", logo: "/placeholder.svg?height=100&width=200" },
  { name: "مؤسسة التنمية المستدامة", logo: "/placeholder.svg?height=100&width=200" },
  { name: "شركة سوناطراك", logo: "/placeholder.svg?height=100&width=200" },
]

export default function AboutPage() {
  const [settings, setSettings] = useState<SiteSettings>(defaultSettings)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const data = await getPublicSettings()
        setSettings(data)
      } catch (error) {
        console.error('Error fetching settings:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSettings()
  }, [])

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      {isLoading && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <div className="animate-spin h-8 w-8 border-4 border-[#0a8754] border-t-transparent rounded-full mx-auto"></div>
            <p className="mt-2 text-center text-gray-700">جاري تحميل البيانات...</p>
          </div>
        </div>
      )}
      <div className="bg-[#0a8754] text-white py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">من نحن</h1>
          <p className="text-lg opacity-90 max-w-2xl mx-auto">
            منصة المبادرات المواطنة الجزائرية هي مشروع وطني يهدف إلى تمكين المواطنين من المشاركة في تطوير مجتمعهم وبناء
            مستقبل أفضل للجزائر.
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 md:p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          <div>
            <h2 className="text-2xl font-bold text-[#0a8754] mb-6">قصتنا</h2>
            <div className="space-y-4 text-gray-700">
              <p>
                بدأت فكرة منصة المبادرات المواطنة الجزائرية في عام 2022، عندما اجتمعت مجموعة من الشباب الجزائري المهتم
                بالتنمية المجتمعية والعمل التطوعي. كان هدفنا هو إيجاد طريقة فعالة لتمكين المواطنين من المشاركة في تطوير
                مجتمعهم وحل المشكلات التي تواجههم.
              </p>
              <p>
                لاحظنا أن هناك العديد من الأفكار والمبادرات الرائعة التي يقترحها المواطنون، لكنها غالباً ما تفتقر إلى
                الدعم والتنظيم اللازمين لتحويلها إلى واقع ملموس. من هنا، ولدت فكرة إنشاء منصة رقمية تجمع بين أصحاب
                الأفكار والمتطوعين والجهات الداعمة.
              </p>
              <p>
                بعد أشهر من العمل والتخطيط، أطلقنا النسخة التجريبية من المنصة في يناير 2023، وحظيت بتفاعل كبير من
                المواطنين. ومنذ ذلك الحين، نمت المنصة لتصبح مساحة نشطة للمبادرات المجتمعية في مختلف المجالات.
              </p>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <img src="/placeholder.svg?height=400&width=600" alt="صورة فريق العمل" className="rounded-lg shadow-md" />
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-[#0a8754] mb-8 text-center">رؤيتنا ورسالتنا</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <CardTitle>رؤيتنا</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700 text-base">
                  نتطلع إلى بناء مجتمع جزائري مبادر ومشارك بفعالية في التنمية المستدامة، حيث يكون لكل مواطن دور في تحسين
                  محيطه وتطوير وطنه.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                  <Heart className="h-8 w-8 text-white" />
                </div>
                <CardTitle>رسالتنا</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700 text-base">
                  تمكين المواطنين الجزائريين من المشاركة الفعالة في تنمية مجتمعهم من خلال توفير منصة تفاعلية تسهل اقتراح
                  المبادرات، مناقشتها، دعمها، وتنفيذها بطريقة منظمة وفعالة.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-[#0a8754] mb-8 text-center">قيمنا</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>المشاركة المجتمعية</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700 text-center">
                  نؤمن بأن التنمية الحقيقية تبدأ من المجتمع، وأن لكل مواطن دور في تحسين محيطه وتطوير وطنه.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="h-8 w-8 text-white" />
                </div>
                <CardTitle>الإبداع والابتكار</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700 text-center">
                  نشجع التفكير الإبداعي والحلول المبتكرة للتحديات المجتمعية، ونؤمن بأن الأفكار الجديدة هي مفتاح التغيير
                  الإيجابي.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Handshake className="h-8 w-8 text-white" />
                </div>
                <CardTitle>التعاون والتكامل</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700 text-center">
                  نؤمن بقوة العمل الجماعي والتعاون بين مختلف الأطراف، ونسعى لبناء شراكات فعالة تساهم في تحقيق أهدافنا
                  المشتركة.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-[#0a8754] mb-8 text-center">فريقنا</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {teamMembers.map((member, index) => (
              <Card key={index}>
                <CardHeader className="text-center pb-2">
                  <Avatar className="w-24 h-24 mx-auto mb-4">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <Badge className="bg-[#0a8754] mx-auto mt-2">{member.role}</Badge>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-700 text-center">{member.bio}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-[#0a8754] mb-8 text-center">شركاؤنا</h2>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {partners.map((partner, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-4 flex items-center justify-center">
                <img src={partner.logo || "/placeholder.svg"} alt={`شعار ${partner.name}`} className="max-h-16" />
              </div>
            ))}
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-bold text-[#0a8754] mb-8 text-center">إنجازاتنا</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-4xl font-bold text-[#0a8754] mb-2">1200+</div>
              <p className="text-gray-700">مبادرة مقترحة</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-4xl font-bold text-[#0a8754] mb-2">350+</div>
              <p className="text-gray-700">مبادرة تم تنفيذها</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-4xl font-bold text-[#0a8754] mb-2">5000+</div>
              <p className="text-gray-700">مشارك نشط</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 md:p-8 mb-16">
          <h2 className="text-2xl font-bold text-[#0a8754] mb-6 text-center">تواصل معنا</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold mb-2">البريد الإلكتروني</h3>
              <p className="text-gray-700">{settings.contactEmail}</p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                <Phone className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold mb-2">الهاتف</h3>
              <p className="text-gray-700">{settings.supportPhone}</p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                <MapPin className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold mb-2">العنوان</h3>
              <p className="text-gray-700">{settings.address}</p>
            </div>
          </div>

          <div className="text-center">
            <Link href="/contact">
              <Button className="bg-[#0a8754] hover:bg-[#097548]">تواصل معنا</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}


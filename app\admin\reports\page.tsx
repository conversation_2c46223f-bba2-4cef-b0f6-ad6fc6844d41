"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertCircle, Loader2, CheckCircle2, XCircle, ChevronLeft, ChevronRight } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { api } from "@/lib/api"

export default function AdminReportsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [reports, setReports] = useState([])
  const [totalReports, setTotalReports] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Filters
  const [statusFilter, setStatusFilter] = useState("pending")
  const [typeFilter, setTypeFilter] = useState("all")
  const [sortBy, setSortBy] = useState("newest")

  // Action dialogs
  const [selectedReport, setSelectedReport] = useState(null)
  const [showResolveDialog, setShowResolveDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [resolution, setResolution] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    fetchReports()
  }, [currentPage, statusFilter, typeFilter, sortBy])

  const fetchReports = async () => {
    try {
      setIsLoading(true)

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        sort: sortBy,
      })

      if (statusFilter !== "all") {
        params.append("status", statusFilter)
      }

      if (typeFilter !== "all") {
        params.append("type", typeFilter)
      }

      // Fetch reports
      const response = await api.get(`/api/admin/reports?${params.toString()}`)
      setReports(response.reports || [])
      if (response.pagination) {
        setTotalReports(response.pagination.total || 0)
        setTotalPages(response.pagination.totalPages || 1)
      }
    } catch (err) {
      setError(err.message || "Failed to load reports")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResolveReport = async () => {
    if (!selectedReport || !resolution.trim()) return

    setIsProcessing(true)

    try {
      await api.put(`/api/admin/reports/${selectedReport._id}/resolve`, { resolution })

      // Update report in the list
      setReports(
        reports.map((report) => (report._id === selectedReport._id ? { ...report, status: "resolved" } : report)),
      )

      setShowResolveDialog(false)
      setResolution("")
    } catch (err) {
      setError(err.message || "Failed to resolve report")
    } finally {
      setIsProcessing(false)
      setSelectedReport(null)
    }
  }

  const handleRejectReport = async () => {
    if (!selectedReport) return

    setIsProcessing(true)

    try {
      await api.put(`/api/admin/reports/${selectedReport._id}/reject`, {})

      // Update report in the list
      setReports(
        reports.map((report) => (report._id === selectedReport._id ? { ...report, status: "rejected" } : report)),
      )

      setShowRejectDialog(false)
    } catch (err) {
      setError(err.message || "Failed to reject report")
    } finally {
      setIsProcessing(false)
      setSelectedReport(null)
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getReportTypeBadge = (type) => {
    switch (type) {
      case "initiative":
        return <Badge className="bg-blue-600">مبادرة</Badge>
      case "comment":
        return <Badge className="bg-purple-600">تعليق</Badge>
      case "user":
        return <Badge className="bg-orange-600">مستخدم</Badge>
      case "post":
        return <Badge className="bg-indigo-600">منشور</Badge>
      default:
        return <Badge>{type}</Badge>
    }
  }

  const getReportStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-yellow-600">قيد المراجعة</Badge>
      case "resolved":
        return <Badge className="bg-green-600">تم الحل</Badge>
      case "rejected":
        return <Badge className="bg-red-600">مرفوض</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getReasonText = (reason) => {
    switch (reason) {
      case "inappropriate":
        return "محتوى غير لائق"
      case "spam":
        return "محتوى مزعج"
      case "offensive":
        return "محتوى مسيء"
      case "misleading":
        return "معلومات مضللة"
      case "other":
        return "سبب آخر"
      default:
        return reason
    }
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">إدارة البلاغات</h1>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>تصفية البلاغات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex gap-2 flex-1">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">قيد المراجعة</SelectItem>
                  <SelectItem value="resolved">تم الحل</SelectItem>
                  <SelectItem value="rejected">مرفوض</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={typeFilter}
                onValueChange={(value) => {
                  setTypeFilter(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="initiative">مبادرة</SelectItem>
                  <SelectItem value="comment">تعليق</SelectItem>
                  <SelectItem value="user">مستخدم</SelectItem>
                  <SelectItem value="post">منشور</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={sortBy}
                onValueChange={(value) => {
                  setSortBy(value)
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="الترتيب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-0">
          {isLoading && reports.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري تحميل البلاغات...</span>
            </div>
          ) : reports.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">لا توجد بلاغات مطابقة للمعايير المحددة</div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>النوع</TableHead>
                    <TableHead>السبب</TableHead>
                    <TableHead>المُبلغ</TableHead>
                    <TableHead>المحتوى المُبلغ عنه</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الإبلاغ</TableHead>
                    <TableHead className="text-left">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report._id}>
                      <TableCell>{getReportTypeBadge(report.type)}</TableCell>
                      <TableCell>{getReasonText(report.reason)}</TableCell>
                      <TableCell>
                        <Link href={`/users/${report.reporter._id}`} className="hover:underline">
                          {report.reporter.name}
                        </Link>
                      </TableCell>
                      <TableCell>
                        {report.relatedInitiative && (
                          <Link href={`/initiatives/${report.relatedInitiative._id}`} className="hover:underline">
                            {report.relatedInitiative.title}
                          </Link>
                        )}
                        {report.relatedUser && (
                          <Link href={`/users/${report.relatedUser._id}`} className="hover:underline">
                            {report.relatedUser.name}
                          </Link>
                        )}
                        {report.relatedComment && <span>تعليق #{report.relatedComment._id.substring(0, 8)}</span>}
                      </TableCell>
                      <TableCell>{getReportStatusBadge(report.status)}</TableCell>
                      <TableCell>{formatDate(report.createdAt)}</TableCell>
                      <TableCell className="text-left">
                        <div className="flex gap-2">
                          <Link href={`/admin/reports/${report._id}`}>
                            <Button variant="outline" size="sm">
                              عرض التفاصيل
                            </Button>
                          </Link>

                          {report.status === "pending" && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-green-600 border-green-600 hover:bg-green-50"
                                onClick={() => {
                                  setSelectedReport(report)
                                  setShowResolveDialog(true)
                                }}
                              >
                                حل
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 border-red-600 hover:bg-red-50"
                                onClick={() => {
                                  setSelectedReport(report)
                                  setShowRejectDialog(true)
                                }}
                              >
                                رفض
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center py-4">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronRight className="h-4 w-4" />
                      السابق
                    </Button>

                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    ))}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      التالي
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Resolve Report Dialog */}
      <Dialog open={showResolveDialog} onOpenChange={setShowResolveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حل البلاغ</DialogTitle>
            <DialogDescription>يرجى إدخال تفاصيل الإجراء المتخذ لحل هذا البلاغ.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="اكتب تفاصيل الإجراء المتخذ هنا..."
              value={resolution}
              onChange={(e) => setResolution(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowResolveDialog(false)}>
              إلغاء
            </Button>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handleResolveReport}
              disabled={isProcessing || !resolution.trim()}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <CheckCircle2 className="ml-2 h-4 w-4" />
                  حل البلاغ
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Report Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>رفض البلاغ</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في رفض هذا البلاغ؟ سيتم إخطار المُبلغ.</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleRejectReport} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <XCircle className="ml-2 h-4 w-4" />
                  رفض البلاغ
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


const fs = require('fs');
const path = require('path');

// Check if .npmrc exists and create it if not
const npmrcPath = path.join(process.cwd(), '.npmrc');
if (!fs.existsSync(npmrcPath)) {
  console.log('Creating .npmrc file to ensure npm is used...');
  fs.writeFileSync(npmrcPath, 'engine-strict=true\nuse-npm=true\n');
}

// Check if package-lock.json exists
const packageLockPath = path.join(process.cwd(), 'package-lock.json');
if (!fs.existsSync(packageLockPath)) {
  console.log('No package-lock.json found. Running npm install...');
  const { execSync } = require('child_process');
  try {
    execSync('npm install --legacy-peer-deps', { stdio: 'inherit' });
    console.log('npm install completed successfully.');
  } catch (error) {
    console.error('Error running npm install:', error.message);
    process.exit(1);
  }
}

console.log('npm configuration is set up correctly.');

import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Update, Initiative } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { ActivityService } from "../services/activity.service"

// Get updates by initiative
export const getUpdatesByInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiativeExists = await Initiative.exists({ _id: initiativeId })
    if (!initiativeExists) {
      return next(createError(404, "Initiative not found"))
    }

    // Get updates
    const updates = await Update.find({
      initiative: initiativeId,
      isPublished: true,
    })
      .populate("author", "name username avatar")
      .sort("-createdAt")

    res.status(200).json({
      success: true,
      count: updates.length,
      updates,
    })
  } catch (error) {
    next(error)
  }
}

// Create update
export const createUpdate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiative, title, content, images } = req.body
    const userId = req.user.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiative)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiativeDoc = await Initiative.findById(initiative)
    if (!initiativeDoc) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author of the initiative or an admin
    if (initiativeDoc.author.toString() !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to add updates to this initiative"))
    }

    // Create update
    const update = new Update({
      initiative,
      title,
      content,
      author: userId,
      images: images || [],
    })

    await update.save()

    // Populate author details
    await update.populate("author", "name username avatar")

    // Enregistrer l'activité
    await ActivityService.initiativeActivity(
      userId,
      "update",
      initiative,
      initiativeDoc.title,
      {
        updateType: "create",
        updateTitle: title,
        updateId: update._id.toString()
      }
    )

    // Create notifications for all supporters
    const supportersIds = initiativeDoc.supporters

    // Create notification for each supporter
    for (const supporterId of supportersIds) {
      // Skip notification for the author
      if (supporterId.toString() === userId) continue

      await createNotification({
        recipient: supporterId,
        sender: userId,
        type: "update",
        content: `New update for initiative "${initiativeDoc.title}": ${title}`,
        relatedInitiative: initiative,
        link: `/initiatives/${initiative}`,
      })
    }

    res.status(201).json({
      success: true,
      message: "Update created successfully",
      update,
    })
  } catch (error) {
    next(error)
  }
}

// Update an update
export const updateUpdate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { title, content, images, isPublished } = req.body
    const userId = req.user.id

    // Validate update ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid update ID"))
    }

    // Find update
    const updateDoc = await Update.findById(id)

    if (!updateDoc) {
      return next(createError(404, "Update not found"))
    }

    // Get initiative
    const initiative = await Initiative.findById(updateDoc.initiative)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author of the initiative/update or an admin
    if (
      updateDoc.author.toString() !== userId &&
      initiative.author.toString() !== userId &&
      req.user.role !== "admin"
    ) {
      return next(createError(403, "You are not authorized to update this update"))
    }

    // Update fields
    if (title) updateDoc.title = title
    if (content) updateDoc.content = content
    if (images) updateDoc.images = images
    if (isPublished !== undefined) updateDoc.isPublished = isPublished

    await updateDoc.save()

    // Populate author details
    await updateDoc.populate("author", "name username avatar")

    // Enregistrer l'activité
    await ActivityService.initiativeActivity(
      userId,
      "update",
      updateDoc.initiative.toString(),
      initiative.title,
      {
        updateType: "edit",
        updateTitle: updateDoc.title,
        updateId: updateDoc._id.toString(),
        updatedFields: Object.keys(req.body)
      }
    )

    res.status(200).json({
      success: true,
      message: "Update updated successfully",
      update: updateDoc,
    })
  } catch (error) {
    next(error)
  }
}

// Delete update
export const deleteUpdate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate update ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid update ID"))
    }

    // Find update
    const updateDoc = await Update.findById(id)

    if (!updateDoc) {
      return next(createError(404, "Update not found"))
    }

    // Get initiative
    const initiative = await Initiative.findById(updateDoc.initiative)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author of the initiative/update or an admin
    if (
      updateDoc.author.toString() !== userId &&
      initiative.author.toString() !== userId &&
      req.user.role !== "admin"
    ) {
      return next(createError(403, "You are not authorized to delete this update"))
    }

    // Enregistrer l'activité avant de supprimer la mise à jour
    await ActivityService.initiativeActivity(
      userId,
      "update",
      initiative._id.toString(),
      initiative.title,
      {
        updateType: "delete",
        updateTitle: updateDoc.title,
        updateId: updateDoc._id.toString()
      }
    )

    // Delete update
    await Update.findByIdAndDelete(id)

    res.status(200).json({
      success: true,
      message: "Update deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Get update by ID
export const getUpdateById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate update ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid update ID"))
    }

    // Find update
    const update = await Update.findById(id).populate("author", "name username avatar").populate("initiative", "title")

    if (!update) {
      return next(createError(404, "Update not found"))
    }

    // Check if update is published or user is authorized to view it
    if (!update.isPublished) {
      // If not published, only author, initiative author, or admin can view
      if (!req.user) {
        return next(createError(403, "You are not authorized to view this update"))
      }

      const initiative = await Initiative.findById(update.initiative)
      if (
        update.author.toString() !== req.user.id &&
        initiative.author.toString() !== req.user.id &&
        req.user.role !== "admin"
      ) {
        return next(createError(403, "You are not authorized to view this update"))
      }
    }

    res.status(200).json({
      success: true,
      update,
    })
  } catch (error) {
    next(error)
  }
}


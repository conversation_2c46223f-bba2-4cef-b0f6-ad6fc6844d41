import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { User, Initiative, Report, Category } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"

// Get admin dashboard stats
export const getAdminStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get total users count
    const totalUsers = await User.countDocuments()

    // Get new users today
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const newUsersToday = await User.countDocuments({ createdAt: { $gte: today } })

    // Get initiatives counts
    const totalInitiatives = await Initiative.countDocuments()
    const pendingInitiatives = await Initiative.countDocuments({ status: "pending" })

    // Get reports counts
    const totalReports = await Report.countDocuments()
    const pendingReports = await Report.countDocuments({ status: "pending" })

    // Get categories count
    const totalCategories = await Category.countDocuments()

    res.status(200).json({
      success: true,
      stats: {
        totalUsers,
        newUsersToday,
        totalInitiatives,
        pendingInitiatives,
        totalReports,
        pendingReports,
        totalCategories,
      },
    })
  } catch (error) {
    next(error)
  }
}

// Get pending initiatives
export const getPendingInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const limit = Number.parseInt(req.query.limit as string) || 10

    const initiatives = await Initiative.find({ status: "pending" })
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort("-createdAt")
      .limit(limit)

    res.status(200).json({
      success: true,
      initiatives,
    })
  } catch (error) {
    next(error)
  }
}

// Get recent users
export const getRecentUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const limit = Number.parseInt(req.query.limit as string) || 10

    const users = await User.find()
      .sort("-createdAt")
      .limit(limit)
      .select("name username email avatar isVerified joinDate")

    res.status(200).json({
      success: true,
      users,
    })
  } catch (error) {
    next(error)
  }
}

// Get all initiatives with pagination and filtering
export const getAllInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Build filter object
    const filter: any = {}

    // Status filter
    if (req.query.status) {
      filter.status = req.query.status
    }

    // Search filter
    if (req.query.search) {
      const searchTerm = req.query.search as string
      filter.$or = [
        { title: { $regex: searchTerm, $options: "i" } },
        { shortDescription: { $regex: searchTerm, $options: "i" } },
        { location: { $regex: searchTerm, $options: "i" } }
      ]
    }

    // Category filter
    if (req.query.category && req.query.category !== "all") {
      filter.category = req.query.category
    }

    // Get total count
    const total = await Initiative.countDocuments(filter)

    // Get initiatives
    const initiatives = await Initiative.find(filter)
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort(req.query.sort as string || "-createdAt")
      .skip(skip)
      .limit(limit)

    res.status(200).json({
      success: true,
      initiatives,
      pagination: {
        total,
        count: initiatives.length,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    next(error)
  }
}

// Get all users with pagination and filtering
export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Build filter object
    const filter: any = {}

    // Role filter
    if (req.query.role) {
      filter.role = req.query.role
    }

    // User type filter
    if (req.query.userType && req.query.userType !== "all") {
      filter.userType = req.query.userType
    }

    // Status filter
    if (req.query.isBlocked === "true") {
      filter.isBlocked = true
    } else if (req.query.isBlocked === "false") {
      filter.isBlocked = false
    }

    // Search filter
    if (req.query.search) {
      const searchTerm = req.query.search as string
      filter.$or = [
        { name: { $regex: searchTerm, $options: "i" } },
        { username: { $regex: searchTerm, $options: "i" } },
        { email: { $regex: searchTerm, $options: "i" } }
      ]
    }

    // Get total count
    const total = await User.countDocuments(filter)

    // Get users
    const users = await User.find(filter)
      .sort(req.query.sort as string || "-createdAt")
      .skip(skip)
      .limit(limit)
      .select("name username email avatar isVerified joinDate role userType isBlocked createdAt")

    // Get initiative counts for each user
    const usersWithInitiativeCounts = await Promise.all(
      users.map(async (user) => {
        const initiativeCount = await Initiative.countDocuments({ author: user._id })
        return {
          ...user.toObject(),
          initiativeCount
        }
      })
    )

    res.status(200).json({
      success: true,
      users: usersWithInitiativeCounts,
      pagination: {
        total,
        count: users.length,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    next(error)
  }
}

// Get all reports with pagination and filtering
export const getAllReports = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Build filter object
    const filter: any = {}

    // Status filter
    if (req.query.status) {
      filter.status = req.query.status
    }

    // Type filter
    if (req.query.type) {
      filter.type = req.query.type
    }

    // Get total count
    const total = await Report.countDocuments(filter)

    // Get reports
    const reports = await Report.find(filter)
      .sort(req.query.sort as string || "-createdAt")
      .skip(skip)
      .limit(limit)

    res.status(200).json({
      success: true,
      reports,
      pagination: {
        total,
        count: reports.length,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    next(error)
  }
}

// Get pending reports
export const getPendingReports = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const limit = Number.parseInt(req.query.limit as string) || 10

    const reports = await Report.find({ status: "pending" })
      .populate("reporter", "name username")
      .populate("relatedInitiative", "title")
      .populate("relatedUser", "name username")
      .sort("-createdAt")
      .limit(limit)

    res.status(200).json({
      success: true,
      reports,
    })
  } catch (error) {
    next(error)
  }
}

// Approve initiative
export const approveInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)

    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    if (initiative.status !== "pending") {
      return next(createError(400, "Initiative is not pending approval"))
    }

    // Update initiative status
    initiative.status = "active"
    await initiative.save()

    // Create notification for initiative author
    await createNotification({
      recipient: initiative.author,
      type: "system",
      content: `Your initiative "${initiative.title}" has been approved and is now active.`,
      relatedInitiative: initiative._id,
      link: `/initiatives/${initiative._id}`,
    })

    res.status(200).json({
      success: true,
      message: "Initiative approved successfully",
      initiative,
    })
  } catch (error) {
    next(error)
  }
}

// Reject initiative
export const rejectInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Find initiative
    const initiative = await Initiative.findById(id)

    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    if (initiative.status !== "pending") {
      return next(createError(400, "Initiative is not pending approval"))
    }

    // Update initiative status
    initiative.status = "rejected"
    await initiative.save()

    // Create notification for initiative author
    await createNotification({
      recipient: initiative.author,
      type: "system",
      content: `Your initiative "${initiative.title}" has been rejected. Please review our guidelines and consider submitting a revised version.`,
      relatedInitiative: initiative._id,
      link: `/initiatives/${initiative._id}`,
    })

    res.status(200).json({
      success: true,
      message: "Initiative rejected successfully",
      initiative,
    })
  } catch (error) {
    next(error)
  }
}

// Change user role
export const changeUserRole = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { role } = req.body

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Validate role
    const validRoles = ["user", "moderator", "admin"]
    if (!validRoles.includes(role)) {
      return next(createError(400, "Invalid role"))
    }

    // Find user
    const user = await User.findById(id)

    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Update user role
    user.role = role
    await user.save()

    res.status(200).json({
      success: true,
      message: "User role updated successfully",
      user: {
        _id: user._id,
        name: user.name,
        username: user.username,
        email: user.email,
        role: user.role,
        userType: user.userType,
      },
    })
  } catch (error) {
    next(error)
  }
}

// Change user type
export const changeUserType = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { userType } = req.body

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Validate user type
    const validUserTypes = ["volunteer", "proposer", "company"]
    if (!validUserTypes.includes(userType)) {
      return next(createError(400, "Invalid user type"))
    }

    // Find user
    const user = await User.findById(id)

    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Update user type
    user.userType = userType
    await user.save()

    res.status(200).json({
      success: true,
      message: "User type updated successfully",
      user: {
        _id: user._id,
        name: user.name,
        username: user.username,
        email: user.email,
        role: user.role,
        userType: user.userType,
      },
    })
  } catch (error) {
    next(error)
  }
}

// Block user
export const blockUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Find user
    const user = await User.findById(id)

    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Check if user is already blocked
    if (user.isBlocked) {
      return next(createError(400, "User is already blocked"))
    }

    // Block user
    user.isBlocked = true
    await user.save()

    res.status(200).json({
      success: true,
      message: "User blocked successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Unblock user
export const unblockUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Find user
    const user = await User.findById(id)

    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Check if user is already unblocked
    if (!user.isBlocked) {
      return next(createError(400, "User is not blocked"))
    }

    // Unblock user
    user.isBlocked = false
    await user.save()

    res.status(200).json({
      success: true,
      message: "User unblocked successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Verify user (activate account)
export const verifyUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Find user
    const user = await User.findById(id)

    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Check if user is already verified
    if (user.isVerified) {
      return next(createError(400, "User is already verified"))
    }

    // Verify user
    user.isVerified = true
    await user.save()

    // Create notification for the user
    await createNotification({
      recipient: user._id,
      type: "system",
      content: "Your account has been verified by an administrator.",
      link: "/profile",
    })

    res.status(200).json({
      success: true,
      message: "User verified successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Resolve report
export const resolveReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { resolution } = req.body

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid report ID"))
    }

    // Validate resolution
    if (!resolution || !resolution.trim()) {
      return next(createError(400, "Resolution is required"))
    }

    // Find report
    const report = await Report.findById(id)

    if (!report) {
      return next(createError(404, "Report not found"))
    }

    if (report.status !== "pending") {
      return next(createError(400, "Report is not pending"))
    }

    // Update report
    report.status = "resolved"
    report.resolution = resolution
    report.reviewedBy = req.user.id
    report.reviewedAt = new Date()
    await report.save()

    // Notify reporter
    await createNotification({
      recipient: report.reporter,
      type: "system",
      content: `Your report has been reviewed and resolved.`,
      link: `/notifications`,
    })

    res.status(200).json({
      success: true,
      message: "Report resolved successfully",
      report,
    })
  } catch (error) {
    next(error)
  }
}

// Reject report
export const rejectReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid report ID"))
    }

    // Find report
    const report = await Report.findById(id)

    if (!report) {
      return next(createError(404, "Report not found"))
    }

    if (report.status !== "pending") {
      return next(createError(400, "Report is not pending"))
    }

    // Update report
    report.status = "rejected"
    report.reviewedBy = req.user.id
    report.reviewedAt = new Date()
    await report.save()

    // Notify reporter
    await createNotification({
      recipient: report.reporter,
      type: "system",
      content: `Your report has been reviewed and no action was deemed necessary.`,
      link: `/notifications`,
    })

    res.status(200).json({
      success: true,
      message: "Report rejected successfully",
      report,
    })
  } catch (error) {
    next(error)
  }
}

// Get report details
export const getReportDetails = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid report ID"))
    }

    // Find report with populated fields
    const report = await Report.findById(id)
      .populate("reporter", "name username avatar")
      .populate("relatedInitiative", "title shortDescription author")
      .populate({
        path: "relatedInitiative",
        populate: { path: "author", select: "name" },
      })
      .populate("relatedComment", "content author initiative")
      .populate({
        path: "relatedComment",
        populate: [
          { path: "author", select: "name" },
          { path: "initiative", select: "title" },
        ],
      })
      .populate("relatedUser", "name username avatar email")
      .populate("relatedPost", "content author")
      .populate({
        path: "relatedPost",
        populate: { path: "author", select: "name" },
      })
      .populate("reviewedBy", "name")

    if (!report) {
      return next(createError(404, "Report not found"))
    }

    res.status(200).json({
      success: true,
      report,
    })
  } catch (error) {
    next(error)
  }
}


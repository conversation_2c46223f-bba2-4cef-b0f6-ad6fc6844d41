import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString: string): string {
  if (!dateString) return ""

  const date = new Date(dateString)

  // Check if date is valid
  if (isNaN(date.getTime())) return ""

  // Format options
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
  }

  // Format date in Arabic locale
  return new Intl.DateTimeFormat("ar-SA", options).format(date)
}

export function getSkillLevelText(level: number): string {
  switch (level) {
    case 1:
      return "مبتدئ"
    case 2:
      return "متوسط"
    case 3:
      return "متقدم"
    case 4:
      return "خبير"
    case 5:
      return "محترف"
    default:
      return "غير محدد"
  }
}

export function getSkillLevelColor(level: number): string {
  switch (level) {
    case 1:
      return "bg-blue-100 text-blue-800"
    case 2:
      return "bg-green-100 text-green-800"
    case 3:
      return "bg-yellow-100 text-yellow-800"
    case 4:
      return "bg-orange-100 text-orange-800"
    case 5:
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

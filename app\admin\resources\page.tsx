"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../../../components/ui/tabs"
import { Badge } from "../../../components/ui/badge"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import {
  Loader2,
  AlertCircle,
  Package,
  DollarSign,
  Users,
  Wrench,
  HelpCircle,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  Ban,
  Search,
  Filter,
  ArrowUpDown,
  Eye,
  BarChart
} from "lucide-react"
import { Input } from "../../../components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table"
import { api } from "../../../lib/api"
import { toast } from "../../../components/ui/use-toast"
import { Toaster } from "../../../components/ui/toaster"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "../../../components/ui/pagination"

interface Resource {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  status: "requested" | "approved" | "rejected" | "delivered" | "canceled"
  requestedDate: string
  approvedDate?: string
  rejectedDate?: string
  deliveredDate?: string
  notes?: string
  provider: {
    _id: string
    name: string
    username: string
    avatar: string
    userType: string
  }
  initiative: {
    _id: string
    title: string
    shortDescription: string
    mainImage: string
    status: string
  }
}

interface ResourceNeed {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  priority: "low" | "medium" | "high" | "critical"
  status: "open" | "in_progress" | "fulfilled" | "canceled"
  createdDate: string
  fulfilledDate?: string
  notes?: string
  initiative: {
    _id: string
    title: string
    shortDescription: string
    mainImage: string
    status: string
  }
}

export default function AdminResourcesPage() {
  const [activeTab, setActiveTab] = useState("resources")
  const [resources, setResources] = useState<Resource[]>([])
  const [resourceNeeds, setResourceNeeds] = useState<ResourceNeed[]>([])
  const [filteredResources, setFilteredResources] = useState<Resource[]>([])
  const [filteredNeeds, setFilteredNeeds] = useState<ResourceNeed[]>([])
  const [isLoadingResources, setIsLoadingResources] = useState(true)
  const [isLoadingNeeds, setIsLoadingNeeds] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Pagination
  const [resourcesPage, setResourcesPage] = useState(1)
  const [needsPage, setNeedsPage] = useState(1)
  const [resourcesPerPage] = useState(10)
  const [needsPerPage] = useState(10)

  // Filters
  const [resourceSearchQuery, setResourceSearchQuery] = useState("")
  const [resourceTypeFilter, setResourceTypeFilter] = useState("all")
  const [resourceStatusFilter, setResourceStatusFilter] = useState("all")
  const [resourceSortBy, setResourceSortBy] = useState("date")

  const [needSearchQuery, setNeedSearchQuery] = useState("")
  const [needTypeFilter, setNeedTypeFilter] = useState("all")
  const [needPriorityFilter, setNeedPriorityFilter] = useState("all")
  const [needStatusFilter, setNeedStatusFilter] = useState("all")
  const [needSortBy, setNeedSortBy] = useState("priority")

  useEffect(() => {
    fetchResources()
  }, [])

  useEffect(() => {
    fetchResourceNeeds()
  }, [])

  // Apply filters when filter parameters change
  useEffect(() => {
    if (resources.length > 0) {
      applyResourceFilters(resources)
    }
  }, [resourceSearchQuery, resourceTypeFilter, resourceStatusFilter, resourceSortBy, resources])

  useEffect(() => {
    if (resourceNeeds.length > 0) {
      applyNeedFilters(resourceNeeds)
    }
  }, [needSearchQuery, needTypeFilter, needPriorityFilter, needStatusFilter, needSortBy, resourceNeeds])

  const fetchResources = async () => {
    setIsLoadingResources(true)
    try {
      // For now, just fetch all resources without pagination
      const response = await api.get("/api/resources", true)

      if (response.success) {
        setResources(response.resources || [])
        // Apply filters client-side for now
        applyResourceFilters(response.resources || [])
      } else {
        setError("Failed to fetch resources")
      }
    } catch (err: any) {
      console.error("Error fetching resources:", err)
      setError(err.message || "An error occurred while fetching resources")
    } finally {
      setIsLoadingResources(false)
    }
  }

  const fetchResourceNeeds = async () => {
    setIsLoadingNeeds(true)
    try {
      // For now, just use mock data
      const mockNeeds = [
        {
          _id: "need1",
          type: "material",
          name: "Building Materials",
          description: "Need construction materials for community center",
          quantity: 100,
          unit: "kg",
          priority: "high",
          status: "open",
          createdDate: new Date().toISOString(),
          initiative: {
            _id: "init1",
            title: "Community Center",
            shortDescription: "Building a community center",
            mainImage: "/images/placeholder.jpg",
            status: "active"
          }
        },
        {
          _id: "need2",
          type: "human",
          name: "Volunteers",
          description: "Need volunteers for cleanup event",
          quantity: 20,
          unit: "people",
          priority: "medium",
          status: "open",
          createdDate: new Date().toISOString(),
          initiative: {
            _id: "init2",
            title: "Beach Cleanup",
            shortDescription: "Cleaning up the local beach",
            mainImage: "/images/placeholder.jpg",
            status: "active"
          }
        }
      ];

      setResourceNeeds(mockNeeds);
      // Apply filters client-side
      applyNeedFilters(mockNeeds);

    } catch (err: any) {
      console.error("Error fetching resource needs:", err)
      setError(err.message || "An error occurred while fetching resource needs")
    } finally {
      setIsLoadingNeeds(false)
    }
  }

  const applyResourceFilters = (resources) => {
    let filtered = [...resources]

    // Apply search filter
    if (resourceSearchQuery) {
      const query = resourceSearchQuery.toLowerCase()
      filtered = filtered.filter(resource =>
        resource.name.toLowerCase().includes(query) ||
        resource.description.toLowerCase().includes(query) ||
        (resource.provider && resource.provider.name && resource.provider.name.toLowerCase().includes(query)) ||
        (resource.initiative && resource.initiative.title && resource.initiative.title.toLowerCase().includes(query)) ||
        (resource.notes && resource.notes.toLowerCase().includes(query))
      )
    }

    // Apply type filter
    if (resourceTypeFilter !== "all") {
      filtered = filtered.filter(resource => resource.type === resourceTypeFilter)
    }

    // Apply status filter
    if (resourceStatusFilter !== "all") {
      filtered = filtered.filter(resource => resource.status === resourceStatusFilter)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (resourceSortBy) {
        case "date":
          return new Date(b.requestedDate || 0).getTime() - new Date(a.requestedDate || 0).getTime()
        case "name":
          return (a.name || '').localeCompare(b.name || '')
        case "quantity":
          return (b.quantity || 0) - (a.quantity || 0)
        case "provider":
          return a.provider && b.provider ? (a.provider.name || '').localeCompare(b.provider.name || '') : 0
        case "initiative":
          return a.initiative && b.initiative ? (a.initiative.title || '').localeCompare(b.initiative.title || '') : 0
        default:
          return 0
      }
    })

    setFilteredResources(filtered)
  }

  const applyNeedFilters = (needs) => {
    let filtered = [...needs]

    // Apply search filter
    if (needSearchQuery) {
      const query = needSearchQuery.toLowerCase()
      filtered = filtered.filter(need =>
        (need.name && need.name.toLowerCase().includes(query)) ||
        (need.description && need.description.toLowerCase().includes(query)) ||
        (need.initiative && need.initiative.title && need.initiative.title.toLowerCase().includes(query)) ||
        (need.notes && need.notes.toLowerCase().includes(query))
      )
    }

    // Apply type filter
    if (needTypeFilter !== "all") {
      filtered = filtered.filter(need => need.type === needTypeFilter)
    }

    // Apply priority filter
    if (needPriorityFilter !== "all") {
      filtered = filtered.filter(need => need.priority === needPriorityFilter)
    }

    // Apply status filter
    if (needStatusFilter !== "all") {
      filtered = filtered.filter(need => need.status === needStatusFilter)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (needSortBy) {
        case "priority":
          return getPriorityValue(b.priority || 'low') - getPriorityValue(a.priority || 'low')
        case "date":
          return new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime()
        case "name":
          return (a.name || '').localeCompare(b.name || '')
        case "quantity":
          return (b.quantity || 0) - (a.quantity || 0)
        case "initiative":
          return a.initiative && b.initiative ? (a.initiative.title || '').localeCompare(b.initiative.title || '') : 0
        default:
          return 0
      }
    })

    setFilteredNeeds(filtered)
  }

  const getPriorityValue = (priority: string) => {
    switch (priority) {
      case "critical": return 4
      case "high": return 3
      case "medium": return 2
      case "low": return 1
      default: return 0
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "مادي"
      case "financial":
        return "مالي"
      case "human":
        return "بشري"
      case "service":
        return "خدمي"
      default:
        return "أخرى"
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "requested":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مطلوب</Badge>
      case "approved":
        return <Badge className="bg-green-100 text-green-800 border-green-200">موافق عليه</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 border-red-200">مرفوض</Badge>
      case "delivered":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">تم التسليم</Badge>
      case "canceled":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">ملغي</Badge>
      case "open":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مفتوح</Badge>
      case "in_progress":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">قيد التنفيذ</Badge>
      case "fulfilled":
        return <Badge className="bg-green-100 text-green-800 border-green-200">تم تلبيته</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "low":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Low</Badge>
      case "medium":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Medium</Badge>
      case "high":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">High</Badge>
      case "critical":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Critical</Badge>
      default:
        return <Badge>{priority}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Get paginated data
  const paginatedResources = filteredResources.slice(
    (resourcesPage - 1) * resourcesPerPage,
    resourcesPage * resourcesPerPage
  )

  const paginatedNeeds = filteredNeeds.slice(
    (needsPage - 1) * needsPerPage,
    needsPage * needsPerPage
  )

  // We now calculate total pages directly in the pagination components

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">إدارة الموارد</h1>
          <p className="text-gray-500">إدارة الموارد واحتياجات الموارد عبر المنصة</p>
        </div>

        <div className="flex gap-2">
          <Link href="/stats/resources">
            <Button variant="outline" className="flex items-center gap-2">
              <BarChart className="h-4 w-4" />
              إحصائيات الموارد
            </Button>
          </Link>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full mb-6">
          <TabsTrigger value="resources">عروض الموارد</TabsTrigger>
          <TabsTrigger value="needs">احتياجات الموارد</TabsTrigger>
        </TabsList>

        <TabsContent value="resources">
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="البحث عن الموارد..."
                    value={resourceSearchQuery}
                    onChange={(e) => setResourceSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="نوع المورد" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="material">مادي</SelectItem>
                    <SelectItem value="financial">مالي</SelectItem>
                    <SelectItem value="human">بشري</SelectItem>
                    <SelectItem value="service">خدمي</SelectItem>
                    <SelectItem value="other">أخرى</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={resourceStatusFilter} onValueChange={setResourceStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="requested">مطلوب</SelectItem>
                    <SelectItem value="approved">موافق عليه</SelectItem>
                    <SelectItem value="rejected">مرفوض</SelectItem>
                    <SelectItem value="delivered">تم التسليم</SelectItem>
                    <SelectItem value="canceled">ملغي</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={resourceSortBy} onValueChange={setResourceSortBy}>
                  <SelectTrigger>
                    <SelectValue placeholder="ترتيب حسب" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">التاريخ</SelectItem>
                    <SelectItem value="name">الاسم</SelectItem>
                    <SelectItem value="quantity">الكمية</SelectItem>
                    <SelectItem value="provider">المزود</SelectItem>
                    <SelectItem value="initiative">المبادرة</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {isLoadingResources ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري تحميل الموارد...</span>
            </div>
          ) : filteredResources.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>لم يتم العثور على موارد تطابق عوامل التصفية.</p>
              </CardContent>
            </Card>
          ) : (
            <>
              <Card>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>المورد</TableHead>
                        <TableHead>النوع</TableHead>
                        <TableHead>الكمية</TableHead>
                        <TableHead>الحالة</TableHead>
                        <TableHead>المزود</TableHead>
                        <TableHead>المبادرة</TableHead>
                        <TableHead>التاريخ</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedResources.map((resource) => (
                        <TableRow key={resource._id}>
                          <TableCell className="font-medium">{resource.name}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {getTypeIcon(resource.type)}
                              <span>{getTypeText(resource.type)}</span>
                            </div>
                          </TableCell>
                          <TableCell>{resource.quantity} {resource.unit}</TableCell>
                          <TableCell>{getStatusBadge(resource.status)}</TableCell>
                          <TableCell>
                            <Link href={`/admin/users/${resource.provider._id}`} className="hover:underline">
                              {resource.provider.name}
                            </Link>
                          </TableCell>
                          <TableCell>
                            <Link href={`/admin/initiatives/${resource.initiative._id}`} className="hover:underline">
                              {resource.initiative.title}
                            </Link>
                          </TableCell>
                          <TableCell>{formatDate(resource.requestedDate)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Link href={`/initiatives/${resource.initiative._id}/resources`}>
                                <Button variant="ghost" size="icon">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {filteredResources.length > resourcesPerPage && (
                <div className="flex justify-center mt-6">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setResourcesPage(p => Math.max(1, p - 1))}
                          disabled={resourcesPage === 1}
                        />
                      </PaginationItem>

                      {Array.from({ length: Math.ceil(filteredResources.length / resourcesPerPage) }).map((_, i) => (
                        <PaginationItem key={i}>
                          <PaginationLink
                            onClick={() => setResourcesPage(i + 1)}
                            isActive={resourcesPage === i + 1}
                          >
                            {i + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setResourcesPage(p => Math.min(Math.ceil(filteredResources.length / resourcesPerPage), p + 1))}
                          disabled={resourcesPage === Math.ceil(filteredResources.length / resourcesPerPage)}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="needs">
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="البحث عن احتياجات الموارد..."
                    value={needSearchQuery}
                    onChange={(e) => setNeedSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={needTypeFilter} onValueChange={setNeedTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="نوع المورد" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="material">مادي</SelectItem>
                    <SelectItem value="financial">مالي</SelectItem>
                    <SelectItem value="human">بشري</SelectItem>
                    <SelectItem value="service">خدمي</SelectItem>
                    <SelectItem value="other">أخرى</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={needPriorityFilter} onValueChange={setNeedPriorityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="الأولوية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأولويات</SelectItem>
                    <SelectItem value="critical">حرجة</SelectItem>
                    <SelectItem value="high">عالية</SelectItem>
                    <SelectItem value="medium">متوسطة</SelectItem>
                    <SelectItem value="low">منخفضة</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={needStatusFilter} onValueChange={setNeedStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="open">مفتوح</SelectItem>
                    <SelectItem value="in_progress">قيد التنفيذ</SelectItem>
                    <SelectItem value="fulfilled">تم تلبيته</SelectItem>
                    <SelectItem value="canceled">ملغي</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={needSortBy} onValueChange={setNeedSortBy}>
                  <SelectTrigger>
                    <SelectValue placeholder="ترتيب حسب" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="priority">الأولوية</SelectItem>
                    <SelectItem value="date">التاريخ</SelectItem>
                    <SelectItem value="name">الاسم</SelectItem>
                    <SelectItem value="quantity">الكمية</SelectItem>
                    <SelectItem value="initiative">المبادرة</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {isLoadingNeeds ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="mr-2">جاري تحميل احتياجات الموارد...</span>
            </div>
          ) : filteredNeeds.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>لم يتم العثور على احتياجات موارد تطابق عوامل التصفية.</p>
              </CardContent>
            </Card>
          ) : (
            <>
              <Card>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>احتياج المورد</TableHead>
                        <TableHead>النوع</TableHead>
                        <TableHead>الكمية</TableHead>
                        <TableHead>الأولوية</TableHead>
                        <TableHead>الحالة</TableHead>
                        <TableHead>المبادرة</TableHead>
                        <TableHead>التاريخ</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedNeeds.map((need) => (
                        <TableRow key={need._id}>
                          <TableCell className="font-medium">{need.name}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {getTypeIcon(need.type)}
                              <span>{getTypeText(need.type)}</span>
                            </div>
                          </TableCell>
                          <TableCell>{need.quantity} {need.unit}</TableCell>
                          <TableCell>{getPriorityBadge(need.priority)}</TableCell>
                          <TableCell>{getStatusBadge(need.status)}</TableCell>
                          <TableCell>
                            <Link href={`/admin/initiatives/${need.initiative._id}`} className="hover:underline">
                              {need.initiative.title}
                            </Link>
                          </TableCell>
                          <TableCell>{formatDate(need.createdDate)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Link href={`/initiatives/${need.initiative._id}/resources`}>
                                <Button variant="ghost" size="icon">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </Link>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {filteredNeeds.length > needsPerPage && (
                <div className="flex justify-center mt-6">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setNeedsPage(p => Math.max(1, p - 1))}
                          disabled={needsPage === 1}
                        />
                      </PaginationItem>

                      {Array.from({ length: Math.ceil(filteredNeeds.length / needsPerPage) }).map((_, i) => (
                        <PaginationItem key={i}>
                          <PaginationLink
                            onClick={() => setNeedsPage(i + 1)}
                            isActive={needsPage === i + 1}
                          >
                            {i + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setNeedsPage(p => Math.min(Math.ceil(filteredNeeds.length / needsPerPage), p + 1))}
                          disabled={needsPage === Math.ceil(filteredNeeds.length / needsPerPage)}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>

      <Toaster />
    </div>
  )
}

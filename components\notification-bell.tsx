"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Bell } from "lucide-react"
import { But<PERSON> } from "./ui/button"
import { api } from "../lib/api"
import { useAuth } from "./auth-provider"

export default function NotificationBell() {
  const { isAuthenticated, user } = useAuth()
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    if (isAuthenticated && user) {
      const fetchUnreadCount = async () => {
        try {
          const response = await api.get('/api/notifications/unread-count', true)
          if (response && response.count !== undefined) {
            setUnreadCount(response.count)
          }
        } catch (error) {
          console.error('Error fetching unread notifications:', error)
        }
      }

      fetchUnreadCount()

      // Set up interval to refresh count (every 60 seconds)
      const interval = setInterval(fetchUnreadCount, 60000)

      return () => clearInterval(interval)
    }
  }, [isAuthenticated, user])

  if (!isAuthenticated) return null

  return (
    <Link href="/notifications">
      <Button variant="ghost" size="icon" className="relative">
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </Button>
    </Link>
  )
}

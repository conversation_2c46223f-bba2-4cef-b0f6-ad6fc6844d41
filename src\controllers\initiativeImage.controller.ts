import { Request, Response, NextFunction } from "express"
import { Initiative } from "../models"
import { createError } from "../utils/error"
import multer from "multer"
import path from "path"
import fs from "fs"
import { v4 as uuidv4 } from "uuid"

// Create upload directory if it doesn't exist
const uploadDir = path.join(process.cwd(), "public/uploads/initiatives")
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

// Configure multer for file storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // Generate unique filename with extension
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`
    cb(null, uniqueFilename)
  }
})

// Filter to only accept image files
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.mimetype.startsWith("image/")) {
    cb(null, true)
  } else {
    cb(createError(400, "Only image files are allowed"))
  }
}

// Configure upload
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB max
  }
})

// Middleware to handle image upload
export const uploadInitiativeImage = (req: Request, res: Response, next: NextFunction) => {
  // Handle initiative image upload

  const uploadSingle = upload.single("image")

  uploadSingle(req, res, (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === "LIMIT_FILE_SIZE") {
          return next(createError(400, "File size too large. Maximum size is 5MB"))
        } else if (err.code === "LIMIT_UNEXPECTED_FILE") {
          return next(createError(400, "Unexpected field. Expected 'image'"))
        }
      }
      return next(createError(400, `File upload error: ${err.message}`))
    }

    if (!req.file) {
      return next(createError(400, "No file uploaded"))
    }
    next()
  })
}

// Controller to update initiative image
export const updateInitiativeImage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params

    // Check if user is authorized to update this initiative
    if (!req.user) {
      return next(createError(401, "Authentication required"))
    }

    // Find the initiative
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author or an admin
    if (req.user.id !== initiative.author.toString() && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to update this initiative"))
    }

    // Check if file was uploaded
    if (!req.file) {
      return next(createError(400, "No file uploaded"))
    }

    // Delete old image if it exists and is not a default image
    if (initiative.mainImage &&
        !initiative.mainImage.includes("default-initiative") &&
        !initiative.mainImage.includes("placeholder")) {
      try {
        const oldImagePath = path.join(process.cwd(), "public", initiative.mainImage)

        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath)
        }
      } catch (error) {
        // Continue even if old image deletion fails
      }
    }

    // Update initiative with new image URL
    const imageUrl = `/uploads/initiatives/${req.file.filename}`

    initiative.mainImage = imageUrl
    await initiative.save()

    res.status(200).json({
      success: true,
      message: "Initiative image updated successfully",
      file: {
        url: imageUrl,
        filename: req.file.filename,
        mimetype: req.file.mimetype,
        size: req.file.size
      }
    })
  } catch (error) {
    console.error("Error in updateInitiativeImage:", error)
    next(error)
  }
}

// Controller to add additional images to an initiative
export const addInitiativeImages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Add initiative images controller
    const { initiativeId } = req.params

    // Check if user is authorized
    if (!req.user) {
      return next(createError(401, "Authentication required"))
    }

    // Find the initiative
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author or an admin
    if (req.user.id !== initiative.author.toString() && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to update this initiative"))
    }

    // Check if file was uploaded
    if (!req.file) {
      return next(createError(400, "No file uploaded"))
    }

    // Add new image to initiative's images array
    const imageUrl = `/uploads/initiatives/${req.file.filename}`

    if (!initiative.images) {
      initiative.images = []
    }

    initiative.images.push(imageUrl)
    await initiative.save()

    res.status(200).json({
      success: true,
      message: "Initiative image added successfully",
      file: {
        url: imageUrl,
        filename: req.file.filename,
        mimetype: req.file.mimetype,
        size: req.file.size
      }
    })
  } catch (error) {
    console.error("Error in addInitiativeImages:", error)
    next(error)
  }
}

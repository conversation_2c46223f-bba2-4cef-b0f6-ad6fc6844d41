import mongoose, { Schema, Document } from "mongoose"

export interface IResourceNeed extends Document {
  initiative: mongoose.Types.ObjectId
  type: string
  name: string
  description: string
  quantity: number
  unit: string
  priority: string
  status: string
  createdDate: Date
  fulfilledDate?: Date
  notes?: string
}

const resourceNeedSchema = new Schema(
  {
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    type: {
      type: String,
      enum: ["material", "financial", "human", "service", "other"],
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
      min: 0,
    },
    unit: {
      type: String,
      required: true,
    },
    priority: {
      type: String,
      enum: ["low", "medium", "high", "critical"],
      default: "medium",
    },
    status: {
      type: String,
      enum: ["open", "in_progress", "fulfilled", "canceled"],
      default: "open",
    },
    createdDate: {
      type: Date,
      default: Date.now,
    },
    fulfilledDate: {
      type: Date,
    },
    notes: {
      type: String,
    },
  },
  { timestamps: true }
)

// Create indexes for better query performance
resourceNeedSchema.index({ initiative: 1, status: 1 })
resourceNeedSchema.index({ initiative: 1, priority: 1 })

// Check if the model already exists to prevent overwriting
const ResourceNeed = mongoose.models.ResourceNeed || mongoose.model<IResourceNeed>("ResourceNeed", resourceNeedSchema)

export default ResourceNeed

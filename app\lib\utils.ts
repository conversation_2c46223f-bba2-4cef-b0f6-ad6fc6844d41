import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString: string): string {
  if (!dateString) return ""
  
  const date = new Date(dateString)
  
  // Check if date is valid
  if (isNaN(date.getTime())) return ""
  
  // Format date in Arabic locale
  return date.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function getSkillLevelText(level: string): string {
  switch (level) {
    case 'beginner': return 'مبتدئ'
    case 'intermediate': return 'متوسط'
    case 'advanced': return 'متقدم'
    case 'expert': return 'خبير'
    default: return level
  }
}

export function getSkillLevelColor(level: string): string {
  switch (level) {
    case 'beginner': return '#4CAF50'
    case 'intermediate': return '#2196F3'
    case 'advanced': return '#FF9800'
    case 'expert': return '#F44336'
    default: return '#9E9E9E'
  }
}

export function truncateText(text: string, maxLength: number): string {
  if (!text) return ""
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + "..."
}

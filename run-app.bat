@echo off
echo Starting Initiatives DZ Application...

REM Check if MongoDB is running
echo Checking MongoDB connection...
node scripts/check-mongodb.js
if %errorlevel% neq 0 (
    echo MongoDB connection failed. Please make sure MongoDB is running.
    exit /b 1
)

REM Ensure npm is used and install dependencies if needed
echo Checking npm configuration...
node scripts/use-npm.js
if %errorlevel% neq 0 (
    echo Failed to configure npm.
    exit /b 1
)

REM Setup database if needed
echo Setting up database...
npm run setup-db

REM Start the application
echo Starting application with full functionality...
npm run dev:transpile

echo Application is running!

import mongoose from "mongoose"
import userSchema from "./user.model"
import initiativeSchema from "./initiative.model"
import commentSchema from "./comment.model"
import categorySchema from "./category.model"
import badgeSchema from "./badge.model"
import badgeAwardSchema from "./badge-award.model"
import votingOptionSchema from "./votingOption.model"
import Update from "./update.model"
import Milestone from "./milestone.model"
import Support from "./support.model"
import postSchema from "./post.model"
import postCommentSchema from "./postComment.model"
import notificationSchema from "./notification.model"
import reportSchema from "./report.model"
import settingsSchema from "./settings.model"
import { Banner } from "./banner.model"
import { Role } from "./role.model"
import { Permission } from "./permission.model"
import Resource from "./resource.model"
import ResourceNeed from "./resourceNeed.model"
import Invitation from "./invitation.model"
import { SocialImpact } from "./social-impact.model"
import { Activity } from "./activity.model"

// Create and export models
export const User = mongoose.models.User || mongoose.model("User", userSchema)
export const Badge = mongoose.models.Badge || mongoose.model("Badge", badgeSchema)
export const BadgeAward = mongoose.models.BadgeAward || mongoose.model("BadgeAward", badgeAwardSchema)
export const Category = mongoose.models.Category || mongoose.model("Category", categorySchema)
export const Initiative = mongoose.models.Initiative || mongoose.model("Initiative", initiativeSchema)
export const VotingOption = mongoose.models.VotingOption || mongoose.model("VotingOption", votingOptionSchema)
export const Comment = mongoose.models.Comment || mongoose.model("Comment", commentSchema)
// Update is already exported from its own file
export { Update }
// Milestone is already exported from its own file
export { Milestone }
// Support is already exported from its own file
export { Support }
export const Post = mongoose.models.Post || mongoose.model("Post", postSchema)
export const PostComment = mongoose.models.PostComment || mongoose.model("PostComment", postCommentSchema)
export const Notification = mongoose.models.Notification || mongoose.model("Notification", notificationSchema)
export const Report = mongoose.models.Report || mongoose.model("Report", reportSchema)
export const Settings = mongoose.models.Settings || mongoose.model("Settings", settingsSchema)
// Banner is already exported from its own file
export { Banner }
// Role is already exported from its own file
export { Role }
// Permission is already exported from its own file
export { Permission }
// Resource is already exported from its own file
export { Resource }
// ResourceNeed is already exported from its own file
export { ResourceNeed }
// Invitation is already exported from its own file
export { Invitation }
// SocialImpact is already exported from its own file
export { SocialImpact }
// Activity is already exported from its own file
export { Activity }

export default {
  User,
  Badge,
  BadgeAward,
  Category,
  Initiative,
  VotingOption,
  Comment,
  Update,
  Milestone,
  Support,
  Post,
  PostComment,
  Notification,
  Report,
  Settings,
  Banner,
  Role,
  Permission,
  Resource,
  ResourceNeed,
  Invitation,
  SocialImpact,
  Activity,
}


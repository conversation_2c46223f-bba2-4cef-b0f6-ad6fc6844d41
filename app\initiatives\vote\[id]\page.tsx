"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ChevronLeft, CheckCircle2 } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

// Mock data for the initiative
const initiativeData = {
  id: "1",
  title: "حملة تشجير الأحياء السكنية",
  category: "بيئة",
  description: "مبادرة لزراعة الأشجار في الأحياء السكنية لتحسين البيئة المحلية وزيادة المساحات الخضراء.",
  author: "أحمد مصطفى",
  authorImage: "/placeholder.svg?height=100&width=100",
  location: "الجزائر العاصمة",
  date: "15 مارس 2024",
  supporters: 124,
  goal: 200,
  votingEndDate: "30 أبريل 2024",
  image: "/placeholder.svg?height=600&width=1200",
  votingOptions: [
    {
      id: "1",
      title: "دعم المبادرة كما هي",
      description: "أؤيد المبادرة بشكلها الحالي وأرى أنها ستحقق فائدة كبيرة للمجتمع.",
      votes: 98,
    },
    {
      id: "2",
      title: "دعم المبادرة مع اقتراح تعديلات",
      description: "أؤيد الفكرة العامة للمبادرة ولكن أقترح بعض التعديلات لتحسينها.",
      votes: 45,
    },
    {
      id: "3",
      title: "عدم دعم المبادرة",
      description: "لا أؤيد هذه المبادرة للأسباب التي سأذكرها في التعليقات.",
      votes: 12,
    },
  ],
}

export default function VotePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [selectedOption, setSelectedOption] = useState<string | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [isVoted, setIsVoted] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)

  const totalVotes = initiativeData.votingOptions.reduce((sum, option) => sum + option.votes, 0)

  const handleVote = () => {
    if (selectedOption) {
      setShowConfirmDialog(true)
    }
  }

  const confirmVote = () => {
    // In a real app, this would submit the vote to the server
    setShowConfirmDialog(false)
    setIsVoted(true)
    setShowSuccessDialog(true)
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="max-w-4xl mx-auto p-4 md:p-8">
        <Button
          variant="ghost"
          className="mb-4 flex items-center gap-2 text-[#0a8754]"
          onClick={() => router.push(`/initiatives/${params.id}`)}
        >
          <ChevronLeft size={16} />
          العودة إلى صفحة المبادرة
        </Button>

        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="relative h-[200px]">
            <img
              src={initiativeData.image || "/placeholder.svg"}
              alt={initiativeData.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <h1 className="text-white text-2xl md:text-3xl font-bold px-4 text-center">التصويت على المبادرة</h1>
            </div>
          </div>

          <div className="p-6">
            <h2 className="text-xl md:text-2xl font-bold text-[#0a8754] mb-4">{initiativeData.title}</h2>

            <div className="flex items-center gap-3 mb-6 pb-6 border-b border-gray-200">
              <Avatar>
                <AvatarImage src={initiativeData.authorImage} alt={initiativeData.author} />
                <AvatarFallback>{initiativeData.author.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{initiativeData.author}</p>
                <p className="text-sm text-gray-500">صاحب المبادرة</p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 mb-4">{initiativeData.description}</p>
              <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                <span>الموقع: {initiativeData.location}</span>
                <span>تاريخ النشر: {initiativeData.date}</span>
                <span>انتهاء التصويت: {initiativeData.votingEndDate}</span>
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">إحصائيات التصويت</h3>
              <div className="space-y-4">
                {initiativeData.votingOptions.map((option) => (
                  <div key={option.id} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>{option.title}</span>
                      <span>
                        {option.votes} صوت ({Math.round((option.votes / totalVotes) * 100)}%)
                      </span>
                    </div>
                    <Progress
                      value={(option.votes / totalVotes) * 100}
                      className="h-2 bg-gray-200"
                      indicatorClassName={
                        option.id === "1" ? "bg-[#0a8754]" : option.id === "2" ? "bg-[#6bab3e]" : "bg-[#d9364c]"
                      }
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">صوّت على المبادرة</h3>

              {isVoted ? (
                <Card className="bg-green-50 border-green-200">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-green-700 flex items-center gap-2">
                      <CheckCircle2 className="h-5 w-5" />
                      تم التصويت بنجاح
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-green-600">
                      شكراً لمشاركتك في التصويت. يمكنك متابعة نتائج التصويت على هذه الصفحة.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {initiativeData.votingOptions.map((option) => (
                    <Card
                      key={option.id}
                      className={`cursor-pointer transition-all ${
                        selectedOption === option.id ? "border-[#0a8754] bg-green-50" : "hover:border-gray-300"
                      }`}
                      onClick={() => setSelectedOption(option.id)}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex items-center gap-2">
                          {selectedOption === option.id && <div className="h-4 w-4 rounded-full bg-[#0a8754]"></div>}
                          {option.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-gray-700">{option.description}</CardDescription>
                      </CardContent>
                    </Card>
                  ))}

                  <div className="flex justify-end pt-4">
                    <Button onClick={handleVote} disabled={!selectedOption} className="bg-[#0a8754] hover:bg-[#097548]">
                      تأكيد التصويت
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Confirm Vote Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد التصويت</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في التصويت؟ لا يمكنك تغيير صوتك بعد التأكيد.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="font-medium">خيارك:</p>
            <p className="mt-1">{initiativeData.votingOptions.find((o) => o.id === selectedOption)?.title}</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={confirmVote} className="bg-[#0a8754]">
              تأكيد التصويت
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-green-700">
              <CheckCircle2 className="h-5 w-5" />
              تم التصويت بنجاح
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>شكراً لمشاركتك في التصويت على هذه المبادرة. صوتك يساهم في تحسين مجتمعنا.</p>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowSuccessDialog(false)} className="bg-[#0a8754]">
              حسناً
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


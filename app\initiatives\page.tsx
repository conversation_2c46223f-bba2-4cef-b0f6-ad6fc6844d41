"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Filter, ChevronLeft, ChevronRight, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { api } from "@/lib/api"
import { getImageUrl } from "@/lib/imageUtils"

// Define types for initiatives
type Initiative = {
  _id: string
  title: string
  shortDescription: string
  location: string
  mainImage: string
  status: string
  supportCount: number
  commentCount: number
  createdAt: string
  author: {
    name: string
    username: string
    avatar: string
  }
  category: {
    name: string
    arabicName: string
    color: string
  }
}

export default function InitiativesPage() {
  const [initiatives, setInitiatives] = useState<Initiative[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [sortOrder, setSortOrder] = useState('newest')

  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0,
    limit: 12,
    hasNextPage: false,
    hasPrevPage: false
  })

  // Effect to fetch initiatives when filters change
  useEffect(() => {
    // Reset to page 1 when filters change
    setPagination(prev => ({ ...prev, currentPage: 1 }))
    fetchInitiatives(1)
  }, [searchTerm, categoryFilter, sortOrder, activeTab])

  // Effect to fetch initiatives when page changes
  useEffect(() => {
    if (pagination.currentPage > 1) {
      fetchInitiatives(pagination.currentPage)
    }
  }, [pagination.currentPage])

  // Function to fetch initiatives with filters and pagination
  const fetchInitiatives = async (page = 1) => {
    setIsLoading(true)
    try {
      console.log('Fetching initiatives from API with filters...')

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString()
      })

      // Add filters
      if (searchTerm) queryParams.append('search', searchTerm)
      if (categoryFilter !== 'all') queryParams.append('category', categoryFilter)
      if (activeTab === 'active' || activeTab === 'completed') {
        queryParams.append('status', activeTab)
      }
      if (sortOrder) queryParams.append('sort', sortOrder)

      // Use the API utility for consistent handling
      const data = await api.get(`/api/initiatives?${queryParams.toString()}`, false)
      console.log('Initiatives data from API utility:', data)

      if (!data || !data.initiatives) {
        console.error('Invalid response data from API utility:', data)
        throw new Error('Initiatives data not found in response')
      }

      // Process images to ensure they have absolute URLs
      const processedInitiatives = data.initiatives.map(initiative => ({
        ...initiative,
        mainImage: getImageUrl(initiative.mainImage),
        images: initiative.images ? initiative.images.map(img => getImageUrl(img)) : []
      }))

      console.log(`Loaded ${data.initiatives.length} initiatives successfully from API utility`)
      setInitiatives(processedInitiatives || [])

      // Update pagination information
      if (data.pagination) {
        setPagination({
          currentPage: data.pagination.currentPage,
          totalPages: data.pagination.totalPages,
          total: data.pagination.total,
          limit: data.pagination.limit,
          hasNextPage: data.pagination.hasNextPage,
          hasPrevPage: data.pagination.hasPrevPage
        })
      }

      setIsLoading(false)
    } catch (apiError) {
      console.error('Error fetching initiatives:', apiError)

      // Fallback: Direct approach with hardcoded URL
      try {
        const apiUrl = 'http://localhost:5000/api/initiatives'
        console.log('Fallback API URL:', apiUrl)

        // Build query parameters
        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString()
        })

        // Add filters
        if (searchTerm) queryParams.append('search', searchTerm)
        if (categoryFilter !== 'all') queryParams.append('category', categoryFilter)
        if (activeTab === 'active' || activeTab === 'completed') {
          queryParams.append('status', activeTab)
        }
        if (sortOrder) queryParams.append('sort', sortOrder)

        // Simple fetch with minimal options
        const response = await fetch(`${apiUrl}?${queryParams.toString()}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          mode: 'cors',
          credentials: 'omit',
          cache: 'no-cache'
        })

        if (!response.ok) {
          throw new Error('Failed to fetch initiatives')
        }

        const data = await response.json()
        console.log('Initiatives data from direct fetch:', data)

        if (!data || !data.initiatives) {
          console.error('Invalid response data:', data)
          throw new Error('Initiatives data not found in response')
        }

        // Process images to ensure they have absolute URLs
        const processedInitiatives = data.initiatives.map(initiative => ({
          ...initiative,
          mainImage: getImageUrl(initiative.mainImage),
          images: initiative.images ? initiative.images.map(img => getImageUrl(img)) : []
        }))

        console.log(`Loaded ${data.initiatives.length} initiatives successfully`)
        setInitiatives(processedInitiatives || [])

        // Update pagination information
        if (data.pagination) {
          setPagination({
            currentPage: data.pagination.currentPage,
            totalPages: data.pagination.totalPages,
            total: data.pagination.total,
            limit: data.pagination.limit,
            hasNextPage: data.pagination.hasNextPage,
            hasPrevPage: data.pagination.hasPrevPage
          })
        }
      } catch (fetchError) {
        console.error('Error with direct fetch:', fetchError)
        setError('Failed to load initiatives. Please try again later.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Initial data fetch
  useEffect(() => {
    fetchInitiatives(1)
  }, [])

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="bg-[#0a8754] text-white py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">المبادرات</h1>
          <p className="text-lg opacity-90 mb-8">
            استكشف المبادرات المجتمعية، ادعم المبادرات التي تهمك، أو أطلق مبادرتك الخاصة
          </p>

          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="ابحث عن مبادرة..."
                className="pl-10 bg-white text-black border-0 h-12"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-[180px] bg-white text-black border-0 h-12">
                <SelectValue placeholder="التصنيف" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التصنيفات</SelectItem>
                <SelectItem value="environment">بيئة</SelectItem>
                <SelectItem value="education">تعليم</SelectItem>
                <SelectItem value="health">صحة</SelectItem>
                <SelectItem value="culture">ثقافة</SelectItem>
                <SelectItem value="technology">تكنولوجيا</SelectItem>
                <SelectItem value="social">اجتماعية</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-full md:w-[180px] bg-white text-black border-0 h-12">
                <SelectValue placeholder="الترتيب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">الأحدث</SelectItem>
                <SelectItem value="popular">الأكثر دعماً</SelectItem>
                <SelectItem value="trending">الأكثر نشاطاً</SelectItem>
              </SelectContent>
            </Select>
            <Button className="h-12 bg-[#d9364c] hover:bg-[#c02e42]">
              <Filter size={18} className="mr-2" />
              تصفية
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 md:p-8">
        <div className="flex justify-between items-center mb-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="bg-white">
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="active">الجارية</TabsTrigger>
              <TabsTrigger value="completed">المكتملة</TabsTrigger>
              <TabsTrigger value="popular">الأكثر دعماً</TabsTrigger>
            </TabsList>
          </Tabs>

          <Link href="/initiatives/create">
            <Button className="bg-[#0a8754] hover:bg-[#097548]">
              <Plus size={18} className="mr-2" />
              إنشاء مبادرة
            </Button>
          </Link>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-green-600" />
            <span className="mr-2">جاري التحميل...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {initiatives.length === 0 ? (
                <div className="col-span-3 text-center py-12 border rounded-lg bg-gray-50">
                  <p className="text-lg text-gray-600 mb-4">
                    {pagination.total === 0
                      ? 'لم يتم العثور على أي مبادرات'
                      : 'لا توجد مبادرات مطابقة لمعايير البحث'}
                  </p>
                  <Link href="/initiatives/create">
                    <Button>إنشاء مبادرة جديدة</Button>
                  </Link>
                </div>
              ) : (
                initiatives.map((initiative) => (
                  <Card key={initiative._id} className="overflow-hidden">
                    <div className="h-[200px] overflow-hidden">
                      <div className="relative w-full h-full">
                        {initiative.mainImage?.startsWith('http') ? (
                          // Pour les images externes, utiliser une balise img standard
                          <img
                            src={initiative.mainImage}
                            alt={initiative.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          // Pour les images locales ou les placeholders, utiliser le composant Image
                          <Image
                            src={initiative.mainImage || "/placeholder.svg?height=200&width=400"}
                            alt={initiative.title}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover"
                          />
                        )}
                      </div>
                    </div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <Badge
                          className="mb-2"
                          style={{
                            backgroundColor: initiative.category?.color || "#0a8754",
                          }}
                        >
                          {initiative.category?.arabicName || initiative.category?.name}
                        </Badge>
                        <Badge
                          variant={initiative.status === "completed" ? "outline" : "default"}
                          className={
                            initiative.status === "completed"
                              ? "border-blue-500 text-blue-500"
                              : "bg-green-500"
                          }
                        >
                          {initiative.status === "completed" ? "مكتملة" : "جارية"}
                        </Badge>
                      </div>
                      <CardTitle className="text-xl">
                        <Link
                          href={`/initiatives/${initiative._id}`}
                          className="hover:text-[#0a8754] transition-colors"
                        >
                          {initiative.title}
                        </Link>
                      </CardTitle>
                      <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                        <span>{initiative.location}</span>
                        <span>•</span>
                        <span>
                          {new Date(initiative.createdAt).toLocaleDateString("ar-DZ", {
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                          })}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="line-clamp-3">
                        {initiative.shortDescription}
                      </CardDescription>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <div className="flex items-center gap-2">
                        <div className="relative w-6 h-6 rounded-full overflow-hidden">
                          {initiative.author?.avatar?.startsWith('http') ? (
                            <img
                              src={initiative.author.avatar}
                              alt={initiative.author?.name || ""}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Image
                              src={getImageUrl(initiative.author?.avatar) || "/placeholder.svg?height=30&width=30"}
                              alt={initiative.author?.name || ""}
                              fill
                              sizes="24px"
                              className="object-cover"
                            />
                          )}
                        </div>
                        <span className="text-sm">{initiative.author?.name}</span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{initiative.supportCount || 0} داعم</span>
                        <span>{initiative.commentCount || 0} تعليق</span>
                      </div>
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>

            {/* Pagination controls */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center mt-8 gap-2">
                <Button
                  variant="outline"
                  onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
                  disabled={!pagination.hasPrevPage || isLoading}
                >
                  <ChevronRight className="h-4 w-4 ml-2" />
                  السابق
                </Button>
                <div className="flex items-center px-4">
                  <span className="text-sm">
                    الصفحة {pagination.currentPage} من {pagination.totalPages}
                  </span>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
                  disabled={!pagination.hasNextPage || isLoading}
                >
                  التالي
                  <ChevronLeft className="h-4 w-4 mr-2" />
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

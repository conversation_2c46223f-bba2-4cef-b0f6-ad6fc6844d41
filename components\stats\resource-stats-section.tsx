"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "../ui/card"
import { Alert, AlertDescription } from "../ui/alert"
import { 
  Loader2, 
  AlertCircle, 
  Package, 
  DollarSign, 
  Users, 
  Wrench,
  HelpCircle,
  CheckCircle,
  XCircle,
  Truck,
  Ban,
  Clock,
  ArrowUp,
  ArrowDown
} from "lucide-react"
import { api } from "../../lib/api"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from "recharts"

interface ResourceStatsProps {
  initiativeId?: string
}

interface ResourceStats {
  resourcesByType: { type: string; count: number }[]
  resourcesByStatus: { status: string; count: number }[]
  needsByType: { type: string; count: number }[]
  needsByPriority: { priority: string; count: number }[]
  needsByStatus: { status: string; count: number }[]
  topInitiativesByResources?: { initiativeId: string; title: string; count: number }[]
  topInitiativesByNeeds?: { initiativeId: string; title: string; count: number }[]
  monthlyData?: { month: string; count: number }[]
  summary?: {
    totalNeeds: number
    fulfilledNeeds: number
    fulfillmentRate: number
    totalResources: number
    deliveredResources: number
    deliveryRate: number
  }
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]
const TYPE_COLORS = {
  material: "#0088FE",
  financial: "#00C49F",
  human: "#FFBB28",
  service: "#FF8042",
  other: "#8884D8"
}
const STATUS_COLORS = {
  requested: "#FFBB28",
  approved: "#00C49F",
  rejected: "#FF8042",
  delivered: "#8884D8",
  canceled: "#CCCCCC",
  open: "#0088FE",
  in_progress: "#FFBB28",
  fulfilled: "#00C49F",
  canceled: "#CCCCCC"
}
const PRIORITY_COLORS = {
  critical: "#FF0000",
  high: "#FF8042",
  medium: "#FFBB28",
  low: "#00C49F"
}

export default function ResourceStatsSection({ initiativeId }: ResourceStatsProps) {
  const [stats, setStats] = useState<ResourceStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    fetchResourceStats()
  }, [initiativeId])
  
  const fetchResourceStats = async () => {
    setIsLoading(true)
    try {
      const endpoint = initiativeId 
        ? `/api/stats/resources/initiative/${initiativeId}`
        : "/api/stats/resources"
      
      const response = await api.get(endpoint, false)
      
      if (response.success) {
        setStats(response.statistics)
      } else {
        setError("Failed to fetch resource statistics")
      }
    } catch (err: any) {
      console.error("Error fetching resource statistics:", err)
      setError(err.message || "An error occurred while fetching resource statistics")
    } finally {
      setIsLoading(false)
    }
  }
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "requested":
        return <Clock className="h-4 w-4" />
      case "approved":
        return <CheckCircle className="h-4 w-4" />
      case "rejected":
        return <XCircle className="h-4 w-4" />
      case "delivered":
        return <Truck className="h-4 w-4" />
      case "canceled":
        return <Ban className="h-4 w-4" />
      case "open":
        return <Clock className="h-4 w-4" />
      case "in_progress":
        return <ArrowUp className="h-4 w-4" />
      case "fulfilled":
        return <CheckCircle className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }
  
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "critical":
        return <ArrowUp className="h-4 w-4 text-red-600" />
      case "high":
        return <ArrowUp className="h-4 w-4 text-orange-600" />
      case "medium":
        return <ArrowUp className="h-4 w-4 text-yellow-600" />
      case "low":
        return <ArrowDown className="h-4 w-4 text-green-600" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }
  
  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "Material"
      case "financial":
        return "Financial"
      case "human":
        return "Human"
      case "service":
        return "Service"
      default:
        return "Other"
    }
  }
  
  const getStatusText = (status: string) => {
    switch (status) {
      case "requested":
        return "Requested"
      case "approved":
        return "Approved"
      case "rejected":
        return "Rejected"
      case "delivered":
        return "Delivered"
      case "canceled":
        return "Canceled"
      case "open":
        return "Open"
      case "in_progress":
        return "In Progress"
      case "fulfilled":
        return "Fulfilled"
      default:
        return status
    }
  }
  
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case "critical":
        return "Critical"
      case "high":
        return "High"
      case "medium":
        return "Medium"
      case "low":
        return "Low"
      default:
        return priority
    }
  }
  
  const formatMonth = (month: string) => {
    const [year, monthNum] = month.split("-")
    const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1)
    return date.toLocaleDateString("en-US", { month: "short", year: "numeric" })
  }
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading resource statistics...</span>
      </div>
    )
  }
  
  if (error || !stats) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error || "Failed to load resource statistics"}</AlertDescription>
      </Alert>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {stats.summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Resource Needs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.summary.totalNeeds}</div>
              <div className="text-sm text-gray-500">Total needs</div>
              <div className="mt-2 flex items-center">
                <div className="text-xl font-semibold">{stats.summary.fulfillmentRate}%</div>
                <div className="ml-2 text-sm text-gray-500">Fulfillment rate</div>
              </div>
              <div className="mt-1 text-sm text-gray-500">
                {stats.summary.fulfilledNeeds} of {stats.summary.totalNeeds} needs fulfilled
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Resource Offers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{stats.summary.totalResources}</div>
              <div className="text-sm text-gray-500">Total offers</div>
              <div className="mt-2 flex items-center">
                <div className="text-xl font-semibold">{stats.summary.deliveryRate}%</div>
                <div className="ml-2 text-sm text-gray-500">Delivery rate</div>
              </div>
              <div className="mt-1 text-sm text-gray-500">
                {stats.summary.deliveredResources} of {stats.summary.totalResources} resources delivered
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Resource Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {stats.summary.totalResources - stats.summary.totalNeeds}
              </div>
              <div className="text-sm text-gray-500">
                {stats.summary.totalResources >= stats.summary.totalNeeds ? "Surplus" : "Deficit"}
              </div>
              <div className="mt-2 text-sm">
                <div className="flex justify-between">
                  <span>Needs:</span>
                  <span>{stats.summary.totalNeeds}</span>
                </div>
                <div className="flex justify-between">
                  <span>Offers:</span>
                  <span>{stats.summary.totalResources}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Resources by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Resources by Type</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.resourcesByType.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No data available</div>
            ) : (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats.resourcesByType}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="type"
                      label={({ type, count }) => `${getTypeText(type)}: ${count}`}
                    >
                      {stats.resourcesByType.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={TYPE_COLORS[entry.type] || COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, getTypeText(name as string)]} />
                    <Legend formatter={(value) => getTypeText(value)} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Resources by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Resources by Status</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.resourcesByStatus.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No data available</div>
            ) : (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats.resourcesByStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="status"
                      label={({ status, count }) => `${getStatusText(status)}: ${count}`}
                    >
                      {stats.resourcesByStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.status] || COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, getStatusText(name as string)]} />
                    <Legend formatter={(value) => getStatusText(value)} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Needs by Type */}
        <Card>
          <CardHeader>
            <CardTitle>Resource Needs by Type</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.needsByType.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No data available</div>
            ) : (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats.needsByType}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="type"
                      label={({ type, count }) => `${getTypeText(type)}: ${count}`}
                    >
                      {stats.needsByType.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={TYPE_COLORS[entry.type] || COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, getTypeText(name as string)]} />
                    <Legend formatter={(value) => getTypeText(value)} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Needs by Priority */}
        <Card>
          <CardHeader>
            <CardTitle>Resource Needs by Priority</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.needsByPriority.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No data available</div>
            ) : (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats.needsByPriority}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="priority"
                      label={({ priority, count }) => `${getPriorityText(priority)}: ${count}`}
                    >
                      {stats.needsByPriority.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={PRIORITY_COLORS[entry.priority] || COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, getPriorityText(name as string)]} />
                    <Legend formatter={(value) => getPriorityText(value)} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Needs by Status */}
        <Card>
          <CardHeader>
            <CardTitle>Resource Needs by Status</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.needsByStatus.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No data available</div>
            ) : (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats.needsByStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="status"
                      label={({ status, count }) => `${getStatusText(status)}: ${count}`}
                    >
                      {stats.needsByStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.status] || COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, getStatusText(name as string)]} />
                    <Legend formatter={(value) => getStatusText(value)} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Monthly Resource Trends */}
        {stats.monthlyData && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Monthly Resource Trends</CardTitle>
            </CardHeader>
            <CardContent>
              {stats.monthlyData.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No data available</div>
              ) : (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={stats.monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="month" 
                        tickFormatter={formatMonth}
                      />
                      <YAxis />
                      <Tooltip 
                        labelFormatter={formatMonth}
                        formatter={(value) => [value, "Resources"]}
                      />
                      <Legend />
                      <Bar dataKey="count" name="Resources" fill="#0088FE" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        )}
        
        {/* Top Initiatives by Resources */}
        {stats.topInitiativesByResources && (
          <Card>
            <CardHeader>
              <CardTitle>Top Initiatives by Resources</CardTitle>
            </CardHeader>
            <CardContent>
              {stats.topInitiativesByResources.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No data available</div>
              ) : (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      layout="vertical"
                      data={stats.topInitiativesByResources}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis 
                        type="category" 
                        dataKey="title" 
                        width={150}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" name="Resources" fill="#00C49F" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        )}
        
        {/* Top Initiatives by Needs */}
        {stats.topInitiativesByNeeds && (
          <Card>
            <CardHeader>
              <CardTitle>Top Initiatives by Needs</CardTitle>
            </CardHeader>
            <CardContent>
              {stats.topInitiativesByNeeds.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No data available</div>
              ) : (
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      layout="vertical"
                      data={stats.topInitiativesByNeeds}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis 
                        type="category" 
                        dataKey="title" 
                        width={150}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" name="Needs" fill="#FFBB28" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

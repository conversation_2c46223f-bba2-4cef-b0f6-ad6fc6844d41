"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../../components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../../../components/ui/tabs"
import {
  Award, Edit, Loader2, Plus, Trash, Trophy, Star, Heart,
  Gift, Users, Wrench, Lightbulb, LucideIcon
} from "lucide-react"
import { api } from "../../../lib/api"
import { toast } from "../../../components/ui/use-toast"
import { useAuth } from "../../../components/auth-provider"
import { Badge } from "../../../components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "../../../components/ui/dialog"
import BadgeForm from "../../../components/admin/badges/BadgeForm"

export default function AdminBadgesPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [badges, setBadges] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedBadge, setSelectedBadge] = useState<any>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingBadge, setDeletingBadge] = useState<any>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }

    // Vérifier si l'utilisateur est un administrateur
    const isAdmin = user?.role === 'admin' ||
                   (typeof user?.role === 'object' && user?.role?.code === 'admin');

    if (!isAdmin) {
      console.log("User is not admin:", user);
      router.push('/')
      toast({
        title: "غير مصرح",
        description: "ليس لديك صلاحية الوصول إلى لوحة الإدارة",
        variant: "destructive"
      })
      return
    }

    fetchBadges()
  }, [isAuthenticated, user, router])

  const fetchBadges = async () => {
    try {
      setIsLoading(true)
      const response = await api.get("/api/badges", true)
      if (response.success) {
        setBadges(response.badges || [])
      } else {
        toast({
          title: "خطأ",
          description: "فشل في جلب الشارات",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error fetching badges:", error)
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء جلب الشارات",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditBadge = (badge: any) => {
    setSelectedBadge(badge)
  }

  const handleDeleteBadge = (badge: any) => {
    setDeletingBadge(badge)
    setShowDeleteDialog(true)
  }

  const confirmDeleteBadge = async () => {
    if (!deletingBadge) return

    try {
      setIsDeleting(true)
      const response = await api.delete(`/api/badges/${deletingBadge._id}`, true)
      if (response.success) {
        toast({
          title: "تم الحذف",
          description: "تم حذف الشارة بنجاح",
          variant: "default"
        })
        fetchBadges()
      } else {
        toast({
          title: "خطأ",
          description: response.message || "فشل في حذف الشارة",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error("Error deleting badge:", error)
      toast({
        title: "خطأ",
        description: error.message || "حدث خطأ أثناء حذف الشارة",
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
      setDeletingBadge(null)
    }
  }

  const handleFormSuccess = () => {
    setSelectedBadge(null)
    setShowCreateForm(false)
    fetchBadges()
  }

  const filteredBadges = activeTab === "all"
    ? badges
    : badges.filter(badge => badge.category === activeTab)

  const getCategoryText = (category: string) => {
    switch (category) {
      case "participation": return "المشاركة"
      case "achievement": return "الإنجاز"
      case "contribution": return "المساهمة"
      case "special": return "خاص"
      case "skill": return "المهارة"
      default: return category
    }
  }

  const getBadgeLevelText = (level: number) => {
    switch (level) {
      case 1: return "المستوى الأول"
      case 2: return "المستوى الثاني"
      case 3: return "المستوى الثالث"
      case 4: return "المستوى الرابع"
      case 5: return "المستوى الخامس"
      default: return `المستوى ${level}`
    }
  }

  const getBadgeIcon = (iconName: string): LucideIcon => {
    switch (iconName) {
      case "award": return Award
      case "trophy": return Trophy
      case "star": return Star
      case "heart": return Heart
      case "gift": return Gift
      case "users": return Users
      case "tool": return Wrench
      case "lightbulb": return Lightbulb
      default: return Award
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }

  if (selectedBadge || showCreateForm) {
    return (
      <div className="container mx-auto py-6">
        <Button
          variant="outline"
          onClick={() => {
            setSelectedBadge(null)
            setShowCreateForm(false)
          }}
          className="mb-6"
        >
          العودة إلى قائمة الشارات
        </Button>
        <BadgeForm
          badge={selectedBadge}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setSelectedBadge(null)
            setShowCreateForm(false)
          }}
        />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة الشارات</h1>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="ml-2 h-4 w-4" />
          إضافة شارة جديدة
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="all">الكل</TabsTrigger>
          <TabsTrigger value="participation">المشاركة</TabsTrigger>
          <TabsTrigger value="achievement">الإنجاز</TabsTrigger>
          <TabsTrigger value="contribution">المساهمة</TabsTrigger>
          <TabsTrigger value="skill">المهارة</TabsTrigger>
          <TabsTrigger value="special">خاص</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === "all" ? "جميع الشارات" : `شارات ${getCategoryText(activeTab)}`}
              </CardTitle>
              <CardDescription>
                {activeTab === "all"
                  ? "عرض وإدارة جميع الشارات المتاحة في النظام"
                  : `عرض وإدارة شارات ${getCategoryText(activeTab)} المتاحة في النظام`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredBadges.length === 0 ? (
                <div className="text-center py-12">
                  <Award className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                  <p className="text-gray-500">لا توجد شارات متاحة</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredBadges.map((badge) => (
                    <Card key={badge._id} className="overflow-hidden">
                      <div className="p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div
                            className="w-12 h-12 rounded-full flex items-center justify-center border-2"
                            style={{
                              backgroundColor: `${badge.color}20`,
                              borderColor: badge.color
                            }}
                          >
                            {React.createElement(getBadgeIcon(badge.icon), {
                              className: "h-6 w-6",
                              style: { color: badge.color }
                            })}
                          </div>
                          <div>
                            <h3 className="font-semibold">{badge.arabicName}</h3>
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Badge variant="outline" className="text-xs">
                                {getCategoryText(badge.category)}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {getBadgeLevelText(badge.level)}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                          {badge.arabicDescription}
                        </p>

                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-500">
                            منحت {badge.issuedCount || 0} مرة
                          </span>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditBadge(badge)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDeleteBadge(badge)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف الشارة</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف شارة "{deletingBadge?.arabicName}"؟
              <br />
              لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteBadge}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري الحذف...
                </>
              ) : (
                <>
                  <Trash className="ml-2 h-4 w-4" />
                  حذف
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

import { Activity } from "../models"
import { createActivityUtil } from "../controllers/activity.controller"

/**
 * Service pour gérer les activités utilisateur
 */
export const ActivityService = {
  /**
   * Enregistrer une activité liée à une initiative
   * @param userId ID de l'utilisateur
   * @param action Action effectuée (create, update, delete, etc.)
   * @param initiativeId ID de l'initiative
   * @param initiativeTitle Titre de l'initiative
   * @param metadata Métadonnées supplémentaires
   */
  initiativeActivity: async (
    userId: string,
    action: string,
    initiativeId: string,
    initiativeTitle: string,
    metadata: Record<string, any> = {}
  ) => {
    let content = ""

    switch (action) {
      case "create":
        content = `قام بإنشاء مبادرة جديدة: ${initiativeTitle}`
        break
      case "update":
        // Si c'est une mise à jour spécifique à l'onglet "Updates"
        if (metadata.updateType) {
          switch (metadata.updateType) {
            case "create":
              content = `قام بإضافة تحديث جديد "${metadata.updateTitle}" لمبادرة: ${initiativeTitle}`;
              break;
            case "edit":
              content = `قام بتعديل التحديث "${metadata.updateTitle}" في مبادرة: ${initiativeTitle}`;
              break;
            case "delete":
              content = `قام بحذف التحديث "${metadata.updateTitle}" من مبادرة: ${initiativeTitle}`;
              break;
            default:
              content = `قام بعملية على تحديث في مبادرة: ${initiativeTitle}`;
          }
        }
        // Sinon, c'est une mise à jour générale de l'initiative
        else if (metadata.updatedFields && metadata.updatedFields.length > 0) {
          const fieldsArabic = metadata.updatedFields.map((field: string) => {
            switch (field) {
              case "title": return "العنوان";
              case "description": return "الوصف";
              case "shortDescription": return "الوصف المختصر";
              case "category": return "الفئة";
              case "location": return "الموقع";
              case "mainImage": return "الصورة الرئيسية";
              case "gallery": return "معرض الصور";
              case "status": return "الحالة";
              case "requiredVolunteers": return "المتطوعين المطلوبين";
              case "socialImpacts": return "التأثير الاجتماعي";
              case "milestones": return "المراحل";
              case "resources": return "الموارد";
              case "resourceNeeds": return "احتياجات الموارد";
              case "updates": return "التحديثات";
              case "isPublic": return "الرؤية العامة";
              case "isPromoted": return "الترويج";
              case "targetDate": return "تاريخ الاستهداف";
              case "startDate": return "تاريخ البدء";
              case "endDate": return "تاريخ الانتهاء";
              case "progress": return "التقدم";
              case "content": return "المحتوى";
              case "images": return "الصور";
              case "isPublished": return "النشر";
              default: return field;
            }
          }).join("، ");
          content = `قام بتحديث مبادرة: ${initiativeTitle} (${fieldsArabic})`;
        } else {
          content = `قام بتحديث مبادرة: ${initiativeTitle}`;
        }
        break
      case "delete":
        content = `قام بحذف مبادرة: ${initiativeTitle}`
        break
      case "complete":
        // Si c'est un jalon qui a été complété
        if (metadata.milestoneTitle) {
          content = `أكمل مرحلة "${metadata.milestoneTitle}" في مبادرة: ${initiativeTitle}`
        } else {
          content = `أكمل مبادرة: ${initiativeTitle}`
        }
        break
      default:
        content = `قام بعملية على مبادرة: ${initiativeTitle}`
    }

    return createActivityUtil(userId, "initiative", action, content, {
      relatedInitiative: initiativeId,
      metadata,
      isPublic: true
    })
  },

  /**
   * Enregistrer une activité liée au support d'une initiative
   * @param userId ID de l'utilisateur
   * @param action Action effectuée (support, unsupport)
   * @param initiativeId ID de l'initiative
   * @param initiativeTitle Titre de l'initiative
   */
  supportActivity: async (
    userId: string,
    action: string,
    initiativeId: string,
    initiativeTitle: string
  ) => {
    let content = ""

    switch (action) {
      case "support":
        content = `قام بدعم مبادرة: ${initiativeTitle}`
        break
      case "unsupport":
        content = `قام بإلغاء دعم مبادرة: ${initiativeTitle}`
        break
      default:
        content = `قام بعملية دعم على مبادرة: ${initiativeTitle}`
    }

    return createActivityUtil(userId, "support", action, content, {
      relatedInitiative: initiativeId,
      isPublic: true
    })
  },

  /**
   * Enregistrer une activité liée à un commentaire
   * @param userId ID de l'utilisateur
   * @param action Action effectuée (comment, reply, delete)
   * @param initiativeId ID de l'initiative
   * @param initiativeTitle Titre de l'initiative
   * @param commentId ID du commentaire
   */
  commentActivity: async (
    userId: string,
    action: string,
    initiativeId: string,
    initiativeTitle: string,
    commentId: string
  ) => {
    let content = ""

    switch (action) {
      case "comment":
        content = `علق على مبادرة: ${initiativeTitle}`
        break
      case "reply":
        content = `رد على تعليق في مبادرة: ${initiativeTitle}`
        break
      case "delete":
        content = `حذف تعليقًا من مبادرة: ${initiativeTitle}`
        break
      default:
        content = `قام بعملية تعليق على مبادرة: ${initiativeTitle}`
    }

    return createActivityUtil(userId, "comment", action, content, {
      relatedInitiative: initiativeId,
      relatedComment: commentId,
      isPublic: true
    })
  },

  /**
   * Enregistrer une activité liée au volontariat
   * @param userId ID de l'utilisateur
   * @param action Action effectuée (join, leave, invite, accept, reject)
   * @param initiativeId ID de l'initiative
   * @param initiativeTitle Titre de l'initiative
   * @param relatedUserId ID de l'utilisateur concerné (pour les invitations)
   */
  volunteerActivity: async (
    userId: string,
    action: string,
    initiativeId: string,
    initiativeTitle: string,
    relatedUserId?: string,
    metadata: Record<string, any> = {}
  ) => {
    let content = ""

    switch (action) {
      case "join":
        if (metadata.role) {
          const roleArabic = metadata.role === "general" ? "متطوع عام" :
                            metadata.role === "coordinator" ? "منسق" :
                            metadata.role === "specialist" ? "متخصص" : metadata.role;
          content = `انضم كمتطوع (${roleArabic}) في مبادرة: ${initiativeTitle}`
        } else {
          content = `انضم كمتطوع في مبادرة: ${initiativeTitle}`
        }
        break
      case "leave":
        content = `غادر كمتطوع من مبادرة: ${initiativeTitle}`
        break
      case "invite":
        if (relatedUserId && metadata.invitedUserName) {
          content = `دعا ${metadata.invitedUserName} للانضمام كمتطوع إلى مبادرة: ${initiativeTitle}`
        } else if (metadata.invitedEmail) {
          content = `دعا مستخدم (${metadata.invitedEmail}) للانضمام كمتطوع إلى مبادرة: ${initiativeTitle}`
        } else {
          content = `دعا متطوعًا للانضمام إلى مبادرة: ${initiativeTitle}`
        }
        break
      case "accept":
        if (metadata.inviterName) {
          content = `قبل دعوة من ${metadata.inviterName} للتطوع في مبادرة: ${initiativeTitle}`
        } else {
          content = `قبل دعوة للتطوع في مبادرة: ${initiativeTitle}`
        }
        break
      case "reject":
        if (metadata.inviterName) {
          content = `رفض دعوة من ${metadata.inviterName} للتطوع في مبادرة: ${initiativeTitle}`
        } else {
          content = `رفض دعوة للتطوع في مبادرة: ${initiativeTitle}`
        }
        break
      default:
        content = `قام بعملية تطوع في مبادرة: ${initiativeTitle}`
    }

    return createActivityUtil(userId, "volunteer", action, content, {
      relatedInitiative: initiativeId,
      relatedUser: relatedUserId,
      isPublic: true
    })
  },

  /**
   * Enregistrer une activité liée à un badge
   * @param userId ID de l'utilisateur
   * @param badgeName Nom du badge
   * @param badgeDescription Description du badge
   */
  badgeActivity: async (
    userId: string,
    action: string,
    badgeName: string,
    badgeDescription: string,
    relatedUserId?: string,
    initiativeId?: string,
    initiativeTitle?: string
  ) => {
    let content = ""

    switch (action) {
      case "earn":
        content = `حصل على وسام جديد: ${badgeName}`
        break
      case "award":
        if (relatedUserId && initiativeTitle) {
          content = `منح وسام ${badgeName} لمساهمته في مبادرة: ${initiativeTitle}`
        } else {
          content = `منح وسام ${badgeName}`
        }
        break
      default:
        content = `عملية متعلقة بوسام: ${badgeName}`
    }

    return createActivityUtil(userId, "badge", action, content, {
      relatedUser: relatedUserId,
      relatedInitiative: initiativeId,
      metadata: { badgeName, badgeDescription },
      isPublic: true
    })
  },

  /**
   * Enregistrer une activité liée au profil utilisateur
   * @param userId ID de l'utilisateur
   * @param action Action effectuée (update)
   * @param details Détails de la mise à jour
   */
  profileActivity: async (
    userId: string,
    action: string,
    details: string
  ) => {
    let content = ""

    switch (action) {
      case "update":
        content = `قام بتحديث ملفه الشخصي: ${details}`
        break
      default:
        content = `قام بعملية على ملفه الشخصي: ${details}`
    }

    return createActivityUtil(userId, "profile", action, content, {
      isPublic: false // Les activités de profil sont privées par défaut
    })
  },

  /**
   * Enregistrer une activité système
   * @param userId ID de l'utilisateur
   * @param action Action effectuée (login, register)
   */
  systemActivity: async (
    userId: string,
    action: string
  ) => {
    let content = ""

    switch (action) {
      case "login":
        content = `قام بتسجيل الدخول إلى الموقع`
        break
      case "register":
        content = `قام بالتسجيل في الموقع`
        break
      default:
        content = `قام بعملية نظام`
    }

    return createActivityUtil(userId, "system", action, content, {
      isPublic: false // Les activités système sont privées par défaut
    })
  }
}

import { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Invitation, Initiative, User } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { ActivityService } from "../services/activity.service"

/**
 * Get all invitations for the current user
 * @route GET /api/invitations
 * @access Private
 */
export const getUserInvitations = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Get all invitations by email (not just pending)
    const invitations = await Invitation.find({
      recipient: user.email
    })
      .populate("initiative", "title shortDescription mainImage status")
      .populate("sender", "name username avatar")
      .sort({ createdAt: -1 })

    res.status(200).json({
      success: true,
      invitations
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Accept an invitation
 * @route PUT /api/invitations/:id/accept
 * @access Private
 */
export const acceptInvitation = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate invitation ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid invitation ID"))
    }

    // Find invitation
    const invitation = await Invitation.findById(id)
    if (!invitation) {
      return next(createError(404, "Invitation not found"))
    }

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Check if invitation is for this user
    if (invitation.recipient !== user.email) {
      return next(createError(403, "This invitation is not for you"))
    }

    // Check if invitation is still pending
    if (invitation.status !== "pending") {
      return next(createError(400, "This invitation has already been processed"))
    }

    // Find initiative
    const initiative = await Initiative.findById(invitation.initiative)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if initiative is active
    if (initiative.status !== "active") {
      return next(createError(400, "You can only join active initiatives"))
    }

    // Check if user is already a volunteer
    if (initiative.currentVolunteers.includes(userId)) {
      // Update invitation status
      invitation.status = "accepted"
      invitation.respondedAt = new Date()
      await invitation.save()

      return res.status(200).json({
        success: true,
        message: "You are already a volunteer for this initiative"
      })
    }

    // Create volunteer application data
    const volunteerData = {
      user: userId,
      role: invitation.role || "general",
      skills: [],
      availability: "flexible",
      message: invitation.message || "",
      joinedAt: new Date(),
      status: "active",
      contributions: [],
      points: 0
    }

    // Add user to currentVolunteers
    initiative.currentVolunteers.push(userId)

    // Store volunteer data in a new field if it doesn't exist
    if (!initiative.volunteersData) {
      initiative.volunteersData = []
    }

    initiative.volunteersData.push(volunteerData)

    // Update invitation status
    invitation.status = "accepted"
    invitation.respondedAt = new Date()

    // Save changes
    await Promise.all([initiative.save(), invitation.save()])

    // Create notification for initiative author
    await createNotification({
      recipient: initiative.author,
      sender: userId,
      type: "volunteer_join",
      content: `قام ${user.name} بقبول دعوتك والانضمام إلى مبادرة "${initiative.title}" كمتطوع`,
      relatedInitiative: initiative._id,
      link: `/initiatives/${initiative._id}/volunteers`,
    })

    // Récupérer le nom de l'inviteur
    const inviter = await User.findById(invitation.sender);
    const inviterName = inviter ? inviter.name : "مستخدم";

    // Enregistrer l'activité
    await ActivityService.volunteerActivity(
      userId,
      "accept",
      initiative._id.toString(),
      initiative.title,
      invitation.sender.toString(),
      {
        inviterName,
        role: invitation.role,
        invitationDate: invitation.createdAt,
        responseDate: invitation.respondedAt
      }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "You have successfully joined this initiative as a volunteer"
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Decline an invitation
 * @route PUT /api/invitations/:id/decline
 * @access Private
 */
export const declineInvitation = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate invitation ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid invitation ID"))
    }

    // Find invitation
    const invitation = await Invitation.findById(id)
    if (!invitation) {
      return next(createError(404, "Invitation not found"))
    }

    // Find user
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Check if invitation is for this user
    if (invitation.recipient !== user.email) {
      return next(createError(403, "This invitation is not for you"))
    }

    // Check if invitation is still pending
    if (invitation.status !== "pending") {
      return next(createError(400, "This invitation has already been processed"))
    }

    // Find initiative
    const initiative = await Initiative.findById(invitation.initiative)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Update invitation status
    invitation.status = "declined"
    invitation.respondedAt = new Date()
    await invitation.save()

    // Create notification for initiative author
    await createNotification({
      recipient: initiative.author,
      sender: userId,
      type: "volunteer_decline",
      content: `قام ${user.name} برفض دعوتك للانضمام إلى مبادرة "${initiative.title}" كمتطوع`,
      relatedInitiative: initiative._id,
      link: `/initiatives/${initiative._id}/volunteers`,
    })

    // Récupérer le nom de l'inviteur
    const inviter = await User.findById(invitation.sender);
    const inviterName = inviter ? inviter.name : "مستخدم";

    // Enregistrer l'activité
    await ActivityService.volunteerActivity(
      userId,
      "reject",
      initiative._id.toString(),
      initiative.title,
      invitation.sender.toString(),
      {
        inviterName,
        role: invitation.role,
        invitationDate: invitation.createdAt,
        responseDate: invitation.respondedAt
      }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "You have declined this invitation"
    })
  } catch (error) {
    next(error)
  }
}

// Helper to check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

export const api = {
  get: async (url: string, withAuth: boolean = true) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (withAuth && isBrowser) {
      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    try {
      const response = await fetch(`http://localhost:5000${url}`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error('Failed to fetch data');
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  },

  post: async (url: string, data: any, withAuth: boolean = true) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (withAuth && isBrowser) {
      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    try {
      const response = await fetch(`http://localhost:5000${url}`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Failed to post data');
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  },

  put: async (url: string, data: any, withAuth: boolean = true) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (withAuth && isBrowser) {
      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    try {
      const response = await fetch(`http://localhost:5000${url}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Failed to update data');
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  },

  delete: async (url: string, withAuth: boolean = true) => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (withAuth && isBrowser) {
      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    try {
      const response = await fetch(`http://localhost:5000${url}`, {
        method: 'DELETE',
        headers
      });

      if (!response.ok) {
        throw new Error('Failed to delete data');
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
};

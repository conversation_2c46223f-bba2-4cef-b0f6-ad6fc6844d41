// Script pour supprimer les compétences "Unnamed skill" de la base de données
const mongoose = require('mongoose');
const { User } = require('../dist/models');

// Connexion à la base de données
mongoose.connect('mongodb://localhost:27017/initiatives_dz', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  console.log('Connected to MongoDB');
  fixSkills();
})
.catch(err => {
  console.error('Error connecting to MongoDB:', err);
  process.exit(1);
});

async function fixSkills() {
  try {
    // Trouver l'utilisateur Mohamed
    const user = await User.findOne({ username: 'mohamed' });
    
    if (!user) {
      console.log('User mohamed not found');
      process.exit(0);
    }
    
    console.log('Found user:', user.username);
    console.log('Current skills:', user.skills);
    
    // Filtrer les compétences pour supprimer celles avec le nom "Unnamed skill"
    const validSkills = user.skills.filter(skill => 
      skill && skill.name && skill.name !== 'Unnamed skill'
    );
    
    console.log('Valid skills:', validSkills);
    
    // Mettre à jour l'utilisateur avec les compétences valides
    user.skills = validSkills;
    
    // Sauvegarder l'utilisateur
    await user.save();
    
    console.log('User updated successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing skills:', error);
    process.exit(1);
  }
}

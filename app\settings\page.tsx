"use client"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Textarea } from "../../components/ui/textarea"
import { Avatar, AvatarImage, AvatarFallback } from "../../components/ui/avatar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs"
import { Switch } from "../../components/ui/switch"
import { Alert, AlertDescription } from "../../components/ui/alert"
import { Loader2, AlertCircle, Save, User, Bell, Shield, Lock, Globe, Eye, Upload, X, Plus, Info } from "lucide-react"
import { Label } from "../../components/ui/label"
import { api } from "../../lib/api"
import { uploadFile } from "../../lib/fileUpload"
import { useAuth } from "../../components/auth-provider"
import { toast } from "../../components/ui/use-toast"
import { Toaster } from "../../components/ui/toaster"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { SKILL_CATEGORIES, SKILL_LEVELS, SKILL_EXAMPLES_AR } from "../../lib/constants/skills"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../components/ui/accordion"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../components/ui/tooltip"
import ProposerTabContent from "@/components/settings/proposer-tab-content"
import CompanyTabContent from "@/components/settings/company-tab-content"
import CivilSocietyTabContent from "@/components/settings/civil-society-tab-content"

export default function SettingsPage() {
  const router = useRouter()
  const { user, isAuthenticated, refreshUserData, updateUser } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("account")

  // User settings state
  const [userSettings, setUserSettings] = useState({
    name: "",
    email: "",
    username: "",
    bio: "",
    location: "",
    website: "",
    avatar: "",
    // Champs spécifiques pour les volontaires
    qualifications: [] as string[],
    skills: [] as Array<{
      name: string,
      category: 'cognitive' | 'technical' | 'interpersonal' | 'organizational' | 'digital' | 'linguistic' | 'transversal',
      level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    }>,
    interests: [] as string[],
    availability: "",
    // Champs spécifiques pour les proposeurs d'idées
    ideaDescription: "",
    objectives: "",
    needs: "",
    // Champs spécifiques pour les entreprises
    companyName: "",
    industry: "",
    services: [] as string[],
    resources: [] as string[],
    customResources: [] as string[],
    supportDescription: "",
    // Champs spécifiques pour les acteurs de la société civile
    organizationName: "",
    organizationType: "",
    activitySector: "",
    scope: "",
    approvalNumber: "",
    memberCount: "",
    foundingYear: "",
    organizationDescription: "",
    contactPhone: "",
    contactPhone2: "",
    contactEmail: "",
    website: "",
    address: "",
    notifications: {
      email: true,
      browser: true,
      initiatives: true,
      comments: true,
      mentions: true
    },
    privacy: {
      showEmail: false,
      showLocation: true,
      showInitiatives: true
    },
    language: "ar",
    theme: "light"
  })

  // Référence pour l'input de fichier caché
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Use a ref to track if we've already fetched user data
  const hasInitializedRef = useRef(false)

  // Définir l'onglet actif en fonction du type d'utilisateur
  useEffect(() => {
    if (user) {
      if (user.userType === 'volunteer') {
        setActiveTab('volunteer');
      } else if (user.userType === 'company') {
        setActiveTab('company');
      } else if (user.userType === 'civilSociety') {
        setActiveTab('civilSociety');
      } else {
        setActiveTab('account');
      }
    }
  }, [user]);

  // Utiliser une référence pour éviter les appels multiples
  const hasRefreshedRef = useRef(false);

  useEffect(() => {
    const fetchUserSettings = async () => {
      try {
        if (!isAuthenticated || !user) {
          toast({
            title: "Authentication Required",
            description: "Please log in to view your settings",
            variant: "destructive"
          })
          router.push('/auth/login')
          return
        }

        setIsLoading(true)

        // Forcer un rafraîchissement des données utilisateur au chargement initial
        if (!hasRefreshedRef.current) {
          console.log("Initial data refresh");
          try {
            const freshUser = await refreshUserData();
            hasRefreshedRef.current = true;

            if (freshUser) {
              console.log("Fresh user data on initial load:", {
                id: freshUser.id,
                userType: freshUser.userType,
                hasSkills: freshUser.skills ? freshUser.skills.length > 0 : false,
                skillsCount: freshUser.skills ? freshUser.skills.length : 0,
                skills: freshUser.skills
              });

              // Utiliser les données fraîches pour initialiser les paramètres
              console.log("Setting user settings from fresh data");
              setUserSettings(prevSettings => ({
                ...prevSettings,
                name: freshUser.name || "",
                email: freshUser.email || "",
                username: freshUser.username || "",
                avatar: freshUser.avatar || "",
                bio: freshUser.bio || "",
                location: freshUser.location || "",
                // Champs spécifiques pour les volontaires
                qualifications: freshUser.qualifications || [],
                skills: freshUser.skills || [],
                interests: freshUser.interests || [],
                availability: freshUser.availability || "",
                // Champs spécifiques pour les proposeurs d'idées
                ideaDescription: freshUser.ideaDescription || "",
                objectives: freshUser.objectives || "",
                needs: freshUser.needs || "",
                // Champs spécifiques pour les entreprises
                companyName: freshUser.companyName || "",
                industry: freshUser.industry || "",
                services: freshUser.services || [],
                resources: freshUser.resources || [],
                customResources: freshUser.customResources || [],
                supportDescription: freshUser.supportDescription || "",
                // Champs spécifiques pour les acteurs de la société civile
                organizationName: freshUser.organizationName || "",
                organizationType: freshUser.organizationType || "",
                activitySector: freshUser.activitySector || "",
                scope: freshUser.scope || "",
                approvalNumber: freshUser.approvalNumber || "",
                memberCount: freshUser.memberCount || "",
                foundingYear: freshUser.foundingYear || "",
                organizationDescription: freshUser.organizationDescription || "",
                contactPhone: freshUser.contactPhone || "",
                contactPhone2: freshUser.contactPhone2 || "",
                contactEmail: freshUser.contactEmail || "",
                website: freshUser.website || "",
                address: freshUser.address || ""
              }));

              // Sortir de la fonction après avoir initialisé avec les données fraîches
              setIsLoading(false);
              return;
            }
          } catch (refreshError) {
            console.error("Error refreshing user data on initial load:", refreshError);
            // Continuer avec les données utilisateur actuelles en cas d'erreur
          }
        }

        // Utiliser directement les données utilisateur actuelles si le rafraîchissement a échoué
        console.log("Current user data:", {
          id: user?.id,
          userType: user?.userType,
          hasSkills: user?.skills ? user.skills.length > 0 : false,
          skillsCount: user?.skills ? user.skills.length : 0,
          skills: user?.skills
        });

        // Set initial settings from user data
        console.log("Setting user settings from current user data");
        setUserSettings(prevSettings => ({
          ...prevSettings,
          name: user.name || "",
          email: user.email || "",
          username: user.username || "",
          avatar: user.avatar || "",
          bio: user.bio || "",
          location: user.location || "",
          // Champs spécifiques pour les volontaires
          qualifications: user.qualifications || [],
          skills: user.skills || [],
          interests: user.interests || [],
          availability: user.availability || "",
          // Champs spécifiques pour les proposeurs d'idées
          ideaDescription: user.ideaDescription || "",
          objectives: user.objectives || "",
          needs: user.needs || "",
          // Champs spécifiques pour les entreprises
          companyName: user.companyName || "",
          industry: user.industry || "",
          services: user.services || [],
          resources: user.resources || [],
          customResources: user.customResources || [],
          supportDescription: user.supportDescription || "",
          // Champs spécifiques pour les acteurs de la société civile
          organizationName: user.organizationName || "",
          organizationType: user.organizationType || "",
          activitySector: user.activitySector || "",
          scope: user.scope || "",
          approvalNumber: user.approvalNumber || "",
          memberCount: user.memberCount || "",
          foundingYear: user.foundingYear || "",
          organizationDescription: user.organizationDescription || "",
          contactPhone: user.contactPhone || "",
          contactPhone2: user.contactPhone2 || "",
          contactEmail: user.contactEmail || "",
          website: user.website || "",
          address: user.address || ""
        }));

        // Try to fetch user settings from API
        try {
          console.log("Fetching user settings from API");
          const response = await api.get(`/api/users/${user.id}/settings`, true);
          console.log("API settings response:", response);

          if (response && response.success && response.settings) {
            console.log("Updating settings with API data");
            // Update settings with response data
            setUserSettings(prevSettings => ({
              ...prevSettings,
              ...response.settings,
              // Ensure these fields are always set from user data
              name: user.name || prevSettings.name,
              email: user.email || prevSettings.email,
              username: user.username || prevSettings.username,
              avatar: user.avatar || prevSettings.avatar,
              // Conserver les compétences de l'utilisateur si elles existent
              skills: user.skills && user.skills.length > 0 ? user.skills : prevSettings.skills
            }));
          }
        } catch (settingsError) {
          console.error('Error fetching user settings:', settingsError)
          // Already set initial settings from user data above
        }
      } catch (err: any) {
        console.error('Error in settings page:', err)
        setError(err.message || 'Failed to load settings')
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserSettings()
  }, [user, isAuthenticated, router, refreshUserData])

  // Fonction pour gérer le téléchargement d'image
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0] || !user) return

    const file = e.target.files[0]

    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      toast({
        title: "خطأ",
        description: "يرجى تحميل صورة فقط",
        variant: "destructive"
      })
      return
    }

    // Vérifier la taille du fichier (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "خطأ",
        description: "يجب أن يكون حجم الصورة أقل من 2 ميجابايت",
        variant: "destructive"
      })
      return
    }

    try {
      setIsSaving(true)
      setError(null)

      // Utiliser notre fonction uploadFile pour télécharger l'image
      const data = await uploadFile(`/api/users/${user.id}/avatar`, file, 'avatar')

      if (data && data.success) {
        // Mettre à jour l'avatar dans les paramètres
        setUserSettings(prev => ({
          ...prev,
          avatar: data.avatarUrl
        }))

        toast({
          title: "تم التحميل بنجاح",
          description: "تم تحديث صورة الملف الشخصي بنجاح",
          variant: "default"
        })

        // Rafraîchir les données utilisateur pour mettre à jour l'avatar dans le header
        await refreshUserData()
      } else {
        setError(data.message || "حدث خطأ أثناء تحميل الصورة")
      }
    } catch (err: any) {
      console.error("Error uploading image:", err)
      setError(err.message || "حدث خطأ أثناء تحميل الصورة")
    } finally {
      setIsSaving(false)
      // Réinitialiser l'input de fichier
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // Fonction pour gérer les champs de type tableau (compétences, intérêts, etc.)
  const handleArrayInput = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    const value = e.target.value;
    // Convertir la chaîne en tableau en divisant par des virgules, mais préserver les espaces dans chaque élément
    const arrayValues = value.split(',').map(item => {
      // Supprimer uniquement les espaces au début et à la fin, pas entre les mots
      return item.trim();
    }).filter(Boolean);

    setUserSettings(prev => ({
      ...prev,
      [field]: arrayValues
    }));
  };

  const saveSettings = async () => {
    if (!user) return

    setIsSaving(true)
    setSuccess(null)
    setError(null)

    try {
      // Préparer les données spécifiques au type d'utilisateur
      let userData = { ...userSettings };

      // Ajouter des logs pour déboguer
      console.log("Saving user settings:", userData);
      console.log("Skills before saving:", userData.skills);

      // Vérifier que les compétences sont correctement formatées
      if (userData.skills && Array.isArray(userData.skills)) {
        // Si les compétences sont un tableau de chaînes, les convertir en objets
        if (userData.skills.length > 0 && typeof userData.skills[0] === 'string') {
          console.log("Converting skills from strings to objects");
          userData.skills = userData.skills.map((skill: string) => ({
            name: skill,
            category: 'technical', // Catégorie par défaut
            level: 'intermediate' // Niveau par défaut
          }));
        }

        // Filtrer les compétences pour supprimer celles avec le nom "Unnamed skill" ou vide
        userData.skills = userData.skills.filter((skill: any) => {
          return skill && skill.name && skill.name.trim() !== '' && skill.name !== 'Unnamed skill';
        }).map((skill: any) => {
          // S'assurer que les propriétés requises sont présentes
          return {
            name: skill.name.trim(),
            category: skill.category || 'technical',
            level: skill.level || 'intermediate'
          };
        });

        console.log("Skills after filtering:", userData.skills);
      }

      console.log("Skills after formatting:", userData.skills);

      // Sauvegarder tous les paramètres en une seule requête
      const response = await api.put(`/api/users/${user.id}/settings`, userData, true);

      if (response && response.success) {
        console.log("Settings saved successfully:", response);
        setSuccess("تم حفظ الإعدادات بنجاح");
        setTimeout(() => setSuccess(null), 3000);

        // Mettre à jour directement les données utilisateur avec les compétences sauvegardées
        const updatedUser = {
          ...user,
          ...userData,
          // S'assurer que les compétences sont incluses
          skills: userData.skills || [],
          qualifications: userData.qualifications || [],
          interests: userData.interests || []
        };

        // Mettre à jour l'utilisateur dans le contexte d'authentification
        updateUser(updatedUser);

        // Refresh user data from API
        await refreshUserData();
      } else {
        console.error("Failed to save settings:", response);
        setError("فشل في حفظ الإعدادات");
      }
    } catch (err: any) {
      console.error("Error saving settings:", err);
      setError(err.message || "حدث خطأ أثناء حفظ الإعدادات");
    } finally {
      setIsSaving(false);
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 settings-page" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">إعدادات الحساب</h1>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={async () => {
              setIsLoading(true);
              try {
                const freshUser = await refreshUserData();
                console.log("Manually refreshed user data:", freshUser);

                if (freshUser) {
                  // Mettre à jour les paramètres avec les données fraîches
                  setUserSettings(prev => ({
                    ...prev,
                    skills: freshUser.skills || [],
                    qualifications: freshUser.qualifications || [],
                    interests: freshUser.interests || []
                  }));

                  toast({
                    title: "تم التحديث",
                    description: "تم تحديث البيانات بنجاح",
                    variant: "default"
                  });
                }
              } catch (error) {
                console.error("Error refreshing data:", error);
                toast({
                  title: "خطأ",
                  description: "حدث خطأ أثناء تحديث البيانات",
                  variant: "destructive"
                });
              } finally {
                setIsLoading(false);
              }
            }}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            ) : (
              <span>تحديث البيانات</span>
            )}
          </Button>

          <Button
            className="bg-[#0a8754] hover:bg-[#097548]"
            onClick={saveSettings}
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            ) : (
              <Save size={16} className="ml-2" />
            )}
            حفظ الإعدادات
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="border border-gray-200 rounded-lg p-4 mb-8 shadow-sm" dir="rtl">
        <Tabs value={activeTab} onValueChange={setActiveTab} dir="rtl" className="min-h-[150px]">
          <TabsList className="mb-6 w-full flex justify-end">
            <TabsTrigger value="danger" className="tabs-trigger">حذف الحساب</TabsTrigger>
            <TabsTrigger value="appearance" className="tabs-trigger">المظهر</TabsTrigger>
            <TabsTrigger value="privacy" className="tabs-trigger">الخصوصية</TabsTrigger>
            <TabsTrigger value="notifications" className="tabs-trigger">الإشعارات</TabsTrigger>
            {user?.userType === 'company' && (
              <TabsTrigger value="company" className="tabs-trigger">معلومات الشركة</TabsTrigger>
            )}
            {user?.userType === 'civilSociety' && (
              <TabsTrigger value="civilSociety" className="tabs-trigger">معلومات الشريك الاجتماعي</TabsTrigger>
            )}
            {user?.userType === 'proposer' && (
              <TabsTrigger value="proposer" className="tabs-trigger">معلومات مقترح المبادرات</TabsTrigger>
            )}
            {user?.userType === 'volunteer' && (
              <TabsTrigger value="volunteer" className="tabs-trigger">معلومات المتطوع</TabsTrigger>
            )}
            <TabsTrigger value="account" className="tabs-trigger">الحساب</TabsTrigger>
          </TabsList>

        <TabsContent value="account" className="text-right">
          <Card>
            <CardHeader className="card-header">
              <CardTitle>تعديل معلومات حسابك وملفك الشخصي</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 card-content">
              <div className="flex justify-center mb-6">
                <div className="text-center">
                  <Avatar className="h-24 w-24 mx-auto mb-4">
                    <AvatarImage src={userSettings.avatar || "/placeholder.svg"} alt={userSettings.name} />
                    <AvatarFallback>{userSettings.name?.charAt(0) || "U"}</AvatarFallback>
                  </Avatar>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    className="hidden"
                    id="avatar-upload"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin ml-2" />
                        جاري التحميل...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 ml-2" />
                        تغيير الصورة
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">يفضل صورة بحجم 400×400 بكسل</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">الاسم الكامل</Label>
                  <Input
                    id="name"
                    value={userSettings.name}
                    onChange={(e) => setUserSettings({ ...userSettings, name: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">اسم المستخدم</Label>
                  <Input
                    id="username"
                    value={userSettings.username}
                    onChange={(e) => setUserSettings({ ...userSettings, username: e.target.value })}
                    dir="rtl"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={userSettings.email}
                    onChange={(e) => setUserSettings({ ...userSettings, email: e.target.value })}
                    disabled
                    dir="rtl"
                  />
                  <p className="text-sm text-gray-500">لا يمكن تغيير البريد الإلكتروني</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">الموقع</Label>
                  <Input
                    id="location"
                    value={userSettings.location}
                    onChange={(e) => setUserSettings({ ...userSettings, location: e.target.value })}
                    placeholder="مثال: الجزائر العاصمة، الجزائر"
                    dir="rtl"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">الموقع الإلكتروني</Label>
                <Input
                  id="website"
                  value={userSettings.website}
                  onChange={(e) => setUserSettings({ ...userSettings, website: e.target.value })}
                  placeholder="https://example.com"
                  dir="rtl"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">نبذة عنك</Label>
                <Textarea
                  id="bio"
                  value={userSettings.bio}
                  onChange={(e) => setUserSettings({ ...userSettings, bio: e.target.value })}
                  placeholder="اكتب نبذة قصيرة عن نفسك..."
                  rows={4}
                  dir="rtl"
                />
              </div>

              <div className="space-y-2">
                <Label>نوع الحساب</Label>
                <div className="p-3 bg-gray-50 rounded-md">
                  <p className="font-medium">{
                    user?.userType === 'volunteer' ? 'متطوع' :
                    user?.userType === 'proposer' ? 'مقترح مبادرات' :
                    user?.userType === 'company' ? 'شركة/مؤسسة' : 'مستخدم'
                  }</p>
                  <p className="text-sm text-gray-500 mt-1">لا يمكن تغيير نوع الحساب بعد التسجيل</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {user?.userType === 'volunteer' && (
          <TabsContent value="volunteer" className="text-right">
            <Card>
              <CardHeader className="card-header">
                <CardTitle>أضف معلومات عن مهاراتك واهتماماتك كمتطوع</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 card-content">
                <div className="space-y-2">
                  <Label htmlFor="qualifications">المؤهلات</Label>
                  <Input
                    id="qualifications"
                    value={userSettings.qualifications.join(', ')}
                    onChange={(e) => handleArrayInput(e, 'qualifications')}
                    placeholder="أدخل مؤهلاتك مفصولة بفواصل"
                    dir="rtl"
                  />
                  <p className="text-xs text-gray-500">مثال: بكالوريوس هندسة، ماجستير إدارة أعمال</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="skills" className="text-lg font-medium">المهارات ومستوياتها</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Info className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-sm">
                          <p>أضف مهاراتك المختلفة وحدد مستوى إتقانك لكل منها. هذا سيساعد مقترحي المبادرات في العثور عليك للمشاركة في مبادراتهم.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-md mb-4">
                    <p className="text-sm text-gray-600 mb-2">
                      تم تصنيف المهارات إلى 7 فئات رئيسية لمساعدتك في تنظيم مهاراتك بشكل أفضل. اختر الفئة المناسبة لكل مهارة وحدد مستوى إتقانك لها.
                    </p>
                  </div>

                  <Accordion type="multiple" className="w-full mb-4" dir="rtl">
                    {SKILL_CATEGORIES.map((category) => (
                      <AccordionItem key={category.id} value={category.id}>
                        <AccordionTrigger className="text-right accordion-trigger">
                          <div className="flex items-center gap-2 w-full justify-end">
                            <span>{category.arabicName}</span>
                            <span>{category.icon}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="accordion-content">
                          <div className="p-2 space-y-2">
                            <p className="text-sm text-gray-600 mb-2">{category.arabicDescription}</p>
                            <div className="flex flex-wrap gap-2 mb-4">
                              {SKILL_EXAMPLES_AR[category.id].map((example, i) => (
                                <Button
                                  key={i}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // Vérifier si cette compétence existe déjà
                                    const exists = userSettings.skills.some(
                                      skill => skill.name === example && skill.category === category.id
                                    );

                                    if (!exists) {
                                      setUserSettings({
                                        ...userSettings,
                                        skills: [
                                          ...userSettings.skills,
                                          {
                                            name: example,
                                            category: category.id,
                                            level: 'intermediate'
                                          }
                                        ]
                                      });
                                    } else {
                                      toast({
                                        title: "تنبيه",
                                        description: "هذه المهارة موجودة بالفعل في قائمتك",
                                        variant: "default"
                                      });
                                    }
                                  }}
                                >
                                  {example}
                                </Button>
                              ))}
                            </div>

                            <div className="flex items-center gap-2">
                              <Input
                                placeholder={`أضف مهارة ${category.arabicName} جديدة...`}
                                className="flex-1"
                                id={`new-skill-${category.id}`}
                                dir="rtl"
                              />
                              <Button
                                variant="outline"
                                onClick={() => {
                                  const input = document.getElementById(`new-skill-${category.id}`) as HTMLInputElement;
                                  const skillName = input.value.trim();

                                  if (skillName) {
                                    // Vérifier si cette compétence existe déjà
                                    const exists = userSettings.skills.some(
                                      skill => skill.name === skillName && skill.category === category.id
                                    );

                                    if (!exists) {
                                      setUserSettings({
                                        ...userSettings,
                                        skills: [
                                          ...userSettings.skills,
                                          {
                                            name: skillName,
                                            category: category.id,
                                            level: 'intermediate'
                                          }
                                        ]
                                      });
                                      input.value = '';
                                    } else {
                                      toast({
                                        title: "تنبيه",
                                        description: "هذه المهارة موجودة بالفعل في قائمتك",
                                        variant: "default"
                                      });
                                    }
                                  }
                                }}
                              >
                                إضافة
                              </Button>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>

                  <div className="bg-gray-50 p-4 rounded-md mb-4">
                    <h3 className="font-medium mb-2">مهاراتك الحالية</h3>
                    {userSettings.skills && userSettings.skills.length > 0 ? (
                      <div className="space-y-3">
                        {userSettings.skills.map((skill, index) => {
                          const category = SKILL_CATEGORIES.find(c => c.id === skill.category);
                          return (
                            <div key={index} className="flex items-center gap-2 bg-white p-2 rounded border">
                              <div className="flex-none w-8 h-8 flex items-center justify-center rounded-full bg-gray-100">
                                {category ? category.icon : '🔧'}
                              </div>
                              <div className="flex-1">
                                <div className="font-medium">{skill.name}</div>
                                <div className="text-xs text-gray-500">
                                  {category ? category.arabicName : 'مهارة عامة'}
                                </div>
                              </div>
                              <Select
                                value={skill.level}
                                onValueChange={(value) => {
                                  const newSkills = [...userSettings.skills];
                                  newSkills[index] = { ...newSkills[index], level: value };
                                  setUserSettings({ ...userSettings, skills: newSkills });
                                }}
                              >
                                <SelectTrigger className="w-[120px]">
                                  <SelectValue placeholder="المستوى" />
                                </SelectTrigger>
                                <SelectContent>
                                  {SKILL_LEVELS.map(level => (
                                    <SelectItem key={level.id} value={level.id}>
                                      {level.arabicName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  const newSkills = [...userSettings.skills];
                                  newSkills.splice(index, 1);
                                  setUserSettings({ ...userSettings, skills: newSkills });
                                }}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">لم تضف أي مهارات بعد</p>
                    )}
                  </div>

                  <p className="text-xs text-gray-500">أضف مهاراتك مع تحديد مستوى إتقانك لكل منها. هذا سيساعد مقترحي المبادرات في العثور عليك للمشاركة في مبادراتهم.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="interests">الاهتمامات</Label>
                  <Input
                    id="interests"
                    value={userSettings.interests.join(', ')}
                    onChange={(e) => handleArrayInput(e, 'interests')}
                    placeholder="أدخل اهتماماتك مفصولة بفواصل"
                    dir="rtl"
                  />
                  <p className="text-xs text-gray-500">مثال: البيئة، التعليم، الصحة</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="availability">التوفر</Label>
                  <Input
                    id="availability"
                    value={userSettings.availability}
                    onChange={(e) => setUserSettings({ ...userSettings, availability: e.target.value })}
                    placeholder="أدخل مدى توفرك للتطوع"
                    dir="rtl"
                  />
                  <p className="text-xs text-gray-500">مثال: عطلة نهاية الأسبوع، مساءً، 5 ساعات أسبوعياً</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {user?.userType === 'proposer' && (
          <TabsContent value="proposer" className="text-right">
            <ProposerTabContent
              userSettings={userSettings}
              setUserSettings={setUserSettings}
              handleArrayInput={handleArrayInput}
            />
          </TabsContent>
        )}

        {user?.userType === 'company' && (
          <TabsContent value="company" className="text-right">
            <CompanyTabContent
              userSettings={userSettings}
              setUserSettings={setUserSettings}
              handleArrayInput={handleArrayInput}
            />
          </TabsContent>
        )}

        {user?.userType === 'civilSociety' && (
          <TabsContent value="civilSociety" className="text-right">
            <CivilSocietyTabContent
              userSettings={userSettings}
              setUserSettings={setUserSettings}
              handleArrayInput={handleArrayInput}
            />
          </TabsContent>
        )}

        <TabsContent value="notifications" className="text-right">
          <Card>
            <CardHeader className="card-header">
              <CardTitle>تحكم في كيفية تلقي الإشعارات</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 card-content">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">قنوات الإشعارات</h3>

                <div className="flex items-center justify-between switch-container">
                  <div>
                    <Label htmlFor="email-notifications">إشعارات البريد الإلكتروني</Label>
                    <p className="text-sm text-gray-500">تلقي الإشعارات عبر البريد الإلكتروني</p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={userSettings.notifications.email}
                    onCheckedChange={(checked) =>
                      setUserSettings({
                        ...userSettings,
                        notifications: {
                          ...userSettings.notifications,
                          email: checked
                        }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between switch-container">
                  <div>
                    <Label htmlFor="browser-notifications">إشعارات المتصفح</Label>
                    <p className="text-sm text-gray-500">تلقي إشعارات المتصفح أثناء تصفح الموقع</p>
                  </div>
                  <Switch
                    id="browser-notifications"
                    checked={userSettings.notifications.browser}
                    onCheckedChange={(checked) =>
                      setUserSettings({
                        ...userSettings,
                        notifications: {
                          ...userSettings.notifications,
                          browser: checked
                        }
                      })
                    }
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">أنواع الإشعارات</h3>

                <div className="flex items-center justify-between switch-container">
                  <div>
                    <Label htmlFor="initiatives-notifications">المبادرات</Label>
                    <p className="text-sm text-gray-500">إشعارات حول المبادرات التي تتابعها</p>
                  </div>
                  <Switch
                    id="initiatives-notifications"
                    checked={userSettings.notifications.initiatives}
                    onCheckedChange={(checked) =>
                      setUserSettings({
                        ...userSettings,
                        notifications: {
                          ...userSettings.notifications,
                          initiatives: checked
                        }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between switch-container">
                  <div>
                    <Label htmlFor="comments-notifications">التعليقات</Label>
                    <p className="text-sm text-gray-500">إشعارات عند التعليق على منشوراتك</p>
                  </div>
                  <Switch
                    id="comments-notifications"
                    checked={userSettings.notifications.comments}
                    onCheckedChange={(checked) =>
                      setUserSettings({
                        ...userSettings,
                        notifications: {
                          ...userSettings.notifications,
                          comments: checked
                        }
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between switch-container">
                  <div>
                    <Label htmlFor="mentions-notifications">الإشارات</Label>
                    <p className="text-sm text-gray-500">إشعارات عند الإشارة إليك في تعليق</p>
                  </div>
                  <Switch
                    id="mentions-notifications"
                    checked={userSettings.notifications.mentions}
                    onCheckedChange={(checked) =>
                      setUserSettings({
                        ...userSettings,
                        notifications: {
                          ...userSettings.notifications,
                          mentions: checked
                        }
                      })
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="privacy" className="text-right">
          <Card>
            <CardHeader className="card-header">
              <CardTitle>تحكم في خصوصية حسابك ومعلوماتك الشخصية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 card-content">
              <div className="flex items-center justify-between switch-container">
                <div>
                  <Label htmlFor="show-email">إظهار البريد الإلكتروني</Label>
                  <p className="text-sm text-gray-500">السماح للآخرين برؤية بريدك الإلكتروني</p>
                </div>
                <Switch
                  id="show-email"
                  checked={userSettings.privacy.showEmail}
                  onCheckedChange={(checked) =>
                    setUserSettings({
                      ...userSettings,
                      privacy: {
                        ...userSettings.privacy,
                        showEmail: checked
                      }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between switch-container">
                <div>
                  <Label htmlFor="show-location">إظهار الموقع</Label>
                  <p className="text-sm text-gray-500">السماح للآخرين برؤية موقعك</p>
                </div>
                <Switch
                  id="show-location"
                  checked={userSettings.privacy.showLocation}
                  onCheckedChange={(checked) =>
                    setUserSettings({
                      ...userSettings,
                      privacy: {
                        ...userSettings.privacy,
                        showLocation: checked
                      }
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between switch-container">
                <div>
                  <Label htmlFor="show-initiatives">إظهار المبادرات</Label>
                  <p className="text-sm text-gray-500">السماح للآخرين برؤية مبادراتك</p>
                </div>
                <Switch
                  id="show-initiatives"
                  checked={userSettings.privacy.showInitiatives}
                  onCheckedChange={(checked) =>
                    setUserSettings({
                      ...userSettings,
                      privacy: {
                        ...userSettings.privacy,
                        showInitiatives: checked
                      }
                    })
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="text-right">
          <Card>
            <CardHeader className="card-header">
              <CardTitle>تخصيص مظهر التطبيق</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 card-content">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">اللغة</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant={userSettings.language === "ar" ? "default" : "outline"}
                    className={userSettings.language === "ar" ? "bg-[#0a8754]" : ""}
                    onClick={() => setUserSettings({ ...userSettings, language: "ar" })}
                  >
                    العربية
                  </Button>
                  <Button
                    variant={userSettings.language === "en" ? "default" : "outline"}
                    className={userSettings.language === "en" ? "bg-[#0a8754]" : ""}
                    onClick={() => setUserSettings({ ...userSettings, language: "en" })}
                  >
                    English
                  </Button>
                  <Button
                    variant={userSettings.language === "fr" ? "default" : "outline"}
                    className={userSettings.language === "fr" ? "bg-[#0a8754]" : ""}
                    onClick={() => setUserSettings({ ...userSettings, language: "fr" })}
                  >
                    Français
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">السمة</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant={userSettings.theme === "light" ? "default" : "outline"}
                    className={userSettings.theme === "light" ? "bg-[#0a8754]" : ""}
                    onClick={() => setUserSettings({ ...userSettings, theme: "light" })}
                  >
                    فاتح
                  </Button>
                  <Button
                    variant={userSettings.theme === "dark" ? "default" : "outline"}
                    className={userSettings.theme === "dark" ? "bg-[#0a8754]" : ""}
                    onClick={() => setUserSettings({ ...userSettings, theme: "dark" })}
                  >
                    داكن
                  </Button>
                  <Button
                    variant={userSettings.theme === "system" ? "default" : "outline"}
                    className={userSettings.theme === "system" ? "bg-[#0a8754]" : ""}
                    onClick={() => setUserSettings({ ...userSettings, theme: "system" })}
                  >
                    النظام
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="danger" className="text-right">
          <Card>
            <CardHeader className="card-header">
              <CardTitle className="text-red-600">حذف الحساب</CardTitle>
            </CardHeader>
            <CardContent className="pt-0 pb-0">
              <div className="border-t border-gray-300 pt-4 mb-4 text-gray-700 bg-red-50 p-4 rounded-md">
                حذف حسابك سيؤدي إلى إزالة جميع بياناتك بشكل نهائي. هذا الإجراء لا يمكن التراجع عنه.
              </div>
            </CardContent>
            <CardContent className="space-y-6 card-content">
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <h3 className="text-lg font-medium text-red-800 mb-2">تحذير: إجراء لا يمكن التراجع عنه</h3>
                <p className="text-red-700 mb-4">
                  عند حذف حسابك، ستتم إزالة جميع البيانات التالية بشكل نهائي:
                </p>
                <ul className="list-disc list-inside space-y-1 text-red-700 mb-4">
                  <li>معلومات ملفك الشخصي</li>
                  <li>جميع المبادرات التي أنشأتها</li>
                  <li>تعليقاتك ومشاركاتك</li>
                  <li>سجل نشاطك وتفاعلاتك</li>
                  <li>جميع البيانات المرتبطة بحسابك</li>
                </ul>
                <p className="text-red-700 font-medium">لا يمكن استرجاع هذه البيانات بعد حذف الحساب.</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Input
                    type="password"
                    placeholder="أدخل كلمة المرور لتأكيد حذف الحساب"
                    className="max-w-md"
                    id="confirm-password"
                    dir="rtl"
                  />
                </div>

                <Button
                  variant="destructive"
                  className="mt-4"
                  onClick={() => {
                    const password = (document.getElementById('confirm-password') as HTMLInputElement).value;
                    if (!password) {
                      toast({
                        title: "خطأ",
                        description: "يرجى إدخال كلمة المرور لتأكيد حذف الحساب",
                        variant: "destructive"
                      });
                      return;
                    }

                    // Show confirmation dialog
                    if (confirm("هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟ لا يمكن التراجع عن هذا الإجراء.")) {
                      // Call API to delete account
                      const deleteAccount = async () => {
                        try {
                          const response = await api.post(`/api/users/delete-account`, { password }, true);

                          if (response && response.success) {
                            toast({
                              title: "تم حذف الحساب",
                              description: "تم حذف حسابك بنجاح. سيتم تسجيل خروجك الآن.",
                              variant: "default"
                            });

                            // Logout and redirect to home page
                            setTimeout(() => {
                              logout();
                              router.push('/');
                            }, 2000);
                          } else {
                            toast({
                              title: "خطأ",
                              description: response?.message || "فشل حذف الحساب. يرجى التحقق من كلمة المرور والمحاولة مرة أخرى.",
                              variant: "destructive"
                            });
                          }
                        } catch (err: any) {
                          console.error("Error deleting account:", err);
                          toast({
                            title: "خطأ",
                            description: err.message || "حدث خطأ أثناء حذف الحساب. يرجى المحاولة مرة أخرى.",
                            variant: "destructive"
                          });
                        }
                      };

                      deleteAccount();
                    }
                  }}
                >
                  حذف حسابي نهائياً
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>

      <Toaster />
    </div>
  )
}

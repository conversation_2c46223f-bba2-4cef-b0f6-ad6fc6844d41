"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "../../../components/ui/button"
import { Input } from "../../../components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../../components/ui/card"
import { Label } from "../../../components/ui/label"
import { Checkbox } from "../../../components/ui/checkbox"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"
import { useAuth } from "../../../components/auth-provider"
import { toast } from "../../../components/ui/use-toast"
import { Toaster } from "../../../components/ui/toaster"

export default function LoginPage() {
  const router = useRouter()
  const { login } = useAuth()
  const [identifier, setIdentifier] = useState("")
  const [password, setPassword] = useState("")
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // Validation
      if (!identifier || !password) {
        setError("يرجى إدخال البريد الإلكتروني وكلمة المرور")
        return
      }

      console.log('Attempting login with:', { identifier, rememberMe })

      // Check if we're trying to log in as admin
      const isAdminLogin = identifier.toLowerCase() === 'admin'
      if (isAdminLogin) {
        console.log('Admin login detected')
      }

      // Use the login function from auth context
      const success = await login(identifier, password, rememberMe)

      console.log('Login result:', { success })

      if (success) {
        toast({
          title: "تم تسجيل الدخول بنجاح",
          description: "تم تسجيل دخولك بنجاح",
          variant: "default"
        })

        // Redirect to home page or dashboard
        router.push("/")
      }
    } catch (err: any) {
      console.error('Login error:', err)

      // Get detailed error message
      let errorMessage = err.message || "حدث خطأ أثناء تسجيل الدخول"

      // Special handling for admin login issues
      if (identifier.toLowerCase() === 'admin') {
        console.log('Admin login failed, checking specific issues')

        // Check for common admin login issues
        if (errorMessage.includes('Invalid credentials')) {
          errorMessage = "بيانات الاعتماد غير صالحة. تأكد من اسم المستخدم وكلمة المرور."
        } else if (errorMessage.includes('Failed to post data')) {
          errorMessage = "فشل الاتصال بالخادم. تأكد من تشغيل الخادم وإعادة المحاولة."
        }
      }

      setError(errorMessage)

      toast({
        title: "فشل تسجيل الدخول",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 px-4" dir="rtl">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">تسجيل الدخول</CardTitle>
          <CardDescription className="text-center">
            أدخل بريدك الإلكتروني وكلمة المرور للوصول إلى حسابك
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4 ml-2" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="identifier">البريد الإلكتروني أو اسم المستخدم</Label>
              <Input
                id="identifier"
                type="text"
                placeholder="البريد الإلكتروني أو اسم المستخدم"
                value={identifier}
                onChange={(e) => setIdentifier(e.target.value)}
                required
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Link href="/auth/forgot-password" className="text-sm text-green-600 hover:text-green-800">
                  نسيت كلمة المرور؟
                </Link>
                <Label htmlFor="password">كلمة المرور</Label>
              </div>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="text-right"
              />
            </div>
            <div className="flex items-center space-x-reverse space-x-4">
              <Checkbox
                id="remember"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              />
              <Label htmlFor="remember" className="text-sm font-normal mr-2">
                تذكرني
              </Label>
            </div>
            <Button type="submit" className="w-full bg-green-600 hover:bg-green-700" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري تسجيل الدخول...
                </>
              ) : (
                "تسجيل الدخول"
              )}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-sm">
            ليس لديك حساب؟{" "}
            <Link href="/auth/register" className="text-green-600 hover:text-green-800 font-medium">
              إنشاء حساب
            </Link>
          </div>
        </CardFooter>
      </Card>
      <Toaster />
    </div>
  )
}


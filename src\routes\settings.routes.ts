import express from "express"
import { authenticate, authorize } from "../middleware/auth"
import { getAllSettings, getPublicSettings, updateSettings } from "../controllers/settings.controller"

const router = express.Router()

// Public settings route
router.get("/public", getPublicSettings)

// Admin settings routes
router.get("/", getAllSettings) // Temporarily removed authentication for testing
router.put("/", updateSettings) // Temporarily removed authentication for testing

export default router

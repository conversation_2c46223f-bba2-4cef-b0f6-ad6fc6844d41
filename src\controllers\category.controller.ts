import { Request, Response, NextFunction } from "express"
import { Category, Initiative } from "../models"
import { createError } from "../utils/error"
import { asyncHandler } from "../utils/error"
import mongoose from "mongoose"

/**
 * Get all categories
 * @route GET /api/categories
 * @access Public
 */
export const getCategories = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const categories = await Category.find().sort({ name: 1 })

  res.status(200).json({
    success: true,
    categories: categories,
  })
})

/**
 * Get category by ID
 * @route GET /api/categories/:id
 * @access Public
 */
export const getCategoryById = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params

  // Validate category ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid category ID"))
  }

  // Find category
  const category = await Category.findById(id)
  if (!category) {
    return next(createError(404, "Category not found"))
  }

  res.status(200).json({
    success: true,
    data: category,
  })
})

/**
 * Create category
 * @route POST /api/categories
 * @access Private (Admin)
 */
export const createCategory = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { name, arabicName, description, color, icon } = req.body

  // Check if category already exists
  const existingCategory = await Category.findOne({ name })
  if (existingCategory) {
    return next(createError(400, "Category already exists"))
  }

  // Create category
  const category = new Category({
    name,
    arabicName,
    description,
    color,
    icon,
  })

  await category.save()

  res.status(201).json({
    success: true,
    data: category,
  })
})

/**
 * Update category
 * @route PUT /api/categories/:id
 * @access Private (Admin)
 */
export const updateCategory = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const { name, arabicName, description, color, icon } = req.body

  // Validate category ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid category ID"))
  }

  // Find category
  const category = await Category.findById(id)
  if (!category) {
    return next(createError(404, "Category not found"))
  }

  // Check if name is taken (if changing name)
  if (name && name !== category.name) {
    const existingCategory = await Category.findOne({ name })
    if (existingCategory) {
      return next(createError(400, "Category name already exists"))
    }
  }

  // Update category
  category.name = name || category.name
  category.arabicName = arabicName || category.arabicName
  category.description = description !== undefined ? description : category.description
  category.color = color || category.color
  category.icon = icon || category.icon

  await category.save()

  res.status(200).json({
    success: true,
    data: category,
  })
})

/**
 * Delete category
 * @route DELETE /api/categories/:id
 * @access Private (Admin)
 */
export const deleteCategory = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params

  // Validate category ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid category ID"))
  }

  // Find category
  const category = await Category.findById(id)
  if (!category) {
    return next(createError(404, "Category not found"))
  }

  // Check if category is in use
  const initiativeCount = await Initiative.countDocuments({ category: id })
  if (initiativeCount > 0) {
    return next(createError(400, "Cannot delete category that is in use"))
  }

  // Delete category
  await category.deleteOne()

  res.status(200).json({
    success: true,
    message: "Category deleted successfully",
  })
})

/**
 * Get initiatives by category
 * @route GET /api/categories/:id/initiatives
 * @access Public
 */
export const getCategoryInitiatives = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params
  const { page = 1, limit = 10, status } = req.query
  const pageNumber = parseInt(page as string)
  const limitNumber = parseInt(limit as string)

  // Validate category ID
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(createError(400, "Invalid category ID"))
  }

  // Find category
  const category = await Category.findById(id)
  if (!category) {
    return next(createError(404, "Category not found"))
  }

  // Build query
  const query: any = { category: id }

  // Add status filter if provided
  if (status && ["pending", "active", "completed", "rejected"].includes(status as string)) {
    query.status = status
  } else {
    // By default, only show active and completed initiatives
    query.status = { $in: ["active", "completed"] }
  }

  // Count total initiatives
  const total = await Initiative.countDocuments(query)

  // Get initiatives
  const initiatives = await Initiative.find(query)
    .select("title shortDescription location status supportCount commentCount viewCount mainImage createdAt author")
    .populate("author", "name username avatar")
    .sort({ createdAt: -1 })
    .skip((pageNumber - 1) * limitNumber)
    .limit(limitNumber)

  res.status(200).json({
    success: true,
    data: {
      category,
      initiatives,
    },
    pagination: {
      total,
      page: pageNumber,
      limit: limitNumber,
      pages: Math.ceil(total / limitNumber),
    },
  })
})

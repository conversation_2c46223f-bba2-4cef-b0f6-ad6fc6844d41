import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/db';
import { Initiative, Milestone, User } from '@/src/models';

export async function GET() {
  try {
    // Connect to the database
    await connectToDatabase();

    // Get initiatives with milestones
    const initiatives = await Initiative.find({ status: { $in: ['active', 'completed'] } })
      .populate('author', 'name username avatar')
      .populate('category', 'name arabicName')
      .sort('-createdAt')
      .limit(10);

    // Get milestones for each initiative
    const initiativesWithProgress = await Promise.all(
      initiatives.map(async (initiative) => {
        const milestones = await Milestone.find({ initiative: initiative._id }).sort('order');
        
        // Calculate progress based on completed milestones
        const totalMilestones = milestones.length;
        const completedMilestones = milestones.filter(m => m.isCompleted).length;
        const progress = totalMilestones > 0 ? Math.round((completedMilestones / totalMilestones) * 100) : 0;
        
        // Format dates to Arabic format
        const formatDate = (date: Date) => {
          try {
            return new Date(date).toLocaleDateString('ar-DZ', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            });
          } catch (error) {
            return '';
          }
        };

        return {
          id: initiative._id,
          title: initiative.title,
          category: initiative.category.arabicName || initiative.category.name,
          location: initiative.location,
          startDate: formatDate(initiative.createdAt),
          endDate: initiative.endDate ? formatDate(initiative.endDate) : '',
          progress: progress,
          status: initiative.status === 'completed' ? 'مكتملة' : 'جارية',
          milestones: milestones.map(m => ({
            id: m._id,
            title: m.title,
            description: m.description,
            completed: m.isCompleted,
            date: formatDate(m.targetDate),
            completedDate: m.completedDate ? formatDate(m.completedDate) : null,
          })),
        };
      })
    );

    return NextResponse.json({ 
      success: true, 
      initiatives: initiativesWithProgress 
    });
  } catch (error) {
    console.error('Error fetching progress data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch progress data' },
      { status: 500 }
    );
  }
}

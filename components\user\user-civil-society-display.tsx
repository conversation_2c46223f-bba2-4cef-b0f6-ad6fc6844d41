"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Building, Briefcase, Users, MapPin, Globe, Phone, Mail, Link as LinkIcon } from "lucide-react"

// Liste des secteurs d'activité pour les acteurs de la société civile
const ACTIVITY_SECTORS = [
  { id: "education", name: "التعليم والتدريب" },
  { id: "health", name: "الصحة والرعاية الطبية" },
  { id: "environment", name: "البيئة والتنمية المستدامة" },
  { id: "culture", name: "الثقافة والفنون" },
  { id: "social", name: "التنمية الاجتماعية" },
  { id: "humanitarian", name: "العمل الإنساني والإغاثة" },
  { id: "human_rights", name: "حقوق الإنسان" },
  { id: "women", name: "تمكين المرأة" },
  { id: "youth", name: "تنمية الشباب" },
  { id: "children", name: "رعاية الطفولة" },
  { id: "disability", name: "ذوي الاحتياجات الخاصة" },
  { id: "sports", name: "الرياضة والترفيه" },
  { id: "religious", name: "الشؤون الدينية" },
  { id: "research", name: "البحث العلمي" },
  { id: "other", name: "أخرى" }
]

// Liste des types d'organisations
const ORGANIZATION_TYPES = [
  { id: "association", name: "جمعية" },
  { id: "club", name: "نادي" },
  { id: "foundation", name: "مؤسسة" },
  { id: "organization", name: "منظمة" }
]

// Liste des portées géographiques
const SCOPE_TYPES = [
  { id: "local", name: "محلي" },
  { id: "regional", name: "إقليمي" },
  { id: "national", name: "وطني" },
  { id: "international", name: "دولي" }
]

// Liste des types de ressources
const RESOURCE_TYPES = [
  { id: "financial", name: "دعم مالي", icon: "💰" },
  { id: "space", name: "مساحات عمل", icon: "🏢" },
  { id: "equipment", name: "معدات وتجهيزات", icon: "🔧" },
  { id: "expertise", name: "خبرات ومهارات", icon: "🧠" },
  { id: "mentorship", name: "إرشاد وتوجيه", icon: "👨‍🏫" },
  { id: "networking", name: "شبكة علاقات", icon: "🔗" },
  { id: "marketing", name: "تسويق وترويج", icon: "📢" },
  { id: "logistics", name: "خدمات لوجستية", icon: "🚚" },
  { id: "volunteers", name: "متطوعين", icon: "👥" },
  { id: "training", name: "تدريب", icon: "📚" }
]

interface UserCivilSocietyDisplayProps {
  organizationName?: string
  organizationType?: string
  activitySector?: string
  scope?: string
  approvalNumber?: string
  memberCount?: string
  foundingYear?: string
  organizationDescription?: string
  contactPhone?: string
  contactPhone2?: string
  contactEmail?: string
  website?: string
  address?: string
  services?: string[]
  resources?: string[]
  customResources?: string[]
  supportDescription?: string
}

export default function UserCivilSocietyDisplay({
  organizationName,
  organizationType,
  activitySector,
  scope,
  approvalNumber,
  memberCount,
  foundingYear,
  organizationDescription,
  contactPhone,
  contactPhone2,
  contactEmail,
  website,
  address,
  services = [],
  resources = [],
  customResources = [],
  supportDescription
}: UserCivilSocietyDisplayProps) {
  // Si aucune information d'organisation n'est disponible
  if (!organizationName && !activitySector && !services.length && !resources.length) {
    return (
      <Card>
        <CardContent className="py-8 text-center text-gray-500">
          <p>لم يتم إضافة معلومات الشريك الاجتماعي بعد.</p>
        </CardContent>
      </Card>
    )
  }

  // Obtenir le nom du secteur d'activité
  const getActivitySectorName = (sectorId?: string) => {
    if (!sectorId) return ''
    const found = ACTIVITY_SECTORS.find(sector => sector.id === sectorId)
    return found ? found.name : sectorId
  }

  // Obtenir le nom du type d'organisation
  const getOrganizationTypeName = (typeId?: string) => {
    if (!typeId) return ''
    const found = ORGANIZATION_TYPES.find(type => type.id === typeId)
    return found ? found.name : typeId
  }

  // Obtenir le nom de la portée géographique
  const getScopeName = (scopeId?: string) => {
    if (!scopeId) return ''
    const found = SCOPE_TYPES.find(scope => scope.id === scopeId)
    return found ? found.name : scopeId
  }

  // Obtenir les informations sur les ressources
  const getResourceInfo = (resourceId: string) => {
    return RESOURCE_TYPES.find(resource => resource.id === resourceId) || {
      id: resourceId,
      name: resourceId,
      icon: "📦"
    }
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Informations de base */}
      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center gap-3">
            <div className="bg-green-100 p-3">
              <Building className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold">{organizationName}</h3>
              <div className="flex flex-wrap gap-4 mt-1">
                {organizationType && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Badge variant="outline">{getOrganizationTypeName(organizationType)}</Badge>
                  </div>
                )}
                {activitySector && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Briefcase className="h-4 w-4" />
                    <span>{getActivitySectorName(activitySector)}</span>
                  </div>
                )}
                {scope && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Globe className="h-4 w-4" />
                    <span>نطاق {getScopeName(scope)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {organizationDescription && (
            <div className="mt-4 text-gray-700">
              <p>{organizationDescription}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            {memberCount && (
              <div className="flex items-center gap-2 text-gray-600">
                <Badge variant="outline">عدد المنخرطين</Badge>
                <span>{memberCount}</span>
              </div>
            )}

            {foundingYear && (
              <div className="flex items-center gap-2 text-gray-600">
                <Badge variant="outline">سنة التأسيس</Badge>
                <span>{foundingYear}</span>
              </div>
            )}

            {approvalNumber && (
              <div className="flex items-center gap-2 text-gray-600">
                <Badge variant="outline">رقم الاعتماد</Badge>
                <span>{approvalNumber}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Informations de contact */}
      {(contactPhone || contactPhone2 || contactEmail || website || address) && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <h3 className="text-lg font-medium">معلومات الاتصال</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {contactPhone && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span>{contactPhone}</span>
                </div>
              )}

              {contactPhone2 && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span>{contactPhone2}</span>
                </div>
              )}

              {contactEmail && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span>{contactEmail}</span>
                </div>
              )}

              {website && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Globe className="h-4 w-4" />
                  <a href={website.startsWith('http') ? website : `https://${website}`}
                     target="_blank"
                     rel="noopener noreferrer"
                     className="text-green-600 hover:underline">
                    {website}
                  </a>
                </div>
              )}

              {address && (
                <div className="flex items-center gap-2 text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{address}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Services et ressources */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Services */}
        {services && services.length > 0 && (
          <Card>
            <CardContent className="p-6 space-y-4">
              <h3 className="text-lg font-medium">الخدمات المقدمة</h3>
              <div className="flex flex-wrap gap-2">
                {services.map((service, index) => (
                  <Badge key={index} variant="outline" className="bg-blue-50 text-blue-800">
                    {service}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Ressources */}
        {(resources.length > 0 || customResources.length > 0) && (
          <Card>
            <CardContent className="p-6 space-y-4">
              <h3 className="text-lg font-medium">الموارد المتاحة للمبادرات</h3>
              <div className="flex flex-wrap gap-2">
                {resources.map((resourceId, index) => {
                  const resource = getResourceInfo(resourceId)
                  return (
                    <Badge key={index} variant="outline" className="bg-green-50 text-green-800">
                      <span className="mr-1">{resource.icon}</span> {resource.name}
                    </Badge>
                  )
                })}

                {customResources.map((resource, index) => (
                  <Badge key={`custom-${index}`} variant="outline" className="bg-green-50 text-green-800">
                    <span className="mr-1">📦</span> {resource}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Description du support */}
      {supportDescription && (
        <Card>
          <CardContent className="p-6 space-y-4">
            <h3 className="text-lg font-medium">وصف الدعم الذي يمكن تقديمه</h3>
            <p className="text-gray-700">{supportDescription}</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { Input } from "../../../components/ui/input"
import { Textarea } from "../../../components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "../../../components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { ChevronLeft, Upload, Info, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "../../../components/ui/alert"
import { api } from "../../../lib/api"
import { uploadFile } from "../../../lib/fileUpload"
import { useAuth } from "../../../components/auth-provider"
import { toast } from "../../../components/ui/use-toast"
import { Toaster } from "../../../components/ui/toaster"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../../components/ui/tabs"
import SocialImpactTab from "../../../components/initiative/social-impact-tab"

const formSchema = z.object({
  title: z
    .string()
    .min(10, {
      message: "العنوان يجب أن يكون 10 أحرف على الأقل",
    })
    .max(100, {
      message: "العنوان يجب أن لا يتجاوز 100 حرف",
    }),
  category: z.string({
    required_error: "يرجى اختيار فئة",
  }),
  location: z.string().min(3, {
    message: "يرجى تحديد الموقع",
  }),
  shortDescription: z
    .string()
    .min(20, {
      message: "الوصف المختصر يجب أن يكون 20 حرف على الأقل",
    })
    .max(200, {
      message: "الوصف المختصر يجب أن لا يتجاوز 200 حرف",
    }),
  fullDescription: z.string().min(100, {
    message: "الوصف الكامل يجب أن يكون 100 حرف على الأقل",
  }),
  goal: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: "يرجى إدخال رقم صحيح أكبر من 0",
  }),
  requiredVolunteers: z.string().refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: "يرجى إدخال رقم صحيح (0 أو أكبر)",
  }),
  image: z.any().optional(),
  // Nouveaux champs structurants (tous optionnels)
  problem: z.string().optional(),
  solution: z.string().optional(),
  beneficiaries: z.string().optional(),
  quantitativeObjectives: z.string().optional(),
  qualitativeObjectives: z.string().optional(),
  selectedImpacts: z.array(z.any()).optional(),
})

export default function CreateInitiativePage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [categories, setCategories] = useState<any[]>([])
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      category: "",
      location: "",
      shortDescription: "",
      fullDescription: "",
      goal: "",
      requiredVolunteers: "0",
      image: undefined,
    },
  })

  // Fetch categories when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoadingCategories(true)
        const response = await api.get('/api/categories', false)

        console.log('Categories response:', response)

        // Check if response has categories property
        if (response && response.categories && Array.isArray(response.categories)) {
          setCategories(response.categories)
        } else if (response && response.data && Array.isArray(response.data)) {
          // Backward compatibility with old API
          setCategories(response.data)
        } else if (response && Array.isArray(response)) {
          // If response is an array, use it directly
          setCategories(response)
        } else {
          // Last resort: try to extract categories from any property that might be an array
          const possibleArrays = Object.values(response || {}).filter(val => Array.isArray(val))
          if (possibleArrays.length > 0) {
            // Use the first array found
            setCategories(possibleArrays[0] as any[])
          } else {
            console.error('Unexpected categories response format:', response)
            setCategories([])
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
        setError('Failed to load categories. Please try again.')
      } finally {
        setIsLoadingCategories(false)
      }
    }

    fetchCategories()

    // Redirect if user is not authenticated
    if (!isAuthenticated) {
      toast({
        title: "You need to be logged in",
        description: "Please log in to create an initiative",
        variant: "destructive"
      })
      router.push('/auth/login')
    }
  }, [isAuthenticated, router])

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>, onChange: (value: any) => void) => {
    const file = e.target.files?.[0]
    if (file) {
      onChange(file) // Update form value

      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSubmitting(true)

      // Prepare form data for image upload if needed
      let mainImage = null
      if (values.image instanceof File) {
        // Upload image first using our custom uploadFile function
        try {
          console.log('Uploading initiative image...')
          const uploadData = await uploadFile('/api/public-upload', values.image)

          if (uploadData && uploadData.file && uploadData.file.url) {
            console.log('Image uploaded successfully:', uploadData.file.url)
            mainImage = uploadData.file.url
          } else {
            throw new Error('Invalid response format from upload API')
          }
        } catch (error) {
          console.error('Error uploading image:', error)
          toast({
            title: "Image Upload Failed",
            description: error instanceof Error ? error.message : "Could not upload the image. Please try again.",
            variant: "destructive"
          })
          setIsSubmitting(false)
          return
        }
      }

      // Prepare initiative data
      const initiativeData = {
        title: values.title,
        shortDescription: values.shortDescription,
        fullDescription: values.fullDescription,
        category: values.category,
        location: values.location,
        wilaya: values.wilaya || '',
        goal: parseInt(values.goal),
        requiredVolunteers: parseInt(values.requiredVolunteers),
        mainImage: mainImage,
        images: [], // Initialize with empty array for additional images
        tags: values.tags ? values.tags.split(',').map(tag => tag.trim()) : [],
        // Nouveaux champs structurants
        problem: values.problem || '',
        solution: values.solution || '',
        beneficiaries: values.beneficiaries || '',
        quantitativeObjectives: values.quantitativeObjectives || '',
        qualitativeObjectives: values.qualitativeObjectives || '',
        selectedImpacts: values.selectedImpacts || []
      }

      // Create initiative
      const response = await api.post('/api/initiatives', initiativeData)

      toast({
        title: "Initiative Created",
        description: "Your initiative has been created successfully!",
        variant: "default"
      })

      // Redirect to the new initiative page
      router.push(`/initiatives/${response.initiative._id}`)
    } catch (error: any) {
      console.error('Error creating initiative:', error)
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create initiative. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="max-w-3xl mx-auto p-4 md:p-8">
        <Button variant="ghost" className="mb-4 flex items-center gap-2 text-[#0a8754]" onClick={() => router.back()}>
          <ChevronLeft size={16} />
          العودة
        </Button>

        <div className="bg-white rounded-lg shadow-md p-6 md:p-8">
          <h1 className="text-2xl md:text-3xl font-bold text-[#0a8754] mb-6">إنشاء مبادرة جديدة</h1>

          <Alert className="mb-6 bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-500" />
            <AlertDescription className="text-blue-700">
              قم بملء النموذج أدناه لإنشاء مبادرتك. كلما كانت المعلومات دقيقة ومفصلة، زادت فرص نجاح مبادرتك.
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs defaultValue="basic" className="w-full" dir="rtl">
                <TabsList className="grid w-full grid-cols-2 mb-6 flex-row-reverse">
                  <TabsTrigger value="basic" className="data-[state=active]:bg-[#0a8754] data-[state=active]:text-white">المعلومات الأساسية</TabsTrigger>
                  <TabsTrigger value="impact" className="data-[state=active]:bg-[#0a8754] data-[state=active]:text-white">التأثير الاجتماعي</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem className="text-right">
                        <FormLabel className="text-right">عنوان المبادرة</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل عنوان المبادرة" className="text-right" {...field} />
                        </FormControl>
                        <FormDescription className="text-right">اختر عنواناً واضحاً ومعبراً عن مبادرتك</FormDescription>
                        <FormMessage className="text-right" />
                      </FormItem>
                    )}
                  />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem className="text-right">
                      <FormLabel className="text-right">الفئة</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value} dir="rtl">
                        <FormControl>
                          <SelectTrigger className="text-right">
                            <SelectValue placeholder="اختر فئة" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent position="popper" className="text-right" align="end">
                          {isLoadingCategories ? (
                            <div className="flex items-center justify-center p-4">
                              <span>جاري تحميل الفئات...</span>
                              <Loader2 className="h-4 w-4 animate-spin ml-2" />
                            </div>
                          ) : error ? (
                            <div className="p-4 text-red-500 text-right">{error}</div>
                          ) : categories.length === 0 ? (
                            <div className="p-4 text-gray-500 text-right">لا توجد فئات متاحة</div>
                          ) : (
                            categories.map((category) => (
                              <SelectItem
                                key={category._id || category.id || Math.random().toString()}
                                value={category._id || category.id || ''}
                                className="text-right">
                                {category.arabicName || category.name || 'فئة غير معروفة'}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem className="text-right">
                      <FormLabel className="text-right">الموقع</FormLabel>
                      <FormControl>
                        <Input placeholder="المدينة أو المنطقة" className="text-right" {...field} />
                      </FormControl>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="shortDescription"
                render={({ field }) => (
                  <FormItem className="text-right">
                    <FormLabel className="text-right">وصف مختصر</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="اكتب وصفاً مختصراً للمبادرة (سيظهر في صفحة المبادرات)"
                        className="resize-none text-right"
                        dir="rtl"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-right">الحد الأقصى 200 حرف</FormDescription>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fullDescription"
                render={({ field }) => (
                  <FormItem className="text-right">
                    <FormLabel className="text-right">الوصف الكامل</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="اشرح مبادرتك بالتفصيل، أهدافها، وكيفية تنفيذها"
                        className="min-h-[200px] text-right"
                        dir="rtl"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-right">
                      اشرح بالتفصيل أهداف المبادرة، وكيفية تنفيذها، والفوائد المتوقعة منها
                    </FormDescription>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="goal"
                  render={({ field }) => (
                    <FormItem className="text-right">
                      <FormLabel className="text-right">هدف الدعم</FormLabel>
                      <FormControl>
                        <Input type="number" min="1" placeholder="عدد الداعمين المستهدف" className="text-right" {...field} />
                      </FormControl>
                      <FormDescription className="text-right">حدد عدد الداعمين الذي تحتاجه لإنجاح مبادرتك</FormDescription>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="requiredVolunteers"
                  render={({ field }) => (
                    <FormItem className="text-right">
                      <FormLabel className="text-right">عدد المتطوعين المطلوب</FormLabel>
                      <FormControl>
                        <Input type="number" min="0" placeholder="عدد المتطوعين المطلوب للمبادرة" className="text-right" {...field} />
                      </FormControl>
                      <FormDescription className="text-right">حدد عدد المتطوعين الذي تحتاجه لتنفيذ مبادرتك</FormDescription>
                      <FormMessage className="text-right" />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="image"
                render={({ field: { value, onChange, ...fieldProps } }) => (
                  <FormItem className="text-right">
                    <FormLabel className="text-right">صورة المبادرة</FormLabel>
                    <FormControl>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        {imagePreview ? (
                          <div className="mb-4">
                            <img
                              src={imagePreview || "/placeholder.svg"}
                              alt="معاينة الصورة"
                              className="max-h-[200px] mx-auto rounded-lg"
                            />
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center py-4">
                            <Upload className="h-12 w-12 text-gray-400 mb-2" />
                            <p className="text-gray-500 mb-1">اسحب وأفلت الصورة هنا أو انقر للاختيار</p>
                            <p className="text-gray-400 text-sm">PNG, JPG, JPEG (الحد الأقصى: 5MB)</p>
                          </div>
                        )}
                        <input
                          type="file"
                          id="initiative-image"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => handleImageChange(e, onChange)}
                          {...fieldProps}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById("initiative-image")?.click()}
                          className="mt-4"
                        >
                          {imagePreview ? "تغيير الصورة" : "اختر صورة"}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="text-right">اختر صورة معبرة عن مبادرتك</FormDescription>
                    <FormMessage className="text-right" />
                  </FormItem>
                )}
              />

                </TabsContent>

                <TabsContent value="impact">
                  <SocialImpactTab
                    formData={form.getValues()}
                    updateFormData={(data) => {
                      // Mettre à jour les champs du formulaire avec les nouvelles valeurs
                      Object.keys(data).forEach(key => {
                        form.setValue(key as any, data[key], { shouldValidate: true });
                      });
                    }}
                    isSubmitting={isSubmitting}
                  />
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-4 pt-4">
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  إلغاء
                </Button>
                <Button type="submit" className="bg-[#0a8754] hover:bg-[#097548]" disabled={isSubmitting}>
                  {isSubmitting ? "جاري الإرسال..." : "إنشاء المبادرة"}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
      <Toaster />
    </div>
  )
}


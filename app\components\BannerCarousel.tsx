"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "../../components/ui/button"

interface Banner {
  _id: string
  image: string
  mainText: string
  subText: string
  mainTextColor: string
  subTextColor: string
  order: number
  isActive: boolean
}

interface BannerCarouselProps {
  banners: Banner[]
  isLoading?: boolean
}

export default function BannerCarousel({ banners, isLoading = false }: BannerCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)

  // Auto-rotate banners
  useEffect(() => {
    if (banners.length <= 1) return

    const interval = setInterval(() => {
      goToNext()
    }, 6000) // Change banner every 6 seconds

    return () => clearInterval(interval)
  }, [banners, currentIndex])

  const goToPrevious = () => {
    if (isTransitioning || banners.length <= 1) return
    setIsTransitioning(true)
    setCurrentIndex((prev) => (prev === 0 ? banners.length - 1 : prev - 1))
    setTimeout(() => setIsTransitioning(false), 500) // Match transition duration
  }

  const goToNext = () => {
    if (isTransitioning || banners.length <= 1) return
    setIsTransitioning(true)
    setCurrentIndex((prev) => (prev === banners.length - 1 ? 0 : prev + 1))
    setTimeout(() => setIsTransitioning(false), 500) // Match transition duration
  }

  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentIndex) return
    setIsTransitioning(true)
    setCurrentIndex(index)
    setTimeout(() => setIsTransitioning(false), 500) // Match transition duration
  }

  if (isLoading) {
    return (
      <div className="relative h-[600px] bg-gray-200 animate-pulse">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-gray-400">Loading...</div>
        </div>
      </div>
    )
  }

  if (banners.length === 0) {
    return (
      <div className="relative h-[600px]">
        <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/40 z-10"></div>
        <img
          src="/placeholder.svg?height=600&width=1200"
          alt="Default banner"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center z-20">
          <div className="text-center text-white max-w-4xl px-4">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">منصة المبادرات المواطنة الجزائرية</h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              شارك في بناء مستقبل أفضل للجزائر من خلال اقتراح وتنفيذ مبادرات مجتمعية
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-[600px] overflow-hidden">
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/40 z-10"></div>

      {/* Banner images */}
      <div className="relative h-full">
        {banners.map((banner, index) => (
          <div
            key={banner._id}
            className={`absolute inset-0 transition-opacity duration-500 ease-in-out ${
              index === currentIndex ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
          >
            <img
              src={banner.image}
              alt={banner.mainText}
              className="w-full h-full object-cover"
              onError={(e) => {
                // Fallback to placeholder if image fails to load
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder.svg?height=600&width=1200";
              }}
            />
          </div>
        ))}
      </div>

      {/* Banner content */}
      <div className="absolute inset-0 flex items-center justify-center z-20">
        <div className="text-center max-w-4xl px-4">
          {banners.map((banner, index) => (
            <div
              key={`content-${banner._id}`}
              className={`transition-opacity duration-500 ease-in-out ${
                index === currentIndex ? "opacity-100" : "opacity-0 absolute inset-0 pointer-events-none"
              }`}
            >
              <h1
                className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6"
                style={{ color: banner.mainTextColor }}
              >
                {banner.mainText}
              </h1>
              <p
                className="text-xl md:text-2xl mb-8 opacity-90"
                style={{ color: banner.subTextColor }}
              >
                {banner.subText}
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                <Button className="bg-[#0a8754] hover:bg-[#097548] text-lg h-12 px-6">
                  <a href="/initiatives">استكشف المبادرات</a>
                </Button>
                <Button
                  variant="outline"
                  className="bg-white/10 backdrop-blur-sm border-white text-white hover:bg-white/20 text-lg h-12 px-6"
                >
                  <a href="/initiatives/create">اقترح مبادرة جديدة</a>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation arrows */}
      {banners.length > 1 && (
        <>
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 text-white bg-black/30 hover:bg-black/50 rounded-full z-30 h-12 w-12"
            onClick={goToPrevious}
            disabled={isTransitioning}
          >
            <ChevronLeft className="h-8 w-8" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 text-white bg-black/30 hover:bg-black/50 rounded-full z-30 h-12 w-12"
            onClick={goToNext}
            disabled={isTransitioning}
          >
            <ChevronRight className="h-8 w-8" />
          </Button>

          {/* Dots navigation */}
          <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-2 z-30">
            {banners.map((_, index) => (
              <button
                key={`dot-${index}`}
                className={`h-3 w-3 rounded-full transition-all ${
                  index === currentIndex
                    ? "bg-white scale-110"
                    : "bg-white/50 hover:bg-white/70"
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { Button } from "../../../../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "../../../../components/ui/card"
import { api } from "../../../../lib/api"
import { useAuth } from "../../../../components/auth-provider"
import { 
  AlertCircle, 
  Loader2, 
  Plus, 
} from "lucide-react"

export default function SimpleAdminBannersPage() {
  const router = useRouter()
  const { isAuthenticated, isAdmin } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [banners, setBanners] = useState([])
  const [error, setError] = useState("")

  // Check authentication
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    } else if (!isLoading && !isAdmin) {
      router.push("/")
    }
  }, [isAuthenticated, isAdmin, isLoading, router])

  // Fetch banners
  useEffect(() => {
    const fetchBanners = async () => {
      setIsLoading(true)
      setError("")

      try {
        const response = await api.get(`/api/banners`, true)

        if (response.success) {
          setBanners(response.banners || [])
        } else {
          setError(response.message || "Failed to fetch banners")
        }
      } catch (err: any) {
        console.error("Error fetching banners:", err)
        setError(err.message || "An error occurred while fetching banners")
      } finally {
        setIsLoading(false)
      }
    }

    if (isAuthenticated && isAdmin) {
      fetchBanners()
    }
  }, [isAuthenticated, isAdmin])

  const handleAddBanner = () => {
    alert("Add banner button clicked!")
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Simple Banner Management</CardTitle>
            <CardDescription>No dialogs or complex components</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Loading...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Simple Banner Management</CardTitle>
            <CardDescription>No dialogs or complex components</CardDescription>
          </div>
          <Button onClick={handleAddBanner}>
            <Plus className="mr-2 h-4 w-4" />
            Add Banner
          </Button>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {banners.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No banners found</p>
              <Button onClick={handleAddBanner}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Banner
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {banners.map((banner: any) => (
                <Card key={banner._id} className="overflow-hidden">
                  <div className="relative h-48">
                    <Image
                      src={banner.image || "/placeholder.svg"}
                      alt={banner.mainText}
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1 truncate">{banner.mainText}</h3>
                    <p className="text-sm text-gray-500 mb-4 line-clamp-2">{banner.subText}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

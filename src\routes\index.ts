import express from "express"
import authRoutes from "./auth.routes"
import userRoutes from "./user.routes"
import initiativeRoutes from "./initiative.routes"
import commentRoutes from "./comment.routes"
import categoryRoutes from "./category.routes"
import voteRoutes from "./vote.routes"
import updateRoutes from "./update.routes"
import milestoneRoutes from "./milestone.routes"
import communityRoutes from "./community.routes"
import notificationRoutes from "./notification.routes"
import reportRoutes from "./report.routes"
import settingsRoutes from "./settings.routes"
import adminRoutes from "./admin.routes"
import uploadRoutes from "./upload.routes"
import publicUploadRoutes from "./public-upload.routes"
import progressRoutes from "./progress.routes"
import bannerRoutes from "./banner.routes"
import statsRoutes from "./stats.routes"
import roleRoutes from "./role.routes"
import resourceRoutes from "./resource.routes"
import resourceNeedRoutes from "./resource-need.routes"
import invitationRoutes from "./invitation.routes"
import badgeRoutes from "./badge.routes"
import directUpdateRoutes from "./direct-update.routes"
import userSkillsRoutes from "./user-skills.routes"
import companyRoutes from "./company.routes"
import socialImpactRoutes from "./social-impact.routes"
import activityRoutes from "./activity.routes"

const router = express.Router()

// Health check route
router.get("/health", (req, res) => {
  res.status(200).json({ status: "ok", message: "API is running" })
})

// API routes
router.use("/auth", authRoutes)
router.use("/users", userRoutes)
router.use("/initiatives", initiativeRoutes)
router.use("/comments", commentRoutes)
router.use("/categories", categoryRoutes)
router.use("/votes", voteRoutes)
router.use("/updates", updateRoutes)
router.use("/milestones", milestoneRoutes)
router.use("/community", communityRoutes)
router.use("/notifications", notificationRoutes)
router.use("/reports", reportRoutes)
router.use("/settings", settingsRoutes)
router.use("/admin", adminRoutes)
router.use("/upload", uploadRoutes)
router.use("/public-upload", publicUploadRoutes)
router.use("/progress", progressRoutes)
router.use("/banners", bannerRoutes)
router.use("/stats", statsRoutes)
router.use("/admin/roles", roleRoutes)
router.use("/companies", companyRoutes)
router.use("/resources", resourceRoutes)
router.use("/resource-needs", resourceNeedRoutes)
router.use("/invitations", invitationRoutes)
router.use("/badges", badgeRoutes)
router.use("/direct-update", directUpdateRoutes)
router.use("/users", userSkillsRoutes)
router.use("/social-impacts", socialImpactRoutes)
router.use("/activities", activityRoutes)

export default router


const mongoose = require('mongoose');

const badgeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  arabicName: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  arabicDescription: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'award'
  },
  color: {
    type: String,
    default: '#4CAF50'
  },
  criteria: {
    type: String
  },
  category: {
    type: String,
    enum: ['participation', 'achievement', 'contribution', 'special', 'skill'],
    default: 'participation'
  },
  level: {
    type: Number,
    min: 1,
    max: 5,
    default: 1
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Badge', badgeSchema);

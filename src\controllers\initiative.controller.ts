import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Initiative, User, Category, Comment, Update, Milestone, Resource, ResourceNeed } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { ActivityService } from "../services/activity.service"

// Get all initiatives with pagination and filtering
export const getAllInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Parse sort parameter
    let sortField = "-createdAt" // Default sort by creation date (newest first)
    if (req.query.sort) {
      const sortParam = req.query.sort as string
      switch (sortParam) {
        case "newest":
          sortField = "-createdAt"
          break
        case "oldest":
          sortField = "createdAt"
          break
        case "popular":
          sortField = "-supportCount"
          break
        case "trending":
          sortField = "-commentCount"
          break
        default:
          sortField = sortParam.startsWith("-") ? sortParam : `-${sortParam}`
      }
    }

    // Build filter object
    const filter: any = {}

    // Search filter (search in title, description, and location)
    if (req.query.search) {
      const searchTerm = req.query.search as string
      filter.$or = [
        { title: { $regex: searchTerm, $options: "i" } },
        { shortDescription: { $regex: searchTerm, $options: "i" } },
        { location: { $regex: searchTerm, $options: "i" } }
      ]
    }

    // Status filter
    if (req.query.status) {
      if (req.query.status === "active") {
        filter.status = "active"
      } else if (req.query.status === "completed") {
        filter.status = "completed"
      } else if (req.query.status === "pending") {
        filter.status = "pending"
      }
    } else {
      // By default, show only active and completed initiatives
      filter.status = { $in: ["active", "completed"] }
    }

    // Category filter
    if (req.query.category && req.query.category !== "all") {
      // Try to find category by name or ID
      let category
      if (mongoose.Types.ObjectId.isValid(req.query.category as string)) {
        category = await Category.findById(req.query.category)
      } else {
        category = await Category.findOne({
          $or: [
            { name: { $regex: new RegExp(`^${req.query.category}$`, "i") } },
            { arabicName: { $regex: new RegExp(`^${req.query.category}$`, "i") } }
          ]
        })
      }

      if (category) {
        filter.category = category._id
      }
    }

    // Location filter
    if (req.query.location) {
      filter.location = { $regex: req.query.location, $options: "i" }
    }

    // Author filter (my initiatives)
    if (req.query.author === "me" && req.user) {
      filter.author = req.user.id
    } else if (req.query.author && mongoose.Types.ObjectId.isValid(req.query.author as string)) {
      filter.author = req.query.author
    }

    // Public initiatives only (unless filtering by author)
    if (!req.query.author) {
      filter.isPublic = true
    }

    // Get initiatives
    const initiatives = await Initiative.find(filter)
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort(sortField)
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Initiative.countDocuments(filter)

    // Return initiatives with pagination metadata
    res.status(200).json({
      success: true,
      initiatives,
      pagination: {
        total,
        count: initiatives.length,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    next(error)
  }
}

// Get initiative by ID
export const getInitiativeById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Get initiative
    const initiative = await Initiative.findById(id)
      .populate("author", "name username avatar bio")
      .populate("category", "name arabicName color")
      .populate("supporters", "name username avatar")
      .populate({
        path: "votingOptions",
        select: "title description voteCount",
      })

    // Check if initiative exists
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Increment view count
    initiative.viewCount += 1
    await initiative.save()

    // Get comments count
    const commentCount = await Comment.countDocuments({ initiative: id, isReply: false })

    // Get updates
    const updates = await Update.find({ initiative: id })
      .populate("author", "name username avatar")
      .sort("-createdAt")
      .limit(5)

    // Get milestones
    const milestones = await Milestone.find({ initiative: id }).sort("order")

    // Get resource needs (top 5 by priority)
    const resourceNeeds = await ResourceNeed.find({ initiative: id })
      .sort({ priority: -1, createdAt: -1 })
      .limit(5)

    // Get resources (top 5 most recent)
    const resources = await Resource.find({ initiative: id })
      .populate("provider", "name username avatar userType")
      .sort({ createdAt: -1 })
      .limit(5)

    // Return initiative with related data
    res.status(200).json({
      success: true,
      initiative,
      commentCount,
      updates,
      milestones,
      resourceNeeds,
      resources,
    })
  } catch (error) {
    next(error)
  }
}

// Create new initiative
export const createInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id
    const {
      title,
      shortDescription,
      fullDescription,
      category,
      location,
      wilaya,
      goal,
      mainImage,
      images,
      tags,
      budget,
      requiredVolunteers,
      // Nouveaux champs structurants
      problem,
      solution,
      beneficiaries,
      quantitativeObjectives,
      qualitativeObjectives,
      socialImpacts,
      selectedImpacts,
    } = req.body

    // Validate category
    const categoryExists = await Category.findById(category)
    if (!categoryExists) {
      return next(createError(400, "Invalid category"))
    }

    // Create initiative
    const newInitiative = new Initiative({
      title,
      shortDescription,
      fullDescription,
      category,
      author: userId,
      location,
      wilaya,
      goal,
      mainImage: mainImage || "/placeholder.svg?height=600&width=1200",
      images: images || [],
      tags: tags || [],
      budget,
      requiredVolunteers,
      // Nouveaux champs structurants
      problem,
      solution,
      beneficiaries,
      quantitativeObjectives,
      qualitativeObjectives,
      socialImpacts: socialImpacts || [],
      selectedImpacts: selectedImpacts || [],
      status: "active", // Set status to active so initiatives are visible immediately
    })

    // Save initiative
    await newInitiative.save()

    // Update user's initiatives array
    await User.findByIdAndUpdate(userId, {
      $push: { initiatives: newInitiative._id },
    })

    // Increment category initiative count
    await Category.findByIdAndUpdate(category, {
      $inc: { initiativeCount: 1 },
    })

    // Enregistrer l'activité
    await ActivityService.initiativeActivity(
      userId,
      "create",
      newInitiative._id.toString(),
      newInitiative.title,
      { category: category.toString() }
    )

    // Return success response
    res.status(201).json({
      success: true,
      message: "Initiative created successfully",
      initiative: newInitiative,
    })
  } catch (error) {
    next(error)
  }
}

// Update initiative
export const updateInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id
    const updates = req.body

    console.log('[Initiative Update] Request received:', {
      initiativeId: id,
      userId,
      updateFields: Object.keys(updates),
      hasSocialImpacts: !!updates.socialImpacts,
      hasSelectedImpacts: !!updates.selectedImpacts
    });

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      console.log('[Initiative Update] Invalid initiative ID:', id);
      return next(createError(400, "Invalid initiative ID"))
    }

    // Get initiative
    const initiative = await Initiative.findById(id)

    // Check if initiative exists
    if (!initiative) {
      console.log('[Initiative Update] Initiative not found:', id);
      return next(createError(404, "Initiative not found"))
    }

    console.log('[Initiative Update] Found initiative:', {
      id: initiative._id,
      title: initiative.title,
      author: initiative.author,
      currentUserId: userId
    });

    // Check if user is the author or an admin
    if (initiative.author.toString() !== userId && req.user.role !== "admin") {
      console.log('[Initiative Update] Unauthorized update attempt:', {
        initiativeAuthor: initiative.author.toString(),
        currentUser: userId,
        userRole: req.user.role
      });
      return next(createError(403, "You are not authorized to update this initiative"))
    }

    // If category is being updated, validate it
    if (updates.category) {
      const categoryExists = await Category.findById(updates.category)
      if (!categoryExists) {
        console.log('[Initiative Update] Invalid category:', updates.category);
        return next(createError(400, "Invalid category"))
      }

      // If category is changing, update category counts
      if (updates.category.toString() !== initiative.category.toString()) {
        console.log('[Initiative Update] Category changing from', initiative.category, 'to', updates.category);
        // Decrement old category count
        await Category.findByIdAndUpdate(initiative.category, {
          $inc: { initiativeCount: -1 },
        })

        // Increment new category count
        await Category.findByIdAndUpdate(updates.category, {
          $inc: { initiativeCount: 1 },
        })
      }
    }

    // Check and sanitize social impacts data
    if (updates.socialImpacts) {
      console.log('[Initiative Update] Processing social impacts:', JSON.stringify(updates.socialImpacts));

      // Ensure each social impact has valid category and impacts
      updates.socialImpacts = updates.socialImpacts.filter(impact => {
        if (!impact.category || !mongoose.Types.ObjectId.isValid(impact.category)) {
          console.log('[Initiative Update] Invalid social impact category:', impact.category);
          return false;
        }

        if (!impact.impacts || !Array.isArray(impact.impacts)) {
          console.log('[Initiative Update] Invalid social impact impacts array:', impact);
          impact.impacts = [];
        } else {
          // Filter out invalid impact IDs
          impact.impacts = impact.impacts.filter(impactId =>
            impactId && mongoose.Types.ObjectId.isValid(impactId)
          );
        }

        return true;
      });
    }

    // Check and sanitize selected impacts data
    if (updates.selectedImpacts) {
      console.log('[Initiative Update] Processing selected impacts:',
        updates.selectedImpacts.length, 'items');

      // Ensure each selected impact has required fields
      updates.selectedImpacts = updates.selectedImpacts.filter(impact => {
        if (!impact._id || !mongoose.Types.ObjectId.isValid(impact._id)) {
          console.log('[Initiative Update] Invalid selected impact ID:', impact._id);
          return false;
        }

        // Ensure required fields exist
        if (!impact.name) impact.name = 'Unnamed impact';
        if (!impact.arabicName) impact.arabicName = 'تأثير غير مسمى';
        if (impact.isActive === undefined) impact.isActive = true;

        return true;
      });
    }

    console.log('[Initiative Update] Applying updates with sanitized data');

    // Update initiative
    const updatedInitiative = await Initiative.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true },
    )
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")

    console.log('[Initiative Update] Update successful for initiative:', id);

    // Enregistrer l'activité avec les champs mis à jour
    const updatedFieldsArray = Object.keys(updates);
    console.log('[Initiative Update] Updated fields:', updatedFieldsArray);

    await ActivityService.initiativeActivity(
      userId,
      "update",
      id,
      updatedInitiative.title,
      {
        category: updatedInitiative.category.toString(),
        updatedFields: updatedFieldsArray
      }
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "Initiative updated successfully",
      initiative: updatedInitiative,
    })
  } catch (error) {
    console.error('[Initiative Update] Error updating initiative:', error);
    next(error);
  }
}

// Delete initiative
export const deleteInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Get initiative
    const initiative = await Initiative.findById(id)

    // Check if initiative exists
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the author or an admin
    if (initiative.author.toString() !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to delete this initiative"))
    }

    // Delete related data
    await Comment.deleteMany({ initiative: id })
    await Update.deleteMany({ initiative: id })
    await Milestone.deleteMany({ initiative: id })
    await Resource.deleteMany({ initiative: id })
    await ResourceNeed.deleteMany({ initiative: id })

    // Remove initiative from user's initiatives array
    await User.findByIdAndUpdate(initiative.author, {
      $pull: { initiatives: id },
    })

    // Remove initiative from supporters' supportedInitiatives array
    await User.updateMany({ supportedInitiatives: id }, { $pull: { supportedInitiatives: id } })

    // Decrement category initiative count
    await Category.findByIdAndUpdate(initiative.category, {
      $inc: { initiativeCount: -1 },
    })

    // Enregistrer l'activité avant de supprimer l'initiative
    await ActivityService.initiativeActivity(
      userId,
      "delete",
      id,
      initiative.title,
      { category: initiative.category.toString() }
    )

    // Delete initiative
    await Initiative.findByIdAndDelete(id)

    // Return success response
    res.status(200).json({
      success: true,
      message: "Initiative deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Support initiative
export const supportInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Get initiative
    const initiative = await Initiative.findById(id)

    // Check if initiative exists
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user has already supported the initiative
    const user = await User.findById(userId)
    if (user.hasSupportedInitiative(initiative._id)) {
      return next(createError(400, "You have already supported this initiative"))
    }

    // Add user to supporters and increment supportCount
    initiative.supporters.push(userId)
    initiative.supportCount += 1
    await initiative.save()

    // Add initiative to user's supportedInitiatives
    user.supportedInitiatives.push(initiative._id)
    await user.save()

    // Create notification for initiative author
    if (initiative.author.toString() !== userId) {
      await createNotification({
        recipient: initiative.author,
        sender: userId,
        type: "support",
        content: `${user.name} has supported your initiative "${initiative.title}"`,
        relatedInitiative: initiative._id,
        link: `/initiatives/${initiative._id}`,
      })
    }

    // Enregistrer l'activité
    await ActivityService.supportActivity(
      userId,
      "support",
      initiative._id.toString(),
      initiative.title
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "Initiative supported successfully",
      supportCount: initiative.supportCount,
    })
  } catch (error) {
    next(error)
  }
}

// Unsupport initiative
export const unsupportInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Get initiative
    const initiative = await Initiative.findById(id)

    // Check if initiative exists
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user has supported the initiative
    const user = await User.findById(userId)
    if (!user.hasSupportedInitiative(initiative._id)) {
      return next(createError(400, "You have not supported this initiative"))
    }

    // Remove user from supporters and decrement supportCount
    initiative.supporters = initiative.supporters.filter((supporterId) => supporterId.toString() !== userId)
    initiative.supportCount -= 1
    await initiative.save()

    // Remove initiative from user's supportedInitiatives
    user.supportedInitiatives = user.supportedInitiatives.filter((initiativeId) => initiativeId.toString() !== id)
    await user.save()

    // Enregistrer l'activité
    await ActivityService.supportActivity(
      userId,
      "unsupport",
      initiative._id.toString(),
      initiative.title
    )

    // Return success response
    res.status(200).json({
      success: true,
      message: "Initiative unsupported successfully",
      supportCount: initiative.supportCount,
    })
  } catch (error) {
    next(error)
  }
}

// Get featured initiatives
export const getFeaturedInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get featured initiatives (promoted or most supported)
    const featuredInitiatives = await Initiative.find({
      status: "active",
      isPublic: true,
      $or: [{ isPromoted: true }, { supportCount: { $gte: 50 } }],
    })
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort("-supportCount")
      .limit(6)

    // Return featured initiatives
    res.status(200).json({
      success: true,
      count: featuredInitiatives.length,
      initiatives: featuredInitiatives,
    })
  } catch (error) {
    next(error)
  }
}

// Search initiatives
export const searchInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { q } = req.query
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    if (!q) {
      return next(createError(400, "Search query is required"))
    }

    // Search initiatives
    const initiatives = await Initiative.find({
      $text: { $search: q as string },
      isPublic: true,
      status: { $in: ["active", "completed"] },
    })
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort({ score: { $meta: "textScore" } })
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Initiative.countDocuments({
      $text: { $search: q as string },
      isPublic: true,
      status: { $in: ["active", "completed"] },
    })

    // Return search results
    res.status(200).json({
      success: true,
      count: initiatives.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      initiatives,
    })
  } catch (error) {
    next(error)
  }
}

// Get initiatives by category
export const getInitiativesByCategory = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { categoryId } = req.params
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Validate category ID
    if (!mongoose.Types.ObjectId.isValid(categoryId)) {
      return next(createError(400, "Invalid category ID"))
    }

    // Check if category exists
    const category = await Category.findById(categoryId)
    if (!category) {
      return next(createError(404, "Category not found"))
    }

    // Get initiatives by category
    const initiatives = await Initiative.find({
      category: categoryId,
      isPublic: true,
      status: { $in: ["active", "completed"] },
    })
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort("-createdAt")
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Initiative.countDocuments({
      category: categoryId,
      isPublic: true,
      status: { $in: ["active", "completed"] },
    })

    // Return initiatives
    res.status(200).json({
      success: true,
      count: initiatives.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      category,
      initiatives,
    })
  } catch (error) {
    next(error)
  }
}

// Get initiatives by status
export const getInitiativesByStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { status } = req.params
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Validate status
    const validStatuses = ["pending", "active", "completed", "rejected"]
    if (!validStatuses.includes(status)) {
      return next(createError(400, "Invalid status"))
    }

    // Get initiatives by status
    const initiatives = await Initiative.find({
      status,
      isPublic: true,
    })
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort("-createdAt")
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Initiative.countDocuments({
      status,
      isPublic: true,
    })

    // Return initiatives
    res.status(200).json({
      success: true,
      count: initiatives.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      initiatives,
    })
  } catch (error) {
    next(error)
  }
}

// Get initiatives by user
export const getInitiativesByUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Validate user ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Check if user exists
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Get initiatives by user
    const initiatives = await Initiative.find({
      author: userId,
      isPublic: true,
    })
      .populate("category", "name arabicName color")
      .sort("-createdAt")
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Initiative.countDocuments({
      author: userId,
      isPublic: true,
    })

    // Return initiatives
    res.status(200).json({
      success: true,
      count: initiatives.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      user: {
        id: user._id,
        name: user.name,
        username: user.username,
        avatar: user.avatar,
      },
      initiatives,
    })
  } catch (error) {
    next(error)
  }
}

// Get initiative statistics
export const getInitiativeStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get counts by status
    const statusCounts = await Initiative.aggregate([
      { $match: { isPublic: true } },
      { $group: { _id: "$status", count: { $sum: 1 } } },
    ])

    // Get counts by category
    const categoryCounts = await Initiative.aggregate([
      { $match: { isPublic: true } },
      { $group: { _id: "$category", count: { $sum: 1 } } },
    ])

    // Get categories
    const categories = await Category.find()

    // Format category counts
    const formattedCategoryCounts = categories.map((category) => {
      const count = categoryCounts.find((c) => c._id.toString() === category._id.toString())
      return {
        id: category._id,
        name: category.name,
        arabicName: category.arabicName,
        color: category.color,
        count: count ? count.count : 0,
      }
    })

    // Format status counts
    const formattedStatusCounts = {
      pending: 0,
      active: 0,
      completed: 0,
      rejected: 0,
    }

    statusCounts.forEach((status) => {
      if (formattedStatusCounts.hasOwnProperty(status._id)) {
        formattedStatusCounts[status._id] = status.count
      }
    })

    // Get total initiatives
    const totalInitiatives = await Initiative.countDocuments({ isPublic: true })

    // Get total supporters
    const totalSupporters = await Initiative.aggregate([
      { $match: { isPublic: true } },
      { $group: { _id: null, count: { $sum: "$supportCount" } } },
    ])

    // Return statistics
    res.status(200).json({
      success: true,
      stats: {
        totalInitiatives,
        totalSupporters: totalSupporters.length > 0 ? totalSupporters[0].count : 0,
        statusCounts: formattedStatusCounts,
        categoryCounts: formattedCategoryCounts,
      },
    })
  } catch (error) {
    next(error)
  }
}


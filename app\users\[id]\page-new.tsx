"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, MapPin, Calendar, Award, Edit, Settings, Package, User, Briefcase, Building } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { api } from "@/lib/api"
import UserResourcesTab from "@/components/user/user-resources-tab"
import UserSkillsDisplay from "@/components/user/user-skills-display"
import UserProposerDisplay from "@/components/user/user-proposer-display"
import UserCompanyDisplay from "@/components/user/user-company-display"

export default function UserProfilePage() {
  const params = useParams()
  const id = params.id as string

  const [user, setUser] = useState<any>(null)
  const [initiatives, setInitiatives] = useState<any[]>([])
  const [supportedInitiatives, setSupportedInitiatives] = useState<any[]>([])
  const [activities, setActivities] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  // Use the auth context
  const { user: currentUser, isAuthenticated } = useAuth()
  const isOwnProfile = isAuthenticated && currentUser && currentUser.id === id

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/users/${id}`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user profile")
        }

        setUser(data.user || data.data) // Handle both response formats
      } catch (err: any) {
        console.error('Error fetching user profile:', err)
        setError(err.message || "An error occurred")
      }
    }

    const fetchUserInitiatives = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/initiatives/user/${id}`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user initiatives")
        }

        setInitiatives(data.initiatives || [])
      } catch (err: any) {
        console.error('Error fetching user initiatives:', err)
        setError(err.message || "An error occurred")
      }
    }

    const fetchSupportedInitiatives = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/users/${id}/supported-initiatives`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch supported initiatives")
        }

        setSupportedInitiatives(data.initiatives || [])
      } catch (err: any) {
        console.error('Error fetching supported initiatives:', err)
        setError(err.message || "An error occurred")
      }
    }

    const fetchUserActivities = async () => {
      try {
        // Use the API utility for consistent handling
        const data = await api.get(`/api/users/${id}/activities`, false)

        if (!data || !data.success) {
          throw new Error(data?.error?.message || "Failed to fetch user activities")
        }

        setActivities(data.activities || [])
      } catch (err: any) {
        console.error('Error fetching user activities:', err)
        setError(err.message || "An error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserProfile()
    fetchUserInitiatives()
    fetchSupportedInitiatives()
    fetchUserActivities()
  }, [id])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  // Add a helper function to safely access user properties
  const getUserProperty = (path: string, defaultValue: any = "") => {
    if (!user) return defaultValue

    const parts = path.split('.')
    let value = user as any

    for (const part of parts) {
      if (value === null || value === undefined) return defaultValue
      value = value[part]
    }

    return value !== null && value !== undefined ? value : defaultValue
  }

  // Format date safely
  const safeFormatDate = (dateString: string | undefined) => {
    if (!dateString) return "Unknown date"
    try {
      return formatDate(dateString)
    } catch (e) {
      return "Unknown date"
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading profile...</span>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "User not found"}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Link href="/">
            <Button variant="outline">Back to Home</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mb-8">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center">
              <Avatar className="h-32 w-32">
                <AvatarImage src={getUserProperty('avatar')} alt={getUserProperty('name')} />
                <AvatarFallback className="text-4xl">{getUserProperty('name', '?').charAt(0)}</AvatarFallback>
              </Avatar>

              {isOwnProfile && (
                <div className="mt-4 flex gap-2">
                  <Link href="/settings">
                    <Button variant="outline" className="flex items-center gap-1">
                      <Settings className="h-4 w-4" />
                      إعدادات
                    </Button>
                  </Link>
                </div>
              )}
            </div>

            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-1">{getUserProperty('name', 'User')}</h1>
              <p className="text-gray-500 mb-4">@{getUserProperty('username', 'username')}</p>

              <div className="flex flex-wrap gap-4 mb-4">
                {getUserProperty('location') && (
                  <div className="flex items-center gap-1 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{getUserProperty('location')}</span>
                  </div>
                )}
                <div className="flex items-center gap-1 text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>Joined {safeFormatDate(getUserProperty('joinDate'))}</span>
                </div>
              </div>

              {getUserProperty('bio') && <p className="text-gray-700 mb-4">{getUserProperty('bio')}</p>}

              {getUserProperty('badges', []).length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {getUserProperty('badges', []).map((badge: any) => (
                    <Badge key={badge._id || badge.id || Math.random()} className="bg-green-600 hover:bg-green-700 flex items-center gap-1">
                      <Award className="h-3 w-3" />
                      {badge.name || 'Badge'}
                    </Badge>
                  ))}
                </div>
              )}

              <div className="flex flex-wrap gap-6 mt-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{initiatives.length}</p>
                  <p className="text-sm text-gray-500">المبادرات</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{supportedInitiatives.length}</p>
                  <p className="text-sm text-gray-500">المبادرات المدعومة</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{activities.length}</p>
                  <p className="text-sm text-gray-500">النشاطات</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    <Package className="h-5 w-5 inline-block mr-1" />
                  </p>
                  <p className="text-sm text-gray-500">الموارد</p>
                </div>
                <div className="text-center">
                  {user.userType === 'volunteer' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <User className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">متطوع</p>
                    </>
                  )}
                  {user.userType === 'proposer' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <Briefcase className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">مقترح مبادرات</p>
                    </>
                  )}
                  {user.userType === 'company' && (
                    <>
                      <p className="text-2xl font-bold text-green-600">
                        <Building className="h-5 w-5 inline-block mr-1" />
                      </p>
                      <p className="text-sm text-gray-500">شركة</p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="initiatives" className="mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="initiatives">المبادرات</TabsTrigger>
          <TabsTrigger value="supported">المبادرات المدعومة</TabsTrigger>
          <TabsTrigger value="profile">الملف الشخصي</TabsTrigger>
          <TabsTrigger value="resources">الموارد</TabsTrigger>
          <TabsTrigger value="activity">النشاطات</TabsTrigger>
        </TabsList>

        <TabsContent value="initiatives">
          <h2 className="text-2xl font-bold mb-4">المبادرات</h2>

          {initiatives.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لم يتم إنشاء أي مبادرات بعد.</p>
                {isOwnProfile && (
                  <Link href="/initiatives/create">
                    <Button className="mt-4 bg-green-600 hover:bg-green-700">إنشاء أول مبادرة</Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {initiatives.map((initiative) => (
                <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-[160px] overflow-hidden">
                    <Image
                      src={initiative.mainImage || "/placeholder.svg"}
                      alt={initiative.title}
                      width={400}
                      height={160}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
                      <Badge
                        variant="outline"
                        className={
                          initiative.status === "active"
                            ? "text-green-600 border-green-200 bg-green-50"
                            : initiative.status === "completed"
                              ? "text-blue-600 border-blue-200 bg-blue-50"
                              : "text-gray-600 border-gray-200 bg-gray-50"
                        }
                      >
                        {initiative.status.charAt(0).toUpperCase() + initiative.status.slice(1)}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg mt-2 line-clamp-1">
                      <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600 transition-colors">
                        {initiative.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 line-clamp-2 text-sm mb-4">{initiative.shortDescription}</p>
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{initiative.supportCount} supporters</span>
                      <span>{initiative.commentCount} comments</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="supported">
          <h2 className="text-2xl font-bold mb-4">المبادرات المدعومة</h2>

          {supportedInitiatives.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لم يتم دعم أي مبادرات بعد.</p>
                <Link href="/initiatives">
                  <Button className="mt-4 bg-green-600 hover:bg-green-700">تصفح المبادرات</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {supportedInitiatives.map((initiative) => (
                <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-[160px] overflow-hidden">
                    <Image
                      src={initiative.mainImage || "/placeholder.svg"}
                      alt={initiative.title}
                      width={400}
                      height={160}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge style={{ backgroundColor: initiative.category.color }}>{initiative.category.name}</Badge>
                      <Badge
                        variant="outline"
                        className={
                          initiative.status === "active"
                            ? "text-green-600 border-green-200 bg-green-50"
                            : initiative.status === "completed"
                              ? "text-blue-600 border-blue-200 bg-blue-50"
                              : "text-gray-600 border-gray-200 bg-gray-50"
                        }
                      >
                        {initiative.status.charAt(0).toUpperCase() + initiative.status.slice(1)}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg mt-2 line-clamp-1">
                      <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600 transition-colors">
                        {initiative.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 line-clamp-2 text-sm mb-4">{initiative.shortDescription}</p>
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{initiative.supportCount} supporters</span>
                      <span>{initiative.commentCount} comments</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="profile">
          <h2 className="text-2xl font-bold mb-4">الملف الشخصي</h2>
          
          {user.userType === 'volunteer' && (
            <UserSkillsDisplay 
              skills={user.skills || []} 
              qualifications={user.qualifications || []} 
              interests={user.interests || []}
              userType={user.userType}
            />
          )}
          
          {user.userType === 'proposer' && (
            <UserProposerDisplay 
              ideaDescription={user.ideaDescription}
              objectives={user.objectives}
              needs={user.needs}
              qualifications={user.qualifications || []}
              skills={user.skills || []}
              interests={user.interests || []}
            />
          )}
          
          {user.userType === 'company' && (
            <UserCompanyDisplay 
              companyName={user.companyName}
              industry={user.industry}
              customIndustry={user.customIndustry}
              companyDescription={user.companyDescription}
              employeeCount={user.employeeCount}
              foundingYear={user.foundingYear}
              contactPhone={user.contactPhone}
              contactEmail={user.contactEmail}
              website={user.website}
              address={user.address}
              services={user.services || []}
              resources={user.resources || []}
              customResources={user.customResources || []}
              supportDescription={user.supportDescription}
            />
          )}
        </TabsContent>

        <TabsContent value="resources">
          <UserResourcesTab userId={id} isOwnProfile={isOwnProfile} />
        </TabsContent>

        <TabsContent value="activity">
          <h2 className="text-2xl font-bold mb-4">النشاطات الأخيرة</h2>

          {activities.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center text-gray-500">
                <p>لا توجد نشاطات حديثة.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {activities.map((activity) => (
                <Card key={activity._id}>
                  <CardContent className="py-4">
                    <div className="flex items-start gap-4">
                      <div className="bg-green-100 rounded-full p-2 text-green-600">
                        {activity.type === "initiative" && <Award className="h-5 w-5" />}
                        {activity.type === "support" && <Award className="h-5 w-5" />}
                        {activity.type === "comment" && <Award className="h-5 w-5" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-700">{activity.content}</p>
                        {activity.relatedInitiative && (
                          <Link
                            href={`/initiatives/${activity.relatedInitiative._id}`}
                            className="text-sm text-green-600 hover:underline"
                          >
                            {activity.relatedInitiative.title}
                          </Link>
                        )}
                        <p className="text-xs text-gray-500 mt-1">{formatDate(activity.date)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

import type mongoose from "mongoose"
import { Schema, type Document } from "mongoose"
import { IRole } from "./role.model"
import { IPermission } from "./permission.model"

export interface IUser extends Document {
  username: string
  email: string
  password: string
  name: string
  avatar?: string
  bio?: string
  location?: string
  joinDate: Date
  role: mongoose.Types.ObjectId | IRole
  userType: "volunteer" | "proposer" | "company" | "civilSociety" // Type d'utilisateur
  directPermissions?: mongoose.Types.ObjectId[] | IPermission[]
  hasPermission: (permissionCode: string) => boolean
  isVerified: boolean
  isBlocked: boolean
  badges: mongoose.Types.ObjectId[]
  initiatives: mongoose.Types.ObjectId[]
  supportedInitiatives: mongoose.Types.ObjectId[]
  votedInitiatives: {
    initiative: mongoose.Types.ObjectId
    option: mongoose.Types.ObjectId
    date: Date
  }[]
  // Champs spécifiques pour les volontaires
  qualifications?: string[]
  skills?: {
    name: string,
    category: 'cognitive' | 'technical' | 'interpersonal' | 'organizational' | 'digital' | 'linguistic' | 'transversal',
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  }[]
  interests?: string[]
  availability?: string
  // Champs spécifiques pour les proposeurs d'idées
  ideaDescription?: string
  objectives?: string
  needs?: string
  // Champs spécifiques pour les entreprises
  companyName?: string
  industry?: string
  customIndustry?: string
  companyDescription?: string
  employeeCount?: string
  foundingYear?: string
  commerceRegisterNumber?: string // Numéro de registre de commerce
  contactPhone?: string
  contactEmail?: string
  website?: string
  address?: string
  services?: string[]
  resources?: string[]
  customResources?: string[]
  supportDescription?: string
  socialLinks?: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
  }
  // Champs spécifiques pour les acteurs de la société civile
  organizationName?: string
  organizationType?: "association" | "club" | "foundation" | "organization"
  activitySector?: string
  scope?: "local" | "regional" | "national" | "international"
  approvalNumber?: string // Numéro d'agrément
  memberCount?: string // Nombre de membres
  foundingYear?: string // Année de fondation
  organizationDescription?: string // Description de l'organisation
  contactPhone?: string // Numéro de téléphone
  contactPhone2?: string // Deuxième numéro de téléphone
  contactEmail?: string // Email de contact
  website?: string // Site web
  address?: string // Adresse
  services?: string[] // Services offerts
  resources?: string[] // Ressources disponibles
  customResources?: string[] // Autres ressources
  supportDescription?: string // Description du soutien
  mainContact?: {
    name?: string
    phone?: string
    email?: string
  }
  settings?: {
    notifications?: {
      email?: boolean
      browser?: boolean
      initiatives?: boolean
      comments?: boolean
      mentions?: boolean
    }
    privacy?: {
      showEmail?: boolean
      showLocation?: boolean
      showInitiatives?: boolean
    }
    language?: string
    theme?: string
    website?: string
  }
  refreshToken?: string
  resetPasswordToken?: string
  resetPasswordExpires?: Date
  verificationToken?: string
  lastLogin?: Date
  hasVotedOnInitiative: (initiativeId: mongoose.Types.ObjectId) => boolean
  hasSupportedInitiative: (initiativeId: mongoose.Types.ObjectId) => boolean
}

const userSchema = new Schema<IUser>(
  {
    username: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      minlength: 3,
      maxlength: 30,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, "Please fill a valid email address"],
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
    },
    name: {
      type: String,
      required: true,
    },
    avatar: {
      type: String,
      default: "/placeholder.svg?height=100&width=100",
    },
    bio: {
      type: String,
      maxlength: 500,
    },
    location: {
      type: String,
    },
    joinDate: {
      type: Date,
      default: Date.now,
    },
    role: {
      type: Schema.Types.ObjectId,
      ref: "Role",
      required: true,
    },
    userType: {
      type: String,
      enum: ["volunteer", "proposer", "company", "civilSociety"],
      default: "volunteer",
      required: true,
    },
    // Champs spécifiques pour les volontaires
    qualifications: [String],
    skills: [
      {
        name: { type: String, required: true },
        category: {
          type: String,
          enum: ['cognitive', 'technical', 'interpersonal', 'organizational', 'digital', 'linguistic', 'transversal'],
          required: true
        },
        level: {
          type: String,
          enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          default: 'beginner'
        }
      }
    ],
    interests: [String],
    availability: String,
    // Champs spécifiques pour les proposeurs d'idées
    ideaDescription: String,
    objectives: String,
    needs: String,
    // Champs spécifiques pour les entreprises
    companyName: String,
    industry: String,
    customIndustry: String,
    companyDescription: String,
    employeeCount: String,
    foundingYear: String,
    commerceRegisterNumber: String, // Numéro de registre de commerce
    contactPhone: String,
    contactEmail: String,
    address: String,
    services: [String],
    resources: [String],
    customResources: [String],
    supportDescription: String,
    // Champs spécifiques pour les acteurs de la société civile
    organizationName: String,
    organizationType: {
      type: String,
      enum: ["association", "club", "foundation", "organization"],
    },
    activitySector: String,
    scope: {
      type: String,
      enum: ["local", "regional", "national", "international"],
    },
    approvalNumber: String, // Numéro d'agrément
    memberCount: String, // Nombre de membres
    foundingYear: String, // Année de fondation
    organizationDescription: String, // Description de l'organisation
    contactPhone: String, // Numéro de téléphone
    contactPhone2: String, // Deuxième numéro de téléphone
    contactEmail: String, // Email de contact
    website: String, // Site web
    address: String, // Adresse
    services: [String], // Services offerts
    resources: [String], // Ressources disponibles
    customResources: [String], // Autres ressources
    supportDescription: String, // Description du soutien
    mainContact: {
      name: String,
      phone: String,
      email: String,
    },
    directPermissions: [{
      type: Schema.Types.ObjectId,
      ref: "Permission",
    }],
    isVerified: {
      type: Boolean,
      default: false,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    },
    badges: [
      {
        type: Schema.Types.ObjectId,
        ref: "Badge",
      },
    ],
    initiatives: [
      {
        type: Schema.Types.ObjectId,
        ref: "Initiative",
      },
    ],
    supportedInitiatives: [
      {
        type: Schema.Types.ObjectId,
        ref: "Initiative",
      },
    ],
    votedInitiatives: [
      {
        initiative: { type: Schema.Types.ObjectId, ref: "Initiative" },
        option: { type: Schema.Types.ObjectId, ref: "VotingOption" },
        date: { type: Date, default: Date.now },
      },
    ],
    socialLinks: {
      facebook: String,
      twitter: String,
      linkedin: String,
      instagram: String,
    },
    settings: {
      emailNotifications: { type: Boolean, default: true },
      initiativeUpdates: { type: Boolean, default: true },
      commentReplies: { type: Boolean, default: true },
      supportNotifications: { type: Boolean, default: true },
      marketingEmails: { type: Boolean, default: false },
    },
    refreshToken: String,
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    verificationToken: String,
    lastLogin: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
)

// Virtual for user's full profile URL
userSchema.virtual("profileUrl").get(function (this: IUser) {
  return `/users/${this._id}`
})

// Method to check if user has already voted on an initiative
userSchema.methods.hasVotedOnInitiative = function (initiativeId: mongoose.Types.ObjectId): boolean {
  return this.votedInitiatives.some(
    (vote: { initiative: mongoose.Types.ObjectId }) => vote.initiative.toString() === initiativeId.toString(),
  )
}

// Method to check if user has already supported an initiative
userSchema.methods.hasSupportedInitiative = function (initiativeId: mongoose.Types.ObjectId): boolean {
  return this.supportedInitiatives.some((id: mongoose.Types.ObjectId) => id.toString() === initiativeId.toString())
}

// Method to check if user has a specific permission
userSchema.methods.hasPermission = async function (permissionCode: string): Promise<boolean> {
  try {
    // Special case for super_admin permission - check if user has admin role
    if (permissionCode === 'admin:manage_roles' || permissionCode === 'system:super_admin') {
      // Handle both string roles (old system) and object roles (new system)
      if (typeof this.role === 'string') {
        return this.role === 'admin';
      } else if (this.role) {
        // Try to get the code from the role object
        const roleCode = this.populated('role') ?
          (this.role as any).code :
          (await this.populate('role')).role?.code;

        return roleCode === 'admin';
      }
    }

    // Populate role and permissions if not already populated
    const user = this.populated('role') ? this : await this.populate('role');
    const populatedUser = user.populated('directPermissions') ? user : await user.populate('directPermissions');

    // Check direct permissions first
    if (populatedUser.directPermissions && populatedUser.directPermissions.length > 0) {
      const hasDirectPermission = populatedUser.directPermissions.some(
        (permission: any) => permission.code === permissionCode
      );
      if (hasDirectPermission) {
        return true;
      }
    }

    // Then check role permissions
    if (populatedUser.role) {
      let populatedRole;

      try {
        populatedRole = populatedUser.role.populated && populatedUser.role.populated('permissions')
          ? populatedUser.role
          : await populatedUser.role.populate('permissions');
      } catch (err) {
        // If we can't populate permissions, check if the user is admin
        if (typeof populatedUser.role === 'object' && populatedUser.role.code === 'admin') {
          return true;
        }
        return false;
      }

      if (populatedRole && populatedRole.permissions) {
        const hasPermission = populatedRole.permissions.some(
          (permission: any) => permission.code === permissionCode
        );

        if (hasPermission) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

// Index for search
userSchema.index({ name: "text", username: "text", email: "text", bio: "text" })

export default userSchema


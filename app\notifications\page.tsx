"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, ThumbsUp, MessageSquare, Bell, Clock, CheckCircle, Package, Truck, Ban } from "lucide-react"
import { api } from "@/lib/api"
import { getCurrentUser } from "@/lib/auth"
import { useNotifications } from "@/components/notification-context"

interface Notification {
  _id: string
  type: string
  content: string
  isRead: boolean
  createdAt: string
  relatedInitiative?: {
    _id: string
    title: string
    shortDescription: string
    mainImage?: string
  }
  sender?: {
    _id: string
    name: string
    username: string
    avatar?: string
  }
  link?: string
}

export default function NotificationsPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  const [notifications, setNotifications] = useState<Notification[]>([])
  const [activeTab, setActiveTab] = useState("all")
  const { unreadCount, setUnreadCount, refreshUnreadCount } = useNotifications()

  // Get current user
  const user = getCurrentUser()

  useEffect(() => {
    // Redirect if not logged in
    if (!user) {
      router.push("/auth/login")
      return
    }

    fetchNotifications()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab])

  const fetchNotifications = async () => {
    try {
      setIsLoading(true)

      // Toujours récupérer toutes les notifications pour pouvoir les filtrer côté client
      try {
        // Fetch notifications
        const response = await api.get('/api/notifications')
        if (response && response.notifications) {
          setNotifications(response.notifications)

          // Calculer le nombre de notifications non lues à partir des données
          const unreadNotifications = response.notifications.filter(n => !n.isRead)
          setUnreadCount(unreadNotifications.length)
        } else {
          // Si la réponse n'a pas le format attendu, utiliser un tableau vide
          setNotifications([])
          setUnreadCount(0)
        }
      } catch (notifErr) {
        console.error("Error fetching notifications:", notifErr)
        setNotifications([])
        setUnreadCount(0)
      }

      // Rafraîchir le compteur global de notifications non lues
      await refreshUnreadCount()
    } catch (err: any) {
      console.error("General error in fetchNotifications:", err)
      setError(err.message || "An error occurred")
      // En cas d'erreur, définir des valeurs par défaut
      setNotifications([])
      setUnreadCount(0)
    } finally {
      setIsLoading(false)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      await api.patch(`/api/notifications/${notificationId}/read`, {})

      // Update notification in state
      setNotifications((prev) =>
        prev.map((notification) =>
          notification._id === notificationId ? { ...notification, isRead: true } : notification,
        ),
      )

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - 1))

      // Refresh global unread count
      await refreshUnreadCount()
    } catch (err: any) {
      setError(err.message || "Failed to mark notification as read")
    }
  }

  const markAllAsRead = async () => {
    try {
      await api.patch("/api/notifications/read-all", {})

      // Update all notifications in state
      setNotifications((prev) => prev.map((notification) => ({ ...notification, isRead: true })))

      // Update unread count
      setUnreadCount(0)

      // Refresh global unread count
      await refreshUnreadCount()
    } catch (err: any) {
      setError(err.message || "Failed to mark all notifications as read")
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      await api.delete(`/api/notifications/${notificationId}`)

      // Remove notification from state
      setNotifications((prev) => prev.filter((notification) => notification._id !== notificationId))

      // Update unread count if needed
      const deletedNotification = notifications.find(n => n._id === notificationId)
      if (deletedNotification && !deletedNotification.isRead) {
        setUnreadCount((prev) => Math.max(0, prev - 1))

        // Refresh global unread count
        await refreshUnreadCount()
      }
    } catch (err: any) {
      setError(err.message || "Failed to delete notification")
    }
  }

  const deleteAllReadNotifications = async () => {
    try {
      // Get IDs of all read notifications
      const readNotificationIds = notifications
        .filter(n => n.isRead)
        .map(n => n._id)

      if (readNotificationIds.length === 0) {
        return
      }

      // Delete all read notifications
      await api.delete("/api/notifications/read-all")

      // Remove read notifications from state
      setNotifications((prev) => prev.filter((notification) => !notification.isRead))

      // No need to update unread count as we're only deleting read notifications
    } catch (err: any) {
      setError(err.message || "Failed to delete read notifications")
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "support":
        return <ThumbsUp className="h-5 w-5" />
      case "comment":
      case "reply":
        return <MessageSquare className="h-5 w-5" />
      case "resource_offer":
        return <Package className="h-5 w-5" />
      case "resource_status_update":
        return <Truck className="h-5 w-5" />
      case "resource_canceled":
        return <Ban className="h-5 w-5" />
      default:
        return <Bell className="h-5 w-5" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري تحميل الإشعارات...</span>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">الإشعارات</h1>

        <div className="flex gap-2">
          {unreadCount > 0 && (
            <Button variant="outline" onClick={markAllAsRead} className="flex items-center gap-1">
              <CheckCircle className="h-4 w-4 ml-1" />
              تعيين الكل كمقروء
            </Button>
          )}

          {notifications.some(n => n.isRead) && (
            <Button variant="outline" onClick={deleteAllReadNotifications} className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 ml-1">
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
              </svg>
              حذف المقروءة
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6 text-right">
          <AlertCircle className="h-4 w-4 ml-2" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-8" dir="rtl">
        <TabsList className="mb-6 w-full flex justify-start">
          <TabsTrigger value="all" className="order-first">جميع الإشعارات</TabsTrigger>
          <TabsTrigger value="unread" disabled={unreadCount === 0} className="order-last">
            غير مقروءة {unreadCount > 0 && `(${notifications.filter(n => !n.isRead).length})`}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          {notifications.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center text-gray-500">
                <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">لا توجد إشعارات</p>
                <p className="text-sm mt-2">ستظهر الإشعارات الجديدة هنا عندما تتلقاها.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <Card
                  key={notification._id}
                  className={`overflow-hidden ${!notification.isRead ? "border-r-4 border-green-600" : ""}`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4 text-right">
                      <div
                        className={`p-2 rounded-full ${notification.isRead ? "bg-gray-100 text-gray-500" : "bg-green-100 text-green-600"}`}
                      >
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="flex-1">
                        <p className="text-gray-700 text-right">{notification.content}</p>

                        {notification.relatedInitiative && (
                          <Link
                            href={`/initiatives/${notification.relatedInitiative._id}`}
                            className="text-sm text-green-600 hover:underline block text-right"
                          >
                            {notification.relatedInitiative.title}
                          </Link>
                        )}

                        <div className="flex items-center justify-end mt-2 text-xs text-gray-500">
                          <span>{formatDate(notification.createdAt)}</span>
                          <Clock className="h-3 w-3 mr-1 ml-1" />
                        </div>
                      </div>

                      <div className="flex flex-col gap-2">
                        {!notification.isRead ? (
                          <Button variant="ghost" size="sm" onClick={() => markAsRead(notification._id)}>
                            تعيين كمقروء
                          </Button>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteNotification(notification._id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            حذف
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="unread">
          {notifications.filter(n => !n.isRead).length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">لا توجد إشعارات غير مقروءة</p>
                <p className="text-sm mt-2">ستظهر الإشعارات غير المقروءة هنا عندما تتلقاها.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {notifications
                .filter(notification => !notification.isRead)
                .map((notification) => (
                  <Card key={notification._id} className="overflow-hidden border-r-4 border-green-600">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4 text-right">
                        <div className="p-2 rounded-full bg-green-100 text-green-600">
                          {getNotificationIcon(notification.type)}
                        </div>

                        <div className="flex-1">
                          <p className="text-gray-700 text-right">{notification.content}</p>

                          {notification.relatedInitiative && (
                            <Link
                              href={`/initiatives/${notification.relatedInitiative._id}`}
                              className="text-sm text-green-600 hover:underline block text-right"
                            >
                              {notification.relatedInitiative.title}
                            </Link>
                          )}

                          <div className="flex items-center justify-end mt-2 text-xs text-gray-500">
                            <span>{formatDate(notification.createdAt)}</span>
                            <Clock className="h-3 w-3 mr-1 ml-1" />
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm" onClick={() => markAsRead(notification._id)}>
                            تعيين كمقروء
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteNotification(notification._id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            حذف
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}


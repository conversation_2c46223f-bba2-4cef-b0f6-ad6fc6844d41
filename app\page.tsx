"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "../components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card"
import { Badge } from "../components/ui/badge"
import { Skeleton } from "../components/ui/skeleton"
import { api } from "../lib/api"
import { getImageUrl } from "../lib/imageUtils"
import BannerCarousel from "./components/BannerCarousel"
import { PlatformStats } from "./components/stats/PlatformStats"
import {
  ThumbsUp,
  MessageSquare,
  Calendar,
  MapPin,
  ArrowRight,
  Users,
  LightbulbIcon,
  BarChart3,
  CheckCircle2,
  Loader2,
} from "lucide-react"

export default function HomePage() {
  const [featuredInitiatives, setFeaturedInitiatives] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [banners, setBanners] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isBannersLoading, setIsBannersLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch banners for carousel
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setIsBannersLoading(true)

        const response = await api.get('/api/banners/active', false)

        if (response && response.banners) {
          // Ensure image URLs are absolute and point to the backend
          const processedBanners = response.banners.map(banner => ({
            ...banner,
            image: getImageUrl(banner.image)
          }))
          setBanners(processedBanners)
        } else {
          // Fallback to default banners if API fails
          setBanners([
            {
              _id: 'default1',
              image: '/banner1.jpg',
              mainText: 'Make a difference in your community',
              subText: 'Join thousands of volunteers supporting local initiatives',
              mainTextColor: '#FFFFFF',
              subTextColor: '#FFFFFF',
              order: 0,
              isActive: true
            },
            {
              _id: 'default2',
              image: '/banner2.jpg',
              mainText: 'Support causes that matter',
              subText: 'Find initiatives aligned with your values and contribute today',
              mainTextColor: '#FFFFFF',
              subTextColor: '#FFFFFF',
              order: 1,
              isActive: true
            }
          ])
        }
      } catch (err: any) {
        console.error('Error fetching banners:', err)
        // Use default banners on error
        setBanners([
          {
            _id: 'default1',
            image: '/banner1.jpg',
            mainText: 'Make a difference in your community',
            subText: 'Join thousands of volunteers supporting local initiatives',
            mainTextColor: '#FFFFFF',
            subTextColor: '#FFFFFF',
            order: 0,
            isActive: true
          },
          {
            _id: 'default2',
            image: '/banner2.jpg',
            mainText: 'Support causes that matter',
            subText: 'Find initiatives aligned with your values and contribute today',
            mainTextColor: '#FFFFFF',
            subTextColor: '#FFFFFF',
            order: 1,
            isActive: true
          }
        ])
      } finally {
        setIsBannersLoading(false)
      }
    }

    fetchBanners()
  }, [])

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)

        // Fetch featured initiatives
        const response = await api.get('/api/initiatives?limit=3&sort=supportCount', false)

        if (response && response.initiatives) {
          // Process images to ensure they have absolute URLs
          const processedInitiatives = response.initiatives.map(initiative => ({
            ...initiative,
            mainImage: getImageUrl(initiative.mainImage),
            images: initiative.images ? initiative.images.map(img => getImageUrl(img)) : []
          }))
          setFeaturedInitiatives(processedInitiatives)
        } else if (response && Array.isArray(response)) {
          setFeaturedInitiatives(response)
        } else {
          console.log('Unexpected initiatives response format:', response)
          // Use fallback data if API fails
          setFeaturedInitiatives([
            {
              _id: "1",
              title: "حملة تشجير الأحياء السكنية",
              category: { arabicName: "بيئة" },
              shortDescription: "مبادرة لزراعة الأشجار في الأحياء السكنية لتحسين البيئة المحلية وزيادة المساحات الخضراء.",
              author: { name: "أحمد مصطفى" },
              location: "الجزائر العاصمة",
              createdAt: "2024-03-15",
              supportCount: 124,
              commentCount: 45,
              mainImage: "/placeholder.svg?height=300&width=500",
            },
            {
              _id: "2",
              title: "مكتبات متنقلة للمناطق النائية",
              category: { arabicName: "تعليم" },
              shortDescription: "إنشاء مكتبات متنقلة للوصول إلى المناطق النائية وتوفير الكتب والموارد التعليمية للأطفال.",
              author: { name: "سارة بن علي" },
              location: "قسنطينة",
              createdAt: "2024-03-10",
              supportCount: 98,
              commentCount: 32,
              mainImage: "/placeholder.svg?height=300&width=500",
            },
            {
              _id: "3",
              title: "حملات توعية صحية في المدارس",
              category: { arabicName: "صحة" },
              shortDescription: "تنظيم حملات توعية صحية في المدارس للتثقيف حول النظافة الشخصية والتغذية السليمة.",
              author: { name: "كريم محمد" },
              location: "وهران",
              createdAt: "2024-03-05",
              supportCount: 76,
              commentCount: 28,
              mainImage: "/placeholder.svg?height=300&width=500",
            },
          ])
        }

        // Fetch categories
        try {
          const categoriesResponse = await api.get('/api/categories', false)

          if (categoriesResponse && categoriesResponse.categories) {
            setCategories(categoriesResponse.categories)
          } else if (categoriesResponse && Array.isArray(categoriesResponse)) {
            setCategories(categoriesResponse)
          }
        } catch (categoriesError) {
          console.error('Error fetching categories:', categoriesError)
        }

      } catch (error) {
        console.error('Error fetching data:', error)
        setError('Failed to load data. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Mock data for success stories
  const successStories = [
    {
      title: "تنظيف شاطئ سيدي فرج",
      description: "نجحت مبادرة تنظيف شاطئ سيدي فرج في جمع أكثر من 2 طن من النفايات البلاستيكية بمشاركة 150 متطوع.",
      image: "/placeholder.svg?height=300&width=500",
      impact: "تحسين البيئة البحرية وزيادة الوعي البيئي",
    },
    {
      title: "حملة محو الأمية الرقمية للكبار",
      description: "تمكنت المبادرة من تدريب أكثر من 500 شخص من كبار السن على استخدام الحاسوب والإنترنت.",
      image: "/placeholder.svg?height=300&width=500",
      impact: "تقليص الفجوة الرقمية وتمكين كبار السن",
    },
  ]

  // Statistics are now fetched dynamically via the PlatformStats component

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      {/* Hero Section with Dynamic Banner Carousel */}
      <BannerCarousel banners={banners} isLoading={isBannersLoading} />

      {/* How It Works Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#0a8754] mb-4">كيف تعمل المنصة؟</h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              منصة المبادرات المواطنة الجزائرية تمكنك من المشاركة في تطوير مجتمعك من خلال أربع خطوات بسيطة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <LightbulbIcon className="h-8 w-8 text-white" />
                </div>
                <CardTitle>1. اقتراح المبادرة</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  قدم فكرتك من خلال نموذج إنشاء المبادرات. اشرح فكرتك بوضوح وحدد أهدافها والفوائد المتوقعة منها.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
                <CardTitle>2. المناقشة والتطوير</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  يناقش المجتمع مبادرتك ويقدم اقتراحات لتحسينها. استفد من هذه الملاحظات لتطوير فكرتك.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <ThumbsUp className="h-8 w-8 text-white" />
                </div>
                <CardTitle>3. التصويت والدعم</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  يقوم أعضاء المجتمع بالتصويت على المبادرات ودعمها. المبادرات التي تحصل على دعم كافٍ تنتقل إلى مرحلة
                  التنفيذ.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <CardTitle>4. التنفيذ والمتابعة</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  بعد اعتماد المبادرة، يبدأ تنفيذها بمشاركة المتطوعين والجهات المعنية. يمكن للجميع متابعة تقدم المبادرة.
                </CardDescription>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-10">
            <Link href="/how-it-works">
              <Button variant="link" className="text-[#0a8754] text-lg">
                تعرف على المزيد
                <ArrowRight className="mr-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Initiatives Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-10">
            <h2 className="text-3xl font-bold text-[#0a8754]">مبادرات مميزة</h2>
            <Link href="/initiatives">
              <Button variant="link" className="text-[#0a8754]">
                عرض جميع المبادرات
                <ArrowRight className="mr-2 h-5 w-5" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? (
              // Loading skeletons
              Array(3).fill(0).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="h-[200px] w-full" />
                  <CardHeader className="pb-2">
                    <Skeleton className="h-6 w-24 mb-2" />
                    <Skeleton className="h-8 w-full mb-2" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                  <CardFooter>
                    <div className="flex justify-between w-full">
                      <div className="flex gap-4">
                        <Skeleton className="h-4 w-12" />
                        <Skeleton className="h-4 w-12" />
                      </div>
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </CardFooter>
                </Card>
              ))
            ) : error ? (
              <div className="col-span-3 text-center py-12 border rounded-lg bg-gray-50">
                <p className="text-lg text-gray-600 mb-4">{error}</p>
                <Button onClick={() => window.location.reload()}>إعادة المحاولة</Button>
              </div>
            ) : featuredInitiatives.length === 0 ? (
              <div className="col-span-3 text-center py-12 border rounded-lg bg-gray-50">
                <p className="text-lg text-gray-600 mb-4">لم يتم العثور على أي مبادرات</p>
                <Link href="/initiatives/create">
                  <Button>إنشاء مبادرة جديدة</Button>
                </Link>
              </div>
            ) : (
              featuredInitiatives.map((initiative) => (
                <Card key={initiative._id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-[200px] overflow-hidden">
                    <img
                      src={initiative.mainImage || "/placeholder.svg"}
                      alt={initiative.title}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-300"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge
                        className={`${
                          initiative.category?.arabicName === "بيئة"
                            ? "bg-green-500"
                            : initiative.category?.arabicName === "تعليم"
                              ? "bg-blue-500"
                              : initiative.category?.arabicName === "صحة"
                                ? "bg-red-500"
                                : "bg-gray-500"
                        }`}
                      >
                        {initiative.category?.arabicName || initiative.category?.name || ""}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl mt-2 line-clamp-2">
                      <Link href={`/initiatives/${initiative._id}`} className="hover:text-[#0a8754] transition-colors">
                        {initiative.title}
                      </Link>
                    </CardTitle>
                    <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                      <span className="flex items-center gap-1">
                        <Calendar size={14} />
                        {new Date(initiative.createdAt).toLocaleDateString('ar-DZ')}
                      </span>
                      <span className="flex items-center gap-1">
                        <MapPin size={14} />
                        {initiative.location}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="line-clamp-3 text-gray-600">{initiative.shortDescription}</CardDescription>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-0">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1 text-sm">
                        <ThumbsUp size={14} className="text-[#0a8754]" />
                        {initiative.supportCount}
                      </span>
                      <span className="flex items-center gap-1 text-sm">
                        <MessageSquare size={14} className="text-[#0a8754]" />
                        {initiative.commentCount}
                      </span>
                    </div>
                    <Link href={`/initiatives/${initiative._id}`}>
                      <Button variant="ghost" className="text-[#0a8754] hover:text-[#097548] p-0">
                        عرض التفاصيل
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 px-4 bg-[#0a8754] text-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">إحصائيات المنصة</h2>
            <p className="text-lg opacity-90 max-w-3xl mx-auto">
              منذ إطلاق المنصة، حققنا العديد من الإنجازات بفضل مشاركة المواطنين الفعالة
            </p>
          </div>

          <PlatformStats />
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#0a8754] mb-4">قصص نجاح</h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              تعرف على بعض المبادرات التي تم تنفيذها بنجاح وأحدثت تأثيراً إيجابياً في المجتمع
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {successStories.map((story, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="h-[250px]">
                  <img
                    src={story.image || "/placeholder.svg"}
                    alt={story.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader>
                  <CardTitle className="text-xl text-[#0a8754]">{story.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{story.description}</p>
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-[#0a8754]" />
                    <span className="text-sm font-medium">التأثير: {story.impact}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-10">
            <Link href="/progress">
              <Button className="bg-[#0a8754] hover:bg-[#097548]">متابعة تقدم المبادرات</Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Community Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#0a8754] mb-4">انضم إلى مجتمعنا</h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              كن جزءاً من مجتمع نشط يسعى لإحداث تغيير إيجابي في الجزائر. شارك أفكارك، تعرف على أشخاص يشاركونك اهتماماتك،
              وساهم في بناء مستقبل أفضل.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                  <LightbulbIcon className="h-8 w-8 text-white" />
                </div>
                <CardTitle>اقترح مبادرات</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  شارك أفكارك ومبادراتك لتطوير مجتمعك. كل فكرة، مهما كانت بسيطة، يمكن أن تحدث فرقاً كبيراً.
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link href="/initiatives/create" className="w-full">
                  <Button className="w-full bg-[#0a8754] hover:bg-[#097548]">قدم مبادرتك</Button>
                </Link>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>تواصل مع المجتمع</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  تفاعل مع أعضاء المجتمع، شارك تجاربك، وتعرف على أشخاص يشاركونك نفس الاهتمامات والأهداف.
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link href="/community" className="w-full">
                  <Button className="w-full bg-[#0a8754] hover:bg-[#097548]">زيارة المجتمع</Button>
                </Link>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-16 h-16 bg-[#0a8754] rounded-full flex items-center justify-center mb-4">
                  <ThumbsUp className="h-8 w-8 text-white" />
                </div>
                <CardTitle>ادعم المبادرات</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  ساهم في دعم المبادرات التي تؤمن بها من خلال التصويت، التطوع، أو المشاركة في تنفيذها.
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link href="/initiatives" className="w-full">
                  <Button className="w-full bg-[#0a8754] hover:bg-[#097548]">استكشف المبادرات</Button>
                </Link>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 px-4 bg-[#0a8754] text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">هل أنت مستعد للمشاركة؟</h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى منصة المبادرات المواطنة الجزائرية اليوم وكن جزءاً من التغيير الإيجابي في مجتمعك
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/initiatives">
              <Button className="bg-white text-[#0a8754] hover:bg-gray-100 text-lg h-12 px-6">استكشف المبادرات</Button>
            </Link>
            <Link href="/initiatives/create">
              <Button variant="outline" className="border-white text-white hover:bg-white/20 text-lg h-12 px-6">
                قدم مبادرتك
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}


import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/db';
import { User, Initiative, Comment, Support } from '@/src/models';

export async function GET() {
  try {
    // Connect to the database
    await connectToDatabase();

    // Get active users (users with initiatives or comments)
    const users = await User.find()
      .sort('-createdAt')
      .limit(10);

    // Get popular initiatives
    const popularInitiatives = await Initiative.find({ status: 'active' })
      .sort('-supportCount -commentCount')
      .populate('author', 'name username avatar')
      .populate('category', 'name arabicName')
      .limit(3);

    // Format date to relative time in Arabic
    const getRelativeTimeString = (date: Date) => {
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - new Date(date).getTime()) / 1000);
      
      if (diffInSeconds < 60) return 'قبل لحظات';
      if (diffInSeconds < 3600) return `قبل ${Math.floor(diffInSeconds / 60)} دقائق`;
      if (diffInSeconds < 86400) return `قبل ${Math.floor(diffInSeconds / 3600)} ساعات`;
      if (diffInSeconds < 604800) return `قبل ${Math.floor(diffInSeconds / 86400)} أيام`;
      if (diffInSeconds < 2592000) return `قبل ${Math.floor(diffInSeconds / 604800)} أسابيع`;
      return `قبل ${Math.floor(diffInSeconds / 2592000)} أشهر`;
    };

    // Process users data
    const membersData = await Promise.all(
      users.map(async (user) => {
        // Get user's initiatives
        const userInitiatives = await Initiative.find({ author: user._id });
        
        // Get user's comments
        const userComments = await Comment.find({ author: user._id });
        
        // Get user's supported initiatives
        const userSupports = await Support.find({ user: user._id });
        
        // Get recent activity
        const recentActivity = [];
        
        // Add recent initiatives
        const recentInitiatives = await Initiative.find({ author: user._id })
          .sort('-createdAt')
          .limit(1);
          
        for (const initiative of recentInitiatives) {
          recentActivity.push({
            type: 'initiative',
            title: `أطلق مبادرة جديدة: ${initiative.title}`,
            date: getRelativeTimeString(initiative.createdAt),
          });
        }
        
        // Add recent comments
        const recentComments = await Comment.find({ author: user._id })
          .sort('-createdAt')
          .populate('initiative', 'title')
          .limit(1);
          
        for (const comment of recentComments) {
          if (comment.initiative) {
            recentActivity.push({
              type: 'comment',
              title: `علق على مبادرة: ${comment.initiative.title}`,
              date: getRelativeTimeString(comment.createdAt),
            });
          }
        }
        
        // Add recent supports
        const recentSupports = await Support.find({ user: user._id })
          .sort('-createdAt')
          .populate('initiative', 'title')
          .limit(1);
          
        for (const support of recentSupports) {
          if (support.initiative) {
            recentActivity.push({
              type: 'support',
              title: `دعم مبادرة: ${support.initiative.title}`,
              date: getRelativeTimeString(support.createdAt),
            });
          }
        }
        
        // Sort activity by date
        recentActivity.sort((a, b) => {
          const dateA = a.date;
          const dateB = b.date;
          if (dateA < dateB) return 1;
          if (dateA > dateB) return -1;
          return 0;
        });

        // Get badges based on user activity
        const badges = [];
        if (userInitiatives.length >= 3) badges.push('مؤسس مبادرات');
        if (userComments.length >= 10) badges.push('متفاعل نشط');
        if (userSupports.length >= 5) badges.push('داعم مميز');
        
        // Format join date
        const joinDate = new Date(user.createdAt).toLocaleDateString('ar-DZ', {
          year: 'numeric',
          month: 'long',
        });

        return {
          id: user._id,
          name: user.name,
          username: `@${user.username}`,
          avatar: user.avatar || '/placeholder.svg?height=100&width=100',
          location: user.location || 'الجزائر',
          joinDate,
          bio: user.bio || 'عضو في مجتمع المبادرات',
          initiatives: userInitiatives.length,
          contributions: userComments.length + userSupports.length,
          badges,
          recentActivity: recentActivity.slice(0, 3),
        };
      })
    );

    // Get recent posts (combining initiatives and comments)
    const recentPosts = [];
    
    // Get recent initiatives
    const recentInitiatives = await Initiative.find()
      .sort('-createdAt')
      .populate('author', 'name username avatar')
      .limit(3);
      
    for (const initiative of recentInitiatives) {
      recentPosts.push({
        id: `initiative-${initiative._id}`,
        author: {
          id: initiative.author._id,
          name: initiative.author.name,
          username: `@${initiative.author.username}`,
          avatar: initiative.author.avatar || '/placeholder.svg?height=100&width=100',
        },
        date: getRelativeTimeString(initiative.createdAt),
        content: `أطلقت مبادرة جديدة: ${initiative.title}\n\n${initiative.shortDescription}`,
        likes: initiative.supportCount || 0,
        comments: initiative.commentCount || 0,
        image: initiative.mainImage || '/placeholder.svg?height=400&width=600',
      });
    }
    
    // Get recent comments
    const recentComments = await Comment.find({ isReply: false })
      .sort('-createdAt')
      .populate('author', 'name username avatar')
      .populate('initiative', 'title')
      .limit(3);
      
    for (const comment of recentComments) {
      if (comment.initiative) {
        recentPosts.push({
          id: `comment-${comment._id}`,
          author: {
            id: comment.author._id,
            name: comment.author.name,
            username: `@${comment.author.username}`,
            avatar: comment.author.avatar || '/placeholder.svg?height=100&width=100',
          },
          date: getRelativeTimeString(comment.createdAt),
          content: `علقت على مبادرة "${comment.initiative.title}":\n\n${comment.content}`,
          likes: comment.likes || 0,
          comments: comment.replies?.length || 0,
          image: null,
        });
      }
    }
    
    // Sort posts by date
    recentPosts.sort((a, b) => {
      const dateA = a.date;
      const dateB = b.date;
      if (dateA < dateB) return 1;
      if (dateA > dateB) return -1;
      return 0;
    });

    // Format popular initiatives
    const formattedInitiatives = popularInitiatives.map(initiative => ({
      id: initiative._id,
      title: initiative.title,
      category: initiative.category.arabicName || initiative.category.name,
      location: initiative.location || 'الجزائر',
      supportCount: initiative.supportCount || 0,
      commentCount: initiative.commentCount || 0,
      description: initiative.shortDescription,
      image: initiative.mainImage || '/placeholder.svg?height=300&width=500',
    }));

    return NextResponse.json({ 
      success: true, 
      members: membersData,
      posts: recentPosts,
      initiatives: formattedInitiatives
    });
  } catch (error) {
    console.error('Error fetching community data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch community data' },
      { status: 500 }
    );
  }
}

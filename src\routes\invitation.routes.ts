import express from "express"
import {
  getUserInvitations,
  acceptInvitation,
  declineInvitation
} from "../controllers/invitation.controller"
import { authenticate } from "../middleware/auth"

const router = express.Router()

// All routes require authentication
router.use(authenticate)

// Get all invitations for the current user
router.get("/", getUserInvitations)

// Accept an invitation
router.put("/:id/accept", acceptInvitation)

// Decline an invitation
router.put("/:id/decline", declineInvitation)

export default router

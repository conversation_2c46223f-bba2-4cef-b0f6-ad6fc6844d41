import { Request, Response, NextFunction } from "express";
import mongoose from "mongoose";
import { Role, Permission, User } from "../models";
import { createError } from "../utils/error";

/**
 * Get all roles
 * @route GET /api/admin/roles
 * @access Admin
 */
export const getAllRoles = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const roles = await Role.find().populate("permissions");

    res.status(200).json({
      success: true,
      roles,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get role by ID
 * @route GET /api/admin/roles/:id
 * @access Admin
 */
export const getRoleById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid role ID"));
    }

    // Find role
    const role = await Role.findById(id).populate("permissions");

    if (!role) {
      return next(createError(404, "Role not found"));
    }

    res.status(200).json({
      success: true,
      role,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new role
 * @route POST /api/admin/roles
 * @access Admin
 */
export const createRole = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, description, code, permissions, isDefault } = req.body;

    // Validate required fields
    if (!name || !description || !code) {
      return next(createError(400, "Name, description, and code are required"));
    }

    // Check if role with same code already exists
    const existingRole = await Role.findOne({ code });
    if (existingRole) {
      return next(createError(400, "Role with this code already exists"));
    }

    // Validate permissions
    if (permissions && permissions.length > 0) {
      for (const permissionId of permissions) {
        if (!mongoose.Types.ObjectId.isValid(permissionId)) {
          return next(createError(400, `Invalid permission ID: ${permissionId}`));
        }

        const permissionExists = await Permission.findById(permissionId);
        if (!permissionExists) {
          return next(createError(404, `Permission not found: ${permissionId}`));
        }
      }
    }

    // If this is set as default, unset any existing default role
    if (isDefault) {
      await Role.updateMany({ isDefault: true }, { isDefault: false });
    }

    // Create new role
    const role = await Role.create({
      name,
      description,
      code,
      permissions: permissions || [],
      isDefault: isDefault || false,
      isSystem: false, // Custom roles are never system roles
    });

    res.status(201).json({
      success: true,
      message: "Role created successfully",
      role,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update a role
 * @route PUT /api/admin/roles/:id
 * @access Admin
 */
export const updateRole = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { name, description, permissions, isDefault } = req.body;

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid role ID"));
    }

    // Find role
    const role = await Role.findById(id);

    if (!role) {
      return next(createError(404, "Role not found"));
    }

    // Prevent modification of system roles' core properties
    if (role.isSystem && req.body.code) {
      return next(createError(400, "Cannot change the code of a system role"));
    }

    // Validate permissions
    if (permissions && permissions.length > 0) {
      for (const permissionId of permissions) {
        if (!mongoose.Types.ObjectId.isValid(permissionId)) {
          return next(createError(400, `Invalid permission ID: ${permissionId}`));
        }

        const permissionExists = await Permission.findById(permissionId);
        if (!permissionExists) {
          return next(createError(404, `Permission not found: ${permissionId}`));
        }
      }
    }

    // If this is set as default, unset any existing default role
    if (isDefault && !role.isDefault) {
      await Role.updateMany({ isDefault: true }, { isDefault: false });
    }

    // Update role
    const updatedRole = await Role.findByIdAndUpdate(
      id,
      {
        name: name || role.name,
        description: description || role.description,
        permissions: permissions || role.permissions,
        isDefault: isDefault !== undefined ? isDefault : role.isDefault,
      },
      { new: true }
    ).populate("permissions");

    res.status(200).json({
      success: true,
      message: "Role updated successfully",
      role: updatedRole,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a role
 * @route DELETE /api/admin/roles/:id
 * @access Admin
 */
export const deleteRole = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid role ID"));
    }

    // Find role
    const role = await Role.findById(id);

    if (!role) {
      return next(createError(404, "Role not found"));
    }

    // Prevent deletion of system roles
    if (role.isSystem) {
      return next(createError(400, "Cannot delete a system role"));
    }

    // Check if any users are using this role
    const usersWithRole = await User.countDocuments({ role: id });
    if (usersWithRole > 0) {
      return next(createError(400, `Cannot delete role: ${usersWithRole} users are assigned to this role`));
    }

    // Delete role
    await Role.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Role deleted successfully",
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all permissions
 * @route GET /api/admin/permissions
 * @access Admin
 */
export const getAllPermissions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const permissions = await Permission.find().sort({ category: 1, name: 1 });

    // Group permissions by category
    const groupedPermissions = permissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {});

    res.status(200).json({
      success: true,
      permissions: groupedPermissions,
    });
  } catch (error) {
    next(error);
  }
};

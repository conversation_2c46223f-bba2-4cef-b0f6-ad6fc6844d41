import { Initiative, Milestone } from "../models";
import { logger } from "./logger";

/**
 * Updates the progress and status of an initiative based on its milestones
 * @param initiativeId The ID of the initiative to update
 * @returns The updated initiative
 */
export const updateInitiativeProgress = async (initiativeId: string) => {
  try {
    // Find initiative
    const initiative = await Initiative.findById(initiativeId);
    if (!initiative) {
      throw new Error(`Initiative not found: ${initiativeId}`);
    }

    // Get all milestones for this initiative
    const milestones = await Milestone.find({ initiative: initiativeId });
    
    // Calculate progress
    let progress = 0;
    if (milestones.length > 0) {
      const completedMilestones = milestones.filter(m => m.isCompleted).length;
      progress = Math.round((completedMilestones / milestones.length) * 100);
    }

    // Determine if status needs to be updated
    let status = initiative.status;
    if (progress === 100 && status !== "completed") {
      status = "completed";
      logger.info(`Initiative ${initiativeId} is now completed`);
    } else if (progress < 100 && status === "completed") {
      status = "active";
      logger.info(`Initiative ${initiativeId} is now active (was completed)`);
    }

    // Update initiative
    const updatedInitiative = await Initiative.findByIdAndUpdate(
      initiativeId,
      { progress, status },
      { new: true }
    );

    logger.info(`Updated initiative ${initiativeId}: progress=${progress}, status=${status}`);
    return updatedInitiative;
  } catch (error) {
    logger.error(`Error updating initiative progress: ${error}`);
    throw error;
  }
};

import type { Request, Response, NextFunction } from "express"
import <PERSON><PERSON> from "joi"
import { createError } from "../../utils/error"

// Validate register request
export const validateRegister = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      "string.base": "Username must be a string",
      "string.alphanum": "Username must only contain alphanumeric characters",
      "string.min": "Username must be at least 3 characters long",
      "string.max": "Username cannot be longer than 30 characters",
      "any.required": "Username is required",
    }),
    email: Joi.string().email().required().messages({
      "string.base": "Email must be a string",
      "string.email": "Please enter a valid email address",
      "any.required": "Email is required",
    }),
    password: Joi.string()
      .min(8)
      .required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/)
      .messages({
        "string.base": "Password must be a string",
        "string.min": "Password must be at least 8 characters long",
        "string.pattern.base":
          "Password must contain at least one uppercase letter, one lowercase letter, and one number",
        "any.required": "Password is required",
      }),
    name: Joi.string().min(2).max(50).required().messages({
      "string.base": "Name must be a string",
      "string.min": "Name must be at least 2 characters long",
      "string.max": "Name cannot be longer than 50 characters",
      "any.required": "Name is required",
    }),
    userType: Joi.string().valid("volunteer", "proposer", "company").messages({
      "string.base": "User type must be a string",
      "any.only": "User type must be one of: volunteer, proposer, company",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

// Validate login request
export const validateLogin = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    email: Joi.string().email().required().messages({
      "string.base": "Email must be a string",
      "string.email": "Please enter a valid email address",
      "any.required": "Email is required",
    }),
    password: Joi.string().required().messages({
      "string.base": "Password must be a string",
      "any.required": "Password is required",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

// Validate reset password request
export const validateResetPassword = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    password: Joi.string()
      .min(8)
      .required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/)
      .messages({
        "string.base": "Password must be a string",
        "string.min": "Password must be at least 8 characters long",
        "string.pattern.base":
          "Password must contain at least one uppercase letter, one lowercase letter, and one number",
        "any.required": "Password is required",
      }),
    confirmPassword: Joi.string().valid(Joi.ref("password")).required().messages({
      "any.only": "Passwords do not match",
      "any.required": "Please confirm your password",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

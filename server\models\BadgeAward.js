const mongoose = require('mongoose');

const badgeAwardSchema = new mongoose.Schema({
  badge: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Badge',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  initiative: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Initiative',
    required: true
  },
  awardedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reason: {
    type: String
  },
  awardedAt: {
    type: Date,
    default: Date.now
  }
});

// Ensure a user can only receive a specific badge once per initiative
badgeAwardSchema.index({ badge: 1, user: 1, initiative: 1 }, { unique: true });

module.exports = mongoose.model('BadgeAward', badgeAwardSchema);

"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../../../components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../../../../components/ui/card"
import { Badge } from "../../../../components/ui/badge"
import { Alert, AlertDescription } from "../../../../components/ui/alert"
import {
  Loader2,
  AlertCircle,
  ChevronLeft,
  Package
} from "lucide-react"
import { api } from "../../../../lib/api"
import { useAuth } from "../../../../components/auth-provider"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../../../components/ui/tabs"
import ResourceNeedsList from "../../../../components/initiative/resource-needs-list"
import ResourceOffersList from "../../../../components/initiative/resource-offers-list"
import { Toaster } from "../../../../components/ui/toaster"

export default function InitiativeResourcesPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params
  const { user, isAuthenticated } = useAuth()

  const [initiative, setInitiative] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("needs")
  const [isAuthor, setIsAuthor] = useState(false)

  useEffect(() => {
    fetchInitiative()
  }, [id])

  useEffect(() => {
    if (initiative && user) {
      setIsAuthor(user.id === initiative.author._id)
    }
  }, [initiative, user])

  const fetchInitiative = async () => {
    setIsLoading(true)
    try {
      const response = await api.get(`/api/initiatives/${id}`, false)

      if (response.success && response.initiative) {
        setInitiative(response.initiative)
      } else {
        setError("Failed to fetch initiative")
      }
    } catch (err: any) {
      console.error("Error fetching initiative:", err)
      setError(err.message || "An error occurred while fetching initiative")
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="mr-2">جاري التحميل...</span>
      </div>
    )
  }

  if (error || !initiative) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "لم يتم العثور على المبادرة"}</AlertDescription>
        </Alert>
        <div className="mt-6">
          <Button onClick={() => router.back()} variant="outline">
            <ChevronLeft className="ml-2 h-4 w-4" />
            العودة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <Toaster />
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-2">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ChevronLeft className="ml-2 h-4 w-4" />
            العودة
          </Button>
          <Link href={`/initiatives/${id}`}>
            <Button variant="outline" size="sm">
              عرض المبادرة
            </Button>
          </Link>
        </div>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">موارد المبادرة</h1>
            <p className="text-gray-600 mt-1">{initiative.title}</p>
            <div className="flex items-center gap-2 mt-2">
              <Badge style={{ backgroundColor: initiative.category.color }}>
                {initiative.category.arabicName || initiative.category.name}
              </Badge>
              <Badge
                className={
                  initiative.status === "active"
                    ? "bg-green-100 text-green-800 border-green-200"
                    : initiative.status === "completed"
                      ? "bg-blue-100 text-blue-800 border-blue-200"
                      : "bg-gray-100 text-gray-800 border-gray-200"
                }
              >
                {initiative.status === "active"
                  ? "نشطة"
                  : initiative.status === "completed"
                    ? "مكتملة"
                    : initiative.status === "pending"
                      ? "قيد المراجعة"
                      : initiative.status}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="needs">احتياجات الموارد</TabsTrigger>
          <TabsTrigger value="offers">عروض الموارد</TabsTrigger>
        </TabsList>

        <TabsContent value="needs" className="mt-6">
          <ResourceNeedsList
            initiativeId={id}
            isAuthor={isAuthor}
            status={initiative.status}
            isDetailPage={true}
          />
        </TabsContent>

        <TabsContent value="offers" className="mt-6">
          <ResourceOffersList
            initiativeId={id}
            isAuthor={isAuthor}
            status={initiative.status}
            isDetailPage={true}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

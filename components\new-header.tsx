"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "./ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "./ui/sheet"
import { Menu, User, LogOut, Settings, PlusCircle, Search, BarChart, Bell, Info, ChevronDown, Building } from "lucide-react"
import { useAuth } from "./auth-provider"
import { useNotifications } from "./notification-context"
import { api } from "../lib/api"

export default function NewHeader() {
  const pathname = usePathname()
  const { user, logout, isAuthenticated } = useAuth()
  const { unreadCount } = useNotifications()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isAboutMenuOpen, setIsAboutMenuOpen] = useState(false)
  const [isSupportersMenuOpen, setIsSupportersMenuOpen] = useState(false)

  // Check if user is admin
  const isAdmin = user && (
    user.role === 'admin' ||
    (typeof user.role === 'object' && user.role?.code === 'admin') ||
    (typeof user.role === 'string' && user.role.includes('admin')) ||
    user.username === 'admin'
  )

  const isActive = (path: string) => {
    return pathname === path
  }

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <img className="h-10 w-auto" src="/placeholder.svg?height=40&width=120" alt="منصة المبادرات" />
            </Link>
            <nav className="hidden md:ml-6 md:flex md:space-x-8 md:space-x-reverse">
              <Link
                href="/"
                className={`px-3 py-2 text-sm font-medium ${
                  isActive("/") ? "text-green-600 border-b-2 border-green-600" : "text-gray-900 hover:text-green-600"
                }`}
              >
                الرئيسية
              </Link>
              <Link
                href="/initiatives"
                className={`px-3 py-2 text-sm font-medium ${
                  pathname.startsWith("/initiatives")
                    ? "text-green-600 border-b-2 border-green-600"
                    : "text-gray-900 hover:text-green-600"
                }`}
              >
                المبادرات
              </Link>
              <div className="relative">
                <button
                  className={`px-3 py-2 text-sm font-medium flex items-center gap-1 ${
                    pathname.startsWith("/companies") || pathname.startsWith("/civil-society")
                      ? "text-green-600 border-b-2 border-green-600"
                      : "text-gray-900 hover:text-green-600"
                  }`}
                  onClick={() => setIsSupportersMenuOpen(!isSupportersMenuOpen)}
                >
                  <span>الداعمون</span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {isSupportersMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    <Link
                      href="/companies"
                      className={`block px-4 py-2 text-sm ${
                        pathname.startsWith("/companies") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsSupportersMenuOpen(false)}
                    >
                      الشركات الداعمة
                    </Link>
                    <Link
                      href="/civil-society"
                      className={`block px-4 py-2 text-sm ${
                        pathname.startsWith("/civil-society") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsSupportersMenuOpen(false)}
                    >
                      ممثلو المجتمع المدني
                    </Link>
                  </div>
                )}
              </div>
              <Link
                href="/progress"
                className={`px-3 py-2 text-sm font-medium ${
                  isActive("/progress")
                    ? "text-green-600 border-b-2 border-green-600"
                    : "text-gray-900 hover:text-green-600"
                }`}
              >
                متابعة التقدم
              </Link>
              <Link
                href="/community"
                className={`px-3 py-2 text-sm font-medium ${
                  isActive("/community")
                    ? "text-green-600 border-b-2 border-green-600"
                    : "text-gray-900 hover:text-green-600"
                }`}
              >
                المجتمع
              </Link>

              {/* About Menu Dropdown */}
              <div className="relative">
                <button
                  className={`px-3 py-2 text-sm font-medium flex items-center gap-1 ${
                    isActive("/about") || isActive("/how-it-works") || isActive("/stats")
                      ? "text-green-600 border-b-2 border-green-600"
                      : "text-gray-900 hover:text-green-600"
                  }`}
                  onClick={() => setIsAboutMenuOpen(!isAboutMenuOpen)}
                  onBlur={() => setTimeout(() => setIsAboutMenuOpen(false), 100)}
                >
                  <span>عن المنصة</span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {isAboutMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    <Link
                      href="/about"
                      className={`block px-4 py-2 text-sm ${
                        isActive("/about") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      من نحن
                    </Link>
                    <Link
                      href="/how-it-works"
                      className={`block px-4 py-2 text-sm ${
                        isActive("/how-it-works") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      كيف يعمل
                    </Link>
                    <Link
                      href="/stats"
                      className={`block px-4 py-2 text-sm ${
                        isActive("/stats") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      الإحصائيات
                    </Link>
                  </div>
                )}
              </div>
            </nav>
          </div>

          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <Link href="/search">
              <Button variant="ghost" size="icon">
                <Search className="h-5 w-5" />
              </Button>
            </Link>

            {isAuthenticated ? (
              <>
                <Link href="/initiatives/create">
                  <Button className="bg-green-600 hover:bg-green-700 flex items-center gap-1">
                    <PlusCircle className="h-4 w-4" />
                    إنشاء مبادرة
                  </Button>
                </Link>

                {/* Notification Bell */}
                <Link href="/notifications">
                  <Button variant="ghost" size="icon" className="relative">
                    <Bell className="h-5 w-5" />
                    {unreadCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {unreadCount > 9 ? '9+' : unreadCount}
                      </span>
                    )}
                  </Button>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-2 h-8 px-2">
                      <span className="text-sm hidden lg:inline-block">{user?.name || user?.username}</span>
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={user?.avatar || "/placeholder.svg?height=32&width=32"}
                          alt={user?.name || ""}
                        />
                        <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/users/${user?.id}`} className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        <span>الملف الشخصي</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/initiatives" className="cursor-pointer">
                        <BarChart className="mr-2 h-4 w-4" />
                        <span>لوحة التحكم</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/profile/invitations" className="cursor-pointer">
                        <Bell className="mr-2 h-4 w-4" />
                        <span>الدعوات</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/settings" className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>الإعدادات</span>
                      </Link>
                    </DropdownMenuItem>
                    {isAdmin && (
                      <DropdownMenuItem asChild>
                        <Link href="/admin/dashboard" className="cursor-pointer">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>لوحة الإدارة</span>
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout} className="cursor-pointer">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>تسجيل الخروج</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <Link href="/auth/login">
                  <Button variant="outline">تسجيل الدخول</Button>
                </Link>
                <Link href="/auth/register">
                  <Button className="bg-green-600 hover:bg-green-700">إنشاء حساب</Button>
                </Link>
              </>
            )}
          </div>

          <div className="flex md:hidden">
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right">
                <div className="flex flex-col h-full">
                  <div className="flex-1 py-4">
                    <div className="mb-8">
                      <Link href="/" onClick={() => setIsMenuOpen(false)}>
                        <img className="h-8 w-auto" src="/placeholder.svg?height=32&width=100" alt="منصة المبادرات" />
                      </Link>
                    </div>

                    <nav className="flex flex-col space-y-4">
                      <Link
                        href="/"
                        className={`px-2 py-1 rounded-md ${
                          isActive("/") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        الرئيسية
                      </Link>
                      <Link
                        href="/initiatives"
                        className={`px-2 py-1 rounded-md ${
                          pathname.startsWith("/initiatives")
                            ? "bg-green-50 text-green-600 font-medium"
                            : "text-gray-900"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        المبادرات
                      </Link>
                      <div className="px-2 py-1 rounded-md text-gray-900 font-medium">
                        الداعمون
                      </div>
                      <div className="pr-4 space-y-1">
                        <Link
                          href="/companies"
                          className={`px-2 py-1 rounded-md block ${
                            pathname.startsWith("/companies") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          الشركات الداعمة
                        </Link>
                        <Link
                          href="/civil-society"
                          className={`px-2 py-1 rounded-md block ${
                            pathname.startsWith("/civil-society") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          ممثلو المجتمع المدني
                        </Link>
                      </div>
                      <Link
                        href="/progress"
                        className={`px-2 py-1 rounded-md ${
                          isActive("/progress") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        متابعة التقدم
                      </Link>
                      <Link
                        href="/community"
                        className={`px-2 py-1 rounded-md ${
                          isActive("/community") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        المجتمع
                      </Link>
                      <div className="px-2 py-1 rounded-md text-gray-900 font-medium">
                        عن المنصة
                      </div>
                      <div className="pr-4 space-y-1">
                        <Link
                          href="/about"
                          className={`px-2 py-1 rounded-md block ${
                            isActive("/about") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          من نحن
                        </Link>
                        <Link
                          href="/how-it-works"
                          className={`px-2 py-1 rounded-md block ${
                            isActive("/how-it-works") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          كيف يعمل
                        </Link>
                        <Link
                          href="/stats"
                          className={`px-2 py-1 rounded-md block ${
                            isActive("/stats") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          الإحصائيات
                        </Link>
                      </div>
                      <Link
                        href="/search"
                        className={`px-2 py-1 rounded-md ${
                          isActive("/search") ? "bg-green-50 text-green-600 font-medium" : "text-gray-900"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        البحث
                      </Link>
                    </nav>
                  </div>

                  <div className="py-4 border-t border-gray-200">
                    {isAuthenticated ? (
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 px-2">
                          <Avatar className="h-10 w-10">
                            <AvatarImage
                              src={user?.avatar || "/placeholder.svg?height=40&width=40"}
                              alt={user?.name || ""}
                            />
                            <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user?.name || user?.username}</p>
                            {user?.username && <p className="text-sm text-gray-500">@{user?.username}</p>}
                          </div>
                        </div>

                        <div className="px-2 mt-2 mb-2">
                          <Link href="/notifications" onClick={() => setIsMenuOpen(false)}>
                            <Button variant="outline" className="w-full justify-between">
                              <span>الإشعارات غير المقروءة</span>
                              {unreadCount > 0 && (
                                <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                  {unreadCount > 9 ? '9+' : unreadCount}
                                </span>
                              )}
                            </Button>
                          </Link>
                        </div>

                        <div className="space-y-2">
                          <Link href={`/users/${user?.id}`} onClick={() => setIsMenuOpen(false)}>
                            <Button variant="ghost" className="w-full justify-start">
                              <User className="mr-2 h-4 w-4" />
                              الملف الشخصي
                            </Button>
                          </Link>
                          <Link href="/dashboard/initiatives" onClick={() => setIsMenuOpen(false)}>
                            <Button variant="ghost" className="w-full justify-start">
                              <BarChart className="mr-2 h-4 w-4" />
                              لوحة التحكم
                            </Button>
                          </Link>
                          <Link href="/profile/invitations" onClick={() => setIsMenuOpen(false)}>
                            <Button variant="ghost" className="w-full justify-start">
                              <Bell className="mr-2 h-4 w-4" />
                              الدعوات
                            </Button>
                          </Link>
                          <Link href="/settings" onClick={() => setIsMenuOpen(false)}>
                            <Button variant="ghost" className="w-full justify-start">
                              <Settings className="mr-2 h-4 w-4" />
                              الإعدادات
                            </Button>
                          </Link>
                          {isAdmin && (
                            <Link href="/admin/dashboard" onClick={() => setIsMenuOpen(false)}>
                              <Button variant="ghost" className="w-full justify-start">
                                <Settings className="mr-2 h-4 w-4" />
                                لوحة الإدارة
                              </Button>
                            </Link>
                          )}
                          <Button
                            variant="ghost"
                            className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => {
                              logout()
                              setIsMenuOpen(false)
                            }}
                          >
                            <LogOut className="mr-2 h-4 w-4" />
                            تسجيل الخروج
                          </Button>
                        </div>

                        <Link href="/initiatives/create" onClick={() => setIsMenuOpen(false)}>
                          <Button className="w-full bg-green-600 hover:bg-green-700">
                            <PlusCircle className="mr-2 h-4 w-4" />
                            إنشاء مبادرة
                          </Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Link href="/auth/login" onClick={() => setIsMenuOpen(false)}>
                          <Button variant="outline" className="w-full">
                            تسجيل الدخول
                          </Button>
                        </Link>
                        <Link href="/auth/register" onClick={() => setIsMenuOpen(false)}>
                          <Button className="w-full bg-green-600 hover:bg-green-700">إنشاء حساب</Button>
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}

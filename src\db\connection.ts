import mongoose from "mongoose"

// Connection URI
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/initiatives_dz"

// Connection options
const options = {
  // These options are deprecated in Mongoose 6+, but keeping them for backward compatibility
  // with older versions of Mongoose
  useNewUrlParser: true,
  useUnifiedTopology: true,
  // Add additional options for better connection handling
  serverSelectionTimeoutMS: 5000, // Timeout for server selection
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  family: 4 // Use IPv4, skip trying IPv6
} as mongoose.ConnectOptions

// Cache the database connection
let cachedConnection: mongoose.Connection | null = null

/**
 * Connect to MongoDB
 * @returns {Promise<mongoose.Connection>} Mongoose connection
 */
export async function connectToDatabase(): Promise<mongoose.Connection> {
  // If we have a cached connection, return it
  if (cachedConnection) {
    return cachedConnection
  }

  try {
    // Connect to MongoDB
    console.log(`Connecting to MongoDB at: ${MONGODB_URI.replace(/\/\/([^:]+):[^@]+@/, '//***:***@')}`)
    const conn = await mongoose.connect(MONGODB_URI, options)

    // Cache the connection
    cachedConnection = conn.connection

    console.log(`MongoDB connected: ${conn.connection.host}`)

    // Handle connection events
    mongoose.connection.on("error", (err) => {
      console.error("MongoDB connection error:", err)
      cachedConnection = null
    })

    mongoose.connection.on("disconnected", () => {
      console.warn("MongoDB disconnected")
      cachedConnection = null
    })

    mongoose.connection.on("connected", () => {
      console.log("MongoDB reconnected")
    })

    // Return the connection
    return conn.connection
  } catch (error) {
    console.error("Error connecting to MongoDB:", error)
    // Create a more descriptive error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`Failed to connect to MongoDB. Please check your connection string and ensure MongoDB is running. Error: ${errorMessage}`);
    throw error
  }
}

/**
 * Disconnect from MongoDB
 */
export async function disconnectFromDatabase(): Promise<void> {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.disconnect()
    cachedConnection = null
    console.log("MongoDB disconnected")
  }
}

/**
 * Get the current MongoDB connection
 * @returns {mongoose.Connection|null} The current connection or null if not connected
 */
export function getConnection(): mongoose.Connection | null {
  return mongoose.connection.readyState !== 0 ? mongoose.connection : null
}

export default {
  connectToDatabase,
  disconnectFromDatabase,
  getConnection,
}


"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../ui/button"
import { Input } from "../ui/input"
import { Textarea } from "../ui/textarea"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "../ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { Loader2 } from "lucide-react"
import { api } from "../../lib/api"
import { toast } from "../ui/use-toast"

interface ResourceNeed {
  _id: string
  type: string
  name: string
  description: string
  quantity: number
  unit: string
  priority: string
  status: string
  notes?: string
}

interface ResourceNeedDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initiativeId: string
  resourceNeed: ResourceNeed | null
  onSuccess: () => void
}

export default function ResourceNeedDialog({
  open,
  onOpenChange,
  initiativeId,
  resourceNeed,
  onSuccess,
}: ResourceNeedDialogProps) {
  const [formData, setFormData] = useState({
    type: "material",
    name: "",
    description: "",
    quantity: 1,
    unit: "",
    priority: "medium",
    notes: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!resourceNeed

  useEffect(() => {
    if (resourceNeed) {
      setFormData({
        type: resourceNeed.type,
        name: resourceNeed.name,
        description: resourceNeed.description,
        quantity: resourceNeed.quantity,
        unit: resourceNeed.unit,
        priority: resourceNeed.priority,
        notes: resourceNeed.notes || "",
      })
    } else {
      // Reset form when not editing
      setFormData({
        type: "material",
        name: "",
        description: "",
        quantity: 1,
        unit: "",
        priority: "medium",
        notes: "",
      })
    }
  }, [resourceNeed, open])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: name === "quantity" ? Number(value) : value,
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let response

      if (isEditing && resourceNeed) {
        // Update existing resource need
        response = await api.put(`/api/resource-needs/${resourceNeed._id}`, formData, true)
      } else {
        // Create new resource need
        response = await api.post(`/api/resource-needs/initiative/${initiativeId}`, formData, true)
      }

      if (response.success) {
        toast({
          title: isEditing ? "تم تحديث الاحتياج بنجاح" : "تم إضافة الاحتياج بنجاح",
          description: isEditing ? "تم تحديث احتياج المورد بنجاح" : "تمت إضافة احتياج المورد بنجاح",
          variant: "default",
        })

        // Call success callback
        onSuccess()
      } else {
        throw new Error(response.message || `Failed to ${isEditing ? 'update' : 'create'} resource need`)
      }
    } catch (err: any) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} resource need:`, err)
      toast({
        title: "خطأ",
        description: err.message || `حدث خطأ أثناء ${isEditing ? 'تحديث' : 'إضافة'} احتياج المورد`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "تعديل احتياج مورد" : "إضافة احتياج مورد"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "قم بتعديل تفاصيل احتياج المورد."
              : "قم بتعبئة النموذج التالي لإضافة احتياج مورد جديد للمبادرة."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="type" className="font-medium">
              نوع المورد
            </label>
            <Select
              value={formData.type}
              onValueChange={(value) => handleSelectChange("type", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="اختر نوع المورد" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="material">مادي</SelectItem>
                <SelectItem value="financial">مالي</SelectItem>
                <SelectItem value="human">بشري</SelectItem>
                <SelectItem value="service">خدمة</SelectItem>
                <SelectItem value="other">آخر</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="name" className="font-medium">
              اسم المورد
            </label>
            <Input
              id="name"
              name="name"
              placeholder="أدخل اسم المورد"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="font-medium">
              وصف المورد
            </label>
            <Textarea
              id="description"
              name="description"
              placeholder="أدخل وصفاً تفصيلياً للمورد المطلوب"
              value={formData.description}
              onChange={handleChange}
              className="min-h-[100px]"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="quantity" className="font-medium">
                الكمية
              </label>
              <Input
                id="quantity"
                name="quantity"
                type="number"
                min="1"
                placeholder="الكمية"
                value={formData.quantity}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="unit" className="font-medium">
                الوحدة
              </label>
              <Input
                id="unit"
                name="unit"
                placeholder="مثال: قطعة، كيلوغرام، ساعة"
                value={formData.unit}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="priority" className="font-medium">
              الأولوية
            </label>
            <Select
              value={formData.priority}
              onValueChange={(value) => handleSelectChange("priority", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="اختر الأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">منخفضة</SelectItem>
                <SelectItem value="medium">متوسطة</SelectItem>
                <SelectItem value="high">عالية</SelectItem>
                <SelectItem value="critical">حرجة</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="notes" className="font-medium">
              ملاحظات إضافية
            </label>
            <Textarea
              id="notes"
              name="notes"
              placeholder="أي ملاحظات إضافية حول المورد المطلوب"
              value={formData.notes}
              onChange={handleChange}
              className="min-h-[80px]"
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : isEditing ? (
                "تحديث الاحتياج"
              ) : (
                "إضافة الاحتياج"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

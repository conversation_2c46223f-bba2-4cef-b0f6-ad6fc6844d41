#!/bin/bash

echo "Starting Initiatives DZ Application..."

# Check if MongoDB is running
echo "Checking MongoDB connection..."
node scripts/check-mongodb.js
if [ $? -ne 0 ]; then
    echo "MongoDB connection failed. Please make sure MongoDB is running."
    exit 1
fi

# Ensure npm is used and install dependencies if needed
echo "Checking npm configuration..."
node scripts/use-npm.js
if [ $? -ne 0 ]; then
    echo "Failed to configure npm."
    exit 1
fi

# Setup database if needed
echo "Setting up database..."
npm run setup-db

# Start the application
echo "Starting application with full functionality..."
npm run dev:transpile

echo "Application is running!"

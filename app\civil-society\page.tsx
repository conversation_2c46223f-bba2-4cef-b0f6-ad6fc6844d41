"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Search, Users, Loader2, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import CivilSocietyCard from "@/components/civil-society/civil-society-card"
import { api } from "@/lib/api"

// Types d'organisations
const ORGANIZATION_TYPES = [
  { id: "all", name: "جميع الأنواع" },
  { id: "association", name: "جمعية" },
  { id: "club", name: "نادي" },
  { id: "foundation", name: "مؤسسة" },
  { id: "organization", name: "منظمة" },
]

// Étendues géographiques
const SCOPE_TYPES = [
  { id: "all", name: "جميع النطاقات" },
  { id: "local", name: "محلي" },
  { id: "regional", name: "إقليمي" },
  { id: "national", name: "وطني" },
  { id: "international", name: "دولي" },
]

// Options de tri
const SORT_OPTIONS = [
  { id: "-createdAt", name: "الأحدث" },
  { id: "createdAt", name: "الأقدم" },
  { id: "name", name: "الاسم (أ-ي)" },
  { id: "-name", name: "الاسم (ي-أ)" },
]

interface CivilSocietyOrg {
  _id: string
  name: string
  username: string
  avatar?: string
  bio?: string
  location?: string
  organizationName?: string
  organizationType?: string
  activitySector?: string
  scope?: string
  organizationDescription?: string
}

interface PaginationState {
  currentPage: number
  totalPages: number
  total: number
  limit: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export default function CivilSocietyPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [organizations, setOrganizations] = useState<CivilSocietyOrg[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "")
  const [typeFilter, setTypeFilter] = useState(searchParams.get("type") || "all")
  const [scopeFilter, setScopeFilter] = useState(searchParams.get("scope") || "all")
  const [sortOrder, setSortOrder] = useState(searchParams.get("sort") || "-createdAt")

  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: parseInt(searchParams.get("page") || "1"),
    totalPages: 1,
    total: 0,
    limit: 12,
    hasNextPage: false,
    hasPrevPage: false,
  })

  useEffect(() => {
    fetchOrganizations()
  }, [pagination.currentPage, typeFilter, scopeFilter, sortOrder])

  const fetchOrganizations = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Build query parameters
      const queryParams = new URLSearchParams()
      queryParams.append("page", pagination.currentPage.toString())
      queryParams.append("limit", pagination.limit.toString())
      queryParams.append("userType", "civilSociety")
      queryParams.append("sort", sortOrder)

      if (searchTerm) {
        queryParams.append("search", searchTerm)
      }

      if (typeFilter && typeFilter !== "all") {
        queryParams.append("organizationType", typeFilter)
      }

      if (scopeFilter && scopeFilter !== "all") {
        queryParams.append("scope", scopeFilter)
      }

      // Fetch organizations using the API helper
      console.log(`Fetching organizations with query: /api/users?userType=civilSociety&${queryParams.toString()}`);
      const data = await api.get(`/api/users?userType=civilSociety&${queryParams.toString()}`)

      if (data.success) {
        setOrganizations(data.users || [])
        setPagination({
          currentPage: data.pagination.currentPage,
          totalPages: data.pagination.totalPages,
          total: data.pagination.total,
          limit: data.pagination.limit,
          hasNextPage: data.pagination.hasNextPage,
          hasPrevPage: data.pagination.hasPrevPage
        })
      } else {
        throw new Error(data.error || "فشل في جلب بيانات منظمات المجتمع المدني")
      }
    } catch (err: any) {
      setError(err.message || "حدث خطأ أثناء جلب بيانات منظمات المجتمع المدني")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, currentPage: 1 }))
    updateUrl()
    fetchOrganizations()
  }

  const handlePageChange = (page: number) => {
    if (page < 1 || page > pagination.totalPages) return
    setPagination(prev => ({ ...prev, currentPage: page }))
    updateUrl(page)
  }

  const updateUrl = (page = 1) => {
    const params = new URLSearchParams()
    if (searchTerm) params.set("search", searchTerm)
    if (typeFilter !== "all") params.set("type", typeFilter)
    if (scopeFilter !== "all") params.set("scope", scopeFilter)
    if (sortOrder !== "-createdAt") params.set("sort", sortOrder)
    if (page > 1) params.set("page", page.toString())

    const newUrl = `/civil-society${params.toString() ? `?${params.toString()}` : ""}`
    router.push(newUrl, { scroll: false })
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="bg-[#0a8754] text-white py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">ممثلو المجتمع المدني</h1>
          <p className="text-lg opacity-90 mb-8">
            تعرف على الجمعيات والمنظمات التي تدعم المبادرات المجتمعية وتساهم في تنميتها
          </p>

          <form onSubmit={handleSearch} className="max-w-xl mx-auto">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="ابحث عن منظمة..."
                className="pr-10 bg-white text-black border-0 h-12 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button type="submit" className="absolute left-1 top-1/2 transform -translate-y-1/2 bg-green-700 hover:bg-green-800 h-10">
                بحث
              </Button>
            </div>
          </form>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col md:flex-row justify-between items-start gap-6 mb-8">
          <div className="w-full md:w-64 space-y-4">
            <Card>
              <CardContent className="p-4">
                <h2 className="text-lg font-bold mb-4 flex items-center">
                  <Users className="ml-2 h-5 w-5" />
                  تصفية النتائج
                </h2>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium block mb-2">نوع المنظمة</label>
                    <Select value={typeFilter} onValueChange={(value) => {
                      setTypeFilter(value)
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر النوع" />
                      </SelectTrigger>
                      <SelectContent>
                        {ORGANIZATION_TYPES.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium block mb-2">النطاق الجغرافي</label>
                    <Select value={scopeFilter} onValueChange={(value) => {
                      setScopeFilter(value)
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر النطاق" />
                      </SelectTrigger>
                      <SelectContent>
                        {SCOPE_TYPES.map((scope) => (
                          <SelectItem key={scope.id} value={scope.id}>
                            {scope.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium block mb-2">ترتيب حسب</label>
                    <Select value={sortOrder} onValueChange={(value) => {
                      setSortOrder(value)
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الترتيب" />
                      </SelectTrigger>
                      <SelectContent>
                        {SORT_OPTIONS.map((option) => (
                          <SelectItem key={option.id} value={option.id}>
                            {option.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      setSearchTerm("")
                      setTypeFilter("all")
                      setScopeFilter("all")
                      setSortOrder("-createdAt")
                      setPagination(prev => ({ ...prev, currentPage: 1 }))
                      router.push("/civil-society")
                    }}
                  >
                    إعادة ضبط الفلاتر
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex-1">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="mr-2">جاري تحميل المنظمات...</span>
              </div>
            ) : organizations.length === 0 ? (
              <div className="text-center py-12 bg-white rounded-lg shadow-sm">
                <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-bold mb-2">لا توجد منظمات</h3>
                <p className="text-gray-500 mb-4">
                  لم يتم العثور على منظمات تطابق معايير البحث الخاصة بك
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setTypeFilter("all")
                    setScopeFilter("all")
                    setSortOrder("-createdAt")
                    setPagination(prev => ({ ...prev, currentPage: 1 }))
                    router.push("/civil-society")
                  }}
                >
                  إعادة ضبط الفلاتر
                </Button>
              </div>
            ) : (
              <>
                <div className="mb-4 flex justify-between items-center">
                  <p className="text-gray-600">
                    تم العثور على {pagination.total} منظمة
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-6 mb-8">
                  {organizations.map((org) => (
                    <CivilSocietyCard key={org._id} organization={org} />
                  ))}
                </div>

                {pagination.totalPages > 1 && (
                  <Pagination className="mt-8">
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(pagination.currentPage - 1)}
                          className={!pagination.hasPrevPage ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                      
                      {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                        .filter(page => {
                          // Show first page, last page, current page, and pages around current page
                          return page === 1 || 
                                 page === pagination.totalPages || 
                                 Math.abs(page - pagination.currentPage) <= 1
                        })
                        .map((page, index, array) => {
                          // Add ellipsis if there are gaps
                          if (index > 0 && array[index - 1] !== page - 1) {
                            return (
                              <PaginationItem key={`ellipsis-${page}`}>
                                <span className="px-4 py-2">...</span>
                              </PaginationItem>
                            )
                          }
                          
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => handlePageChange(page)}
                                isActive={page === pagination.currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          )
                        })}
                      
                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(pagination.currentPage + 1)}
                          className={!pagination.hasNextPage ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

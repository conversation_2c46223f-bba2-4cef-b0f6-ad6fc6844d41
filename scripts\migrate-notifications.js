const { MongoClient, ObjectId } = require('mongodb');

async function migrateNotifications() {
  try {
    const client = await MongoClient.connect('mongodb://localhost:27017/initiatives_dz');
    const db = client.db();
    const notificationsCollection = db.collection('notifications');
    
    console.log('Starting notification migration...');
    
    // Trouver toutes les notifications
    const allNotifications = await notificationsCollection.find().toArray();
    console.log(`Found ${allNotifications.length} notifications to process`);
    
    let migratedCount = 0;
    let alreadyStandardCount = 0;
    
    // Traiter chaque notification
    for (const notification of allNotifications) {
      // Vérifier si la notification utilise l'ancien format (title/message/read)
      if (notification.title !== undefined || notification.message !== undefined || notification.read !== undefined || notification.initiative !== undefined) {
        const updates = {};
        
        // Convertir title/message en content
        if (notification.title && notification.message) {
          updates.content = `${notification.title}: ${notification.message}`;
        } else if (notification.message) {
          updates.content = notification.message;
        } else if (notification.title) {
          updates.content = notification.title;
        }
        
        // Convertir read en isRead
        if (notification.read !== undefined) {
          updates.isRead = notification.read;
        }
        
        // Convertir initiative en relatedInitiative
        if (notification.initiative) {
          updates.relatedInitiative = notification.initiative;
        }
        
        // Mettre à jour la notification
        await notificationsCollection.updateOne(
          { _id: notification._id },
          { 
            $set: updates,
            $unset: {
              title: "",
              message: "",
              read: "",
              initiative: ""
            }
          }
        );
        
        migratedCount++;
      } else {
        alreadyStandardCount++;
      }
    }
    
    console.log(`Migration complete!`);
    console.log(`- ${migratedCount} notifications migrated to standard format`);
    console.log(`- ${alreadyStandardCount} notifications already in standard format`);
    
    await client.close();
  } catch (error) {
    console.error('Error during migration:', error);
  }
}

migrateNotifications();

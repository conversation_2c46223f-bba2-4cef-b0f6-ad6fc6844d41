import express from "express"
import { authenticate } from "../middleware/auth"
import {
  getUpdatesByInitiative,
  getUpdateById,
  createUpdate,
  updateUpdate,
  deleteUpdate
} from "../controllers/update.controller"

const router = express.Router()

// Public routes
router.get("/initiative/:initiativeId", getUpdatesByInitiative)
router.get("/:id", getUpdateById)

// Protected routes
router.post("/", authenticate, createUpdate) // Add a route for creating updates without URL parameter
router.post("/initiative/:initiativeId", authenticate, createUpdate) // Keep the original route for backward compatibility
router.put("/:id", authenticate, updateUpdate)
router.delete("/:id", authenticate, deleteUpdate)

export default router

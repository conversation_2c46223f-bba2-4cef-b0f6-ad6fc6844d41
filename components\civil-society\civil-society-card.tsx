"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Users, MapPin, Globe } from "lucide-react"

// Types d'organisations
const ORGANIZATION_TYPES = [
  { id: "association", name: "جمعية", icon: "🏛️" },
  { id: "club", name: "نادي", icon: "🏆" },
  { id: "foundation", name: "مؤسسة", icon: "🏢" },
  { id: "organization", name: "منظمة", icon: "🌐" },
]

// Étendues géographiques
const SCOPE_TYPES = [
  { id: "local", name: "محلي", icon: "🏙️" },
  { id: "regional", name: "إقليمي", icon: "🗺️" },
  { id: "national", name: "وطني", icon: "🇩🇿" },
  { id: "international", name: "دولي", icon: "🌍" },
]

interface CivilSocietyCardProps {
  organization: {
    _id: string
    name: string
    username: string
    avatar?: string
    bio?: string
    location?: string
    organizationName?: string
    organizationType?: string
    activitySector?: string
    scope?: string
    organizationDescription?: string
  }
}

export default function CivilSocietyCard({ organization }: CivilSocietyCardProps) {
  // Obtenir le nom du type d'organisation
  const getOrganizationTypeName = (typeId?: string) => {
    if (!typeId) return ''
    const found = ORGANIZATION_TYPES.find(type => type.id === typeId)
    return found ? found.name : typeId
  }

  // Obtenir l'icône du type d'organisation
  const getOrganizationTypeIcon = (typeId?: string) => {
    if (!typeId) return '🏛️'
    const found = ORGANIZATION_TYPES.find(type => type.id === typeId)
    return found ? found.icon : '🏛️'
  }

  // Obtenir le nom de l'étendue
  const getScopeName = (scopeId?: string) => {
    if (!scopeId) return ''
    const found = SCOPE_TYPES.find(scope => scope.id === scopeId)
    return found ? found.name : scopeId
  }

  // Obtenir l'icône de l'étendue
  const getScopeIcon = (scopeId?: string) => {
    if (!scopeId) return '🌍'
    const found = SCOPE_TYPES.find(scope => scope.id === scopeId)
    return found ? found.icon : '🌍'
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-4">
          <Avatar className="h-20 w-20 shrink-0">
            <AvatarImage
              src={organization.avatar}
              alt={organization.name}
              preserveAspectRatio={true}
              style={{ objectFit: "contain", maxWidth: "100%", maxHeight: "100%" }}
            />
            <AvatarFallback>
              <Users className="h-8 w-8 text-gray-400" />
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-bold mb-1">{organization.organizationName || organization.name}</h3>
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-1">
              <span>{getOrganizationTypeIcon(organization.organizationType)}</span>
              <span>{getOrganizationTypeName(organization.organizationType)}</span>
            </div>
            {organization.location && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <MapPin className="h-4 w-4 shrink-0" />
                <span>{organization.location}</span>
              </div>
            )}
          </div>
          {organization.scope && (
            <Badge variant="outline" className="bg-green-50 text-green-800 self-start">
              <span className="mr-1">{getScopeIcon(organization.scope)}</span> {getScopeName(organization.scope)}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {(organization.bio || organization.organizationDescription) && (
          <p className="text-gray-600 mb-4 line-clamp-2">{organization.bio || organization.organizationDescription}</p>
        )}

        {organization.activitySector && (
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">قطاع النشاط</h4>
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              {organization.activitySector}
            </Badge>
          </div>
        )}

        <Link href={`/users/${organization._id}`}>
          <Button className="w-full bg-green-600 hover:bg-green-700">عرض الملف الكامل</Button>
        </Link>
      </CardContent>
    </Card>
  )
}

import express from "express"
import mongoose from "mongoose"
import { Initiative, Milestone, User, Category } from "../models"

const router = express.Router()

// Get progress data with pagination and filtering
router.get("/", async (req, res) => {
  try {
    // Parse pagination parameters
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Build filter object
    const filter: any = {}

    // Status filter
    if (req.query.status) {
      if (req.query.status === "active") {
        filter.status = "active"
      } else if (req.query.status === "completed") {
        filter.status = "completed"
      } else {
        // Default: both active and completed
        filter.status = { $in: ['active', 'completed'] }
      }
    } else {
      // Default: both active and completed
      filter.status = { $in: ['active', 'completed'] }
    }

    // Search filter (search in title, category, and location)
    if (req.query.search) {
      const searchTerm = req.query.search as string
      filter.$or = [
        { title: { $regex: searchTerm, $options: "i" } },
        { location: { $regex: searchTerm, $options: "i" } }
      ]

      // Try to find category by name
      const categories = await Category.find({
        $or: [
          { name: { $regex: searchTerm, $options: "i" } },
          { arabicName: { $regex: searchTerm, $options: "i" } }
        ]
      })

      if (categories.length > 0) {
        const categoryIds = categories.map(cat => cat._id)
        filter.$or.push({ category: { $in: categoryIds } })
      }
    }

    // Get total count for pagination
    const total = await Initiative.countDocuments(filter)

    // Get initiatives with milestones
    const initiatives = await Initiative.find(filter)
      .populate('author', 'name username avatar')
      .populate('category', 'name arabicName')
      .sort('-createdAt')
      .skip(skip)
      .limit(limit)

    // Get milestones for each initiative
    const initiativesWithProgress = await Promise.all(
      initiatives.map(async (initiative) => {
        const milestones = await Milestone.find({ initiative: initiative._id }).sort('order')

        // Calculate progress based on completed milestones
        const totalMilestones = milestones.length
        const completedMilestones = milestones.filter(m => m.isCompleted).length
        const progress = totalMilestones > 0 ? Math.round((completedMilestones / totalMilestones) * 100) : 0

        // Format dates to Arabic format
        const formatDate = (date: Date) => {
          try {
            return new Date(date).toLocaleDateString('ar-DZ', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })
          } catch (error) {
            return ''
          }
        }

        return {
          id: initiative._id,
          title: initiative.title,
          category: initiative.category.arabicName || initiative.category.name,
          location: initiative.location,
          startDate: formatDate(initiative.createdAt),
          endDate: initiative.endDate ? formatDate(initiative.endDate) : '',
          progress: progress,
          status: initiative.status === 'completed' ? 'مكتملة' : 'جارية',
          milestones: milestones.map(m => ({
            id: m._id,
            title: m.title,
            description: m.description,
            completed: m.isCompleted,
            date: formatDate(m.targetDate),
            completedDate: m.completedDate ? formatDate(m.completedDate) : null,
          })),
        }
      })
    )

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit)

    return res.json({
      success: true,
      initiatives: initiativesWithProgress,
      pagination: {
        total,
        count: initiativesWithProgress.length,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching progress data:', error)
    return res.status(500).json(
      { success: false, error: 'Failed to fetch progress data' }
    )
  }
})

export default router

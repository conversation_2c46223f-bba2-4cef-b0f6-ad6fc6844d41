import mongoose from 'mongoose';
import { Initiative } from '../models';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz';

async function updateInitiativeStatus() {
  try {
    console.log(`Connecting to MongoDB at: ${MONGODB_URI}`);
    await mongoose.connect(MONGODB_URI);
    console.log('MongoDB connected');

    // Update all initiatives with status "pending" to "active"
    const result = await Initiative.updateMany(
      { status: 'pending' },
      { $set: { status: 'active' } }
    );

    console.log(`Updated ${result.modifiedCount} initiatives from "pending" to "active"`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
  } catch (error) {
    console.error('Error updating initiative status:', error);
  }
}

// Run the function
updateInitiativeStatus();

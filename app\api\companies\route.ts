import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/db';
import { User } from '@/src/models';

export async function GET(request: Request) {
  try {
    // Connect to the database
    await connectToDatabase();

    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get('page')) || 1;
    const limit = Number(searchParams.get('limit')) || 12;
    const search = searchParams.get('search') || '';
    const industry = searchParams.get('industry') || 'all';
    const resource = searchParams.get('resource') || 'all';
    const sort = searchParams.get('sort') || '-createdAt';

    // Build filter object
    const filter: any = { userType: "company" };

    // Industry filter
    if (industry !== 'all') {
      filter.industry = industry;
    }

    // Resource filter
    if (resource !== 'all') {
      filter.resources = resource;
    }

    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { companyName: { $regex: search, $options: 'i' } },
        { industry: { $regex: search, $options: 'i' } },
        { services: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await User.countDocuments(filter);

    // Get companies
    const companies = await User.find(filter)
      .select("name username avatar bio location companyName industry services resources companyDescription")
      .sort(sort)
      .skip(skip)
      .limit(limit);

    return NextResponse.json({
      success: true,
      companies,
      pagination: {
        total,
        count: companies.length,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        limit,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching companies:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch companies' },
      { status: 500 }
    );
  }
}

import { Schema } from "mongoose"

const votingOptionSchema = new Schema(
  {
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    votes: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    voteCount: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true },
)

export default votingOptionSchema


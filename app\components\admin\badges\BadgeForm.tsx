"use client"

import { useState } from "react"
import { Button } from "../../../components/ui/button"
import { Input } from "../../../components/ui/input"
import { Label } from "../../../components/ui/label"
import { Textarea } from "../../../components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../../components/ui/card"
import { Award, Loader2, Save } from "lucide-react"
import { api } from "../../../lib/api"
import { toast } from "../../../components/ui/use-toast"

interface BadgeFormProps {
  badge?: any
  onSuccess: () => void
}

export default function BadgeForm({ badge, onSuccess }: BadgeFormProps) {
  const [formData, setFormData] = useState({
    name: badge?.name || "",
    arabicName: badge?.arabicName || "",
    description: badge?.description || "",
    arabicDescription: badge?.arabicDescription || "",
    icon: badge?.icon || "award",
    color: badge?.color || "#4CAF50",
    criteria: badge?.criteria || "",
    category: badge?.category || "participation",
    level: badge?.level || 1
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let response
      if (badge?._id) {
        // Update existing badge
        response = await api.put(`/api/badges/${badge._id}`, formData, true)
      } else {
        // Create new badge
        response = await api.post("/api/badges", formData, true)
      }

      if (response.success) {
        toast({
          title: badge?._id ? "تم تحديث الشارة" : "تم إنشاء الشارة",
          description: badge?._id ? "تم تحديث الشارة بنجاح" : "تم إنشاء الشارة بنجاح",
          variant: "default"
        })
        onSuccess()
      } else {
        throw new Error(response.message || "حدث خطأ أثناء حفظ الشارة")
      }
    } catch (error: any) {
      console.error("Error saving badge:", error)
      toast({
        title: "خطأ",
        description: error.message || "حدث خطأ أثناء حفظ الشارة",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{badge?._id ? "تعديل شارة" : "إنشاء شارة جديدة"}</CardTitle>
        <CardDescription>
          {badge?._id ? "قم بتعديل بيانات الشارة" : "قم بإدخال بيانات الشارة الجديدة"}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">اسم الشارة (بالإنجليزية)</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Badge Name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="arabicName">اسم الشارة (بالعربية)</Label>
              <Input
                id="arabicName"
                name="arabicName"
                value={formData.arabicName}
                onChange={handleChange}
                placeholder="اسم الشارة"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="description">وصف الشارة (بالإنجليزية)</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Badge Description"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="arabicDescription">وصف الشارة (بالعربية)</Label>
              <Textarea
                id="arabicDescription"
                name="arabicDescription"
                value={formData.arabicDescription}
                onChange={handleChange}
                placeholder="وصف الشارة"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">فئة الشارة</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleSelectChange("category", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر فئة الشارة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="participation">المشاركة</SelectItem>
                  <SelectItem value="achievement">الإنجاز</SelectItem>
                  <SelectItem value="contribution">المساهمة</SelectItem>
                  <SelectItem value="special">خاص</SelectItem>
                  <SelectItem value="skill">المهارة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="level">مستوى الشارة</Label>
              <Select
                value={formData.level.toString()}
                onValueChange={(value) => handleSelectChange("level", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر مستوى الشارة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">المستوى الأول</SelectItem>
                  <SelectItem value="2">المستوى الثاني</SelectItem>
                  <SelectItem value="3">المستوى الثالث</SelectItem>
                  <SelectItem value="4">المستوى الرابع</SelectItem>
                  <SelectItem value="5">المستوى الخامس</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="color">لون الشارة</Label>
              <div className="flex gap-2">
                <Input
                  id="color"
                  name="color"
                  type="color"
                  value={formData.color}
                  onChange={handleChange}
                  className="w-12 h-10 p-1"
                />
                <Input
                  name="color"
                  value={formData.color}
                  onChange={handleChange}
                  placeholder="#RRGGBB"
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="criteria">معايير الحصول على الشارة</Label>
            <Textarea
              id="criteria"
              name="criteria"
              value={formData.criteria}
              onChange={handleChange}
              placeholder="اذكر المعايير التي يجب توفرها للحصول على هذه الشارة"
              required
            />
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
            <h3 className="font-medium mb-3">معاينة الشارة</h3>
            <div className="flex items-center gap-4">
              <div 
                className="w-16 h-16 rounded-full flex items-center justify-center border-2"
                style={{ 
                  backgroundColor: `${formData.color}20`,
                  borderColor: formData.color
                }}
              >
                <Award className="h-8 w-8" style={{ color: formData.color }} />
              </div>
              <div>
                <h4 className="font-semibold text-lg">{formData.arabicName || "اسم الشارة"}</h4>
                <p className="text-sm text-gray-600">{formData.arabicDescription || "وصف الشارة"}</p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onSuccess}>
            إلغاء
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                جاري الحفظ...
              </>
            ) : (
              <>
                <Save className="ml-2 h-4 w-4" />
                {badge?._id ? "تحديث الشارة" : "إنشاء الشارة"}
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}

"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"

export default function EditProfileRedirect() {
  const router = useRouter()

  useEffect(() => {
    router.push("/users/settings")
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-green-600" />
      <span className="mr-2">جاري التحويل إلى صفحة الإعدادات...</span>
    </div>
  )
}

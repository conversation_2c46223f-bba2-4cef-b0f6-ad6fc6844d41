import mongoose, { Schema, Document } from "mongoose"

export interface IResource extends Document {
  initiative: mongoose.Types.ObjectId
  provider: mongoose.Types.ObjectId
  providerType?: string // Type d'acteur (company, civil_society, volunteer, etc.)
  type: string
  name: string
  description: string
  quantity: number
  unit: string
  status: string
  requestedDate: Date
  approvedDate?: Date
  rejectedDate?: Date
  deliveredDate?: Date
  expectedDeliveryDate?: Date // Date prévue de livraison
  contactPerson?: string // Personne de contact pour la livraison
  contactEmail?: string // Email de contact
  contactPhone?: string // Téléphone de contact
  isPublic: boolean // Si la contribution doit être affichée publiquement
  notes?: string
  attachments?: string[]
  relatedResourceNeed?: mongoose.Types.ObjectId // Besoin auquel cette ressource répond
}

const resourceSchema = new Schema(
  {
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    provider: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    providerType: {
      type: String,
      enum: ["company", "civilSociety", "volunteer", "proposer", "admin"],
    },
    type: {
      type: String,
      enum: ["material", "financial", "human", "service", "other"],
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
      min: 0,
    },
    unit: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ["requested", "approved", "rejected", "delivered", "canceled"],
      default: "requested",
    },
    requestedDate: {
      type: Date,
      default: Date.now,
    },
    approvedDate: {
      type: Date,
    },
    rejectedDate: {
      type: Date,
    },
    deliveredDate: {
      type: Date,
    },
    expectedDeliveryDate: {
      type: Date,
    },
    contactPerson: {
      type: String,
    },
    contactEmail: {
      type: String,
    },
    contactPhone: {
      type: String,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
    notes: {
      type: String,
    },
    attachments: [
      {
        type: String,
      },
    ],
    relatedResourceNeed: {
      type: Schema.Types.ObjectId,
      ref: "ResourceNeed",
    },
  },
  { timestamps: true }
)

// Create indexes for better query performance
resourceSchema.index({ initiative: 1, status: 1 })
resourceSchema.index({ provider: 1, status: 1 })
resourceSchema.index({ relatedResourceNeed: 1 })
resourceSchema.index({ providerType: 1 })

// Check if the model already exists to prevent overwriting
const Resource = mongoose.models.Resource || mongoose.model<IResource>("Resource", resourceSchema)

export default Resource

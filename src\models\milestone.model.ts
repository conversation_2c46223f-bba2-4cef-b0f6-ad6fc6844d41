import mongoose, { Schema, model } from "mongoose"

const milestoneSchema = new Schema(
  {
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    targetDate: {
      type: Date,
      required: true,
    },
    completedDate: {
      type: Date,
    },
    isCompleted: {
      type: Boolean,
      default: false,
    },
    order: {
      type: Number,
      required: true,
    },
  },
  { timestamps: true },
)

// Check if the model already exists to prevent overwriting
const Milestone = mongoose.models.Milestone || model("Milestone", milestoneSchema)

export default Milestone

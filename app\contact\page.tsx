"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Loader2, Mail, Phone, MapPin } from "lucide-react"
import { api } from "@/lib/api"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      setError("Please fill in all required fields")
      return
    }

    setIsSubmitting(true)
    setError("")

    try {
      // Send contact form data to API
      await api.post("/api/contact", formData, false)

      // Show success message
      setSuccess(true)

      // Reset form
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
      })
    } catch (err: any) {
      setError(err.message || "An error occurred while sending your message")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen py-12" dir="rtl">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">تواصل معنا</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            نحن هنا للإجابة على استفساراتك ومساعدتك في كل ما يتعلق بمنصة المبادرات. يمكنك التواصل معنا من خلال النموذج
            أدناه.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>أرسل رسالة</CardTitle>
                <CardDescription>يرجى ملء النموذج أدناه وسنقوم بالرد عليك في أقرب وقت ممكن.</CardDescription>
              </CardHeader>
              <CardContent>
                {error && (
                  <Alert variant="destructive" className="mb-6">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="mb-6 bg-green-50 border-green-200">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      تم إرسال رسالتك بنجاح. سنقوم بالرد عليك في أقرب وقت ممكن.
                    </AlertDescription>
                  </Alert>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="block font-medium">
                        الاسم <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="أدخل اسمك الكامل"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="email" className="block font-medium">
                        البريد الإلكتروني <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="أدخل بريدك الإلكتروني"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="subject" className="block font-medium">
                      الموضوع
                    </label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      placeholder="موضوع الرسالة"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="message" className="block font-medium">
                      الرسالة <span className="text-red-500">*</span>
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      placeholder="اكتب رسالتك هنا..."
                      className="min-h-[200px]"
                      required
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" className="bg-[#0a8754] hover:bg-[#097548]" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          جاري الإرسال...
                        </>
                      ) : (
                        "إرسال الرسالة"
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>معلومات التواصل</CardTitle>
                <CardDescription>يمكنك التواصل معنا مباشرة من خلال:</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start gap-3">
                  <div className="bg-[#0a8754] text-white p-2 rounded-full">
                    <Mail className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">البريد الإلكتروني</h3>
                    <p className="text-gray-600 mt-1"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-[#0a8754] text-white p-2 rounded-full">
                    <Phone className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">الهاتف</h3>
                    <p className="text-gray-600 mt-1">+213 XX XX XX XX</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-[#0a8754] text-white p-2 rounded-full">
                    <MapPin className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-medium">العنوان</h3>
                    <p className="text-gray-600 mt-1">الجزائر العاصمة، الجزائر</p>
                  </div>
                </div>

                <div className="pt-6 mt-6 border-t border-gray-200">
                  <h3 className="font-medium mb-3">ساعات العمل</h3>
                  <div className="space-y-2 text-gray-600">
                    <div className="flex justify-between">
                      <span>الأحد - الخميس:</span>
                      <span>9:00 - 17:00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الجمعة:</span>
                      <span>9:00 - 12:00</span>
                    </div>
                    <div className="flex justify-between">
                      <span>السبت:</span>
                      <span>مغلق</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}


import type { Request, Response, NextFunction } from "express"
import <PERSON><PERSON> from "joi"
import { createError } from "../../utils/error"

// Validate comment creation
export const validateComment = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    initiative: Joi.string().required().messages({
      "string.empty": "Initiative ID is required",
      "any.required": "Initiative ID is required",
    }),
    content: Joi.string().required().min(1).max(1000).messages({
      "string.empty": "Comment content is required",
      "string.min": "Comment content must be at least 1 character long",
      "string.max": "Comment content cannot exceed 1000 characters",
      "any.required": "Comment content is required",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

// Validate comment update
export const validateCommentUpdate = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    content: Joi.string().required().min(1).max(1000).messages({
      "string.empty": "Comment content is required",
      "string.min": "Comment content must be at least 1 character long",
      "string.max": "Comment content cannot exceed 1000 characters",
      "any.required": "Comment content is required",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}


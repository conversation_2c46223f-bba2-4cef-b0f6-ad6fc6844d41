import mongoose, { Document, Schema } from 'mongoose';

export interface INotification extends Document {
  recipient: mongoose.Types.ObjectId;
  sender?: mongoose.Types.ObjectId;
  type: string;
  content: string;
  isRead: boolean;
  relatedInitiative?: mongoose.Types.ObjectId;
  link?: string;
  createdAt: Date;
  updatedAt: Date;
}

const notificationSchema = new Schema<INotification>(
  {
    recipient: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    sender: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    type: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true,
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    relatedInitiative: {
      type: Schema.Types.ObjectId,
      ref: 'Initiative',
    },
    link: {
      type: String,
    },
  },
  { timestamps: true }
);

export default mongoose.model<INotification>('Notification', notificationSchema);

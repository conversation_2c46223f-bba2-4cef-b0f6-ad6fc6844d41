import mongoose from "mongoose";
import { Initiative, Milestone } from "../models";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/initiatives_dz")
  .then(() => console.log("MongoDB connected"))
  .catch(err => console.error("MongoDB connection error:", err));

// Function to update all initiatives
const updateAllInitiatives = async () => {
  try {
    // Get all initiatives
    const initiatives = await Initiative.find({});
    console.log(`Found ${initiatives.length} initiatives`);

    let updatedCount = 0;
    let noChangeCount = 0;
    let errorCount = 0;

    // Process each initiative
    for (const initiative of initiatives) {
      try {
        console.log(`\nProcessing initiative: ${initiative.title} (${initiative._id})`);
        console.log(`Current status: ${initiative.status}, progress: ${initiative.progress}`);

        // Get milestones for this initiative
        const milestones = await Milestone.find({ initiative: initiative._id });
        console.log(`Total milestones: ${milestones.length}`);

        if (milestones.length === 0) {
          console.log("No milestones found, skipping");
          continue;
        }

        // Count completed milestones
        const completedMilestones = milestones.filter(m => m.isCompleted).length;
        console.log(`Completed milestones: ${completedMilestones}`);

        // Calculate progress
        const progress = Math.round((completedMilestones / milestones.length) * 100);
        console.log(`Calculated progress: ${progress}`);

        // Determine if update is needed
        let needsUpdate = false;
        let updateData: any = {};

        if (initiative.progress !== progress) {
          updateData.progress = progress;
          needsUpdate = true;
        }

        // Update status based on progress
        let newStatus = initiative.status;
        if (progress === 100 && initiative.status !== "completed") {
          newStatus = "completed";
          updateData.status = newStatus;
          needsUpdate = true;
        } else if (progress < 100 && progress > 0 && initiative.status === "completed") {
          newStatus = "active";
          updateData.status = newStatus;
          needsUpdate = true;
        }

        // Update initiative if needed
        if (needsUpdate) {
          console.log(`Updating initiative: ${JSON.stringify(updateData)}`);
          await Initiative.findByIdAndUpdate(initiative._id, updateData);
          updatedCount++;
          console.log("Initiative updated successfully");
        } else {
          console.log("No update needed");
          noChangeCount++;
        }
      } catch (err) {
        console.error(`Error processing initiative ${initiative._id}:`, err);
        errorCount++;
      }
    }

    console.log("\n--- Summary ---");
    console.log(`Total initiatives: ${initiatives.length}`);
    console.log(`Updated: ${updatedCount}`);
    console.log(`No change needed: ${noChangeCount}`);
    console.log(`Errors: ${errorCount}`);

  } catch (error) {
    console.error("Error updating initiatives:", error);
  } finally {
    // Close MongoDB connection
    mongoose.connection.close();
    console.log("MongoDB connection closed");
  }
};

// Update all initiatives
updateAllInitiatives();

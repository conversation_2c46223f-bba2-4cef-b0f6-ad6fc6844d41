import mongoose from "mongoose"
import { Badge } from "../models"
import { connectToDatabase } from "../config/database"

const badges = [
  {
    name: "Participation Badge",
    arabicName: "شارة المشاركة",
    description: "Awarded for active participation in initiatives",
    arabicDescription: "تمنح للمشاركة النشطة في المبادرات",
    icon: "award",
    color: "#4CAF50",
    criteria: "المشاركة النشطة في المبادرة",
    category: "participation",
    level: 1
  },
  {
    name: "Achievement Badge",
    arabicName: "شارة الإنجاز",
    description: "Awarded for completing significant milestones",
    arabicDescription: "تمنح لإكمال مراحل مهمة في المبادرة",
    icon: "trophy",
    color: "#FFC107",
    criteria: "إكمال مراحل مهمة في المبادرة",
    category: "achievement",
    level: 1
  },
  {
    name: "Contribution Badge",
    arabicName: "شارة المساهمة",
    description: "Awarded for valuable contributions to initiatives",
    arabicDescription: "تمنح للمساهمات القيمة في المبادرات",
    icon: "gift",
    color: "#2196F3",
    criteria: "تقديم مساهمات قيمة للمبادرة",
    category: "contribution",
    level: 1
  },
  {
    name: "Leadership Badge",
    arabicName: "شارة القيادة",
    description: "Awarded for demonstrating leadership skills",
    arabicDescription: "تمنح لإظهار مهارات القيادة",
    icon: "star",
    color: "#9C27B0",
    criteria: "إظهار مهارات القيادة في المبادرة",
    category: "skill",
    level: 2
  },
  {
    name: "Innovation Badge",
    arabicName: "شارة الابتكار",
    description: "Awarded for innovative ideas and solutions",
    arabicDescription: "تمنح للأفكار والحلول المبتكرة",
    icon: "lightbulb",
    color: "#FF5722",
    criteria: "تقديم أفكار وحلول مبتكرة للمبادرة",
    category: "skill",
    level: 2
  },
  {
    name: "Teamwork Badge",
    arabicName: "شارة العمل الجماعي",
    description: "Awarded for excellent teamwork",
    arabicDescription: "تمنح للعمل الجماعي الممتاز",
    icon: "users",
    color: "#3F51B5",
    criteria: "إظهار مهارات العمل الجماعي الممتازة",
    category: "skill",
    level: 1
  },
  {
    name: "Dedication Badge",
    arabicName: "شارة التفاني",
    description: "Awarded for dedication and commitment",
    arabicDescription: "تمنح للتفاني والالتزام",
    icon: "heart",
    color: "#E91E63",
    criteria: "إظهار التفاني والالتزام تجاه المبادرة",
    category: "special",
    level: 2
  },
  {
    name: "Problem Solver Badge",
    arabicName: "شارة حل المشكلات",
    description: "Awarded for solving complex problems",
    arabicDescription: "تمنح لحل المشكلات المعقدة",
    icon: "tool",
    color: "#795548",
    criteria: "حل المشكلات المعقدة في المبادرة",
    category: "skill",
    level: 3
  }
]

const seedBadges = async () => {
  try {
    // Connect to the database
    await connectToDatabase()
    console.log("Connected to database")

    // Check if badges already exist
    const existingBadgesCount = await Badge.countDocuments()
    if (existingBadgesCount > 0) {
      console.log(`${existingBadgesCount} badges already exist in the database`)
      console.log("Skipping badge seeding")
      process.exit(0)
    }

    // Insert badges
    const result = await Badge.insertMany(badges)
    console.log(`${result.length} badges inserted successfully`)
    process.exit(0)
  } catch (error) {
    console.error("Error seeding badges:", error)
    process.exit(1)
  }
}

seedBadges()

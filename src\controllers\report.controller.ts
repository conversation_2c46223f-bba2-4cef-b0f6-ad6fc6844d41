import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Report, User, Initiative, Comment, Post } from "../models"
import { createError } from "../utils/error"

// Create report
export const createReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { type, reason, description, relatedInitiative, relatedComment, relatedUser, relatedPost } = req.body
    const userId = req.user.id

    // Validate report type
    const validTypes = ["initiative", "comment", "user", "post"]
    if (!validTypes.includes(type)) {
      return next(createError(400, "Invalid report type"))
    }

    // Validate reason
    const validReasons = ["inappropriate", "spam", "offensive", "misleading", "other"]
    if (!validReasons.includes(reason)) {
      return next(createError(400, "Invalid reason"))
    }

    // Validate related entity based on type
    if (type === "initiative") {
      if (!relatedInitiative || !mongoose.Types.ObjectId.isValid(relatedInitiative)) {
        return next(createError(400, "Invalid initiative ID"))
      }

      // Check if initiative exists
      const initiative = await Initiative.findById(relatedInitiative)
      if (!initiative) {
        return next(createError(404, "Initiative not found"))
      }

      // Check if user is reporting their own initiative
      if (initiative.author.toString() === userId) {
        return next(createError(400, "You cannot report your own initiative"))
      }
    } else if (type === "comment") {
      if (!relatedComment || !mongoose.Types.ObjectId.isValid(relatedComment)) {
        return next(createError(400, "Invalid comment ID"))
      }

      // Check if comment exists
      const comment = await Comment.findById(relatedComment)
      if (!comment) {
        return next(createError(404, "Comment not found"))
      }

      // Check if user is reporting their own comment
      if (comment.author.toString() === userId) {
        return next(createError(400, "You cannot report your own comment"))
      }
    } else if (type === "user") {
      if (!relatedUser || !mongoose.Types.ObjectId.isValid(relatedUser)) {
        return next(createError(400, "Invalid user ID"))
      }

      // Check if user exists
      const user = await User.findById(relatedUser)
      if (!user) {
        return next(createError(404, "User not found"))
      }

      // Check if user is reporting themselves
      if (relatedUser === userId) {
        return next(createError(400, "You cannot report yourself"))
      }
    } else if (type === "post") {
      if (!relatedPost || !mongoose.Types.ObjectId.isValid(relatedPost)) {
        return next(createError(400, "Invalid post ID"))
      }

      // Check if post exists
      const post = await Post.findById(relatedPost)
      if (!post) {
        return next(createError(404, "Post not found"))
      }

      // Check if user is reporting their own post
      if (post.author.toString() === userId) {
        return next(createError(400, "You cannot report your own post"))
      }
    }

    // Create report
    const report = new Report({
      reporter: userId,
      type,
      reason,
      description,
      relatedInitiative: type === "initiative" ? relatedInitiative : undefined,
      relatedComment: type === "comment" ? relatedComment : undefined,
      relatedUser: type === "user" ? relatedUser : undefined,
      relatedPost: type === "post" ? relatedPost : undefined,
      status: "pending",
    })

    await report.save()

    // Mark the related entity as reported
    if (type === "initiative") {
      await Initiative.findByIdAndUpdate(relatedInitiative, { isReported: true })
    } else if (type === "comment") {
      await Comment.findByIdAndUpdate(relatedComment, { isReported: true })
    } else if (type === "post") {
      await Post.findByIdAndUpdate(relatedPost, { isReported: true })
    }

    res.status(201).json({
      success: true,
      message: "Report submitted successfully",
      report,
    })
  } catch (error) {
    next(error)
  }
}

// Get user reports
export const getUserReports = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Get reports submitted by the user
    const reports = await Report.find({ reporter: userId })
      .populate("relatedInitiative", "title")
      .populate("relatedUser", "name username")
      .sort("-createdAt")
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Report.countDocuments({ reporter: userId })

    res.status(200).json({
      success: true,
      count: reports.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      reports,
    })
  } catch (error) {
    next(error)
  }
}

// Get report by ID
export const getReportById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate report ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid report ID"))
    }

    // Find report
    const report = await Report.findById(id)
      .populate("reporter", "name username")
      .populate("relatedInitiative", "title")
      .populate("relatedComment", "content")
      .populate("relatedUser", "name username")
      .populate("relatedPost", "content")
      .populate("reviewedBy", "name")

    if (!report) {
      return next(createError(404, "Report not found"))
    }

    // Check if user is the reporter or an admin/moderator
    if (report.reporter._id.toString() !== userId && req.user.role !== "admin" && req.user.role !== "moderator") {
      return next(createError(403, "You are not authorized to view this report"))
    }

    res.status(200).json({
      success: true,
      report,
    })
  } catch (error) {
    next(error)
  }
}


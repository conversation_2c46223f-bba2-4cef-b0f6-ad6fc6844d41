import { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Initiative, Resource, ResourceNeed, User } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { createActivityUtil } from "./activity.controller"

/**
 * Get all resources
 */
export const getResources = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { type, status, sort, limit = 100, page = 1, search } = req.query

    // Build query
    const query: any = {}
    if (type) {
      query.type = type
    }
    if (status) {
      query.status = status
    }

    // Add search functionality
    if (search) {
      const searchRegex = new RegExp(search as string, 'i')
      query.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { notes: searchRegex }
      ]
    }

    // Build sort
    let sortOption: any = { createdAt: -1 }
    if (sort === "name") {
      sortOption = { name: 1 }
    } else if (sort === "quantity") {
      sortOption = { quantity: -1 }
    } else if (sort === "provider") {
      // We'll need to sort after population
      sortOption = { createdAt: -1 } // Default sort for now
    } else if (sort === "initiative") {
      // We'll need to sort after population
      sortOption = { createdAt: -1 } // Default sort for now
    }

    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit)

    // Get resources
    let resources = await Resource.find(query)
      .populate("provider", "name username avatar userType")
      .populate("initiative", "title shortDescription mainImage status")
      .sort(sortOption)
      .skip(skip)
      .limit(Number(limit))

    // Handle special sorting cases that require populated fields
    if (sort === "provider") {
      resources = resources.sort((a, b) =>
        (a.provider as any).name.localeCompare((b.provider as any).name)
      )
    } else if (sort === "initiative") {
      resources = resources.sort((a, b) =>
        (a.initiative as any).title.localeCompare((b.initiative as any).title)
      )
    }

    // Get total count
    const total = await Resource.countDocuments(query)

    res.status(200).json({
      success: true,
      count: resources.length,
      total,
      page: Number(page),
      pages: Math.ceil(total / Number(limit)),
      resources,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get all resource needs
 */
export const getResourceNeeds = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { type, priority, status, sort, limit = 100, page = 1, search } = req.query

    // Build query
    const query: any = {}
    if (type) {
      query.type = type
    }
    if (priority) {
      query.priority = priority
    }
    if (status) {
      query.status = status
    }

    // Add search functionality
    if (search) {
      const searchRegex = new RegExp(search as string, 'i')
      query.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { notes: searchRegex }
      ]
    }

    // Build sort
    let sortOption: any = { priority: -1, createdAt: -1 }
    if (sort === "name") {
      sortOption = { name: 1 }
    } else if (sort === "date") {
      sortOption = { createdAt: -1 }
    } else if (sort === "quantity") {
      sortOption = { quantity: -1 }
    } else if (sort === "initiative") {
      // We'll need to sort after population
      sortOption = { priority: -1, createdAt: -1 } // Default sort for now
    }

    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit)

    // Get resource needs
    let resourceNeeds = await ResourceNeed.find(query)
      .populate("initiative", "title shortDescription mainImage status author")
      .sort(sortOption)
      .skip(skip)
      .limit(Number(limit))

    // Handle special sorting cases that require populated fields
    if (sort === "initiative") {
      resourceNeeds = resourceNeeds.sort((a, b) =>
        (a.initiative as any).title.localeCompare((b.initiative as any).title)
      )
    }

    // Get total count
    const total = await ResourceNeed.countDocuments(query)

    res.status(200).json({
      success: true,
      count: resourceNeeds.length,
      total,
      page: Number(page),
      pages: Math.ceil(total / Number(limit)),
      resourceNeeds,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get all resources for an initiative
 */
export const getInitiativeResources = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params
    const { status } = req.query

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Build query
    const query: any = { initiative: initiativeId }
    if (status) {
      query.status = status
    }

    // Get resources
    const resources = await Resource.find(query)
      .populate("provider", "name username avatar userType")
      .sort({ createdAt: -1 })

    res.status(200).json({
      success: true,
      count: resources.length,
      resources,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get all resources provided by a user
 */
export const getUserResources = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params
    const { status } = req.query

    // Validate user ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return next(createError(400, "Invalid user ID"))
    }

    // Check if user exists
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Build query
    const query: any = { provider: userId }
    if (status) {
      query.status = status
    }

    // Get resources
    const resources = await Resource.find(query)
      .populate("initiative", "title shortDescription mainImage status")
      .sort({ createdAt: -1 })

    res.status(200).json({
      success: true,
      count: resources.length,
      resources,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get a specific resource by ID
 */
export const getResourceById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate resource ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid resource ID"))
    }

    // Get resource
    const resource = await Resource.findById(id)
      .populate("provider", "name username avatar userType")
      .populate("initiative", "title shortDescription mainImage status author")

    if (!resource) {
      return next(createError(404, "Resource not found"))
    }

    res.status(200).json({
      success: true,
      resource,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get a specific resource need by ID
 */
export const getResourceNeedById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params

    // Validate resource need ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid resource need ID"))
    }

    // Get resource need
    const resourceNeed = await ResourceNeed.findById(id)
      .populate("initiative", "title shortDescription mainImage status author")

    if (!resourceNeed) {
      return next(createError(404, "Resource need not found"))
    }

    console.log("Resource need found:", resourceNeed)

    res.status(200).json({
      success: true,
      resourceNeed,
    })
  } catch (error) {
    console.error("Error fetching resource need:", error)
    next(error)
  }
}

/**
 * Offer a resource to an initiative
 */
export const offerResource = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params
    const {
      type,
      name,
      description,
      quantity,
      unit,
      notes,
      attachments,
      expectedDeliveryDate,
      contactPerson,
      contactEmail,
      contactPhone,
      isPublic,
      relatedResourceNeed
    } = req.body
    const userId = req.user?.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if initiative is active
    if (initiative.status !== "active") {
      return next(createError(400, "Cannot offer resources to inactive initiatives"))
    }

    // Get user to determine provider type
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Create resource
    const resource = new Resource({
      initiative: initiativeId,
      provider: userId,
      providerType: user.userType || "volunteer", // Utiliser directement le type d'utilisateur
      type,
      name,
      description,
      quantity,
      unit,
      status: "requested",
      requestedDate: new Date(),
      expectedDeliveryDate: expectedDeliveryDate ? new Date(expectedDeliveryDate) : undefined,
      contactPerson: contactPerson || user.name,
      contactEmail: contactEmail || user.email,
      contactPhone: contactPhone,
      isPublic: isPublic !== undefined ? isPublic : true,
      notes,
      attachments,
      relatedResourceNeed: relatedResourceNeed && mongoose.Types.ObjectId.isValid(relatedResourceNeed)
        ? relatedResourceNeed
        : undefined
    })

    // Save resource
    await resource.save()

    // If this resource is related to a specific need, update the need status
    if (resource.relatedResourceNeed) {
      const resourceNeed = await ResourceNeed.findById(resource.relatedResourceNeed)
      if (resourceNeed && resourceNeed.status === "open") {
        resourceNeed.status = "in_progress"
        await resourceNeed.save()
      }
    }

    // Créer une activité pour cette contribution
    let activityContent = `قدم موردًا لمبادرة "${initiative.title}"`
    if (user.userType === 'company' || user.userType === 'civil_society') {
      activityContent = `قدم دعمًا مؤسسيًا لمبادرة "${initiative.title}"`

      // Activité spécifique pour les acteurs institutionnels
      await createActivityUtil(
        userId,
        "resource",
        "contribute",
        activityContent,
        {
          relatedInitiative: initiativeId,
          metadata: {
            resourceType: resource.type,
            resourceName: resource.name,
            providerType: user.userType,
            isInstitutional: true
          },
          isPublic: resource.isPublic
        }
      )
    } else {
      // Activité standard pour les autres utilisateurs
      await createActivityUtil(
        userId,
        "resource",
        "offer",
        activityContent,
        {
          relatedInitiative: initiativeId,
          metadata: {
            resourceType: resource.type,
            resourceName: resource.name
          },
          isPublic: resource.isPublic
        }
      )
    }

    // Send notification to initiative author
    await createNotification({
      recipient: initiative.author.toString(),
      sender: userId,
      type: "resource_offer",
      content: `تم تقديم عرض موارد جديد لمبادرتك "${initiative.title}" من ${getProviderTypeInArabic(user.userType)}`,
      relatedInitiative: initiativeId,
      link: `/initiatives/${initiativeId}/resources`,
    })

    // Si c'est une réponse à un besoin spécifique, ajouter cette information dans la notification
    if (resource.relatedResourceNeed) {
      const resourceNeed = await ResourceNeed.findById(resource.relatedResourceNeed)
      if (resourceNeed) {
        await createNotification({
          recipient: initiative.author.toString(),
          sender: userId,
          type: "resource_need_response",
          content: `تم تقديم عرض لتلبية احتياج "${resourceNeed.name}" في مبادرتك "${initiative.title}"`,
          relatedInitiative: initiativeId,
          link: `/initiatives/${initiativeId}/resources`,
        })
      }
    }

    res.status(201).json({
      success: true,
      message: "Resource offered successfully",
      resource,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Update resource status (approve, reject, mark as delivered)
 */
export const updateResourceStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { status, notes } = req.body
    const userId = req.user?.id

    // Validate resource ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid resource ID"))
    }

    // Get resource
    const resource = await Resource.findById(id)
      .populate("initiative", "title author")
      .populate("provider", "name")

    if (!resource) {
      return next(createError(404, "Resource not found"))
    }

    // Check if user is the initiative author
    if (resource.initiative.author.toString() !== userId) {
      return next(createError(403, "Only initiative author can update resource status"))
    }

    // Update status and related date fields
    resource.status = status
    if (notes) resource.notes = notes

    if (status === "approved") {
      resource.approvedDate = new Date()
    } else if (status === "rejected") {
      resource.rejectedDate = new Date()
    } else if (status === "delivered") {
      resource.deliveredDate = new Date()
    }

    // Save resource
    await resource.save()

    // Send notification to resource provider
    await createNotification({
      recipient: resource.provider._id.toString(),
      sender: userId,
      type: "resource_status_update",
      content: `تم تحديث حالة الموارد التي قدمتها لمبادرة "${resource.initiative.title}" إلى "${getStatusInArabic(status)}"`,
      relatedInitiative: resource.initiative._id.toString(),
      link: `/initiatives/${resource.initiative._id}/resources`,
    })

    // Si la ressource est liée à un besoin spécifique et que le statut est "delivered", mettre à jour le statut du besoin
    if (resource.relatedResourceNeed && status === "delivered") {
      const resourceNeed = await ResourceNeed.findById(resource.relatedResourceNeed)
      if (resourceNeed && resourceNeed.status !== "fulfilled") {
        resourceNeed.status = "fulfilled"
        resourceNeed.fulfilledDate = new Date()
        await resourceNeed.save()

        // Notification pour l'accomplissement d'un besoin
        await createNotification({
          recipient: resource.provider._id.toString(),
          sender: userId,
          type: "resource_need_fulfilled",
          content: `تم تلبية احتياج "${resourceNeed.name}" في مبادرة "${resource.initiative.title}" بفضل مساهمتك!`,
          relatedInitiative: resource.initiative._id.toString(),
          link: `/initiatives/${resource.initiative._id}/resources`,
        })

        // Créer une activité pour l'accomplissement d'un besoin
        const provider = await User.findById(resource.provider)
        if (provider) {
          const isInstitutional = provider.userType === 'company' || provider.userType === 'civil_society'

          // Activité pour l'initiative
          await createActivityUtil(
            resource.initiative.author.toString(),
            "resource_need",
            "fulfilled",
            `تم تلبية احتياج "${resourceNeed.name}" في مبادرة "${resource.initiative.title}"`,
            {
              relatedInitiative: resource.initiative._id.toString(),
              relatedUser: resource.provider._id.toString(),
              metadata: {
                resourceType: resource.type,
                resourceName: resource.name,
                needName: resourceNeed.name,
                isInstitutional
              },
              isPublic: true
            }
          )

          // Activité pour le fournisseur
          await createActivityUtil(
            resource.provider._id.toString(),
            "resource_need",
            "contribute",
            `ساهم في تلبية احتياج "${resourceNeed.name}" في مبادرة "${resource.initiative.title}"`,
            {
              relatedInitiative: resource.initiative._id.toString(),
              metadata: {
                resourceType: resource.type,
                resourceName: resource.name,
                needName: resourceNeed.name,
                isInstitutional
              },
              isPublic: resource.isPublic
            }
          )
        }
      }
    }

    res.status(200).json({
      success: true,
      message: "Resource status updated successfully",
      resource,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Cancel a resource offer (by the provider)
 */
export const cancelResourceOffer = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user?.id

    // Validate resource ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid resource ID"))
    }

    // Get resource
    const resource = await Resource.findById(id)
      .populate("initiative", "title author")

    if (!resource) {
      return next(createError(404, "Resource not found"))
    }

    // Check if user is the resource provider
    if (resource.provider.toString() !== userId) {
      return next(createError(403, "Only resource provider can cancel the offer"))
    }

    // Check if resource can be canceled
    if (resource.status !== "requested" && resource.status !== "approved") {
      return next(createError(400, "Cannot cancel resources that are already delivered or rejected"))
    }

    // Update status
    resource.status = "canceled"
    await resource.save()

    // Send notification to initiative author
    await createNotification({
      recipient: resource.initiative.author.toString(),
      sender: userId,
      type: "resource_canceled",
      content: `تم إلغاء عرض الموارد المقدم لمبادرتك "${resource.initiative.title}"`,
      relatedInitiative: resource.initiative._id.toString(),
      link: `/initiatives/${resource.initiative._id}/resources`,
    })

    res.status(200).json({
      success: true,
      message: "Resource offer canceled successfully",
      resource,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Get all resource needs for an initiative
 */
export const getInitiativeResourceNeeds = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params
    const { status } = req.query

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Build query
    const query: any = { initiative: initiativeId }
    if (status) {
      query.status = status
    }

    // Get resource needs
    const resourceNeeds = await ResourceNeed.find(query)
      .sort({ priority: -1, createdAt: -1 })

    res.status(200).json({
      success: true,
      count: resourceNeeds.length,
      resourceNeeds,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Create a new resource need for an initiative
 */
export const createResourceNeed = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params
    const { type, name, description, quantity, unit, priority, notes } = req.body
    const userId = req.user?.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiative = await Initiative.findById(initiativeId)
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Check if user is the initiative author
    if (initiative.author.toString() !== userId) {
      return next(createError(403, "Only initiative author can create resource needs"))
    }

    // Create resource need
    const resourceNeed = new ResourceNeed({
      initiative: initiativeId,
      type,
      name,
      description,
      quantity,
      unit,
      priority: priority || "medium",
      status: "open",
      createdDate: new Date(),
      notes,
    })

    // Save resource need
    await resourceNeed.save()

    res.status(201).json({
      success: true,
      message: "Resource need created successfully",
      resourceNeed,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Update a resource need
 */
export const updateResourceNeed = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { type, name, description, quantity, unit, priority, status, notes } = req.body
    const userId = req.user?.id

    // Check if this is a status update only (from the /needs/:id/status route)
    const isStatusUpdate = req.path.endsWith('/status')

    // Validate resource need ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid resource need ID"))
    }

    // Get resource need
    const resourceNeed = await ResourceNeed.findById(id)
      .populate("initiative", "author")

    if (!resourceNeed) {
      return next(createError(404, "Resource need not found"))
    }

    // Check if user is the initiative author
    if (resourceNeed.initiative.author.toString() !== userId) {
      return next(createError(403, "Only initiative author can update resource needs"))
    }

    // Update resource need based on whether it's a status update or full update
    if (isStatusUpdate) {
      // Only update status and notes for status update route
      if (notes) resourceNeed.notes = notes

      // Update status if provided
      if (status && status !== resourceNeed.status) {
        resourceNeed.status = status

        // If status is changed to fulfilled, set fulfilled date
        if (status === "fulfilled") {
          resourceNeed.fulfilledDate = new Date()
        }
      }
    } else {
      // Full update for the regular update route
      if (type) resourceNeed.type = type
      if (name) resourceNeed.name = name
      if (description) resourceNeed.description = description
      if (quantity) resourceNeed.quantity = quantity
      if (unit) resourceNeed.unit = unit
      if (priority) resourceNeed.priority = priority
      if (notes) resourceNeed.notes = notes

      // Update status if provided
      if (status && status !== resourceNeed.status) {
        resourceNeed.status = status

        // If status is changed to fulfilled, set fulfilled date
        if (status === "fulfilled") {
          resourceNeed.fulfilledDate = new Date()
        }
      }
    }

    // Save resource need
    await resourceNeed.save()

    res.status(200).json({
      success: true,
      message: "Resource need updated successfully",
      resourceNeed,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Delete a resource need
 */
export const deleteResourceNeed = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user?.id

    // Validate resource need ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid resource need ID"))
    }

    // Get resource need
    const resourceNeed = await ResourceNeed.findById(id)
      .populate("initiative", "author")

    if (!resourceNeed) {
      return next(createError(404, "Resource need not found"))
    }

    // Check if user is the initiative author
    if (resourceNeed.initiative.author.toString() !== userId) {
      return next(createError(403, "Only initiative author can delete resource needs"))
    }

    // Delete resource need
    await resourceNeed.deleteOne()

    res.status(200).json({
      success: true,
      message: "Resource need deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Helper function to get status in Arabic
 */
const getStatusInArabic = (status: string): string => {
  switch (status) {
    case "requested":
      return "مطلوب"
    case "approved":
      return "موافق عليه"
    case "rejected":
      return "مرفوض"
    case "delivered":
      return "تم التسليم"
    case "canceled":
      return "ملغي"
    default:
      return status
  }
}

/**
 * Helper function to get provider type in Arabic
 */
const getProviderTypeInArabic = (providerType: string): string => {
  switch (providerType) {
    case "company":
      return "شركة"
    case "civil_society":
      return "فاعل في المجتمع المدني"
    case "volunteer":
      return "متطوع"
    case "proposer":
      return "مقترح مبادرات"
    case "admin":
      return "مدير"
    default:
      return "مستخدم"
  }
}

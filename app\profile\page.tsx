"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Textarea } from "../../components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "../../components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "../../components/ui/avatar"
import { Badge } from "../../components/ui/badge"
import { Alert, AlertDescription } from "../../components/ui/alert"
import { Loader2, AlertCircle, Edit, LogOut, User, Settings, MapPin, Calendar, Mail, AtSign, Award, GraduationCap, Briefcase, Heart, Clock, History, Building, Target, FileText, Users, BarChart, Bell, Check, X } from "lucide-react"
import BadgeDisplay from "../../components/badges/BadgeDisplay"
import { getSkillLevelText, getSkillLevelColor } from "../../lib/utils"
import { api } from "../../lib/api"
import { useAuth } from "../../components/auth-provider"
import { toast } from "../../components/ui/use-toast"
import { Toaster } from "../../components/ui/toaster"
import ClientOnly from "../../components/client-only"

export default function ProfilePage() {
  const router = useRouter()
  const { user, isAuthenticated, logout, refreshUserData } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userInitiatives, setUserInitiatives] = useState<any[]>([])
  const [supportedInitiatives, setSupportedInitiatives] = useState<any[]>([])
  const [profileData, setProfileData] = useState<any>(null)
  const [invitations, setInvitations] = useState<any[]>([])
  const [loadingInvitations, setLoadingInvitations] = useState(false)
  const [processingInvitation, setProcessingInvitation] = useState<string | null>(null)
  const [userBadges, setUserBadges] = useState<any[]>([])

  // Use a ref to track if we've already fetched user data
  const hasInitializedRef = useState(false)

  // Fonction pour récupérer les invitations
  const fetchInvitations = async () => {
    if (!isAuthenticated) return;

    try {
      setLoadingInvitations(true);
      const response = await api.get("/api/invitations", true);

      if (response.success) {
        setInvitations(response.invitations || []);
      } else {
        console.error("Failed to fetch invitations:", response.message);
        setInvitations([]);
      }
    } catch (err: any) {
      console.error("Error fetching invitations:", err);
      setInvitations([]);
    } finally {
      setLoadingInvitations(false);
    }
  };

  // Fonction pour accepter une invitation
  const handleAcceptInvitation = async (invitationId: string) => {
    try {
      setProcessingInvitation(invitationId);

      const response = await api.put(`/api/invitations/${invitationId}/accept`, {}, true);

      if (response.success) {
        toast({
          title: "تم قبول الدعوة",
          description: "تم قبول الدعوة بنجاح وانضممت إلى المبادرة كمتطوع",
          variant: "default",
        });

        // Update invitation status locally
        setInvitations(prev =>
          prev.map(inv =>
            inv._id === invitationId
              ? { ...inv, status: "accepted", respondedAt: new Date().toISOString() }
              : inv
          )
        );
      } else {
        throw new Error(response.message || "Failed to accept invitation");
      }
    } catch (err: any) {
      console.error("Error accepting invitation:", err);
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء قبول الدعوة",
        variant: "destructive",
      });
    } finally {
      setProcessingInvitation(null);
    }
  };

  // Fonction pour refuser une invitation
  const handleDeclineInvitation = async (invitationId: string) => {
    try {
      setProcessingInvitation(invitationId);

      const response = await api.put(`/api/invitations/${invitationId}/decline`, {}, true);

      if (response.success) {
        toast({
          title: "تم رفض الدعوة",
          description: "تم رفض الدعوة بنجاح",
          variant: "default",
        });

        // Update invitation status locally
        setInvitations(prev =>
          prev.map(inv =>
            inv._id === invitationId
              ? { ...inv, status: "declined", respondedAt: new Date().toISOString() }
              : inv
          )
        );
      } else {
        throw new Error(response.message || "Failed to decline invitation");
      }
    } catch (err: any) {
      console.error("Error declining invitation:", err);
      toast({
        title: "خطأ",
        description: err.message || "حدث خطأ أثناء رفض الدعوة",
        variant: "destructive",
      });
    } finally {
      setProcessingInvitation(null);
    }
  };

  useEffect(() => {
    const fetchUserData = async () => {
      // Skip if we've already initialized or if we're still loading auth state
      if (hasInitializedRef[0]) {
        return
      }

      try {
        if (!isAuthenticated || !user) {
          toast({
            title: "Authentication Required",
            description: "Please log in to view your profile",
            variant: "destructive"
          })
          router.push('/auth/login')
          return
        }

        setIsLoading(true)

        // Set profile data from current user first
        setProfileData(user)

        // Then try to refresh in the background
        try {
          // Mark as initialized to prevent future calls
          hasInitializedRef[1](true)

          const userData = await refreshUserData()
          if (userData) {
            setProfileData(userData)
          }
        } catch (profileError) {
          console.error('Error refreshing user data:', profileError)
        }

        // Fetch user's initiatives
        try {
          const userInitiativesData = await api.get(`/api/users/${user.id}/initiatives`, false)
          setUserInitiatives(userInitiativesData.initiatives || [])
        } catch (initiativesError) {
          console.error('Error fetching user initiatives:', initiativesError)
          setUserInitiatives([])
        }

        // Fetch supported initiatives
        try {
          const supportedInitiativesData = await api.get(`/api/users/${user.id}/supported`, false)
          setSupportedInitiatives(supportedInitiativesData.initiatives || [])
        } catch (supportedError) {
          console.error('Error fetching supported initiatives:', supportedError)
          // Try alternative URL
          try {
            const alternativeData = await api.get(`/api/users/${user.id}/supported-initiatives`, false)
            setSupportedInitiatives(alternativeData.initiatives || [])
          } catch (alternativeError) {
            console.error('Error fetching from alternative URL:', alternativeError)
            setSupportedInitiatives([])
          }
        }

        // Fetch user badges
        try {
          const badgesData = await api.get(`/api/users/${user.id}/badges`, false)
          setUserBadges(badgesData.badgeAwards || [])
        } catch (badgesError) {
          console.error('Error fetching user badges:', badgesError)
          setUserBadges([])
        }

        // Ne pas appeler fetchInvitations() ici, nous le faisons dans un useEffect séparé

      } catch (err: any) {
        console.error('Error fetching user data:', err)
        setError(err.message || 'Failed to load profile data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [user, isAuthenticated, router, refreshUserData, hasInitializedRef])

  // Effet séparé pour récupérer les invitations
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      fetchInvitations()
    }
  }, [isAuthenticated, isLoading])

  const handleLogout = () => {
    logout()
    toast({
      title: "Logged Out",
      description: "You have been successfully logged out",
      variant: "default"
    })
    router.push('/')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#f5f5f5]">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto text-[#0a8754]" />
          <p className="mt-4 text-lg">جاري تحميل بيانات الملف الشخصي...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#f5f5f5]">
        <div className="max-w-md w-full p-6 bg-white rounded-lg shadow-md">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/')} className="w-full">
            العودة إلى الصفحة الرئيسية
          </Button>
        </div>
      </div>
    )
  }

  return (
    <ClientOnly>
      <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="max-w-6xl mx-auto p-4 md:p-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Profile Sidebar */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={profileData?.avatar || "/placeholder.svg"} alt={profileData?.name} />
                    <AvatarFallback>{profileData?.name?.charAt(0) || "U"}</AvatarFallback>
                  </Avatar>
                </div>
                <CardTitle className="text-xl">{profileData?.name}</CardTitle>
                <CardDescription className="flex items-center justify-center gap-1">
                  <AtSign className="h-4 w-4" />
                  {profileData?.username}
                </CardDescription>
                {profileData?.userType && (
                  <div className="mt-2">
                    <Badge className={
                      profileData.userType === 'volunteer' ? 'bg-blue-100 text-blue-800' :
                      profileData.userType === 'proposer' ? 'bg-green-100 text-green-800' :
                      profileData.userType === 'company' ? 'bg-amber-100 text-amber-800' : ''
                    }>
                      {profileData.userType === 'volunteer' ? 'متطوع' :
                       profileData.userType === 'proposer' ? 'مقترح مبادرات' :
                       profileData.userType === 'company' ? 'شركة/مؤسسة' : profileData.userType}
                    </Badge>
                  </div>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                {profileData?.bio && (
                  <p className="text-gray-600 text-center">{profileData.bio}</p>
                )}

                <div className="space-y-2">
                  {profileData?.email && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="h-4 w-4" />
                      <span>{profileData.email}</span>
                    </div>
                  )}

                  {profileData?.location && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span>{profileData.location}</span>
                    </div>
                  )}

                  {profileData?.joinDate && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      <span>انضم في {new Date(profileData.joinDate).toLocaleDateString('ar-DZ')}</span>
                    </div>
                  )}
                </div>

                {/* Statistiques de l'utilisateur */}
                <div className="pt-4 border-t border-gray-200">
                  <h3 className="font-semibold text-center mb-3">إحصائيات</h3>
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="bg-blue-50 rounded-lg p-2">
                      <p className="text-xl font-bold text-blue-600">{userInitiatives.length}</p>
                      <p className="text-xs text-gray-600">مبادرات</p>
                    </div>
                    <div className="bg-green-50 rounded-lg p-2">
                      <p className="text-xl font-bold text-green-600">{supportedInitiatives.length}</p>
                      <p className="text-xs text-gray-600">مدعومة</p>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-2">
                      <p className="text-xl font-bold text-purple-600">{profileData?.badges?.length || 0}</p>
                      <p className="text-xs text-gray-600">شارات</p>
                    </div>
                  </div>
                </div>

                {/* Statistiques spécifiques au type d'utilisateur */}
                {profileData?.userType === 'volunteer' && (
                  <div className="pt-2">
                    <div className="grid grid-cols-2 gap-2 text-center">
                      <div className="bg-amber-50 rounded-lg p-2">
                        <p className="text-xl font-bold text-amber-600">{profileData?.skills?.length || 0}</p>
                        <p className="text-xs text-gray-600">مهارات</p>
                      </div>
                      <div className="bg-indigo-50 rounded-lg p-2">
                        <p className="text-xl font-bold text-indigo-600">{profileData?.qualifications?.length || 0}</p>
                        <p className="text-xs text-gray-600">مؤهلات</p>
                      </div>
                    </div>
                  </div>
                )}

                {profileData?.userType === 'proposer' && (
                  <div className="pt-2">
                    <div className="grid grid-cols-2 gap-2 text-center">
                      <div className="bg-amber-50 rounded-lg p-2">
                        <p className="text-xl font-bold text-amber-600">
                          {userInitiatives.filter(i => i.status === 'active').length}
                        </p>
                        <p className="text-xs text-gray-600">مبادرات نشطة</p>
                      </div>
                      <div className="bg-indigo-50 rounded-lg p-2">
                        <p className="text-xl font-bold text-indigo-600">
                          {userInitiatives.filter(i => i.status === 'completed').length}
                        </p>
                        <p className="text-xs text-gray-600">مبادرات مكتملة</p>
                      </div>
                    </div>
                  </div>
                )}

                {profileData?.userType === 'company' && (
                  <div className="pt-2">
                    <div className="grid grid-cols-2 gap-2 text-center">
                      <div className="bg-amber-50 rounded-lg p-2">
                        <p className="text-xl font-bold text-amber-600">{profileData?.services?.length || 0}</p>
                        <p className="text-xs text-gray-600">خدمات</p>
                      </div>
                      <div className="bg-indigo-50 rounded-lg p-2">
                        <p className="text-xl font-bold text-indigo-600">{profileData?.resources?.length || 0}</p>
                        <p className="text-xs text-gray-600">موارد</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="pt-4 space-y-2">
                  <Link href="/profile/invitations">
                    <Button variant="outline" className="w-full flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      دعوات التطوع
                    </Button>
                  </Link>

                  <Link href="/settings">
                    <Button variant="outline" className="w-full flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      إعدادات الحساب
                    </Button>
                  </Link>

                  <Button
                    variant="outline"
                    className="w-full flex items-center gap-2 text-red-500 hover:text-red-600 hover:bg-red-50"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4" />
                    تسجيل الخروج
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="md:col-span-2">
            <Tabs defaultValue="initiatives">
              <TabsList className="w-full bg-white mb-6">
                <TabsTrigger value="initiatives" className="flex-1">مبادراتي</TabsTrigger>
                <TabsTrigger value="supported" className="flex-1">المبادرات التي أدعمها</TabsTrigger>
                <TabsTrigger value="invitations" className="flex-1">الدعوات</TabsTrigger>
                <TabsTrigger value="badges" className="flex-1">الشارات</TabsTrigger>
                <TabsTrigger value="info" className="flex-1">معلوماتي</TabsTrigger>
              </TabsList>

              <TabsContent value="initiatives">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>مبادراتي</CardTitle>
                      <Link href="/initiatives/create">
                        <Button size="sm">إنشاء مبادرة جديدة</Button>
                      </Link>
                    </div>
                    <CardDescription>المبادرات التي قمت بإنشائها</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {userInitiatives.length === 0 ? (
                      <div className="text-center py-8">
                        <User className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">لم تقم بإنشاء أي مبادرات بعد</p>
                        <Link href="/initiatives/create">
                          <Button className="mt-4">إنشاء مبادرة جديدة</Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {userInitiatives.map((initiative) => (
                          <Card key={initiative._id} className="overflow-hidden">
                            <div className="flex flex-col md:flex-row">
                              <div className="w-full md:w-1/3 h-[120px] md:h-auto relative">
                                <Image
                                  src={initiative.mainImage || "/placeholder.svg"}
                                  alt={initiative.title}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                              <div className="p-4 w-full md:w-2/3">
                                <div className="flex justify-between items-start mb-2">
                                  <h3 className="font-semibold text-lg">{initiative.title}</h3>
                                  <Badge
                                    className={
                                      initiative.status === "active"
                                        ? "bg-green-100 text-green-800"
                                        : initiative.status === "completed"
                                        ? "bg-blue-100 text-blue-800"
                                        : "bg-yellow-100 text-yellow-800"
                                    }
                                  >
                                    {initiative.status === "active" ? "جارية" :
                                     initiative.status === "completed" ? "مكتملة" :
                                     initiative.status === "pending" ? "قيد الانتظار" : initiative.status}
                                  </Badge>
                                </div>
                                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                                  {initiative.shortDescription}
                                </p>
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-4 text-sm text-gray-500">
                                    <span>{initiative.supportCount} داعم</span>
                                    <span>{initiative.commentCount} تعليق</span>
                                  </div>
                                  <div className="flex gap-2">
                                    <Link href={`/initiatives/${initiative._id}/edit`}>
                                      <Button size="sm" variant="outline">
                                        تعديل
                                      </Button>
                                    </Link>
                                    <Link href={`/initiatives/${initiative._id}`}>
                                      <Button size="sm">
                                        عرض
                                      </Button>
                                    </Link>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="supported">
                <Card>
                  <CardHeader>
                    <CardTitle>المبادرات التي أدعمها</CardTitle>
                    <CardDescription>المبادرات التي قمت بدعمها</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {supportedInitiatives.length === 0 ? (
                      <div className="text-center py-8">
                        <User className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">لم تقم بدعم أي مبادرات بعد</p>
                        <Link href="/initiatives">
                          <Button className="mt-4">استكشف المبادرات</Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {supportedInitiatives.map((initiative) => (
                          <Card key={initiative._id} className="overflow-hidden">
                            <div className="flex flex-col md:flex-row">
                              <div className="w-full md:w-1/3 h-[120px] md:h-auto relative">
                                <Image
                                  src={initiative.mainImage || "/placeholder.svg"}
                                  alt={initiative.title}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                              <div className="p-4 w-full md:w-2/3">
                                <div className="flex justify-between items-start mb-2">
                                  <h3 className="font-semibold text-lg">{initiative.title}</h3>
                                  <Badge
                                    className={
                                      initiative.status === "active"
                                        ? "bg-green-100 text-green-800"
                                        : initiative.status === "completed"
                                        ? "bg-blue-100 text-blue-800"
                                        : "bg-yellow-100 text-yellow-800"
                                    }
                                  >
                                    {initiative.status === "active" ? "جارية" :
                                     initiative.status === "completed" ? "مكتملة" :
                                     initiative.status === "pending" ? "قيد الانتظار" : initiative.status}
                                  </Badge>
                                </div>
                                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                                  {initiative.shortDescription}
                                </p>
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-2 text-sm text-gray-500">
                                    <div className="flex items-center">
                                      <Avatar className="h-6 w-6 mr-1">
                                        <AvatarImage src={initiative.author?.avatar || "/placeholder.svg"} />
                                        <AvatarFallback>{initiative.author?.name?.charAt(0) || "U"}</AvatarFallback>
                                      </Avatar>
                                      <span>{initiative.author?.name}</span>
                                    </div>
                                  </div>
                                  <Link href={`/initiatives/${initiative._id}`}>
                                    <Button size="sm">
                                      عرض
                                    </Button>
                                  </Link>
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="badges">
                <Card>
                  <CardHeader>
                    <CardTitle>الشارات</CardTitle>
                    <CardDescription>الشارات التي حصلت عليها من المبادرات المختلفة</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <BadgeDisplay badges={userBadges} showTitle={false} />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="invitations">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle>دعوات التطوع</CardTitle>
                        <CardDescription>إدارة دعوات التطوع للمبادرات المختلفة</CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={fetchInvitations}
                          disabled={loadingInvitations}
                        >
                          {loadingInvitations ? (
                            <Loader2 className="h-4 w-4 animate-spin ml-2" />
                          ) : (
                            <Clock className="h-4 w-4 ml-2" />
                          )}
                          تحديث
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="pending" className="w-full">
                      <TabsList className="w-full mb-4">
                        <TabsTrigger value="pending" className="flex-1">قيد الانتظار</TabsTrigger>
                        <TabsTrigger value="accepted" className="flex-1">مقبولة</TabsTrigger>
                        <TabsTrigger value="declined" className="flex-1">مرفوضة</TabsTrigger>
                        <TabsTrigger value="all" className="flex-1">الكل</TabsTrigger>
                      </TabsList>

                      <TabsContent value="pending">
                        {loadingInvitations ? (
                          <div className="flex items-center justify-center py-12">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="mr-2">جاري تحميل الدعوات...</span>
                          </div>
                        ) : invitations.filter(inv => inv.status === "pending").length === 0 ? (
                          <div className="text-center py-6">
                            <Bell className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                            <p className="text-gray-500 mb-4">ليس لديك أي دعوات تنتظر الرد في الوقت الحالي</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {invitations.filter(inv => inv.status === "pending").map((invitation) => (
                              <Card key={invitation._id} className="overflow-hidden">
                                <div className="p-4">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <h3 className="font-semibold text-lg">
                                        <Link href={`/initiatives/${invitation.initiative._id}`} className="hover:text-primary transition-colors">
                                          {invitation.initiative.title}
                                        </Link>
                                      </h3>
                                      <div className="flex items-center gap-2 text-sm text-gray-500">
                                        <span>دعوة من {invitation.sender.name}</span>
                                        <span>•</span>
                                        <span>{new Date(invitation.createdAt).toLocaleDateString('ar-SA')}</span>
                                      </div>
                                    </div>
                                    <Badge className="bg-yellow-500">قيد الانتظار</Badge>
                                  </div>

                                  {invitation.message && (
                                    <div className="bg-muted/30 p-3 rounded-md text-sm my-3">
                                      <p className="font-medium mb-1">رسالة:</p>
                                      <p className="text-muted-foreground">{invitation.message}</p>
                                    </div>
                                  )}

                                  <div className="flex gap-2 mt-4">
                                    <Button
                                      className="flex-1 bg-green-600 hover:bg-green-700"
                                      onClick={() => handleAcceptInvitation(invitation._id)}
                                      disabled={processingInvitation === invitation._id}
                                    >
                                      {processingInvitation === invitation._id ? (
                                        <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                                      ) : (
                                        <Check className="ml-2 h-4 w-4" />
                                      )}
                                      قبول
                                    </Button>
                                    <Button
                                      variant="outline"
                                      className="flex-1 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                                      onClick={() => handleDeclineInvitation(invitation._id)}
                                      disabled={processingInvitation === invitation._id}
                                    >
                                      <X className="ml-2 h-4 w-4" />
                                      رفض
                                    </Button>
                                  </div>
                                </div>
                              </Card>
                            ))}
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="accepted">
                        {loadingInvitations ? (
                          <div className="flex items-center justify-center py-12">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="mr-2">جاري تحميل الدعوات...</span>
                          </div>
                        ) : invitations.filter(inv => inv.status === "accepted").length === 0 ? (
                          <div className="text-center py-6">
                            <Check className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                            <p className="text-gray-500 mb-4">لم تقبل أي دعوات حتى الآن</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {invitations.filter(inv => inv.status === "accepted").map((invitation) => (
                              <Card key={invitation._id} className="overflow-hidden">
                                <div className="p-4">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <h3 className="font-semibold text-lg">
                                        <Link href={`/initiatives/${invitation.initiative._id}`} className="hover:text-primary transition-colors">
                                          {invitation.initiative.title}
                                        </Link>
                                      </h3>
                                      <div className="flex items-center gap-2 text-sm text-gray-500">
                                        <span>دعوة من {invitation.sender.name}</span>
                                        <span>•</span>
                                        <span>تم القبول في {new Date(invitation.respondedAt).toLocaleDateString('ar-SA')}</span>
                                      </div>
                                    </div>
                                    <Badge className="bg-green-500">تم القبول</Badge>
                                  </div>

                                  <div className="mt-4">
                                    <Link href={`/initiatives/${invitation.initiative._id}`}>
                                      <Button variant="outline" className="w-full">
                                        عرض المبادرة
                                      </Button>
                                    </Link>
                                  </div>
                                </div>
                              </Card>
                            ))}
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="declined">
                        {loadingInvitations ? (
                          <div className="flex items-center justify-center py-12">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="mr-2">جاري تحميل الدعوات...</span>
                          </div>
                        ) : invitations.filter(inv => inv.status === "declined").length === 0 ? (
                          <div className="text-center py-6">
                            <X className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                            <p className="text-gray-500 mb-4">لم ترفض أي دعوات حتى الآن</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {invitations.filter(inv => inv.status === "declined").map((invitation) => (
                              <Card key={invitation._id} className="overflow-hidden">
                                <div className="p-4">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <h3 className="font-semibold text-lg">
                                        <Link href={`/initiatives/${invitation.initiative._id}`} className="hover:text-primary transition-colors">
                                          {invitation.initiative.title}
                                        </Link>
                                      </h3>
                                      <div className="flex items-center gap-2 text-sm text-gray-500">
                                        <span>دعوة من {invitation.sender.name}</span>
                                        <span>•</span>
                                        <span>تم الرفض في {new Date(invitation.respondedAt).toLocaleDateString('ar-SA')}</span>
                                      </div>
                                    </div>
                                    <Badge className="bg-red-500">تم الرفض</Badge>
                                  </div>
                                </div>
                              </Card>
                            ))}
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="all">
                        {loadingInvitations ? (
                          <div className="flex items-center justify-center py-12">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="mr-2">جاري تحميل الدعوات...</span>
                          </div>
                        ) : invitations.length === 0 ? (
                          <div className="text-center py-6">
                            <Bell className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                            <p className="text-gray-500 mb-4">لم تتلق أي دعوات حتى الآن</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {invitations.map((invitation) => (
                              <Card key={invitation._id} className="overflow-hidden">
                                <div className="p-4">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <h3 className="font-semibold text-lg">
                                        <Link href={`/initiatives/${invitation.initiative._id}`} className="hover:text-primary transition-colors">
                                          {invitation.initiative.title}
                                        </Link>
                                      </h3>
                                      <div className="flex items-center gap-2 text-sm text-gray-500">
                                        <span>دعوة من {invitation.sender.name}</span>
                                        <span>•</span>
                                        <span>{new Date(invitation.createdAt).toLocaleDateString('ar-SA')}</span>
                                      </div>
                                    </div>
                                    {invitation.status === "pending" && <Badge className="bg-yellow-500">قيد الانتظار</Badge>}
                                    {invitation.status === "accepted" && <Badge className="bg-green-500">تم القبول</Badge>}
                                    {invitation.status === "declined" && <Badge className="bg-red-500">تم الرفض</Badge>}
                                  </div>

                                  {invitation.message && invitation.status === "pending" && (
                                    <div className="bg-muted/30 p-3 rounded-md text-sm my-3">
                                      <p className="font-medium mb-1">رسالة:</p>
                                      <p className="text-muted-foreground">{invitation.message}</p>
                                    </div>
                                  )}

                                  {invitation.status === "pending" && (
                                    <div className="flex gap-2 mt-4">
                                      <Button
                                        className="flex-1 bg-green-600 hover:bg-green-700"
                                        onClick={() => handleAcceptInvitation(invitation._id)}
                                        disabled={processingInvitation === invitation._id}
                                      >
                                        {processingInvitation === invitation._id ? (
                                          <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                                        ) : (
                                          <Check className="ml-2 h-4 w-4" />
                                        )}
                                        قبول
                                      </Button>
                                      <Button
                                        variant="outline"
                                        className="flex-1 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                                        onClick={() => handleDeclineInvitation(invitation._id)}
                                        disabled={processingInvitation === invitation._id}
                                      >
                                        <X className="ml-2 h-4 w-4" />
                                        رفض
                                      </Button>
                                    </div>
                                  )}

                                  {invitation.status === "accepted" && (
                                    <div className="mt-4">
                                      <Link href={`/initiatives/${invitation.initiative._id}`}>
                                        <Button variant="outline" className="w-full">
                                          عرض المبادرة
                                        </Button>
                                      </Link>
                                    </div>
                                  )}
                                </div>
                              </Card>
                            ))}
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="info">
                <Card>
                  <CardHeader>
                    <CardTitle>معلوماتي الشخصية</CardTitle>
                    <CardDescription>
                      {profileData?.userType === 'volunteer' ? 'معلوماتك كمتطوع' :
                       profileData?.userType === 'proposer' ? 'معلوماتك كمقترح مبادرات' :
                       profileData?.userType === 'company' ? 'معلومات الشركة/المؤسسة' : 'معلوماتك الشخصية'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {profileData?.userType === 'volunteer' && (
                      <div className="space-y-6">
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                          <div className="flex items-center mb-4">
                            <div className="bg-blue-500 text-white p-2 rounded-full">
                              <Award className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold text-lg mr-2">ملف المتطوع</h3>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              {/* Section pour les qualifications */}
                              <div className="bg-white p-3 rounded-md border border-blue-100 mb-4">
                                <h4 className="font-semibold mb-2 flex items-center">
                                  <GraduationCap className="h-4 w-4 ml-1" />
                                  المؤهلات
                                </h4>
                                {profileData.qualifications && profileData.qualifications.length > 0 ? (
                                  <div className="flex flex-wrap gap-2">
                                    {profileData.qualifications.map((qualification, index) => (
                                      <Badge key={index} variant="outline" className="bg-blue-50 border-blue-200">
                                        {qualification}
                                      </Badge>
                                    ))}
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-500">لم يتم إضافة أي مؤهلات بعد</p>
                                    <Link href="/settings">
                                      <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                    </Link>
                                  </div>
                                )}
                              </div>

                              {/* Section pour les compétences */}
                              <div className="bg-white p-3 rounded-md border border-blue-100">
                                <h4 className="font-semibold mb-2 flex items-center">
                                  <Briefcase className="h-4 w-4 ml-1" />
                                  المهارات
                                </h4>
                                {profileData.skills && profileData.skills.length > 0 ? (
                                  <div className="flex flex-wrap gap-2">
                                    {profileData.skills.map((skill, index) => (
                                      <Badge
                                        key={index}
                                        variant="outline"
                                        style={{
                                          backgroundColor: `${getSkillLevelColor(skill.level)}20`,
                                          borderColor: getSkillLevelColor(skill.level)
                                        }}
                                        className="flex items-center gap-1"
                                      >
                                        {skill.name}
                                        <span className="text-xs px-1 rounded-full ml-1" style={{ backgroundColor: getSkillLevelColor(skill.level), color: 'white' }}>
                                          {getSkillLevelText(skill.level)}
                                        </span>
                                      </Badge>
                                    ))}
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-500">لم يتم إضافة أي مهارات بعد</p>
                                    <Link href="/settings">
                                      <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                    </Link>
                                  </div>
                                )}
                              </div>
                            </div>

                            <div>
                              {/* Section pour les intérêts */}
                              <div className="bg-white p-3 rounded-md border border-blue-100 mb-4">
                                <h4 className="font-semibold mb-2 flex items-center">
                                  <Heart className="h-4 w-4 ml-1" />
                                  الاهتمامات
                                </h4>
                                {profileData.interests && profileData.interests.length > 0 ? (
                                  <div className="flex flex-wrap gap-2">
                                    {profileData.interests.map((interest, index) => (
                                      <Badge key={index} variant="outline" className="bg-purple-50 border-purple-200">
                                        {interest}
                                      </Badge>
                                    ))}
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-500">لم يتم إضافة أي اهتمامات بعد</p>
                                    <Link href="/settings">
                                      <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                    </Link>
                                  </div>
                                )}
                              </div>

                              {/* Section pour la disponibilité */}
                              <div className="bg-white p-3 rounded-md border border-blue-100">
                                <h4 className="font-semibold mb-2 flex items-center">
                                  <Clock className="h-4 w-4 ml-1" />
                                  التوفر
                                </h4>
                                {profileData.availability ? (
                                  <p className="text-gray-700 p-2 bg-amber-50 rounded-md border border-amber-100">
                                    {profileData.availability}
                                  </p>
                                ) : (
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-500">لم يتم تحديد أوقات التوفر بعد</p>
                                    <Link href="/settings">
                                      <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                    </Link>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {(!profileData.qualifications || profileData.qualifications.length === 0) &&
                           (!profileData.skills || profileData.skills.length === 0) &&
                           (!profileData.interests || profileData.interests.length === 0) &&
                           !profileData.availability && (
                            <div className="text-center py-4 mt-4 border-t border-blue-100">
                              <p className="text-gray-500 mb-4">أكمل ملفك الشخصي كمتطوع لزيادة فرص المشاركة في المبادرات</p>
                              <Link href="/settings">
                                <Button className="bg-blue-600 hover:bg-blue-700">إضافة معلوماتي كمتطوع</Button>
                              </Link>
                            </div>
                          )}
                        </div>

                        {/* Section pour l'historique des activités */}
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="flex items-center mb-4">
                            <div className="bg-gray-500 text-white p-2 rounded-full">
                              <History className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold text-lg mr-2">سجل النشاطات</h3>
                          </div>

                          {supportedInitiatives.length > 0 ? (
                            <div className="space-y-3">
                              {supportedInitiatives.slice(0, 3).map((initiative) => (
                                <div key={initiative._id} className="flex items-center p-2 bg-white rounded-md border border-gray-100">
                                  <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium">{initiative.title}</p>
                                    <p className="text-xs text-gray-500">تم دعم المبادرة</p>
                                  </div>
                                  <Link href={`/initiatives/${initiative._id}`}>
                                    <Button variant="ghost" size="sm">عرض</Button>
                                  </Link>
                                </div>
                              ))}

                              {supportedInitiatives.length > 3 && (
                                <div className="text-center mt-2">
                                  <Button variant="link" size="sm" className="text-blue-600">
                                    عرض المزيد ({supportedInitiatives.length - 3})
                                  </Button>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="text-center py-6">
                              <p className="text-gray-500">لم تشارك في أي نشاطات بعد</p>
                              <Link href="/initiatives">
                                <Button variant="outline" className="mt-2">استكشف المبادرات</Button>
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {profileData?.userType === 'proposer' && (
                      <div className="space-y-6">
                        <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                          <div className="flex items-center mb-4">
                            <div className="bg-green-500 text-white p-2 rounded-full">
                              <Target className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold text-lg mr-2">ملف مقترح المبادرات</h3>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              {/* Section pour les compétences */}
                              <div className="bg-white p-3 rounded-md border border-green-100 mb-4">
                                <h4 className="font-semibold mb-2 flex items-center">
                                  <Briefcase className="h-4 w-4 ml-1" />
                                  المهارات
                                </h4>
                                {profileData.skills && profileData.skills.length > 0 ? (
                                  <div className="flex flex-wrap gap-2">
                                    {profileData.skills.map((skill, index) => (
                                      <Badge
                                        key={index}
                                        variant="outline"
                                        style={{
                                          backgroundColor: `${getSkillLevelColor(skill.level)}20`,
                                          borderColor: getSkillLevelColor(skill.level)
                                        }}
                                        className="flex items-center gap-1"
                                      >
                                        {skill.name}
                                        <span className="text-xs px-1 rounded-full ml-1" style={{ backgroundColor: getSkillLevelColor(skill.level), color: 'white' }}>
                                          {getSkillLevelText(skill.level)}
                                        </span>
                                      </Badge>
                                    ))}
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-500">لم يتم إضافة أي مهارات بعد</p>
                                    <Link href="/settings">
                                      <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                    </Link>
                                  </div>
                                )}
                              </div>
                            </div>

                            <div>
                              {/* Section pour les qualifications */}
                              <div className="bg-white p-3 rounded-md border border-green-100">
                                <h4 className="font-semibold mb-2 flex items-center">
                                  <GraduationCap className="h-4 w-4 ml-1" />
                                  المؤهلات
                                </h4>
                                {profileData.qualifications && profileData.qualifications.length > 0 ? (
                                  <div className="flex flex-wrap gap-2">
                                    {profileData.qualifications.map((qualification, index) => (
                                      <Badge key={index} variant="outline" className="bg-blue-50 border-blue-200">
                                        {qualification}
                                      </Badge>
                                    ))}
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm text-gray-500">لم يتم إضافة أي مؤهلات بعد</p>
                                    <Link href="/settings">
                                      <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                    </Link>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {(!profileData.skills || profileData.skills.length === 0) &&
                           (!profileData.qualifications || profileData.qualifications.length === 0) && (
                            <div className="text-center py-4 mt-4 border-t border-green-100">
                              <p className="text-gray-500 mb-4">أكمل ملفك الشخصي كمقترح مبادرات لزيادة فرص نجاح مبادراتك</p>
                              <Link href="/settings">
                                <Button className="bg-green-600 hover:bg-green-700">إضافة معلوماتي كمقترح</Button>
                              </Link>
                            </div>
                          )}
                        </div>

                        {/* Section pour les initiatives proposées */}
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="flex items-center mb-4">
                            <div className="bg-gray-500 text-white p-2 rounded-full">
                              <BarChart className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold text-lg mr-2">المبادرات المقترحة</h3>
                          </div>

                          {userInitiatives.length > 0 ? (
                            <div className="space-y-3">
                              {userInitiatives.slice(0, 3).map((initiative) => (
                                <div key={initiative._id} className="flex items-center p-2 bg-white rounded-md border border-gray-100">
                                  <div className={`w-2 h-2 rounded-full ml-2 ${
                                    initiative.status === 'active' ? 'bg-green-500' :
                                    initiative.status === 'pending' ? 'bg-yellow-500' :
                                    initiative.status === 'completed' ? 'bg-blue-500' : 'bg-gray-500'
                                  }`}></div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium">{initiative.title}</p>
                                    <p className="text-xs text-gray-500">
                                      {initiative.status === 'active' ? 'نشطة' :
                                       initiative.status === 'pending' ? 'قيد المراجعة' :
                                       initiative.status === 'completed' ? 'مكتملة' : 'غير معروفة'}
                                    </p>
                                  </div>
                                  <Link href={`/initiatives/${initiative._id}`}>
                                    <Button variant="ghost" size="sm">عرض</Button>
                                  </Link>
                                </div>
                              ))}

                              {userInitiatives.length > 3 && (
                                <div className="text-center mt-2">
                                  <Button variant="link" size="sm" className="text-blue-600">
                                    عرض المزيد ({userInitiatives.length - 3})
                                  </Button>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="text-center py-6">
                              <p className="text-gray-500">لم تقم بإنشاء أي مبادرات بعد</p>
                              <Link href="/initiatives/create">
                                <Button variant="outline" className="mt-2">إنشاء مبادرة جديدة</Button>
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {profileData?.userType === 'company' && (
                      <div className="space-y-6">
                        <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                          <div className="flex items-center mb-4">
                            <div className="bg-amber-500 text-white p-2 rounded-full">
                              <Building className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold text-lg mr-2">ملف الشركة/المؤسسة</h3>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="bg-white p-3 rounded-md border border-amber-100">
                              <h4 className="font-semibold mb-2 flex items-center">
                                <Building className="h-4 w-4 ml-1" />
                                اسم الشركة/المؤسسة
                              </h4>
                              {profileData.companyName ? (
                                <p className="text-gray-700">{profileData.companyName}</p>
                              ) : (
                                <div className="flex items-center justify-between">
                                  <p className="text-sm text-gray-500">لم يتم تحديد اسم الشركة/المؤسسة</p>
                                  <Link href="/settings">
                                    <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                  </Link>
                                </div>
                              )}
                            </div>

                            <div className="bg-white p-3 rounded-md border border-amber-100">
                              <h4 className="font-semibold mb-2 flex items-center">
                                <Briefcase className="h-4 w-4 ml-1" />
                                المجال
                              </h4>
                              {profileData.industry ? (
                                <p className="text-gray-700">{profileData.industry}</p>
                              ) : (
                                <div className="flex items-center justify-between">
                                  <p className="text-sm text-gray-500">لم يتم تحديد المجال</p>
                                  <Link href="/settings">
                                    <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                  </Link>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="mt-4 space-y-4">
                            <div className="bg-white p-3 rounded-md border border-amber-100">
                              <h4 className="font-semibold mb-2 flex items-center">
                                <Users className="h-4 w-4 ml-1" />
                                الخدمات
                              </h4>
                              {profileData.services && profileData.services.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                  {profileData.services.map((service, index) => (
                                    <Badge key={index} variant="outline" className="bg-blue-50 border-blue-200">
                                      {service}
                                    </Badge>
                                  ))}
                                </div>
                              ) : (
                                <div className="flex items-center justify-between">
                                  <p className="text-sm text-gray-500">لم يتم إضافة أي خدمات بعد</p>
                                  <Link href="/settings">
                                    <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                  </Link>
                                </div>
                              )}
                            </div>

                            <div className="bg-white p-3 rounded-md border border-amber-100">
                              <h4 className="font-semibold mb-2 flex items-center">
                                <Target className="h-4 w-4 ml-1" />
                                الموارد المتاحة
                              </h4>
                              {profileData.resources && profileData.resources.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                  {profileData.resources.map((resource, index) => (
                                    <Badge key={index} variant="outline" className="bg-green-50 border-green-200">
                                      {resource}
                                    </Badge>
                                  ))}
                                </div>
                              ) : (
                                <div className="flex items-center justify-between">
                                  <p className="text-sm text-gray-500">لم يتم إضافة أي موارد بعد</p>
                                  <Link href="/settings">
                                    <Button variant="outline" size="sm" className="text-xs">إضافة</Button>
                                  </Link>
                                </div>
                              )}
                            </div>
                          </div>

                          {!profileData.companyName && !profileData.industry &&
                           (!profileData.services || profileData.services.length === 0) &&
                           (!profileData.resources || profileData.resources.length === 0) && (
                            <div className="text-center py-4 mt-4 border-t border-amber-100">
                              <p className="text-gray-500 mb-4">أكمل ملف الشركة/المؤسسة لزيادة فرص المشاركة في المبادرات</p>
                              <Link href="/settings">
                                <Button className="bg-amber-600 hover:bg-amber-700">إضافة معلومات الشركة/المؤسسة</Button>
                              </Link>
                            </div>
                          )}
                        </div>

                        {/* Section pour les initiatives soutenues */}
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="flex items-center mb-4">
                            <div className="bg-gray-500 text-white p-2 rounded-full">
                              <Heart className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold text-lg mr-2">المبادرات المدعومة</h3>
                          </div>

                          {supportedInitiatives.length > 0 ? (
                            <div className="space-y-3">
                              {supportedInitiatives.slice(0, 3).map((initiative) => (
                                <div key={initiative._id} className="flex items-center p-2 bg-white rounded-md border border-gray-100">
                                  <div className="w-2 h-2 bg-amber-500 rounded-full ml-2"></div>
                                  <div className="flex-1">
                                    <p className="text-sm font-medium">{initiative.title}</p>
                                    <p className="text-xs text-gray-500">تم دعم المبادرة</p>
                                  </div>
                                  <Link href={`/initiatives/${initiative._id}`}>
                                    <Button variant="ghost" size="sm">عرض</Button>
                                  </Link>
                                </div>
                              ))}

                              {supportedInitiatives.length > 3 && (
                                <div className="text-center mt-2">
                                  <Button variant="link" size="sm" className="text-blue-600">
                                    عرض المزيد ({supportedInitiatives.length - 3})
                                  </Button>
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="text-center py-6">
                              <p className="text-gray-500">لم تقم بدعم أي مبادرات بعد</p>
                              <Link href="/initiatives">
                                <Button variant="outline" className="mt-2">استكشف المبادرات</Button>
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
      <Toaster />
    </div>
    </ClientOnly>
  )
}

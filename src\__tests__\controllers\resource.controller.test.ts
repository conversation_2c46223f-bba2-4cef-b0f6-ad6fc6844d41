import { Request, Response } from "express"
import mongoose from "mongoose"
import { 
  getResources, 
  getResourceNeeds, 
  getInitiativeResources, 
  getUserResources, 
  getResourceById, 
  offerResource, 
  updateResourceStatus, 
  cancelResourceOffer, 
  getInitiativeResourceNeeds, 
  createResourceNeed, 
  updateResourceNeed, 
  deleteResourceNeed 
} from "../../controllers/resource.controller"
import { Initiative, Resource, ResourceNeed, User } from "../../models"
import { createNotification } from "../../utils/notification"

// Mock the models and utils
jest.mock("../../models", () => ({
  Initiative: {
    findById: jest.fn(),
  },
  Resource: {
    find: jest.fn(),
    findById: jest.fn(),
    countDocuments: jest.fn(),
  },
  ResourceNeed: {
    find: jest.fn(),
    findById: jest.fn(),
    countDocuments: jest.fn(),
  },
  User: {
    findById: jest.fn(),
  },
}))

jest.mock("../../utils/notification", () => ({
  createNotification: jest.fn(),
}))

// Mock mongoose.Types.ObjectId.isValid
jest.mock("mongoose", () => ({
  ...jest.requireActual("mongoose"),
  Types: {
    ObjectId: {
      isValid: jest.fn(),
    },
  },
}))

describe("Resource Controller", () => {
  let req: Partial<Request>
  let res: Partial<Response>
  let next: jest.Mock

  beforeEach(() => {
    req = {
      params: {},
      query: {},
      body: {},
      user: { id: "user123" },
    }
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    }
    next = jest.fn()

    // Reset all mocks
    jest.clearAllMocks()
  })

  describe("getResources", () => {
    it("should get all resources with pagination", async () => {
      // Setup
      req.query = { limit: "10", page: "1" }
      const mockResources = [{ _id: "resource1" }, { _id: "resource2" }]
      
      // Mock implementations
      ;(Resource.find as jest.Mock).mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockResources),
      })
      ;(Resource.countDocuments as jest.Mock).mockResolvedValue(2)

      // Execute
      await getResources(req as Request, res as Response, next)

      // Assert
      expect(Resource.find).toHaveBeenCalled()
      expect(Resource.countDocuments).toHaveBeenCalled()
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        count: 2,
        total: 2,
        page: 1,
        pages: 1,
        resources: mockResources,
      })
    })

    it("should handle errors", async () => {
      // Setup
      const error = new Error("Database error")
      ;(Resource.find as jest.Mock).mockImplementation(() => {
        throw error
      })

      // Execute
      await getResources(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalledWith(error)
    })
  })

  describe("getResourceNeeds", () => {
    it("should get all resource needs with pagination", async () => {
      // Setup
      req.query = { limit: "10", page: "1" }
      const mockNeeds = [{ _id: "need1" }, { _id: "need2" }]
      
      // Mock implementations
      ;(ResourceNeed.find as jest.Mock).mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockNeeds),
      })
      ;(ResourceNeed.countDocuments as jest.Mock).mockResolvedValue(2)

      // Execute
      await getResourceNeeds(req as Request, res as Response, next)

      // Assert
      expect(ResourceNeed.find).toHaveBeenCalled()
      expect(ResourceNeed.countDocuments).toHaveBeenCalled()
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        count: 2,
        total: 2,
        page: 1,
        pages: 1,
        resourceNeeds: mockNeeds,
      })
    })

    it("should handle errors", async () => {
      // Setup
      const error = new Error("Database error")
      ;(ResourceNeed.find as jest.Mock).mockImplementation(() => {
        throw error
      })

      // Execute
      await getResourceNeeds(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalledWith(error)
    })
  })

  describe("getInitiativeResources", () => {
    it("should get resources for a specific initiative", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      const mockResources = [{ _id: "resource1" }, { _id: "resource2" }]
      
      // Mock implementations
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockResolvedValue({ _id: "initiative123" })
      ;(Resource.find as jest.Mock).mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockResources),
      })

      // Execute
      await getInitiativeResources(req as Request, res as Response, next)

      // Assert
      expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith("initiative123")
      expect(Initiative.findById).toHaveBeenCalledWith("initiative123")
      expect(Resource.find).toHaveBeenCalledWith({ initiative: "initiative123" })
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        count: 2,
        resources: mockResources,
      })
    })

    it("should return 400 for invalid initiative ID", async () => {
      // Setup
      req.params = { initiativeId: "invalid-id" }
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(false)

      // Execute
      await getInitiativeResources(req as Request, res as Response, next)

      // Assert
      expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith("invalid-id")
      expect(next).toHaveBeenCalled()
      expect(next.mock.calls[0][0].statusCode).toBe(400)
    })

    it("should return 404 if initiative not found", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockResolvedValue(null)

      // Execute
      await getInitiativeResources(req as Request, res as Response, next)

      // Assert
      expect(Initiative.findById).toHaveBeenCalledWith("initiative123")
      expect(next).toHaveBeenCalled()
      expect(next.mock.calls[0][0].statusCode).toBe(404)
    })

    it("should handle errors", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      const error = new Error("Database error")
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockImplementation(() => {
        throw error
      })

      // Execute
      await getInitiativeResources(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalledWith(error)
    })
  })

  describe("offerResource", () => {
    it("should create a new resource offer", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      req.body = {
        type: "material",
        name: "Test Resource",
        description: "Test Description",
        quantity: 10,
        unit: "pieces",
      }
      req.user = { id: "user123" }
      
      const mockInitiative = {
        _id: "initiative123",
        title: "Test Initiative",
        author: "author123",
        status: "active",
      }
      
      const mockResource = {
        _id: "resource123",
        initiative: "initiative123",
        provider: "user123",
        ...req.body,
        save: jest.fn().mockResolvedValue(true),
      }
      
      // Mock implementations
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockResolvedValue(mockInitiative)
      ;(Resource as any).mockImplementation(() => mockResource)
      ;(createNotification as jest.Mock).mockResolvedValue(true)

      // Execute
      await offerResource(req as Request, res as Response, next)

      // Assert
      expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith("initiative123")
      expect(Initiative.findById).toHaveBeenCalledWith("initiative123")
      expect(mockResource.save).toHaveBeenCalled()
      expect(createNotification).toHaveBeenCalled()
      expect(res.status).toHaveBeenCalledWith(201)
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: "Resource offered successfully",
        resource: mockResource,
      })
    })

    it("should return 400 for inactive initiative", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      req.body = {
        type: "material",
        name: "Test Resource",
        description: "Test Description",
        quantity: 10,
        unit: "pieces",
      }
      
      const mockInitiative = {
        _id: "initiative123",
        title: "Test Initiative",
        author: "author123",
        status: "completed", // Not active
      }
      
      // Mock implementations
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockResolvedValue(mockInitiative)

      // Execute
      await offerResource(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalled()
      expect(next.mock.calls[0][0].statusCode).toBe(400)
      expect(next.mock.calls[0][0].message).toBe("Cannot offer resources to inactive initiatives")
    })

    it("should handle errors", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      const error = new Error("Database error")
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockImplementation(() => {
        throw error
      })

      // Execute
      await offerResource(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalledWith(error)
    })
  })

  // Additional tests for other controller methods would follow the same pattern
})

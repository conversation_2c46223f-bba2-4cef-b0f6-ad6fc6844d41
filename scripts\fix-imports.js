const fs = require('fs');
const path = require('path');

// Directory containing UI components
const uiDir = path.join(__dirname, '..', 'components', 'ui');

// Function to fix imports in a file
function fixImportsInFile(filePath) {
  try {
    // Read the file content
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Replace @/lib/utils with ../../lib/utils
    const updatedContent = content.replace(/from\s+["']@\/lib\/utils["']/g, 'from "../../lib/utils"');
    
    // Replace other common imports
    const furtherUpdatedContent = updatedContent
      .replace(/from\s+["']@\/components\/ui\/([^"']+)["']/g, 'from "./$1"')
      .replace(/from\s+["']@\/lib\/([^"']+)["']/g, 'from "../../lib/$1"')
      .replace(/from\s+["']@\/hooks\/([^"']+)["']/g, 'from "../../hooks/$1"')
      .replace(/from\s+["']@\/components\/([^"']+)["']/g, 'from "../$1"');
    
    // Write the updated content back to the file
    if (content !== furtherUpdatedContent) {
      fs.writeFileSync(filePath, furtherUpdatedContent, 'utf8');
      console.log(`Fixed imports in ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Get all .tsx and .ts files in the UI directory
const files = fs.readdirSync(uiDir)
  .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'))
  .map(file => path.join(uiDir, file));

// Fix imports in each file
let fixedCount = 0;
files.forEach(file => {
  if (fixImportsInFile(file)) {
    fixedCount++;
  }
});

console.log(`Fixed imports in ${fixedCount} files out of ${files.length}`);

// Also fix imports in other key components
const otherComponents = [
  path.join(__dirname, '..', 'components', 'theme-provider.tsx'),
  path.join(__dirname, '..', 'components', 'auth-provider.tsx'),
  path.join(__dirname, '..', 'components', 'header.tsx'),
  path.join(__dirname, '..', 'components', 'footer.tsx')
];

otherComponents.forEach(file => {
  if (fs.existsSync(file) && fixImportsInFile(file)) {
    console.log(`Fixed imports in ${path.basename(file)}`);
  }
});

console.log('Import paths fixed successfully!');

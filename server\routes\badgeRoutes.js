const express = require('express');
const router = express.Router();
const badgeController = require('../controllers/badgeController');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Public routes
router.get('/', badgeController.getAllBadges);
router.get('/:id', badgeController.getBadgeById);

// Admin routes
router.post('/', authenticateToken, isAdmin, badgeController.createBadge);
router.put('/:id', authenticateToken, isAdmin, badgeController.updateBadge);
router.delete('/:id', authenticateToken, isAdmin, badgeController.deleteBadge);

module.exports = router;

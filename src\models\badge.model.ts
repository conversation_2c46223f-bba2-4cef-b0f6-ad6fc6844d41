import { Schema } from "mongoose"

export interface IBadge {
  name: string;
  arabicName: string;
  description: string;
  arabicDescription: string;
  icon: string;
  color: string;
  criteria: string;
  category: "participation" | "achievement" | "contribution" | "special" | "skill";
  level?: number; // 1-5 pour indiquer le niveau du badge
  issuedCount?: number; // Nombre de fois que ce badge a été attribué
}

const badgeSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    arabicName: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    arabicDescription: {
      type: String,
      required: true,
    },
    icon: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: "#4CAF50", // Couleur verte par défaut
    },
    criteria: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      enum: ["participation", "achievement", "contribution", "special", "skill"],
      required: true,
    },
    level: {
      type: Number,
      min: 1,
      max: 5,
      default: 1,
    },
    issuedCount: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true },
)

export default badgeSchema


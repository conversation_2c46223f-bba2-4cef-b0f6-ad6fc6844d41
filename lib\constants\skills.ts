// Types de compétences
export const SKILL_CATEGORIES = [
  {
    id: 'cognitive',
    name: 'Compétences cognitives',
    arabicName: 'المهارات المعرفية',
    description: 'Capacité à comprendre, analyser, résoudre et apprendre',
    arabicDescription: 'القدرة على الفهم والتحليل وحل المشكلات والتعلم',
    examples: ['Analyse et synthèse', 'Résolution de problèmes', 'Esprit critique', 'Capacité d\'apprentissage', 'Prise de décision'],
    arabicExamples: ['التحليل والتركيب', 'حل المشكلات', 'التفكير النقدي', 'القدرة على التعلم', 'اتخاذ القرار'],
    icon: '🧠'
  },
  {
    id: 'technical',
    name: 'Compétences techniques',
    arabicName: 'المهارات التقنية',
    description: 'Savoir-faire spécifiques à un métier, acquises par la formation ou l\'expérience',
    arabicDescription: 'المهارات الخاصة بمهنة معينة، المكتسبة من خلال التدريب أو الخبرة',
    examples: ['Programmation', 'Comptabilité', 'Rédaction', 'Conception graphique', 'Gestion de projet'],
    arabicExamples: ['البرمجة', 'المحاسبة', 'الكتابة', 'التصميم الجرافيكي', 'إدارة المشاريع'],
    icon: '🛠️'
  },
  {
    id: 'interpersonal',
    name: 'Compétences interpersonnelles',
    arabicName: 'المهارات الشخصية',
    description: 'Comportement, communication et intelligence émotionnelle',
    arabicDescription: 'السلوك والتواصل والذكاء العاطفي',
    examples: ['Communication', 'Travail en équipe', 'Leadership', 'Gestion des conflits', 'Empathie'],
    arabicExamples: ['التواصل', 'العمل الجماعي', 'القيادة', 'إدارة النزاعات', 'التعاطف'],
    icon: '🤝'
  },
  {
    id: 'organizational',
    name: 'Compétences organisationnelles',
    arabicName: 'المهارات التنظيمية',
    description: 'Capacité à structurer, planifier, gérer des ressources ou des priorités',
    arabicDescription: 'القدرة على التنظيم والتخطيط وإدارة الموارد أو الأولويات',
    examples: ['Gestion du temps', 'Planification de projet', 'Organisation personnelle', 'Capacité à déléguer'],
    arabicExamples: ['إدارة الوقت', 'تخطيط المشاريع', 'التنظيم الشخصي', 'القدرة على التفويض'],
    icon: '🧭'
  },
  {
    id: 'digital',
    name: 'Compétences numériques',
    arabicName: 'المهارات الرقمية',
    description: 'Capacité à utiliser efficacement les technologies de l\'information',
    arabicDescription: 'القدرة على استخدام تكنولوجيا المعلومات بكفاءة',
    examples: ['Bureautique', 'Réseaux sociaux', 'Sécurité informatique', 'Plateformes collaboratives'],
    arabicExamples: ['المكتبية', 'وسائل التواصل الاجتماعي', 'أمن المعلومات', 'منصات التعاون'],
    icon: '💻'
  },
  {
    id: 'linguistic',
    name: 'Compétences linguistiques',
    arabicName: 'المهارات اللغوية',
    description: 'Compétences en langues et adaptation aux environnements multiculturels',
    arabicDescription: 'المهارات اللغوية والتكيف مع البيئات متعددة الثقافات',
    examples: ['Langues étrangères', 'Traduction', 'Sensibilité interculturelle'],
    arabicExamples: ['اللغات الأجنبية', 'الترجمة', 'الحساسية بين الثقافات'],
    icon: '🌍'
  },
  {
    id: 'transversal',
    name: 'Compétences transversales',
    arabicName: 'المهارات العرضية',
    description: 'Compétences applicables à de nombreux domaines',
    arabicDescription: 'المهارات القابلة للتطبيق في العديد من المجالات',
    examples: ['Esprit d\'initiative', 'Adaptabilité', 'Créativité', 'Apprentissage continu'],
    arabicExamples: ['روح المبادرة', 'القدرة على التكيف', 'الإبداع', 'التعلم المستمر'],
    icon: '🧪'
  }
];

// Niveaux de compétence
export const SKILL_LEVELS = [
  {
    id: 'beginner',
    name: 'Débutant',
    arabicName: 'مبتدئ',
    description: 'Connaissances de base, nécessite supervision',
    arabicDescription: 'معرفة أساسية، يتطلب إشرافًا'
  },
  {
    id: 'intermediate',
    name: 'Intermédiaire',
    arabicName: 'متوسط',
    description: 'Bonne maîtrise, autonomie sur des tâches courantes',
    arabicDescription: 'إتقان جيد، استقلالية في المهام الروتينية'
  },
  {
    id: 'advanced',
    name: 'Avancé',
    arabicName: 'متقدم',
    description: 'Maîtrise approfondie, peut former d\'autres personnes',
    arabicDescription: 'إتقان متعمق، يمكنه تدريب الآخرين'
  },
  {
    id: 'expert',
    name: 'Expert',
    arabicName: 'خبير',
    description: 'Expertise reconnue, référence dans le domaine',
    arabicDescription: 'خبرة معترف بها، مرجع في المجال'
  }
];

// Exemples de compétences par catégorie
export const SKILL_EXAMPLES = {
  cognitive: [
    'Analyse de données',
    'Résolution de problèmes complexes',
    'Pensée critique',
    'Prise de décision',
    'Raisonnement logique'
  ],
  technical: [
    'Développement web',
    'Conception graphique',
    'Rédaction technique',
    'Comptabilité',
    'Marketing digital'
  ],
  interpersonal: [
    'Communication orale',
    'Négociation',
    'Gestion d\'équipe',
    'Résolution de conflits',
    'Écoute active'
  ],
  organizational: [
    'Gestion du temps',
    'Planification stratégique',
    'Coordination d\'événements',
    'Gestion de projet',
    'Priorisation des tâches'
  ],
  digital: [
    'Suite bureautique',
    'Gestion des réseaux sociaux',
    'Analyse de données',
    'Outils collaboratifs',
    'Sécurité informatique'
  ],
  linguistic: [
    'Anglais',
    'Français',
    'Arabe',
    'Traduction',
    'Communication interculturelle'
  ],
  transversal: [
    'Créativité',
    'Adaptabilité',
    'Autonomie',
    'Esprit d\'initiative',
    'Apprentissage continu'
  ]
};

// Exemples de compétences par catégorie en arabe
export const SKILL_EXAMPLES_AR = {
  cognitive: [
    'تحليل البيانات',
    'حل المشكلات المعقدة',
    'التفكير النقدي',
    'اتخاذ القرار',
    'التفكير المنطقي'
  ],
  technical: [
    'تطوير الويب',
    'التصميم الجرافيكي',
    'الكتابة التقنية',
    'المحاسبة',
    'التسويق الرقمي'
  ],
  interpersonal: [
    'التواصل الشفهي',
    'التفاوض',
    'إدارة الفريق',
    'حل النزاعات',
    'الاستماع النشط'
  ],
  organizational: [
    'إدارة الوقت',
    'التخطيط الاستراتيجي',
    'تنسيق الفعاليات',
    'إدارة المشاريع',
    'ترتيب الأولويات'
  ],
  digital: [
    'برامج المكتب',
    'إدارة وسائل التواصل الاجتماعي',
    'تحليل البيانات',
    'أدوات التعاون',
    'أمن المعلومات'
  ],
  linguistic: [
    'الإنجليزية',
    'الفرنسية',
    'العربية',
    'الترجمة',
    'التواصل بين الثقافات'
  ],
  transversal: [
    'الإبداع',
    'القدرة على التكيف',
    'الاستقلالية',
    'روح المبادرة',
    'التعلم المستمر'
  ]
};

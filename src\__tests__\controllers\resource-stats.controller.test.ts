import { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { getResourceStats, getInitiativeResourceStats } from "../../controllers/resource-stats.controller"
import { createError } from "../../utils/error"
import { Resource, ResourceNeed, Initiative } from "../../models"

// Mock the models
jest.mock("../../models", () => ({
  Resource: {
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
  },
  ResourceNeed: {
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
  },
  Initiative: {
    findById: jest.fn(),
  },
}))

// Mock mongoose.Types.ObjectId
jest.mock("mongoose", () => ({
  ...jest.requireActual("mongoose"),
  Types: {
    ObjectId: class {
      constructor(id: string) {
        return { id }
      }
    },
    ObjectId: {
      isValid: jest.fn(),
    },
  },
}))

// Mock createError
jest.mock("../../utils/error", () => ({
  createError: jest.fn((statusCode, message) => ({
    statusCode,
    message,
  })),
}))

describe("Resource Stats Controller", () => {
  let req: Partial<Request>
  let res: Partial<Response>
  let next: jest.Mock

  beforeEach(() => {
    req = {
      params: {},
    }
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    }
    next = jest.fn()

    // Reset all mocks
    jest.clearAllMocks()
  })

  describe("getResourceStats", () => {
    it("should return resource statistics", async () => {
      // Setup
      const mockResourcesByType = [
        { _id: "material", count: 10 },
        { _id: "financial", count: 5 },
      ]

      const mockResourcesByStatus = [
        { _id: "delivered", count: 8 },
        { _id: "requested", count: 7 },
      ]

      const mockNeedsByType = [
        { _id: "material", count: 12 },
        { _id: "human", count: 3 },
      ]

      const mockNeedsByPriority = [
        { _id: "high", count: 8 },
        { _id: "medium", count: 7 },
      ]

      const mockNeedsByStatus = [
        { _id: "open", count: 10 },
        { _id: "fulfilled", count: 5 },
      ]

      const mockTopInitiativesByResources = [
        {
          initiativeId: "initiative1",
          title: "Initiative 1",
          count: 5
        },
      ]

      const mockTopInitiativesByNeeds = [
        {
          initiativeId: "initiative2",
          title: "Initiative 2",
          count: 7
        },
      ]

      const mockResourcesByMonth = [
        {
          _id: { year: 2023, month: 5 },
          count: 10
        },
      ]

      // Mock implementations
      ;(Resource.aggregate as jest.Mock)
        .mockImplementationOnce(() => Promise.resolve(mockResourcesByType))
        .mockImplementationOnce(() => Promise.resolve(mockResourcesByStatus))
        .mockImplementationOnce(() => Promise.resolve(mockTopInitiativesByResources))
        .mockImplementationOnce(() => Promise.resolve(mockResourcesByMonth))

      ;(ResourceNeed.aggregate as jest.Mock)
        .mockImplementationOnce(() => Promise.resolve(mockNeedsByType))
        .mockImplementationOnce(() => Promise.resolve(mockNeedsByPriority))
        .mockImplementationOnce(() => Promise.resolve(mockNeedsByStatus))
        .mockImplementationOnce(() => Promise.resolve(mockTopInitiativesByNeeds))

      // Execute
      await getResourceStats(req as Request, res as Response, next)

      // Assert
      expect(Resource.aggregate).toHaveBeenCalledTimes(4)
      expect(ResourceNeed.aggregate).toHaveBeenCalledTimes(4)
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        statistics: {
          resourcesByType: [
            { type: "material", count: 10 },
            { type: "financial", count: 5 },
          ],
          resourcesByStatus: [
            { status: "delivered", count: 8 },
            { status: "requested", count: 7 },
          ],
          needsByType: [
            { type: "material", count: 12 },
            { type: "human", count: 3 },
          ],
          needsByPriority: [
            { priority: "high", count: 8 },
            { priority: "medium", count: 7 },
          ],
          needsByStatus: [
            { status: "open", count: 10 },
            { status: "fulfilled", count: 5 },
          ],
          topInitiativesByResources: mockTopInitiativesByResources,
          topInitiativesByNeeds: mockTopInitiativesByNeeds,
          monthlyData: [
            { month: "2023-05", count: 10 },
          ],
        },
      })
    })

    it("should handle errors", async () => {
      // Setup
      const error = new Error("Database error")
      ;(Resource.aggregate as jest.Mock).mockImplementation(() => {
        throw error
      })

      // Execute
      await getResourceStats(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalledWith({
        statusCode: 500,
        message: "Failed to fetch resource statistics",
      })
      expect(createError).toHaveBeenCalledWith(500, "Failed to fetch resource statistics")
    })
  })

  describe("getInitiativeResourceStats", () => {
    it("should return resource statistics for a specific initiative", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }

      const mockResourcesByType = [
        { _id: "material", count: 5 },
        { _id: "financial", count: 3 },
      ]

      const mockResourcesByStatus = [
        { _id: "delivered", count: 4 },
        { _id: "requested", count: 4 },
      ]

      const mockNeedsByType = [
        { _id: "material", count: 6 },
        { _id: "human", count: 2 },
      ]

      const mockNeedsByPriority = [
        { _id: "high", count: 5 },
        { _id: "medium", count: 3 },
      ]

      const mockNeedsByStatus = [
        { _id: "open", count: 5 },
        { _id: "fulfilled", count: 3 },
      ]

      // Mock implementations
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockResolvedValue({ _id: "initiative123" })

      ;(Resource.aggregate as jest.Mock)
        .mockImplementationOnce(() => Promise.resolve(mockResourcesByType))
        .mockImplementationOnce(() => Promise.resolve(mockResourcesByStatus))

      ;(ResourceNeed.aggregate as jest.Mock)
        .mockImplementationOnce(() => Promise.resolve(mockNeedsByType))
        .mockImplementationOnce(() => Promise.resolve(mockNeedsByPriority))
        .mockImplementationOnce(() => Promise.resolve(mockNeedsByStatus))

      ;(ResourceNeed.countDocuments as jest.Mock)
        .mockResolvedValueOnce(8) // total needs
        .mockResolvedValueOnce(3) // fulfilled needs

      ;(Resource.countDocuments as jest.Mock)
        .mockResolvedValueOnce(8) // total resources
        .mockResolvedValueOnce(4) // delivered resources

      // Execute
      await getInitiativeResourceStats(req as Request, res as Response, next)

      // Assert
      expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith("initiative123")
      expect(Initiative.findById).toHaveBeenCalledWith("initiative123")
      expect(Resource.aggregate).toHaveBeenCalledTimes(2)
      expect(ResourceNeed.aggregate).toHaveBeenCalledTimes(3)
      expect(ResourceNeed.countDocuments).toHaveBeenCalledTimes(2)
      expect(Resource.countDocuments).toHaveBeenCalledTimes(2)
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        statistics: {
          resourcesByType: [
            { type: "material", count: 5 },
            { type: "financial", count: 3 },
          ],
          resourcesByStatus: [
            { status: "delivered", count: 4 },
            { status: "requested", count: 4 },
          ],
          needsByType: [
            { type: "material", count: 6 },
            { type: "human", count: 2 },
          ],
          needsByPriority: [
            { priority: "high", count: 5 },
            { priority: "medium", count: 3 },
          ],
          needsByStatus: [
            { status: "open", count: 5 },
            { status: "fulfilled", count: 3 },
          ],
          summary: {
            totalNeeds: 8,
            fulfilledNeeds: 3,
            fulfillmentRate: 38,
            totalResources: 8,
            deliveredResources: 4,
            deliveryRate: 50,
          },
        },
      })
    })

    it("should return 400 for invalid initiative ID", async () => {
      // Setup
      req.params = { initiativeId: "invalid-id" }
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(false)

      // Execute
      await getInitiativeResourceStats(req as Request, res as Response, next)

      // Assert
      expect(mongoose.Types.ObjectId.isValid).toHaveBeenCalledWith("invalid-id")
      expect(next).toHaveBeenCalledWith({
        statusCode: 400,
        message: "Invalid initiative ID",
      })
      expect(createError).toHaveBeenCalledWith(400, "Invalid initiative ID")
    })

    it("should return 404 if initiative not found", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockResolvedValue(null)

      // Execute
      await getInitiativeResourceStats(req as Request, res as Response, next)

      // Assert
      expect(Initiative.findById).toHaveBeenCalledWith("initiative123")
      expect(next).toHaveBeenCalledWith({
        statusCode: 404,
        message: "Initiative not found",
      })
      expect(createError).toHaveBeenCalledWith(404, "Initiative not found")
    })

    it("should handle errors", async () => {
      // Setup
      req.params = { initiativeId: "initiative123" }
      const error = new Error("Database error")
      ;(mongoose.Types.ObjectId.isValid as jest.Mock).mockReturnValue(true)
      ;(Initiative.findById as jest.Mock).mockImplementation(() => {
        throw error
      })

      // Execute
      await getInitiativeResourceStats(req as Request, res as Response, next)

      // Assert
      expect(next).toHaveBeenCalledWith({
        statusCode: 500,
        message: "Failed to fetch initiative resource statistics",
      })
      expect(createError).toHaveBeenCalledWith(500, "Failed to fetch initiative resource statistics")
    })
  })
})

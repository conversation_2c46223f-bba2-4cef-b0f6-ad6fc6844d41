import express from "express"
import { authenticate } from "../middleware/auth"
import {
  getMilestonesByInitiative,
  getMilestoneById,
  createMilestone,
  updateMilestone,
  deleteMilestone
} from "../controllers/milestone.controller"

const router = express.Router()

// Public routes
router.get("/initiative/:initiativeId", getMilestonesByInitiative)
router.get("/:id", getMilestoneById)

// Protected routes
router.post("/", authenticate, createMilestone) // Add a route for creating milestones without URL parameter
router.post("/initiative/:initiativeId", authenticate, createMilestone) // Keep the original route for backward compatibility
router.put("/:id", authenticate, updateMilestone)
router.delete("/:id", authenticate, deleteMilestone)
router.patch("/:id/complete", authenticate, updateMilestone) // Use updateMilestone for completing milestones

export default router

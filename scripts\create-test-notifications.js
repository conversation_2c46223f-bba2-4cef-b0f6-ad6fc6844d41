const mongoose = require('mongoose');
const { MongoClient, ObjectId } = require('mongodb');

// Configuration de la connexion MongoDB
const MONGODB_URI = 'mongodb://localhost:27017/initiatives_dz';
const USER_ID = '681396ffa9c3356546496c2b'; // Remplacez par l'ID de l'utilisateur pour lequel vous voulez créer des notifications

// Types de notifications possibles
const notificationTypes = [
  'initiative_invitation',
  'initiative_update',
  'comment',
  'support',
  'milestone_completed',
  'admin_message',
  'system'
];

// Fonction pour créer des notifications de test
async function createTestNotifications() {
  try {
    // Connexion à MongoDB
    const client = await MongoClient.connect(MONGODB_URI);
    const db = client.db();
    
    // Récupérer quelques initiatives pour les références
    const initiatives = await db.collection('initiatives').find().limit(3).toArray();
    
    // Créer les notifications
    const notifications = [];
    for (let i = 0; i < 10; i++) {
      const type = notificationTypes[Math.floor(Math.random() * notificationTypes.length)];
      const initiative = initiatives.length > 0 ? initiatives[Math.floor(Math.random() * initiatives.length)]._id : null;
      
      let title = '';
      let message = '';
      
      switch (type) {
        case 'initiative_invitation':
          title = 'دعوة للمشاركة في مبادرة';
          message = 'تمت دعوتك للمشاركة في مبادرة جديدة';
          break;
        case 'initiative_update':
          title = 'تحديث المبادرة';
          message = 'تم تحديث مبادرة كنت تتابعها';
          break;
        case 'comment':
          title = 'تعليق جديد';
          message = 'قام شخص ما بالتعليق على مبادرتك';
          break;
        case 'support':
          title = 'دعم جديد';
          message = 'قام شخص ما بدعم مبادرتك';
          break;
        case 'milestone_completed':
          title = 'اكتمال مرحلة';
          message = 'تم اكتمال مرحلة في مبادرة كنت تتابعها';
          break;
        case 'admin_message':
          title = 'رسالة من الإدارة';
          message = 'لديك رسالة جديدة من إدارة المنصة';
          break;
        case 'system':
          title = 'إشعار نظام';
          message = 'هناك تحديث جديد في النظام';
          break;
      }
      
      const notification = {
        recipient: new ObjectId(USER_ID),
        type,
        title,
        message,
        read: Math.random() > 0.7, // 30% chance of being read
        initiative: initiative ? initiative._id : null,
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)), // Random date within the last week
        updatedAt: new Date()
      };
      
      notifications.push(notification);
    }
    
    // Insérer les notifications dans la base de données
    const result = await db.collection('notifications').insertMany(notifications);
    
    console.log(`Created ${result.insertedCount} test notifications for user ${USER_ID}`);
    
    // Fermer la connexion
    await client.close();
  } catch (error) {
    console.error('Error creating test notifications:', error);
  }
}

// Exécuter la fonction
createTestNotifications();

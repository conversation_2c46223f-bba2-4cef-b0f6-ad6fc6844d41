"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { SKILL_CATEGORIES, SKILL_LEVELS } from "@/lib/constants/skills"

interface Skill {
  name: string
  category: 'cognitive' | 'technical' | 'interpersonal' | 'organizational' | 'digital' | 'linguistic' | 'transversal'
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
}

interface UserSkillsDisplayProps {
  skills: Skill[]
  qualifications?: string[]
  interests?: string[]
  userType: 'volunteer' | 'proposer' | 'company'
}

export default function UserSkillsDisplay({ 
  skills, 
  qualifications = [], 
  interests = [],
  userType
}: UserSkillsDisplayProps) {
  // Si aucune compétence n'est disponible
  if (!skills || skills.length === 0) {
    return (
      <Card>
        <CardContent className="py-8 text-center text-gray-500">
          <p>لم يتم إضافة أي مهارات بعد.</p>
        </CardContent>
      </Card>
    )
  }

  // Regrouper les compétences par catégorie
  const skillsByCategory: Record<string, Skill[]> = {}
  
  skills.forEach(skill => {
    if (!skillsByCategory[skill.category]) {
      skillsByCategory[skill.category] = []
    }
    skillsByCategory[skill.category].push(skill)
  })

  // Obtenir les informations de catégorie
  const getCategoryInfo = (categoryId: string) => {
    return SKILL_CATEGORIES.find(cat => cat.id === categoryId) || {
      id: categoryId,
      arabicName: 'فئة غير معروفة',
      icon: '🔧',
      arabicDescription: ''
    }
  }

  // Obtenir les informations de niveau
  const getLevelInfo = (levelId: string) => {
    return SKILL_LEVELS.find(level => level.id === levelId) || {
      id: levelId,
      arabicName: 'مستوى غير معروف',
      color: 'bg-gray-200'
    }
  }

  // Obtenir la couleur de fond pour un niveau
  const getLevelBgColor = (level: string) => {
    switch(level) {
      case 'beginner': return 'bg-blue-100 text-blue-800'
      case 'intermediate': return 'bg-green-100 text-green-800'
      case 'advanced': return 'bg-purple-100 text-purple-800'
      case 'expert': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Qualifications */}
      {qualifications && qualifications.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-medium">المؤهلات العلمية</h3>
          <div className="flex flex-wrap gap-2">
            {qualifications.map((qualification, index) => (
              <Badge key={index} variant="outline" className="bg-gray-50 text-gray-800">
                {qualification}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Compétences par catégorie */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">المهارات</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.keys(skillsByCategory).map(categoryId => {
            const category = getCategoryInfo(categoryId)
            return (
              <Card key={categoryId} className="overflow-hidden">
                <div className="bg-gray-50 p-3 border-b flex items-center justify-between">
                  <h4 className="font-medium flex items-center gap-2">
                    <span>{category.arabicName}</span>
                    <span>{category.icon}</span>
                  </h4>
                  <Badge variant="outline" className="bg-white">
                    {skillsByCategory[categoryId].length}
                  </Badge>
                </div>
                <CardContent className="p-3">
                  <div className="flex flex-wrap gap-2">
                    {skillsByCategory[categoryId].map((skill, index) => {
                      const levelInfo = getLevelInfo(skill.level)
                      return (
                        <div 
                          key={index} 
                          className="flex items-center gap-1 border rounded-full px-3 py-1 text-sm"
                        >
                          <span>{skill.name}</span>
                          <Badge className={`${getLevelBgColor(skill.level)} text-xs ml-1`}>
                            {levelInfo.arabicName}
                          </Badge>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Intérêts */}
      {interests && interests.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-medium">مجالات الاهتمام</h3>
          <div className="flex flex-wrap gap-2">
            {interests.map((interest, index) => (
              <Badge key={index} variant="outline" className="bg-green-50 text-green-800">
                {interest}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

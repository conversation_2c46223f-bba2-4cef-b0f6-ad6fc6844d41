"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Info, Building, Briefcase, Users, MapPin, Globe, Phone, Mail, Link as LinkIcon, X } from "lucide-react"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface CompanyTabContentProps {
  userSettings: any
  setUserSettings: (settings: any) => void
  handleArrayInput: (e: React.ChangeEvent<HTMLInputElement>, field: string) => void
}

// Liste des secteurs d'activité
const INDUSTRY_SECTORS = [
  { id: "technology", name: "تكنولوجيا المعلومات" },
  { id: "education", name: "التعليم والتدريب" },
  { id: "health", name: "الصحة والرعاية الطبية" },
  { id: "finance", name: "المالية والمصرفية" },
  { id: "manufacturing", name: "التصنيع" },
  { id: "retail", name: "التجزئة والتجارة" },
  { id: "construction", name: "البناء والإنشاءات" },
  { id: "agriculture", name: "الزراعة" },
  { id: "energy", name: "الطاقة" },
  { id: "transportation", name: "النقل والمواصلات" },
  { id: "media", name: "الإعلام والاتصالات" },
  { id: "tourism", name: "السياحة والضيافة" },
  { id: "consulting", name: "الاستشارات" },
  { id: "nonprofit", name: "المنظمات غير الربحية" },
  { id: "government", name: "القطاع الحكومي" },
  { id: "other", name: "أخرى" }
]

// Liste des types de ressources
const RESOURCE_TYPES = [
  { id: "financial", name: "دعم مالي", icon: "💰" },
  { id: "space", name: "مساحات عمل", icon: "🏢" },
  { id: "equipment", name: "معدات وتجهيزات", icon: "🔧" },
  { id: "expertise", name: "خبرات ومهارات", icon: "🧠" },
  { id: "mentorship", name: "إرشاد وتوجيه", icon: "👨‍🏫" },
  { id: "networking", name: "شبكة علاقات", icon: "🔗" },
  { id: "marketing", name: "تسويق وترويج", icon: "📢" },
  { id: "logistics", name: "خدمات لوجستية", icon: "🚚" },
  { id: "technology", name: "حلول تقنية", icon: "💻" },
  { id: "training", name: "تدريب", icon: "📚" }
]

export default function CompanyTabContent({ 
  userSettings, 
  setUserSettings,
  handleArrayInput
}: CompanyTabContentProps) {
  return (
    <Card>
      <CardHeader className="card-header">
        <CardTitle>أضف معلومات عن شركتك أو مؤسستك</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 card-content">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="companyProfile" className="text-lg font-medium">المعلومات الأساسية</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>أضف معلومات أساسية عن شركتك أو مؤسستك لمساعدة المبادرات في التعرف عليك بشكل أفضل.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="companyName" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                اسم الشركة أو المؤسسة
              </Label>
              <Input
                id="companyName"
                value={userSettings.companyName || ''}
                onChange={(e) => setUserSettings({ ...userSettings, companyName: e.target.value })}
                placeholder="أدخل اسم الشركة أو المؤسسة"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="industry" className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                مجال العمل
              </Label>
              <Select
                value={userSettings.industry || ''}
                onValueChange={(value) => setUserSettings({ ...userSettings, industry: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر مجال عمل الشركة" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRY_SECTORS.map(sector => (
                    <SelectItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {userSettings.industry === 'other' && (
                <Input
                  className="mt-2"
                  placeholder="حدد المجال"
                  value={userSettings.customIndustry || ''}
                  onChange={(e) => setUserSettings({ ...userSettings, customIndustry: e.target.value })}
                  dir="rtl"
                />
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="employeeCount" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                عدد الموظفين
              </Label>
              <Select
                value={userSettings.employeeCount || ''}
                onValueChange={(value) => setUserSettings({ ...userSettings, employeeCount: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر عدد الموظفين" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-10">1-10 موظفين</SelectItem>
                  <SelectItem value="11-50">11-50 موظف</SelectItem>
                  <SelectItem value="51-200">51-200 موظف</SelectItem>
                  <SelectItem value="201-500">201-500 موظف</SelectItem>
                  <SelectItem value="501+">أكثر من 500 موظف</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="foundingYear" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                سنة التأسيس
              </Label>
              <Input
                id="foundingYear"
                type="number"
                value={userSettings.foundingYear || ''}
                onChange={(e) => setUserSettings({ ...userSettings, foundingYear: e.target.value })}
                placeholder="مثال: 2010"
                dir="rtl"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyDescription" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              نبذة عن الشركة
            </Label>
            <Textarea
              id="companyDescription"
              value={userSettings.companyDescription || ''}
              onChange={(e) => setUserSettings({ ...userSettings, companyDescription: e.target.value })}
              placeholder="اكتب نبذة مختصرة عن شركتك أو مؤسستك ومجال عملها..."
              rows={4}
              dir="rtl"
            />
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <Label htmlFor="contactInfo" className="text-lg font-medium">معلومات الاتصال</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>أضف معلومات الاتصال الخاصة بشركتك لتسهيل التواصل معك من قبل المبادرات.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contactPhone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                رقم الهاتف
              </Label>
              <Input
                id="contactPhone"
                value={userSettings.contactPhone || ''}
                onChange={(e) => setUserSettings({ ...userSettings, contactPhone: e.target.value })}
                placeholder="أدخل رقم هاتف الشركة"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactEmail" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                البريد الإلكتروني للتواصل
              </Label>
              <Input
                id="contactEmail"
                type="email"
                value={userSettings.contactEmail || ''}
                onChange={(e) => setUserSettings({ ...userSettings, contactEmail: e.target.value })}
                placeholder="أدخل البريد الإلكتروني للتواصل"
                dir="rtl"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="website" className="flex items-center gap-2">
              <LinkIcon className="h-4 w-4" />
              الموقع الإلكتروني
            </Label>
            <Input
              id="website"
              value={userSettings.website || ''}
              onChange={(e) => setUserSettings({ ...userSettings, website: e.target.value })}
              placeholder="https://example.com"
              dir="rtl"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              العنوان
            </Label>
            <Textarea
              id="address"
              value={userSettings.address || ''}
              onChange={(e) => setUserSettings({ ...userSettings, address: e.target.value })}
              placeholder="أدخل عنوان الشركة أو المؤسسة"
              rows={2}
              dir="rtl"
            />
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <Label htmlFor="services" className="text-lg font-medium">الخدمات والموارد</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>حدد الخدمات والموارد التي يمكن لشركتك تقديمها للمبادرات المختلفة.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="space-y-2">
            <Label htmlFor="services">الخدمات المقدمة</Label>
            <Input
              id="services"
              value={userSettings.services?.join(', ') || ''}
              onChange={(e) => handleArrayInput(e, 'services')}
              placeholder="أدخل الخدمات التي تقدمها مفصولة بفواصل"
              dir="rtl"
            />
            <p className="text-xs text-gray-500">مثال: استشارات إدارية، تطوير برمجيات، تدريب</p>
          </div>

          <div className="space-y-4">
            <Label htmlFor="resources">الموارد المتاحة للمبادرات</Label>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {RESOURCE_TYPES.map(resource => (
                <Button
                  key={resource.id}
                  variant={userSettings.resources?.includes(resource.id) ? "default" : "outline"}
                  className={userSettings.resources?.includes(resource.id) ? "bg-[#0a8754]" : ""}
                  onClick={() => {
                    const currentResources = userSettings.resources || [];
                    if (currentResources.includes(resource.id)) {
                      setUserSettings({
                        ...userSettings,
                        resources: currentResources.filter((r: string) => r !== resource.id)
                      });
                    } else {
                      setUserSettings({
                        ...userSettings,
                        resources: [...currentResources, resource.id]
                      });
                    }
                  }}
                >
                  <span className="mr-1">{resource.icon}</span> {resource.name}
                </Button>
              ))}
            </div>
            
            <div className="space-y-2 mt-4">
              <Label htmlFor="customResources">موارد أخرى</Label>
              <Input
                id="customResources"
                value={userSettings.customResources?.join(', ') || ''}
                onChange={(e) => handleArrayInput(e, 'customResources')}
                placeholder="أدخل موارد أخرى مفصولة بفواصل"
                dir="rtl"
              />
              <p className="text-xs text-gray-500">أضف أي موارد أخرى غير مذكورة أعلاه</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="supportDescription">وصف الدعم الذي يمكن تقديمه</Label>
            <Textarea
              id="supportDescription"
              value={userSettings.supportDescription || ''}
              onChange={(e) => setUserSettings({ ...userSettings, supportDescription: e.target.value })}
              placeholder="اشرح كيف يمكن لشركتك أو مؤسستك دعم المبادرات المختلفة..."
              rows={4}
              dir="rtl"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

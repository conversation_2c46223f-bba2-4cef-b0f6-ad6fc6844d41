"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { Button } from "../ui/button"
import { Textarea } from "../ui/textarea"
import { Input } from "../ui/input"
import { Card, CardContent, CardHeader } from "../ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { Alert, AlertDescription } from "../ui/alert"
import { AlertCircle, Loader2, Plus } from "lucide-react"

interface Update {
  _id: string
  title: string
  content: string
  author: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  images: string[]
  createdAt: string
}

interface UpdatesListProps {
  initiativeId: string
  initialUpdates: Update[]
  isAuthor: boolean
}

export default function UpdatesList({ initiativeId, initialUpdates, isAuthor }: UpdatesListProps) {
  const router = useRouter()
  const [updates, setUpdates] = useState<Update[]>(initialUpdates || [])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [showForm, setShowForm] = useState(false)
  const [updateTitle, setUpdateTitle] = useState("")
  const [updateContent, setUpdateContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Get current user from localStorage or sessionStorage
  const getCurrentUser = () => {
    if (typeof window !== "undefined") {
      const userStr = localStorage.getItem("user") || sessionStorage.getItem("user")
      return userStr ? JSON.parse(userStr) : null
    }
    return null
  }

  const user = getCurrentUser()

  // Get token from localStorage or sessionStorage
  const getToken = () => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("accessToken") || sessionStorage.getItem("accessToken")
    }
    return null
  }

  useEffect(() => {
    if (initialUpdates && initialUpdates.length > 0) {
      setUpdates(initialUpdates)
    } else {
      fetchUpdates()
    }
  }, [initiativeId, initialUpdates])

  const fetchUpdates = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/updates/initiative/${initiativeId}`,
      )

      if (!response.ok) {
        throw new Error("Failed to fetch updates")
      }

      const data = await response.json()
      setUpdates(data.updates || [])
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitUpdate = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      router.push("/auth/login")
      return
    }

    if (!updateTitle.trim() || !updateContent.trim()) return

    setIsSubmitting(true)

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/updates`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          initiative: initiativeId,
          title: updateTitle,
          content: updateContent,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to post update")
      }

      // Add new update to the list
      setUpdates((prev) => [data.update, ...prev])
      setUpdateTitle("")
      setUpdateContent("")
      setShowForm(false)
    } catch (err: any) {
      setError(err.message || "Failed to post update")
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div dir="rtl">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold">التحديثات</h2>
        {isAuthor && (
          <Button onClick={() => setShowForm(!showForm)} className="bg-green-600 hover:bg-green-700">
            <Plus className="ml-2 h-4 w-4" />
            إضافة تحديث
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Update Form */}
      {showForm && (
        <Card className="mb-8">
          <CardHeader>
            <h3 className="text-lg font-semibold text-right">نشر تحديث جديد</h3>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmitUpdate} className="space-y-4 text-right">
              <div className="space-y-2">
                <label htmlFor="title" className="font-medium">
                  العنوان
                </label>
                <Input
                  id="title"
                  value={updateTitle}
                  onChange={(e) => setUpdateTitle(e.target.value)}
                  placeholder="عنوان التحديث"
                  required
                  className="text-right"
                  dir="rtl"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="content" className="font-medium">
                  المحتوى
                </label>
                <Textarea
                  id="content"
                  value={updateContent}
                  onChange={(e) => setUpdateContent(e.target.value)}
                  placeholder="شارك آخر تقدم في مبادرتك..."
                  className="min-h-[150px] text-right"
                  required
                  dir="rtl"
                />
              </div>

              <div className="flex justify-start gap-2">
                <Button
                  type="submit"
                  disabled={isSubmitting || !updateTitle.trim() || !updateContent.trim()}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري النشر...
                    </>
                  ) : (
                    "نشر التحديث"
                  )}
                </Button>
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  إلغاء
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Updates List */}
      <div className="space-y-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-green-600" />
            <span className="mr-2">جاري تحميل التحديثات...</span>
          </div>
        ) : updates.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>لا توجد تحديثات حتى الآن.</p>
            {isAuthor && (
              <Button onClick={() => setShowForm(true)} variant="link" className="text-green-600">
                نشر أول تحديث
              </Button>
            )}
          </div>
        ) : (
          updates.map((update) => (
            <Card key={update._id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4 flex-row-reverse">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={update.author.avatar} alt={update.author.name} />
                    <AvatarFallback>{update.author.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="text-right">
                    <div className="font-medium">{update.author.name}</div>
                    <div className="text-sm text-gray-500">{formatDate(update.createdAt)}</div>
                  </div>
                </div>

                <h3 className="text-xl font-bold mb-2 text-right">{update.title}</h3>
                <div className="prose max-w-none mb-4 text-right" dir="rtl">
                  {update.content.split("\n").map((paragraph, i) => (
                    <p key={i}>{paragraph}</p>
                  ))}
                </div>

                {update.images && update.images.length > 0 && (
                  <div className="grid grid-cols-2 gap-2 mt-4">
                    {update.images.map((image, index) => (
                      <div key={index} className="relative aspect-video rounded-md overflow-hidden">
                        <Image
                          src={image || "/placeholder.svg"}
                          alt={`صورة التحديث ${index + 1}`}
                          fill
                          style={{ objectFit: "cover" }}
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}


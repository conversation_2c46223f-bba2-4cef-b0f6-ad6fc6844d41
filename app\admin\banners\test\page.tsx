"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../../../../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../../../../components/ui/card"
import { api } from "../../../../lib/api"

export default function TestBannerAdmin() {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    // Just to test if the page loads
    setLoading(false)
  }, [])

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Test Banner Page</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading...</p>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : (
            <div>
              <p>This is a test page to check if the component loads correctly.</p>
              <Button className="mt-4">Test Button</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

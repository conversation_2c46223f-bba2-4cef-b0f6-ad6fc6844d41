import { Schema } from "mongoose"

const reportSchema = new Schema(
  {
    reporter: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: ["initiative", "comment", "post", "user"],
      required: true,
    },
    reason: {
      type: String,
      required: true,
      enum: ["inappropriate", "spam", "offensive", "misleading", "other"],
    },
    description: {
      type: String,
    },
    relatedInitiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
    },
    relatedComment: {
      type: Schema.Types.ObjectId,
      ref: "Comment",
    },
    relatedPost: {
      type: Schema.Types.ObjectId,
      ref: "Post",
    },
    relatedUser: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    status: {
      type: String,
      enum: ["pending", "reviewed", "resolved", "rejected"],
      default: "pending",
    },
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    resolution: {
      type: String,
    },
  },
  { timestamps: true },
)

export default reportSchema


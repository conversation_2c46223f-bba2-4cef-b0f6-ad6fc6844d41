require('dotenv').config();
const mongoose = require('mongoose');

// Connection URI
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz';

async function testConnection() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('MongoDB connection successful!');
    console.log(`Connected to database: ${mongoose.connection.name}`);
    console.log(`Host: ${mongoose.connection.host}`);
    console.log(`Port: ${mongoose.connection.port}`);
    
    // Create a simple test collection and document
    const testSchema = new mongoose.Schema({
      name: String,
      date: { type: Date, default: Date.now }
    });
    
    const Test = mongoose.model('Test', testSchema);
    
    // Check if we can create and retrieve data
    const testDoc = new Test({ name: 'Test Connection' });
    await testDoc.save();
    console.log('Created test document:', testDoc);
    
    const retrievedDoc = await Test.findById(testDoc._id);
    console.log('Retrieved test document:', retrievedDoc);
    
    // Clean up
    await Test.deleteOne({ _id: testDoc._id });
    console.log('Test document deleted');
    
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    return false;
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the test
testConnection()
  .then(success => {
    if (success) {
      console.log('Database connection test completed successfully!');
      process.exit(0);
    } else {
      console.error('Database connection test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error during test:', error);
    process.exit(1);
  });

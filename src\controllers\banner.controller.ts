import type { Request, Response, NextFunction } from "express";
import { Banner } from "../models/banner.model";
import { createError } from "../utils/error";

/**
 * Get all banners
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const getAllBanners = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get query parameters
    const { limit = 10, page = 1, sort = "order", active } = req.query;
    
    // Build query
    const query: any = {};
    
    // Filter by active status if provided
    if (active !== undefined) {
      query.isActive = active === 'true';
    }
    
    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit);
    
    // Get banners
    const banners = await Banner.find(query)
      .sort({ [String(sort)]: 1 })
      .skip(skip)
      .limit(Number(limit));
    
    // Get total count
    const total = await Banner.countDocuments(query);
    
    // Return success response
    res.status(200).json({
      success: true,
      count: banners.length,
      total,
      page: Number(page),
      pages: Math.ceil(total / Number(limit)),
      banners,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get active banners for homepage
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const getActiveBanners = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get active banners sorted by order
    const banners = await Banner.find({ isActive: true })
      .sort({ order: 1 });
    
    // Return success response
    res.status(200).json({
      success: true,
      count: banners.length,
      banners,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get banner by ID
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const getBannerById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    
    // Get banner
    const banner = await Banner.findById(id);
    
    // Check if banner exists
    if (!banner) {
      return next(createError(404, "Banner not found"));
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      banner,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new banner
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const createBanner = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { image, mainText, subText, mainTextColor, subTextColor, order, isActive } = req.body;
    
    // Create banner
    const banner = await Banner.create({
      image,
      mainText,
      subText,
      mainTextColor,
      subTextColor,
      order,
      isActive,
    });
    
    // Return success response
    res.status(201).json({
      success: true,
      banner,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update a banner
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const updateBanner = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { image, mainText, subText, mainTextColor, subTextColor, order, isActive } = req.body;
    
    // Find and update banner
    const banner = await Banner.findByIdAndUpdate(
      id,
      {
        image,
        mainText,
        subText,
        mainTextColor,
        subTextColor,
        order,
        isActive,
      },
      { new: true, runValidators: true }
    );
    
    // Check if banner exists
    if (!banner) {
      return next(createError(404, "Banner not found"));
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      banner,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a banner
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const deleteBanner = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    
    // Find and delete banner
    const banner = await Banner.findByIdAndDelete(id);
    
    // Check if banner exists
    if (!banner) {
      return next(createError(404, "Banner not found"));
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      message: "Banner deleted successfully",
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update banner order
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const updateBannerOrder = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { banners } = req.body;
    
    // Check if banners array is provided
    if (!banners || !Array.isArray(banners)) {
      return next(createError(400, "Banners array is required"));
    }
    
    // Update order for each banner
    const updatePromises = banners.map(async (item: { id: string; order: number }) => {
      return Banner.findByIdAndUpdate(item.id, { order: item.order });
    });
    
    // Wait for all updates to complete
    await Promise.all(updatePromises);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: "Banner order updated successfully",
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Toggle banner active status
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const toggleBannerStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    
    // Find banner
    const banner = await Banner.findById(id);
    
    // Check if banner exists
    if (!banner) {
      return next(createError(404, "Banner not found"));
    }
    
    // Toggle active status
    banner.isActive = !banner.isActive;
    
    // Save banner
    await banner.save();
    
    // Return success response
    res.status(200).json({
      success: true,
      banner,
    });
  } catch (error) {
    next(error);
  }
};

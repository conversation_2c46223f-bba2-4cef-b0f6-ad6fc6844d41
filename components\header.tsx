"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "./ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "./ui/sheet"
import { Menu, User, LogOut, Settings, PlusCircle, Search, BarChart, Bell, Info, ChevronDown, Building } from "lucide-react"
import { useAuth } from "./auth-provider"
import { api } from "../lib/api"

export default function Header() {
  const pathname = usePathname()
  const { user, logout, isAuthenticated } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isAboutMenuOpen, setIsAboutMenuOpen] = useState(false)
  const [isSupportersMenuOpen, setIsSupportersMenuOpen] = useState(false)

  // Check if user is admin
  const isAdmin = user && (
    user.role === 'admin' ||
    (typeof user.role === 'object' && user.role?.code === 'admin') ||
    (typeof user.role === 'string' && user.role.includes('admin')) ||
    user.username === 'admin'
  )

  const isActive = (path: string) => {
    return pathname === path
  }

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <img className="h-10 w-auto" src="/logo.png" alt="منصة المبادرات" />
            </Link>
            <nav className="hidden md:ml-6 md:flex md:space-x-8 md:space-x-reverse">
              <Link
                href="/"
                className={`px-3 py-2 text-sm font-medium ${
                  isActive("/") ? "text-green-600 border-b-2 border-green-600" : "text-gray-900 hover:text-green-600"
                }`}
              >
                الرئيسية
              </Link>
              <Link
                href="/initiatives"
                className={`px-3 py-2 text-sm font-medium ${
                  pathname.startsWith("/initiatives")
                    ? "text-green-600 border-b-2 border-green-600"
                    : "text-gray-900 hover:text-green-600"
                }`}
              >
                المبادرات
              </Link>
              <div className="relative">
                <button
                  className={`px-3 py-2 text-sm font-medium flex items-center gap-1 ${
                    pathname.startsWith("/companies") || pathname.startsWith("/civil-society")
                      ? "text-green-600 border-b-2 border-green-600"
                      : "text-gray-900 hover:text-green-600"
                  }`}
                  onClick={() => setIsSupportersMenuOpen(!isSupportersMenuOpen)}
                >
                  <span>الداعمون</span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {isSupportersMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    <Link
                      href="/companies"
                      className={`block px-4 py-2 text-sm ${
                        pathname.startsWith("/companies") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsSupportersMenuOpen(false)}
                    >
                      الشركات الداعمة
                    </Link>
                    <Link
                      href="/civil-society"
                      className={`block px-4 py-2 text-sm ${
                        pathname.startsWith("/civil-society") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsSupportersMenuOpen(false)}
                    >
                      ممثلو المجتمع المدني
                    </Link>
                  </div>
                )}
              </div>
              <Link
                href="/progress"
                className={`px-3 py-2 text-sm font-medium ${
                  isActive("/progress")
                    ? "text-green-600 border-b-2 border-green-600"
                    : "text-gray-900 hover:text-green-600"
                }`}
              >
                متابعة التقدم
              </Link>
              <Link
                href="/community"
                className={`px-3 py-2 text-sm font-medium ${
                  isActive("/community")
                    ? "text-green-600 border-b-2 border-green-600"
                    : "text-gray-900 hover:text-green-600"
                }`}
              >
                المجتمع
              </Link>
              <div className="relative">
                <button
                  className={`px-3 py-2 text-sm font-medium flex items-center gap-1 ${
                    isActive("/about") || isActive("/how-it-works") || isActive("/stats")
                      ? "text-green-600 border-b-2 border-green-600"
                      : "text-gray-900 hover:text-green-600"
                  }`}
                  onClick={() => setIsAboutMenuOpen(!isAboutMenuOpen)}
                >
                  <span>عن المنصة</span>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {isAboutMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    <Link
                      href="/about"
                      className={`block px-4 py-2 text-sm ${
                        isActive("/about") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsAboutMenuOpen(false)}
                    >
                      من نحن
                    </Link>
                    <Link
                      href="/how-it-works"
                      className={`block px-4 py-2 text-sm ${
                        isActive("/how-it-works") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsAboutMenuOpen(false)}
                    >
                      كيف يعمل
                    </Link>
                    <Link
                      href="/stats"
                      className={`block px-4 py-2 text-sm ${
                        isActive("/stats") ? "bg-green-50 text-green-600" : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setIsAboutMenuOpen(false)}
                    >
                      الإحصائيات
                    </Link>
                  </div>
                )}
              </div>
            </nav>
          </div>

          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <Link href="/search">
              <Button variant="ghost" size="icon">
                <Search className="h-5 w-5" />
              </Button>
            </Link>

            {isAuthenticated ? (
              <>
                <Link href="/initiatives/create">
                  <Button className="bg-green-600 hover:bg-green-700 flex items-center gap-1">
                    <PlusCircle className="h-4 w-4" />
                    إنشاء مبادرة
                  </Button>
                </Link>

                {/* Notification Bell */}
                <Link href="/notifications">
                  <Button variant="ghost" size="icon" className="relative">
                    <Bell className="h-5 w-5" />
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      3
                    </span>
                  </Button>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-2 h-8 px-2">
                      <span className="text-sm hidden lg:inline-block">{user?.name || user?.username}</span>
                      <Avatar className="h-8 w-8" square={user?.userType === 'company'}>
                        <AvatarImage
                          src={user?.avatar || ""}
                          alt={user?.name || ""}
                          preserveAspectRatio={true}
                          style={{
                            objectFit: "contain",
                            maxWidth: "100%",
                            maxHeight: "100%",
                            backgroundColor: user?.userType === 'company' ? "white" : "transparent"
                          }}
                        />
                        <AvatarFallback className="bg-gray-200" square={user?.userType === 'company'}>
                          {user?.userType === 'company' ?
                            <Building className="h-4 w-4 text-gray-600" /> :
                            <User className="h-4 w-4 text-gray-600" />
                          }
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/users/${user?.id}`} className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        <span>الملف الشخصي</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/initiatives" className="cursor-pointer">
                        <BarChart className="mr-2 h-4 w-4" />
                        <span>لوحة التحكم</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/profile/invitations" className="cursor-pointer">
                        <Bell className="mr-2 h-4 w-4" />
                        <span>الدعوات</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/users/settings" className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>الإعدادات</span>
                      </Link>
                    </DropdownMenuItem>
                    {isAdmin && (
                      <DropdownMenuItem asChild>
                        <Link href="/admin/dashboard" className="cursor-pointer">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>لوحة الإدارة</span>
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout} className="cursor-pointer">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>تسجيل الخروج</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <Link href="/auth/login">
                  <Button variant="outline">تسجيل الدخول</Button>
                </Link>
                <Link href="/auth/register">
                  <Button className="bg-green-600 hover:bg-green-700">إنشاء حساب</Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <div className="py-4">
                  <div className="px-2 space-y-1">
                    <Link
                      href="/"
                      className={`block px-3 py-2 rounded-md text-base font-medium ${
                        isActive("/") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      الرئيسية
                    </Link>
                    <Link
                      href="/initiatives"
                      className={`block px-3 py-2 rounded-md text-base font-medium ${
                        pathname.startsWith("/initiatives") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      المبادرات
                    </Link>
                    <div className="px-3 py-2 rounded-md text-base font-medium text-gray-900">
                      الداعمون
                    </div>
                    <div className="pr-4 space-y-1">
                      <Link
                        href="/companies"
                        className={`block px-3 py-2 rounded-md text-base font-medium ${
                          pathname.startsWith("/companies") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        الشركات الداعمة
                      </Link>
                      <Link
                        href="/civil-society"
                        className={`block px-3 py-2 rounded-md text-base font-medium ${
                          pathname.startsWith("/civil-society") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        ممثلو المجتمع المدني
                      </Link>
                    </div>
                    <Link
                      href="/progress"
                      className={`block px-3 py-2 rounded-md text-base font-medium ${
                        isActive("/progress") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      متابعة التقدم
                    </Link>
                    <Link
                      href="/community"
                      className={`block px-3 py-2 rounded-md text-base font-medium ${
                        isActive("/community") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      المجتمع
                    </Link>
                    <div className="px-3 py-2 rounded-md text-base font-medium text-gray-900">
                      عن المنصة
                    </div>
                    <div className="pr-4 space-y-1">
                      <Link
                        href="/about"
                        className={`block px-3 py-2 rounded-md text-base font-medium ${
                          isActive("/about") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        من نحن
                      </Link>
                      <Link
                        href="/how-it-works"
                        className={`block px-3 py-2 rounded-md text-base font-medium ${
                          isActive("/how-it-works") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        كيف يعمل
                      </Link>
                      <Link
                        href="/stats"
                        className={`block px-3 py-2 rounded-md text-base font-medium ${
                          isActive("/stats") ? "bg-green-50 text-green-600" : "text-gray-900 hover:bg-gray-50"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        الإحصائيات
                      </Link>
                    </div>
                  </div>
                  <div className="border-t border-gray-200 pt-4 pb-3">
                    {isAuthenticated ? (
                      <div className="px-2 space-y-1">
                        <div className="flex items-center px-3">
                          <div className="flex-shrink-0">
                            <Avatar className="h-10 w-10" square={user?.userType === 'company'}>
                              <AvatarImage
                                src={user?.avatar || ""}
                                alt={user?.name || ""}
                                preserveAspectRatio={true}
                                style={{
                                  objectFit: "contain",
                                  maxWidth: "100%",
                                  maxHeight: "100%",
                                  backgroundColor: user?.userType === 'company' ? "white" : "transparent"
                                }}
                              />
                              <AvatarFallback className="bg-gray-200" square={user?.userType === 'company'}>
                                {user?.userType === 'company' ?
                                  <Building className="h-4 w-4 text-gray-600" /> :
                                  <User className="h-4 w-4 text-gray-600" />
                                }
                              </AvatarFallback>
                            </Avatar>
                          </div>
                          <div className="mr-3">
                            <div className="text-base font-medium text-gray-800">{user?.name || user?.username}</div>
                            <div className="text-sm font-medium text-gray-500">{user?.email}</div>
                          </div>
                        </div>
                        <Link
                          href={`/users/${user?.id}`}
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          الملف الشخصي
                        </Link>
                        <Link
                          href="/dashboard/initiatives"
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          لوحة التحكم
                        </Link>
                        <Link
                          href="/profile/invitations"
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          الدعوات
                        </Link>
                        <Link
                          href="/users/settings"
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          الإعدادات
                        </Link>
                        {isAdmin && (
                          <Link
                            href="/admin/dashboard"
                            className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            لوحة الإدارة
                          </Link>
                        )}
                        <button
                          onClick={() => {
                            logout()
                            setIsMenuOpen(false)
                          }}
                          className="block w-full text-right px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                        >
                          تسجيل الخروج
                        </button>
                      </div>
                    ) : (
                      <div className="px-2 space-y-1">
                        <Link
                          href="/auth/login"
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          تسجيل الدخول
                        </Link>
                        <Link
                          href="/auth/register"
                          className="block px-3 py-2 rounded-md text-base font-medium text-gray-900 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          إنشاء حساب
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}

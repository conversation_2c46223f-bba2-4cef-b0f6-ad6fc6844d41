import express from "express"
import { authenticate, adminOnly } from "../middleware/auth"
import {
  getAdminStats,
  getPendingInitiatives,
  getAllInitiatives,
  getRecentUsers,
  getAllUsers,
  getPendingReports,
  getAllReports,
  approveInitiative,
  rejectInitiative,
  changeUserRole,
  changeUserType,
  blockUser,
  unblockUser,
  verifyUser,
  resolveReport,
  rejectReport,
  getReportDetails
} from "../controllers/admin.controller"

const router = express.Router()

// All routes require admin authentication
router.use(authenticate)
router.use(adminOnly)

// Dashboard and stats
router.get("/stats", getAdminStats)

// Initiatives management
router.get("/initiatives", getAllInitiatives)
router.get("/initiatives/pending", getPendingInitiatives)
router.patch("/initiatives/:id/approve", approveInitiative)
router.patch("/initiatives/:id/reject", rejectInitiative)

// Users management
router.get("/users", getAllUsers)
router.get("/users/recent", getRecentUsers)
router.patch("/users/:id/role", changeUserRole)
router.patch("/users/:id/userType", changeUserType)
router.patch("/users/:id/block", blockUser)
router.patch("/users/:id/unblock", unblockUser)
router.patch("/users/:id/verify", verifyUser)

// Reports management
router.get("/reports", getAllReports)
router.get("/reports/pending", getPendingReports)
router.get("/reports/:id", getReportDetails)
router.patch("/reports/:id/resolve", resolveReport)
router.patch("/reports/:id/reject", rejectReport)

export default router

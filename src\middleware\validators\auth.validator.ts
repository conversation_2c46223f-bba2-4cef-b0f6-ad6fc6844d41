import type { Request, Response, NextFunction } from "express"
import <PERSON><PERSON> from "joi"
import { createError } from "../../utils/error"

// Validate register request
export const validateRegister = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    username: Joi.string().min(3).max(30).pattern(/^[a-zA-Z0-9_-]+$/).required().messages({
      "string.base": "Username must be a string",
      "string.pattern.base": "Username must only contain letters, numbers, underscores, and hyphens",
      "string.min": "Username must be at least 3 characters long",
      "string.max": "Username cannot be longer than 30 characters",
      "any.required": "Username is required",
    }),
    email: Joi.string().email().required().messages({
      "string.base": "Email must be a string",
      "string.email": "Please enter a valid email address",
      "any.required": "Email is required",
    }),
    password: Joi.string()
      .min(8)
      .required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/)
      .messages({
        "string.base": "Password must be a string",
        "string.min": "Password must be at least 8 characters long",
        "string.pattern.base":
          "Password must contain at least one uppercase letter, one lowercase letter, and one number",
        "any.required": "Password is required",
      }),
    name: Joi.string().min(2).max(50).required().messages({
      "string.base": "Name must be a string",
      "string.min": "Name must be at least 2 characters long",
      "string.max": "Name cannot be longer than 50 characters",
      "any.required": "Name is required",
    }),
    userType: Joi.string().valid("volunteer", "proposer", "company", "civilSociety").messages({
      "string.base": "User type must be a string",
      "any.only": "User type must be one of: volunteer, proposer, company, civilSociety",
    }),
    // Champs spécifiques pour les acteurs de la société civile
    organizationName: Joi.string().when('userType', {
      is: 'civilSociety',
      then: Joi.string().required().messages({
        "any.required": "Organization name is required for civil society actors",
      }),
      otherwise: Joi.string().optional(),
    }),
    organizationType: Joi.string().valid("association", "club", "foundation", "organization").when('userType', {
      is: 'civilSociety',
      then: Joi.string().required().messages({
        "any.required": "Organization type is required for civil society actors",
      }),
      otherwise: Joi.string().optional(),
    }),
    activitySector: Joi.string().when('userType', {
      is: 'civilSociety',
      then: Joi.string().required().messages({
        "any.required": "Activity sector is required for civil society actors",
      }),
      otherwise: Joi.string().optional(),
    }),
    scope: Joi.string().valid("local", "regional", "national", "international").when('userType', {
      is: 'civilSociety',
      then: Joi.string().required().messages({
        "any.required": "Scope is required for civil society actors",
      }),
      otherwise: Joi.string().optional(),
    }),
    address: Joi.string().when('userType', {
      is: 'civilSociety',
      then: Joi.string().required().messages({
        "any.required": "Address is required for civil society actors",
      }),
      otherwise: Joi.string().optional(),
    }),
    approvalNumber: Joi.string().when('userType', {
      is: 'civilSociety',
      then: Joi.string().required().messages({
        "any.required": "Approval number is required for civil society actors",
      }),
      otherwise: Joi.string().optional(),
    }),
    // Champs spécifiques pour les entreprises
    companyName: Joi.string().when('userType', {
      is: 'company',
      then: Joi.string().required().messages({
        "any.required": "Company name is required for companies",
      }),
      otherwise: Joi.string().optional(),
    }),
    commerceRegisterNumber: Joi.string().when('userType', {
      is: 'company',
      then: Joi.string().required().messages({
        "any.required": "Commerce register number is required for companies",
      }),
      otherwise: Joi.string().optional(),
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

// Validate login request
export const validateLogin = (req: Request, res: Response, next: NextFunction) => {
  // Log the request body for debugging
  console.log('[Auth Validator] Login request body:', req.body)

  // Define schemas for different login methods
  const emailSchema = Joi.object({
    email: Joi.string().email().required().messages({
      "string.base": "Email must be a string",
      "string.email": "Please enter a valid email address",
      "any.required": "Email is required",
    }),
    password: Joi.string().required().messages({
      "string.base": "Password must be a string",
      "any.required": "Password is required",
    }),
  })

  const usernameSchema = Joi.object({
    username: Joi.string().min(3).max(30).required().messages({
      "string.base": "Username must be a string",
      "string.min": "Username must be at least 3 characters long",
      "string.max": "Username cannot be longer than 30 characters",
      "any.required": "Username is required",
    }),
    password: Joi.string().required().messages({
      "string.base": "Password must be a string",
      "any.required": "Password is required",
    }),
  })

  // Check if we have a username or email in the request
  if (req.body.username) {
    console.log('[Auth Validator] Validating with username schema')
    const { error } = usernameSchema.validate(req.body)
    if (error) {
      return next(createError(400, error.details[0].message))
    }
  } else if (req.body.email) {
    console.log('[Auth Validator] Validating with email schema')
    const { error } = emailSchema.validate(req.body)
    if (error) {
      return next(createError(400, error.details[0].message))
    }
  } else {
    console.log('[Auth Validator] No username or email provided')
    return next(createError(400, "Please provide either an email or a username"))
  }

  next()
}

// Validate reset password request
export const validateResetPassword = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    password: Joi.string()
      .min(8)
      .required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/)
      .messages({
        "string.base": "Password must be a string",
        "string.min": "Password must be at least 8 characters long",
        "string.pattern.base":
          "Password must contain at least one uppercase letter, one lowercase letter, and one number",
        "any.required": "Password is required",
      }),
    confirmPassword: Joi.string().valid(Joi.ref("password")).required().messages({
      "any.only": "Passwords do not match",
      "any.required": "Please confirm your password",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

import mongoose from 'mongoose'
import { Notification, User, Initiative } from '../models'

/**
 * Crée des notifications de test pour un utilisateur
 * @param userId ID de l'utilisateur pour lequel créer des notifications
 * @param count Nombre de notifications à créer
 */
export const createTestNotifications = async (userId: string, count: number = 5) => {
  try {
    // Vérifier si l'utilisateur existe
    const user = await User.findById(userId)
    if (!user) {
      throw new Error(`User with ID ${userId} not found`)
    }

    // Trouver quelques initiatives pour les références
    const initiatives = await Initiative.find().limit(3)
    
    // Types de notifications possibles
    const notificationTypes = [
      'initiative_invitation',
      'initiative_update',
      'comment',
      'support',
      'milestone_completed',
      'admin_message',
      'system'
    ]

    // Créer les notifications
    const notifications = []
    for (let i = 0; i < count; i++) {
      const type = notificationTypes[Math.floor(Math.random() * notificationTypes.length)]
      const initiative = initiatives.length > 0 ? initiatives[Math.floor(Math.random() * initiatives.length)]._id : null
      
      let title = ''
      let message = ''
      
      switch (type) {
        case 'initiative_invitation':
          title = 'دعوة للمشاركة في مبادرة'
          message = 'تمت دعوتك للمشاركة في مبادرة جديدة'
          break
        case 'initiative_update':
          title = 'تحديث المبادرة'
          message = 'تم تحديث مبادرة كنت تتابعها'
          break
        case 'comment':
          title = 'تعليق جديد'
          message = 'قام شخص ما بالتعليق على مبادرتك'
          break
        case 'support':
          title = 'دعم جديد'
          message = 'قام شخص ما بدعم مبادرتك'
          break
        case 'milestone_completed':
          title = 'اكتمال مرحلة'
          message = 'تم اكتمال مرحلة في مبادرة كنت تتابعها'
          break
        case 'admin_message':
          title = 'رسالة من الإدارة'
          message = 'لديك رسالة جديدة من إدارة المنصة'
          break
        case 'system':
          title = 'إشعار نظام'
          message = 'هناك تحديث جديد في النظام'
          break
      }
      
      const notification = new Notification({
        recipient: userId,
        type,
        title,
        message,
        read: Math.random() > 0.7, // 30% chance of being read
        initiative: initiative,
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)) // Random date within the last week
      })
      
      notifications.push(notification)
    }
    
    // Sauvegarder les notifications
    await Notification.insertMany(notifications)
    
    return {
      success: true,
      count: notifications.length,
      message: `Created ${notifications.length} test notifications for user ${userId}`
    }
  } catch (error) {
    console.error('Error creating test notifications:', error)
    throw error
  }
}

/**
 * Supprime toutes les notifications de test pour un utilisateur
 * @param userId ID de l'utilisateur pour lequel supprimer les notifications
 */
export const clearTestNotifications = async (userId: string) => {
  try {
    const result = await Notification.deleteMany({ recipient: userId })
    
    return {
      success: true,
      count: result.deletedCount,
      message: `Deleted ${result.deletedCount} notifications for user ${userId}`
    }
  } catch (error) {
    console.error('Error clearing test notifications:', error)
    throw error
  }
}

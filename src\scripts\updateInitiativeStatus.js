// Script to update initiative status from pending to active
const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const dbName = 'initiatives_dz';

async function updateInitiativeStatus() {
  const client = new MongoClient(uri);
  
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const initiatives = db.collection('initiatives');
    
    // Update all initiatives with status "pending" to "active"
    const result = await initiatives.updateMany(
      { status: 'pending' },
      { $set: { status: 'active' } }
    );
    
    console.log(`Updated ${result.modifiedCount} initiatives from "pending" to "active"`);
  } catch (error) {
    console.error('Error updating initiative status:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
updateInitiativeStatus();

"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "../../../components/ui/button"
import { ChevronLeft } from "lucide-react"
import ResourceStatsSection from "../../../components/stats/resource-stats-section"

export default function ResourceStatsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <Link href="/stats">
            <Button variant="outline" size="sm">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Statistics
            </Button>
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold">Resource Statistics</h1>
        <p className="text-gray-600 mt-1">
          Detailed statistics and analytics about resources across the platform
        </p>
      </div>
      
      <ResourceStatsSection />
    </div>
  )
}

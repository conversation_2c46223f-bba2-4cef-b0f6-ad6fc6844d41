import { Request, Response, NextFunction } from "express"
import <PERSON><PERSON> from "joi"
import { createError } from "../../utils/error"

// Validate voting option creation
export const validateVotingOption = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    initiative: Joi.string().required().messages({
      "string.empty": "Initiative ID is required",
      "any.required": "Initiative ID is required",
    }),
    title: Joi.string().min(3).max(100).required().messages({
      "string.empty": "Title is required",
      "string.min": "Title must be at least {#limit} characters long",
      "string.max": "Title cannot exceed {#limit} characters",
      "any.required": "Title is required",
    }),
    description: Joi.string().min(10).max(500).required().messages({
      "string.empty": "Description is required",
      "string.min": "Description must be at least {#limit} characters long",
      "string.max": "Description cannot exceed {#limit} characters",
      "any.required": "Description is required",
    }),
  })

  const { error } = schema.validate(req.body, { abortEarly: false })

  if (error) {
    const errorMessage = error.details.map((detail) => detail.message).join(", ")
    return next(createError(400, errorMessage))
  }

  next()
}

// Validate vote submission
export const validateVote = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    optionId: Joi.string().required().messages({
      "string.empty": "Option ID is required",
      "any.required": "Option ID is required",
    }),
  })

  const { error } = schema.validate(req.body, { abortEarly: false })

  if (error) {
    const errorMessage = error.details.map((detail) => detail.message).join(", ")
    return next(createError(400, errorMessage))
  }

  next()
}

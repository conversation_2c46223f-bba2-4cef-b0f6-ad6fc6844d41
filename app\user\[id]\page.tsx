"use client";

import React from "react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { api } from "@/lib/api";

type User = {
  id: string;
  name: string;
  email: string;
  userType: 'volunteer' | 'proposer' | 'company';
  skills?: string[];
  qualification?: string[];
  interest?: string[];
};

export default function UserProfilePage() {
  const params = useParams();
  const id = params.id as string;
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const data = await api.get(`/api/user/${id}`);
        if (data && data.success) {
          setUser(data.user || data.data);
        } else {
          setError('Failed to load user data');
        }
      } catch (err) {
        setError('Error fetching user data');
        console.error('Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [id]);

  if (loading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (error) {
    return <div className="p-1 text-center text-red-500">{error}</div>;
  }

  if (!user) {
    return <div className="p-8 text-center">User not found</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">User Profile</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Personal Information</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">Name</h3>
                <p>{user.name || 'Not available'}</p>
              </div>
              <div>
                <h3 className="font-medium">Email</h3>
                <p>{user.email || 'Not available'}</p>
              </div>
              <div>
                <h3 className="font-medium">User Type</h3>
                <p>{user.userType || 'Not available'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { Button } from "../ui/button"
import { Alert, AlertDescription } from "../ui/alert"
import {
  Loader2,
  AlertCircle,
  Package,
  DollarSign,
  Users,
  Wrench,
  HelpCircle,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  Ban,
  Search,
  Filter,
  ArrowDownToLine,
  ArrowUpFromLine
} from "lucide-react"
import { Skeleton } from "../ui/skeleton"
import { Input } from "../ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import Link from "next/link"
import { api } from "../../lib/api"
import InitiativeNameDisplay from "./initiative-name-display"

interface UserResourcesTabProps {
  userId: string
  isOwnProfile: boolean
}

interface Resource {
  _id: string
  type: "material" | "financial" | "human" | "service" | "other"
  name: string
  description: string
  quantity: number
  unit: string
  status: "requested" | "approved" | "rejected" | "delivered" | "canceled"
  requestedDate: string
  approvedDate?: string
  rejectedDate?: string
  deliveredDate?: string
  notes?: string
  initiative: {
    _id: string
    title: string
    shortDescription: string
    mainImage: string
    status: string
  }
}

export default function UserResourcesTab({ userId, isOwnProfile }: UserResourcesTabProps) {
  const [resources, setResources] = useState<Resource[]>([])
  const [resourceNeeds, setResourceNeeds] = useState<any[]>([])
  const [filteredResources, setFilteredResources] = useState<Resource[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Filters
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [resourceType, setResourceType] = useState("offered") // "offered" ou "requested"

  useEffect(() => {
    fetchUserResources()
  }, [userId])

  useEffect(() => {
    console.log("Mise à jour des filtres - Ressources:", resources.length, "Besoins:", resourceNeeds.length, "Type:", resourceType)
    applyFilters()
  }, [resources, resourceNeeds, resourceType, searchQuery, typeFilter, statusFilter])

  const fetchUserResources = async () => {
    setIsLoading(true)
    try {
      // Récupérer les ressources offertes par l'utilisateur
      const resourcesResponse = await api.get(`/api/resources/user/${userId}`, false)
      console.log("Ressources offertes:", resourcesResponse)

      // Vérifier la structure de la réponse
      const resourcesData = resourcesResponse.data?.resources || resourcesResponse.resources || []
      console.log("Structure des ressources offertes:", resourcesData.length, "ressources trouvées")

      // Récupérer les informations de l'utilisateur pour obtenir ses initiatives
      const userResponse = await api.get(`/api/users/${userId}`, false)
      console.log("Données utilisateur:", userResponse)

      // Vérifier la structure de la réponse
      if (userResponse.success && userResponse.data) {
        console.log("Structure de la réponse:", Object.keys(userResponse.data))
        if (userResponse.data.user) {
          console.log("Données utilisateur disponibles:", Object.keys(userResponse.data.user))
        }
      }

      if (resourcesResponse.success && userResponse.success) {
        // Extraire les ressources de la réponse
        const resourcesList = resourcesResponse.data?.resources || resourcesResponse.resources || []
        console.log("Nombre de ressources offertes:", resourcesList.length)
        setResources(resourcesList)

        // Récupérer les besoins de ressources pour les initiatives de l'utilisateur
        // Vérifier si userResponse.data.user existe et contient des initiatives
        let allResourceNeeds: any[] = []

        // Adapter à la structure réelle de la réponse API
        const userData = userResponse.data?.user || {}
        const userInitiatives = userData.initiatives || []

        console.log("Initiatives de l'utilisateur:", userInitiatives)

        if (Array.isArray(userInitiatives) && userInitiatives.length > 0) {
          // Utiliser les initiatives extraites de la structure correcte
          for (const initiativeId of userInitiatives) {
            const needsResponse = await api.get(`/api/resource-needs/initiative/${initiativeId}`, false)
            console.log(`Besoins pour l'initiative ${initiativeId}:`, needsResponse)
            if (needsResponse.success) {
              // Vérifier la structure de la réponse
              const resourceNeeds = needsResponse.data?.resourceNeeds || needsResponse.resourceNeeds || []
              console.log(`Structure des besoins pour l'initiative ${initiativeId}:`, resourceNeeds.length, "besoins trouvés")

              // Ajouter l'ID de l'initiative à chaque besoin pour pouvoir y accéder plus tard
              const needsWithInitiative = resourceNeeds.map((need: any) => ({
                ...need,
                initiativeId
              }))
              allResourceNeeds = [...allResourceNeeds, ...needsWithInitiative]
            }
          }
        } else {
          console.log("Aucune initiative trouvée pour cet utilisateur ou format de réponse incorrect:", userResponse)
        }

        console.log("Nombre de besoins de ressources trouvés:", allResourceNeeds.length)
        setResourceNeeds(allResourceNeeds)
      } else {
        setError("Failed to fetch user resources")
      }
    } catch (err: any) {
      console.error("Error fetching user resources:", err)
      setError(err.message || "An error occurred while fetching user resources")
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = () => {
    // Sélectionner la source de données en fonction du type de ressource
    let sourceData = resourceType === "offered" ? resources : resourceNeeds
    console.log("Application des filtres pour:", resourceType, "avec", sourceData.length, "éléments")
    console.log("Données sources:", sourceData)
    let filtered = [...sourceData]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()

      if (resourceType === "offered") {
        filtered = filtered.filter((resource: any) =>
          resource.name.toLowerCase().includes(query) ||
          resource.description.toLowerCase().includes(query) ||
          (resource.initiative?.title && resource.initiative.title.toLowerCase().includes(query)) ||
          (resource.notes && resource.notes.toLowerCase().includes(query))
        )
      } else {
        // Filtre pour les besoins de ressources
        filtered = filtered.filter((need: any) =>
          need.name.toLowerCase().includes(query) ||
          need.description.toLowerCase().includes(query)
        )
      }
    }

    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter((item: any) => item.type === typeFilter)
    }

    // Apply status filter
    if (statusFilter !== "all") {
      if (resourceType === "offered") {
        filtered = filtered.filter((resource: any) => resource.status === statusFilter)
      } else {
        // Mapper les statuts des besoins aux valeurs du filtre
        const statusMap: any = {
          "requested": "open",
          "approved": "in_progress",
          "delivered": "fulfilled"
        }
        filtered = filtered.filter((need: any) => need.status === statusMap[statusFilter] || need.status === statusFilter)
      }
    }

    console.log("Résultats filtrés:", filtered.length, "éléments")
    setFilteredResources(filtered as any)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "material":
        return <Package className="h-4 w-4" />
      case "financial":
        return <DollarSign className="h-4 w-4" />
      case "human":
        return <Users className="h-4 w-4" />
      case "service":
        return <Wrench className="h-4 w-4" />
      default:
        return <HelpCircle className="h-4 w-4" />
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case "material":
        return "مادي"
      case "financial":
        return "مالي"
      case "human":
        return "بشري"
      case "service":
        return "خدمة"
      default:
        return "أخرى"
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "requested":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مطلوب</Badge>
      case "approved":
        return <Badge className="bg-green-100 text-green-800 border-green-200">موافق عليه</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800 border-red-200">مرفوض</Badge>
      case "delivered":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">تم التسليم</Badge>
      case "canceled":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">ملغي</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getInitiativeStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border-green-200">نشط</Badge>
      case "completed":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مكتمل</Badge>
      case "pending":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">قيد الانتظار</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    // Utiliser ar-DZ (arabe algérien) qui utilise les chiffres latins
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">
          {resourceType === "offered" ? "الموارد المقدمة" : "الموارد المطلوبة"}
        </h2>

        <div className="flex gap-2">
          <Button
            variant={resourceType === "offered" ? "default" : "outline"}
            size="sm"
            onClick={() => setResourceType("offered")}
            className={resourceType === "offered" ? "bg-green-600 hover:bg-green-700" : ""}
          >
            <ArrowUpFromLine className="h-4 w-4 ml-2" />
            الموارد المقدمة
          </Button>
          <Button
            variant={resourceType === "requested" ? "default" : "outline"}
            size="sm"
            onClick={() => setResourceType("requested")}
            className={resourceType === "requested" ? "bg-green-600 hover:bg-green-700" : ""}
          >
            <ArrowDownToLine className="h-4 w-4 ml-2" />
            الموارد المطلوبة
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="البحث عن الموارد..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="نوع المورد" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأنواع</SelectItem>
                <SelectItem value="material">مادي</SelectItem>
                <SelectItem value="financial">مالي</SelectItem>
                <SelectItem value="human">بشري</SelectItem>
                <SelectItem value="service">خدمة</SelectItem>
                <SelectItem value="other">أخرى</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="requested">مطلوب</SelectItem>
                <SelectItem value="approved">قيد التنفيذ</SelectItem>
                <SelectItem value="delivered">تم التسليم</SelectItem>
                {resourceType === "offered" && (
                  <>
                    <SelectItem value="rejected">مرفوض</SelectItem>
                    <SelectItem value="canceled">ملغي</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Resources List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-green-600" />
          <span className="mr-2">جاري تحميل الموارد...</span>
        </div>
      ) : filteredResources.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            {resourceType === "offered" ? (
              <p className="mb-4">لم يتم تقديم موارد بعد.</p>
            ) : (
              <p className="mb-4">لم يتم طلب موارد بعد.</p>
            )}
            <Link href="/initiatives">
              <Button className="bg-green-600 hover:bg-green-700">تصفح المبادرات</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredResources.map((item: any) => (
            resourceType === "offered" ? (
              // Affichage des ressources offertes
              <Card key={item._id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-2 rounded-full bg-green-100">
                        {getTypeIcon(item.type)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{item.name}</CardTitle>
                        <div className="text-sm text-gray-500">{getTypeText(item.type)}</div>
                      </div>
                    </div>
                    {getStatusBadge(item.status)}
                  </div>
                </CardHeader>

                <CardContent className="pb-4">
                  <p className="text-sm text-gray-600 mb-3">{item.description}</p>

                  <div className="flex items-center gap-1 text-sm mb-2">
                    <span className="font-medium">الكمية:</span>
                    <span>{item.quantity} {item.unit}</span>
                  </div>

                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>تم التقديم في {formatDate(item.requestedDate)}</span>
                  </div>

                  {item.approvedDate && (
                    <div className="flex items-center text-sm text-green-600 mb-1">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      <span>تمت الموافقة في {formatDate(item.approvedDate)}</span>
                    </div>
                  )}

                  {item.rejectedDate && (
                    <div className="flex items-center text-sm text-red-600 mb-1">
                      <XCircle className="h-4 w-4 mr-1" />
                      <span>تم الرفض في {formatDate(item.rejectedDate)}</span>
                    </div>
                  )}

                  {item.deliveredDate && (
                    <div className="flex items-center text-sm text-purple-600 mb-1">
                      <Truck className="h-4 w-4 mr-1" />
                      <span>تم التسليم في {formatDate(item.deliveredDate)}</span>
                    </div>
                  )}

                  <div className="mt-4 p-3 bg-gray-50 rounded-md">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">المبادرة:</h4>
                      {getInitiativeStatusBadge(item.initiative.status)}
                    </div>
                    <p className="text-sm font-medium mb-1">{item.initiative.title}</p>
                    <p className="text-xs text-gray-600 line-clamp-2 mb-2">{item.initiative.shortDescription}</p>
                    <Link href={`/initiatives/${item.initiative._id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        عرض المبادرة
                      </Button>
                    </Link>
                  </div>

                  {isOwnProfile && item.status === "requested" && (
                    <div className="mt-4">
                      <Link href={`/initiatives/${item.initiative._id}/resources`}>
                        <Button variant="outline" size="sm" className="w-full text-red-600 border-red-200 hover:bg-red-50">
                          <Ban className="mr-1 h-4 w-4" />
                          إلغاء العرض
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              // Affichage des ressources demandées (besoins)
              <Card key={item._id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-2">
                      <div className="p-2 rounded-full bg-blue-100">
                        {getTypeIcon(item.type)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{item.name}</CardTitle>
                        <div className="text-sm text-gray-500">{getTypeText(item.type)}</div>
                      </div>
                    </div>
                    {item.status === "open" && (
                      <Badge className="bg-blue-100 text-blue-800 border-blue-200">مفتوح</Badge>
                    )}
                    {item.status === "in_progress" && (
                      <Badge className="bg-amber-100 text-amber-800 border-amber-200">قيد التنفيذ</Badge>
                    )}
                    {item.status === "fulfilled" && (
                      <Badge className="bg-green-100 text-green-800 border-green-200">مكتمل</Badge>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="pb-4">
                  <p className="text-sm text-gray-600 mb-3">{item.description}</p>

                  <div className="flex items-center gap-1 text-sm mb-2">
                    <span className="font-medium">الكمية المطلوبة:</span>
                    <span>{item.quantity} {item.unit}</span>
                  </div>

                  <div className="flex items-center gap-1 text-sm mb-2">
                    <span className="font-medium">الأولوية:</span>
                    <span>
                      {item.priority === 3 && "عالية"}
                      {item.priority === 2 && "متوسطة"}
                      {item.priority === 1 && "منخفضة"}
                    </span>
                  </div>

                  {item.createdAt && (
                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>تم الإنشاء في {formatDate(item.createdAt)}</span>
                    </div>
                  )}

                  {item.fulfilledDate && (
                    <div className="flex items-center text-sm text-green-600 mb-1">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      <span>تم الاكتمال في {formatDate(item.fulfilledDate)}</span>
                    </div>
                  )}

                  <div className="mt-4 p-3 bg-gray-50 rounded-md">
                    <div className="mb-2">
                      <h4 className="font-medium">المبادرة:</h4>
                    </div>

                    {/* Récupérer le nom de l'initiative à partir de l'API */}
                    <InitiativeNameDisplay initiativeId={item.initiativeId} />

                    <Link href={`/initiatives/${item.initiativeId}/resources`}>
                      <Button variant="outline" size="sm" className="w-full mt-2">
                        عرض صفحة الموارد
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )
          ))}
        </div>
      )}
    </div>
  )
}

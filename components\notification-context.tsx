"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { api } from '@/lib/api'
import { useAuth } from './auth-provider'

interface NotificationContextType {
  unreadCount: number
  setUnreadCount: React.Dispatch<React.SetStateAction<number>>
  refreshUnreadCount: () => Promise<void>
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  const [unreadCount, setUnreadCount] = useState(0)
  const { isAuthenticated, user } = useAuth()

  const refreshUnreadCount = async () => {
    if (!isAuthenticated || !user) {
      setUnreadCount(0)
      return
    }

    try {
      // Check if token exists in localStorage before making the request
      const token = localStorage.getItem('accessToken')
      if (!token) {
        // Use debug level logging instead of info level to reduce console noise
        if (process.env.NODE_ENV === 'development') {
          console.debug('No authentication token found, skipping notification check')
        }
        return
      }

      const response = await api.get('/api/notifications/unread-count', true)
      if (response && typeof response.count === 'number') {
        setUnreadCount(response.count)
      }
    } catch (error) {
      // Don't log the error if it's an authentication error (401)
      if (error instanceof Error && !error.message.includes('401')) {
        console.error('Error fetching unread notifications:', error)
      }
      // Silent fail - don't disrupt the user experience for notification errors
    }
  }

  // Fetch unread count when user logs in
  useEffect(() => {
    if (isAuthenticated && user) {
      // Add a small delay to ensure auth is fully initialized
      const timer = setTimeout(() => {
        refreshUnreadCount()
      }, 1000)

      // Set up interval to refresh count (every 60 seconds)
      const interval = setInterval(refreshUnreadCount, 60000)

      return () => {
        clearTimeout(timer)
        clearInterval(interval)
      }
    } else {
      // Reset count when user logs out
      setUnreadCount(0)
    }
  }, [isAuthenticated, user])

  return (
    <NotificationContext.Provider value={{ unreadCount, setUnreadCount, refreshUnreadCount }}>
      {children}
    </NotificationContext.Provider>
  )
}

export const useNotifications = () => {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

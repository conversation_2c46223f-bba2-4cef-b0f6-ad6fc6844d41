import { Schema } from "mongoose"

export interface IBadgeAward {
  badge: Schema.Types.ObjectId;
  user: Schema.Types.ObjectId;
  initiative: Schema.Types.ObjectId;
  awardedBy: Schema.Types.ObjectId;
  awardedAt: Date;
  reason: string;
}

const badgeAwardSchema = new Schema(
  {
    badge: {
      type: Schema.Types.ObjectId,
      ref: "Badge",
      required: true,
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    initiative: {
      type: Schema.Types.ObjectId,
      ref: "Initiative",
      required: true,
    },
    awardedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    awardedAt: {
      type: Date,
      default: Date.now,
    },
    reason: {
      type: String,
      required: true,
    },
  },
  { timestamps: true },
)

// Créer un index composé pour éviter les doublons
badgeAwardSchema.index({ badge: 1, user: 1, initiative: 1 }, { unique: true })

export default badgeAwardSchema

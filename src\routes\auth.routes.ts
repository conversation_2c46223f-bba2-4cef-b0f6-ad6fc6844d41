import express from "express"
import {
  register,
  login,
  refreshToken,
  logout,
  verifyEmail,
  forgotPassword,
  resetPassword,
} from "../controllers/auth.controller"
import { validateRegister, validateLogin, validateResetPassword } from "../middleware/validators/auth.validator"

const router = express.Router()

// Auth routes
router.post("/register", validateRegister, register)
router.post("/login", validateLogin, login)
router.post("/refresh-token", refreshToken)
router.post("/logout", logout)
router.get("/verify-email/:token", verifyEmail)
router.post("/forgot-password", forgotPassword)
router.post("/reset-password/:token", validateResetPassword, resetPassword)

export default router


"use client"

import { useState, useEffect } from "react"
import { api } from "../../lib/api"
import { Skeleton } from "../ui/skeleton"

interface InitiativeNameDisplayProps {
  initiativeId: string
}

export default function InitiativeNameDisplay({ initiativeId }: InitiativeNameDisplayProps) {
  const [initiative, setInitiative] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchInitiative = async () => {
      if (!initiativeId) {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const response = await api.get(`/api/initiatives/${initiativeId}`, false)

        if (response.success) {
          setInitiative(response.data?.initiative || response.initiative)
        } else {
          setError("فشل في تحميل بيانات المبادرة")
        }
      } catch (err: any) {
        console.error("Error fetching initiative:", err)
        setError(err.message || "حدث خطأ أثناء تحميل بيانات المبادرة")
      } finally {
        setIsLoading(false)
      }
    }

    fetchInitiative()
  }, [initiativeId])

  if (isLoading) {
    return <Skeleton className="h-5 w-full mb-2" />
  }

  if (error) {
    return <p className="text-sm text-red-500">{error}</p>
  }

  if (!initiative) {
    return <p className="text-sm text-gray-500">لا توجد معلومات متاحة</p>
  }

  return (
    <>
      <p className="text-sm font-medium mb-1">{initiative.title}</p>
      {initiative.shortDescription && (
        <p className="text-xs text-gray-600 line-clamp-2 mb-2">{initiative.shortDescription}</p>
      )}
    </>
  )
}

{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "skipLibCheck": true, "outDir": "dist", "rootDir": "src", "typeRoots": ["./node_modules/@types", "./src/types"], "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "isolatedModules": false, "noEmit": false, "declaration": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"], "ts-node": {"transpileOnly": true, "files": true}}
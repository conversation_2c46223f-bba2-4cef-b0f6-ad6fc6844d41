"use client"

import React from 'react'

export function LogoSVG({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const height = size === 'sm' ? 32 : size === 'md' ? 40 : 48
  const width = height * 1.5
  
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 120 80" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      style={{ minWidth: width, minHeight: height }}
    >
      <rect width="120" height="80" rx="8" fill="#0a8754" />
      <text 
        x="60" 
        y="50" 
        fontFamily="Arial, sans-serif" 
        fontSize="36" 
        fontWeight="bold" 
        fill="white" 
        textAnchor="middle" 
        dominantBaseline="middle"
      >
        ID
      </text>
    </svg>
  )
}

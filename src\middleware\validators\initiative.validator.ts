import type { Request, Response, NextFunction } from "express"
import <PERSON><PERSON> from "joi"
import { createError } from "../../utils/error"

// Validate initiative creation request
export const validateInitiative = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    title: Joi.string().min(10).max(100).required().messages({
      "string.base": "Title must be a string",
      "string.min": "Title must be at least 10 characters long",
      "string.max": "Title cannot be longer than 100 characters",
      "any.required": "Title is required",
    }),
    shortDescription: Joi.string().min(20).max(200).required().messages({
      "string.base": "Short description must be a string",
      "string.min": "Short description must be at least 20 characters long",
      "string.max": "Short description cannot be longer than 200 characters",
      "any.required": "Short description is required",
    }),
    fullDescription: Joi.string().min(100).required().messages({
      "string.base": "Full description must be a string",
      "string.min": "Full description must be at least 100 characters long",
      "any.required": "Full description is required",
    }),
    category: Joi.string().required().messages({
      "string.base": "Category must be a string",
      "any.required": "Category is required",
    }),
    location: Joi.string().required().messages({
      "string.base": "Location must be a string",
      "any.required": "Location is required",
    }),
    wilaya: Joi.string().allow("", null).messages({
      "string.base": "Wilaya must be a string",
    }),
    goal: Joi.number().integer().min(1).required().messages({
      "number.base": "Goal must be a number",
      "number.integer": "Goal must be an integer",
      "number.min": "Goal must be at least 1",
      "any.required": "Goal is required",
    }),
    mainImage: Joi.string().allow("", null).messages({
      "string.base": "Main image must be a string",
    }),
    images: Joi.array().items(Joi.string()).messages({
      "array.base": "Images must be an array",
    }),
    tags: Joi.array().items(Joi.string()).messages({
      "array.base": "Tags must be an array",
    }),
    budget: Joi.number().allow(null).messages({
      "number.base": "Budget must be a number",
    }),
    requiredVolunteers: Joi.number().integer().allow(null).messages({
      "number.base": "Required volunteers must be a number",
      "number.integer": "Required volunteers must be an integer",
    }),
    // Nouveaux champs structurants
    problem: Joi.string().allow("", null).messages({
      "string.base": "Problem must be a string",
    }),
    solution: Joi.string().allow("", null).messages({
      "string.base": "Solution must be a string",
    }),
    beneficiaries: Joi.string().allow("", null).messages({
      "string.base": "Beneficiaries must be a string",
    }),
    quantitativeObjectives: Joi.string().allow("", null).messages({
      "string.base": "Quantitative objectives must be a string",
    }),
    qualitativeObjectives: Joi.string().allow("", null).messages({
      "string.base": "Qualitative objectives must be a string",
    }),
    // Champs d'impact social
    socialImpacts: Joi.array().items(
      Joi.object({
        category: Joi.string().required(),
        impacts: Joi.array().items(Joi.string())
      })
    ).messages({
      "array.base": "Social impacts must be an array",
    }),
    selectedImpacts: Joi.array().items(
      Joi.object({
        _id: Joi.string().required(),
        name: Joi.string().required(),
        arabicName: Joi.string().required(),
        isActive: Joi.boolean().default(true),
        description: Joi.string().allow("", null),
        arabicDescription: Joi.string().allow("", null),
      })
    ).messages({
      "array.base": "Selected impacts must be an array",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}

// Validate initiative update request
export const validateInitiativeUpdate = (req: Request, res: Response, next: NextFunction) => {
  const schema = Joi.object({
    title: Joi.string().min(10).max(100).messages({
      "string.base": "Title must be a string",
      "string.min": "Title must be at least 10 characters long",
      "string.max": "Title cannot be longer than 100 characters",
    }),
    shortDescription: Joi.string().min(20).max(200).messages({
      "string.base": "Short description must be a string",
      "string.min": "Short description must be at least 20 characters long",
      "string.max": "Short description cannot be longer than 200 characters",
    }),
    fullDescription: Joi.string().min(100).messages({
      "string.base": "Full description must be a string",
      "string.min": "Full description must be at least 100 characters long",
    }),
    category: Joi.string().messages({
      "string.base": "Category must be a string",
    }),
    location: Joi.string().messages({
      "string.base": "Location must be a string",
    }),
    wilaya: Joi.string().allow("", null).messages({
      "string.base": "Wilaya must be a string",
    }),
    goal: Joi.number().integer().min(1).messages({
      "number.base": "Goal must be a number",
      "number.integer": "Goal must be an integer",
      "number.min": "Goal must be at least 1",
    }),
    mainImage: Joi.string().allow("", null).messages({
      "string.base": "Main image must be a string",
    }),
    images: Joi.array().items(Joi.string()).messages({
      "array.base": "Images must be an array",
    }),
    tags: Joi.array().items(Joi.string()).messages({
      "array.base": "Tags must be an array",
    }),
    budget: Joi.number().allow(null).messages({
      "number.base": "Budget must be a number",
    }),
    requiredVolunteers: Joi.number().integer().allow(null).messages({
      "number.base": "Required volunteers must be a number",
      "number.integer": "Required volunteers must be an integer",
    }),
    // Nouveaux champs structurants
    problem: Joi.string().allow("", null).messages({
      "string.base": "Problem must be a string",
    }),
    solution: Joi.string().allow("", null).messages({
      "string.base": "Solution must be a string",
    }),
    beneficiaries: Joi.string().allow("", null).messages({
      "string.base": "Beneficiaries must be a string",
    }),
    quantitativeObjectives: Joi.string().allow("", null).messages({
      "string.base": "Quantitative objectives must be a string",
    }),
    qualitativeObjectives: Joi.string().allow("", null).messages({
      "string.base": "Qualitative objectives must be a string",
    }),
    // Champs d'impact social
    socialImpacts: Joi.array().items(
      Joi.object({
        category: Joi.string().required(),
        impacts: Joi.array().items(Joi.string())
      })
    ).messages({
      "array.base": "Social impacts must be an array",
    }),
    selectedImpacts: Joi.array().items(
      Joi.object({
        _id: Joi.string().required(),
        name: Joi.string().required(),
        arabicName: Joi.string().required(),
        isActive: Joi.boolean().default(true),
        description: Joi.string().allow("", null),
        arabicDescription: Joi.string().allow("", null),
      })
    ).messages({
      "array.base": "Selected impacts must be an array",
    }),
    // Autres champs existants
    status: Joi.string().valid("draft", "pending", "active", "completed", "rejected").messages({
      "string.base": "Status must be a string",
      "any.only": "Status must be one of: draft, pending, active, completed, rejected",
    }),
    progress: Joi.number().min(0).max(100).messages({
      "number.base": "Progress must be a number",
      "number.min": "Progress must be at least 0",
      "number.max": "Progress cannot be more than 100",
    }),
    isVotingEnabled: Joi.boolean().messages({
      "boolean.base": "isVotingEnabled must be a boolean",
    }),
    votingEndDate: Joi.date().messages({
      "date.base": "Voting end date must be a valid date",
    }),
    startDate: Joi.date().messages({
      "date.base": "Start date must be a valid date",
    }),
    endDate: Joi.date().messages({
      "date.base": "End date must be a valid date",
    }),
    isPromoted: Joi.boolean().messages({
      "boolean.base": "isPromoted must be a boolean",
    }),
    isPublic: Joi.boolean().messages({
      "boolean.base": "isPublic must be a boolean",
    }),
  })

  const { error } = schema.validate(req.body)
  if (error) {
    return next(createError(400, error.details[0].message))
  }

  next()
}


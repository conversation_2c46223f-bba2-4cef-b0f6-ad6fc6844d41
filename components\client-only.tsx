'use client'

import { useEffect, useState, ReactNode, memo } from 'react'

interface ClientOnlyProps {
  children: ReactNode
}

/**
 * Component that only renders its children on the client side
 * This helps avoid hydration errors when server and client rendering differ
 */
function ClientOnly({ children }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    // Return a placeholder with similar structure to avoid layout shifts
    return <div style={{ visibility: 'hidden' }}></div>
  }

  return <>{children}</>
}

// Use memo to prevent unnecessary re-renders
export default memo(ClientOnly)

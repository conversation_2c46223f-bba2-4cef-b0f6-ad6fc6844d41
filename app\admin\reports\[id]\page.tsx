"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, CheckCircle2, XCircle, ArrowLeft } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { api } from "@/lib/api"
import { getCurrentUser } from "@/lib/auth"

interface Report {
  _id: string
  type: string
  reason: string
  description?: string
  reporter: {
    _id: string
    name: string
    username: string
    avatar: string
  }
  status: string
  createdAt: string
  relatedInitiative?: {
    _id: string
    title: string
    shortDescription: string
    author: {
      _id: string
      name: string
    }
  }
  relatedComment?: {
    _id: string
    content: string
    author: {
      _id: string
      name: string
    }
    initiative: {
      _id: string
      title: string
    }
  }
  relatedUser?: {
    _id: string
    name: string
    username: string
    avatar: string
    email: string
  }
  relatedPost?: {
    _id: string
    content: string
    author: {
      _id: string
      name: string
    }
  }
  resolution?: string
  reviewedBy?: {
    _id: string
    name: string
  }
  reviewedAt?: string
}

export default function ReportDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { id } = params

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [report, setReport] = useState<Report | null>(null)

  // Action dialogs
  const [showResolveDialog, setShowResolveDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [resolution, setResolution] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  // Get current user
  const user = getCurrentUser()

  useEffect(() => {
    // Check if user is admin
    if (!user || user.role !== "admin") {
      router.push("/")
      return
    }

    fetchReportDetails()
  }, [router, user, id])

  const fetchReportDetails = async () => {
    try {
      setIsLoading(true)

      const response = await api.get(`/api/admin/reports/${id}`)
      setReport(response.report || null)
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResolveReport = async () => {
    setIsProcessing(true)

    try {
      await api.put(`/api/admin/reports/${id}/resolve`, { resolution })

      // Update report status
      setReport((prev) => (prev ? { ...prev, status: "resolved", resolution } : null))

      setShowResolveDialog(false)
    } catch (err: any) {
      setError(err.message || "Failed to resolve report")
    } finally {
      setIsProcessing(false)
      setResolution("")
    }
  }

  const handleRejectReport = async () => {
    setIsProcessing(true)

    try {
      await api.put(`/api/admin/reports/${id}/reject`, {})

      // Update report status
      setReport((prev) => (prev ? { ...prev, status: "rejected" } : null))

      setShowRejectDialog(false)
    } catch (err: any) {
      setError(err.message || "Failed to reject report")
    } finally {
      setIsProcessing(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-yellow-600">قيد المراجعة</Badge>
      case "resolved":
        return <Badge className="bg-green-600">تم الحل</Badge>
      case "rejected":
        return <Badge className="bg-red-600">مرفوض</Badge>
      default:
        return <Badge className="bg-gray-600">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "initiative":
        return <Badge className="bg-blue-600">مبادرة</Badge>
      case "comment":
        return <Badge className="bg-purple-600">تعليق</Badge>
      case "user":
        return <Badge className="bg-orange-600">مستخدم</Badge>
      case "post":
        return <Badge className="bg-indigo-600">منشور</Badge>
      default:
        return <Badge className="bg-gray-600">{type}</Badge>
    }
  }

  const getReasonText = (reason: string) => {
    switch (reason) {
      case "inappropriate":
        return "محتوى غير لائق"
      case "spam":
        return "محتوى مزعج أو غير مرغوب فيه"
      case "offensive":
        return "محتوى مسيء"
      case "misleading":
        return "معلومات مضللة"
      case "other":
        return "سبب آخر"
      default:
        return reason
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading report details...</span>
      </div>
    )
  }

  if (!report) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "Report not found"}</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Link href="/admin/reports">
            <Button variant="outline" className="flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              العودة إلى البلاغات
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">تفاصيل البلاغ</h1>

        <div className="flex gap-2">
          <Link href="/admin/reports">
            <Button variant="outline" className="flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              العودة إلى البلاغات
            </Button>
          </Link>

          {report.status === "pending" && (
            <>
              <Button
                className="bg-green-600 hover:bg-green-700 flex items-center gap-1"
                onClick={() => setShowResolveDialog(true)}
              >
                <CheckCircle2 className="h-4 w-4" />
                حل البلاغ
              </Button>
              <Button
                variant="outline"
                className="text-red-600 border-red-600 hover:bg-red-50 flex items-center gap-1"
                onClick={() => setShowRejectDialog(true)}
              >
                <XCircle className="h-4 w-4" />
                رفض البلاغ
              </Button>
            </>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>معلومات البلاغ</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <div>
                    <span className="font-medium">نوع البلاغ:</span>
                    <div className="mt-1">{getTypeBadge(report.type)}</div>
                  </div>
                  <div>
                    <span className="font-medium">الحالة:</span>
                    <div className="mt-1">{getStatusBadge(report.status)}</div>
                  </div>
                  <div>
                    <span className="font-medium">تاريخ الإبلاغ:</span>
                    <div className="mt-1 text-gray-600">{formatDate(report.createdAt)}</div>
                  </div>
                </div>

                <Separator />

                <div>
                  <span className="font-medium">سبب البلاغ:</span>
                  <div className="mt-1 p-3 bg-gray-50 rounded-md">{getReasonText(report.reason)}</div>
                </div>

                {report.description && (
                  <div>
                    <span className="font-medium">وصف المشكلة:</span>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md whitespace-pre-line">{report.description}</div>
                  </div>
                )}

                {report.resolution && (
                  <div>
                    <span className="font-medium">الإجراء المتخذ:</span>
                    <div className="mt-1 p-3 bg-green-50 border border-green-200 rounded-md whitespace-pre-line">
                      {report.resolution}
                    </div>
                  </div>
                )}

                {report.reviewedBy && (
                  <div>
                    <span className="font-medium">تمت المراجعة بواسطة:</span>
                    <div className="mt-1 text-gray-600">
                      {report.reviewedBy.name} - {report.reviewedAt && formatDate(report.reviewedAt)}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>المحتوى المُبلغ عنه</CardTitle>
            </CardHeader>
            <CardContent>
              {report.type === "initiative" && report.relatedInitiative && (
                <div className="space-y-4">
                  <div>
                    <span className="font-medium">عنوان المبادرة:</span>
                    <div className="mt-1">
                      <Link
                        href={`/initiatives/${report.relatedInitiative._id}`}
                        className="text-green-600 hover:underline"
                      >
                        {report.relatedInitiative.title}
                      </Link>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">وصف المبادرة:</span>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md">{report.relatedInitiative.shortDescription}</div>
                  </div>

                  <div>
                    <span className="font-medium">كاتب المبادرة:</span>
                    <div className="mt-1">
                      <Link
                        href={`/users/${report.relatedInitiative.author._id}`}
                        className="text-green-600 hover:underline"
                      >
                        {report.relatedInitiative.author.name}
                      </Link>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Link href={`/initiatives/${report.relatedInitiative._id}`}>
                      <Button>عرض المبادرة</Button>
                    </Link>
                  </div>
                </div>
              )}

              {report.type === "comment" && report.relatedComment && (
                <div className="space-y-4">
                  <div>
                    <span className="font-medium">محتوى التعليق:</span>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md">{report.relatedComment.content}</div>
                  </div>

                  <div>
                    <span className="font-medium">كاتب التعليق:</span>
                    <div className="mt-1">
                      <Link
                        href={`/users/${report.relatedComment.author._id}`}
                        className="text-green-600 hover:underline"
                      >
                        {report.relatedComment.author.name}
                      </Link>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">المبادرة المتعلقة:</span>
                    <div className="mt-1">
                      <Link
                        href={`/initiatives/${report.relatedComment.initiative._id}`}
                        className="text-green-600 hover:underline"
                      >
                        {report.relatedComment.initiative.title}
                      </Link>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Link href={`/initiatives/${report.relatedComment.initiative._id}`}>
                      <Button>عرض المبادرة</Button>
                    </Link>
                  </div>
                </div>
              )}

              {report.type === "user" && report.relatedUser && (
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={report.relatedUser.avatar} alt={report.relatedUser.name} />
                      <AvatarFallback>{report.relatedUser.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{report.relatedUser.name}</div>
                      <div className="text-sm text-gray-500">@{report.relatedUser.username}</div>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">البريد الإلكتروني:</span>
                    <div className="mt-1">{report.relatedUser.email}</div>
                  </div>

                  <div className="flex justify-end">
                    <Link href={`/users/${report.relatedUser._id}`}>
                      <Button>عرض الملف الشخصي</Button>
                    </Link>
                  </div>
                </div>
              )}

              {report.type === "post" && report.relatedPost && (
                <div className="space-y-4">
                  <div>
                    <span className="font-medium">محتوى المنشور:</span>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md">{report.relatedPost.content}</div>
                  </div>

                  <div>
                    <span className="font-medium">كاتب المنشور:</span>
                    <div className="mt-1">
                      <Link href={`/users/${report.relatedPost.author._id}`} className="text-green-600 hover:underline">
                        {report.relatedPost.author.name}
                      </Link>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Link href={`/community`}>
                      <Button>عرض المجتمع</Button>
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>معلومات المُبلغ</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3 mb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={report.reporter.avatar} alt={report.reporter.name} />
                  <AvatarFallback>{report.reporter.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{report.reporter.name}</div>
                  <div className="text-sm text-gray-500">@{report.reporter.username}</div>
                </div>
              </div>

              <div className="flex justify-center mt-4">
                <Link href={`/users/${report.reporter._id}`}>
                  <Button variant="outline">عرض الملف الشخصي</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Resolve Report Dialog */}
      <Dialog open={showResolveDialog} onOpenChange={setShowResolveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حل البلاغ</DialogTitle>
            <DialogDescription>يرجى إدخال تفاصيل الإجراء المتخذ لحل هذا البلاغ.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={4}
              placeholder="اكتب تفاصيل الإجراء المتخذ هنا..."
              value={resolution}
              onChange={(e) => setResolution(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowResolveDialog(false)}>
              إلغاء
            </Button>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handleResolveReport}
              disabled={isProcessing || !resolution.trim()}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  حل البلاغ
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Report Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>رفض البلاغ</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في رفض هذا البلاغ؟ سيتم إخطار المُبلغ.</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleRejectReport} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-4 w-4" />
                  رفض البلاغ
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


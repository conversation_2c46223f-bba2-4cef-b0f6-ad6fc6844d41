import type { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models';
import { createError } from '../utils/error';

interface DecodedToken {
  id: string;
  role: string;
  permissions?: string[];
  iat: number;
  exp: number;
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export const authenticate = (req: Request, res: Response, next: NextFunction) => {
  // Get token from header or query param
  let token = req.header('Authorization')?.replace('Bearer ', '') || req.query.token as string;

  if (!token) {
    return res.status(401).json({
      success: false,
      error: {
        message: 'Authentication required',
        statusCode: 401
      }
    });
  }

  try {
    // Special handling for demo mode - REMOVE THIS IN PRODUCTION
    if (process.env.NODE_ENV === 'development' && token.includes('demo-token')) {
      req.user = {
        id: 'demo-user-123',
        role: 'admin',
        permissions: ['admin:access', 'admin:manage_users', 'admin:manage_content', 'admin:manage_roles']
      };
      // No debug logs in production code
      return next();
    }

    // Verify normal tokens
    const decoded = jwt.verify(token, JWT_SECRET) as DecodedToken;
    req.user = decoded;
    next();
  } catch (err) {
    // Log authentication errors (consider using a proper logger in production)
    return res.status(401).json({
      success: false,
      error: {
        message: 'Invalid or expired token',
        statusCode: 401
      }
    });
  }
};

export const adminOnly = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      return next(createError(401, "Unauthorized"));
    }

    // Get user from database with role information
    const user = await User.findById(req.user.id)
      .populate({
        path: "role",
        select: "code name",
      });

    if (!user) {
      return next(createError(404, "User not found"));
    }

    // Special case for user with username 'admin'
    if (user.username === 'admin') {
      console.log('[Auth] Admin access granted to user with username "admin"');
      return next();
    }

    // Check if user has admin role
    // Support both old string-based roles and new object-based roles
    const roleCode = typeof user.role === 'string'
      ? user.role
      : (user.role as any)?.code;

    if (roleCode !== 'admin') {
      return next(createError(403, "Admin access required"));
    }

    next();
  } catch (error) {
    next(error);
  }
};

import mongoose from "mongoose";
import bcrypt from "bcryptjs";
import { connectToDatabase } from "../db/connection";
import { User } from "../models";

/**
 * Script to create an admin user
 */
async function createAdminUser() {
  try {
    console.log("Connecting to database...");
    await connectToDatabase();

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ username: "admin" });
    if (existingAdmin) {
      console.log("Admin user already exists");
      return;
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash("Admin123!", salt);

    // Create admin user
    const adminUser = new User({
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      name: "Admin User",
      role: "admin",
      isVerified: true,
      avatar: "https://ui-avatars.com/api/?name=Admin+User&background=0D8ABC&color=fff",
      bio: "Administrator of the Initiatives DZ platform",
      location: "Algiers, Algeria",
      joinDate: new Date("2023-01-01")
    });

    // Save admin user
    await adminUser.save();

    console.log("Admin user created successfully!");
    console.log("Username: admin");
    console.log("Password: Admin123!");
    console.log("Email: <EMAIL>");
  } catch (error) {
    console.error("Error creating admin user:", error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log("Database connection closed");
  }
}

// Run the script
createAdminUser();

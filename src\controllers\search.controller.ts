import { Request, Response, NextFunction } from "express"
import { User, Initiative } from "../models"
import { createError } from "../utils/error"

/**
 * Search users
 * @route GET /api/users/search
 * @access Public
 */
export const searchUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { q } = req.query
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    if (!q) {
      return next(createError(400, "Search query is required"))
    }

    // Search users
    const query = {
      $or: [
        { name: { $regex: q as string, $options: "i" } },
        { username: { $regex: q as string, $options: "i" } },
        { email: { $regex: q as string, $options: "i" } },
      ],
    }

    const users = await User.find(query)
      .select("name username email avatar bio location userType skills joinDate isVerified")
      .sort({ joinDate: -1 })
      .skip(skip)
      .limit(limit)

    // Count total users
    const total = await User.countDocuments(query)

    res.status(200).json({
      success: true,
      users,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Search initiatives
 * @route GET /api/initiatives/search
 * @access Public
 */
export const searchInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { q } = req.query
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    if (!q) {
      return next(createError(400, "Search query is required"))
    }

    // Search initiatives
    const query = {
      $or: [
        { title: { $regex: q as string, $options: "i" } },
        { shortDescription: { $regex: q as string, $options: "i" } },
        { description: { $regex: q as string, $options: "i" } },
      ],
      isPublic: true,
      status: { $in: ["active", "completed"] },
    }

    const initiatives = await Initiative.find(query)
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)

    // Count total initiatives
    const total = await Initiative.countDocuments(query)

    res.status(200).json({
      success: true,
      initiatives,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    })
  } catch (error) {
    next(error)
  }
}

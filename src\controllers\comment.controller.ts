import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Comment, Initiative } from "../models"
import { createError } from "../utils/error"
import { createNotification } from "../utils/notification"
import { ActivityService } from "../services/activity.service"

// Get comments by initiative
export const getCommentsByInitiative = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiativeId } = req.params

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiativeId)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiativeExists = await Initiative.exists({ _id: initiativeId })
    if (!initiativeExists) {
      return next(createError(404, "Initiative not found"))
    }

    // Get comments that are not replies (parent comments)
    const comments = await Comment.find({
      initiative: initiativeId,
      isReply: false,
      isHidden: false,
    })
      .populate("author", "name username avatar")
      .populate({
        path: "replies",
        match: { isHidden: false },
        populate: { path: "author", select: "name username avatar" },
      })
      .sort("-createdAt")

    res.status(200).json({
      success: true,
      count: comments.length,
      comments,
    })
  } catch (error) {
    next(error)
  }
}

// Create comment
export const createComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { initiative, content } = req.body
    const userId = req.user.id

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiative)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if initiative exists
    const initiativeDoc = await Initiative.findById(initiative)
    if (!initiativeDoc) {
      return next(createError(404, "Initiative not found"))
    }

    // Create comment
    const comment = new Comment({
      initiative,
      author: userId,
      content,
      isReply: false,
    })

    await comment.save()

    // Increment comment count on initiative
    await Initiative.findByIdAndUpdate(initiative, { $inc: { commentCount: 1 } })

    // Populate author details
    await comment.populate("author", "name username avatar")

    // Create notification for initiative author if it's not the same user
    if (initiativeDoc.author.toString() !== userId) {
      await createNotification({
        recipient: initiativeDoc.author,
        sender: userId,
        type: "comment",
        content: `Someone commented on your initiative "${initiativeDoc.title}"`,
        relatedInitiative: initiative,
        relatedComment: comment._id,
        link: `/initiatives/${initiative}`,
      })
    }

    // Enregistrer l'activité
    await ActivityService.commentActivity(
      userId,
      "comment",
      initiative,
      initiativeDoc.title,
      comment._id.toString()
    )

    res.status(201).json({
      success: true,
      message: "Comment created successfully",
      comment,
    })
  } catch (error) {
    next(error)
  }
}

// Update comment
export const updateComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { content } = req.body
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await Comment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user is the author
    if (comment.author.toString() !== userId && req.user.role !== "admin" && req.user.role !== "moderator") {
      return next(createError(403, "You are not authorized to update this comment"))
    }

    // Update comment
    comment.content = content
    await comment.save()

    res.status(200).json({
      success: true,
      message: "Comment updated successfully",
      comment,
    })
  } catch (error) {
    next(error)
  }
}

// Delete comment
export const deleteComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await Comment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user is the author or admin/moderator
    if (comment.author.toString() !== userId && req.user.role !== "admin" && req.user.role !== "moderator") {
      return next(createError(403, "You are not authorized to delete this comment"))
    }

    // If it's a parent comment, delete all replies
    if (!comment.isReply) {
      await Comment.deleteMany({ parentComment: id })

      // Decrement comment count on initiative for parent and all replies
      const repliesCount = await Comment.countDocuments({ parentComment: id })
      await Initiative.findByIdAndUpdate(comment.initiative, {
        $inc: { commentCount: -(repliesCount + 1) },
      })
    } else {
      // Decrement comment count on initiative for just the reply
      await Initiative.findByIdAndUpdate(comment.initiative, {
        $inc: { commentCount: -1 },
      })
    }

    // Delete comment
    await Comment.findByIdAndDelete(id)

    res.status(200).json({
      success: true,
      message: "Comment deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Like comment
export const likeComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await Comment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user already liked the comment
    if (comment.likes.includes(userId)) {
      return next(createError(400, "You have already liked this comment"))
    }

    // Add user to likes array and increment like count
    comment.likes.push(userId)
    comment.likeCount += 1
    await comment.save()

    // Create notification for comment author if it's not the same user
    if (comment.author.toString() !== userId) {
      await createNotification({
        recipient: comment.author,
        sender: userId,
        type: "like",
        content: "Someone liked your comment",
        relatedInitiative: comment.initiative,
        relatedComment: comment._id,
        link: `/initiatives/${comment.initiative}`,
      })
    }

    res.status(200).json({
      success: true,
      message: "Comment liked successfully",
      likeCount: comment.likeCount,
    })
  } catch (error) {
    next(error)
  }
}

// Unlike comment
export const unlikeComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Find comment
    const comment = await Comment.findById(id)

    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user has liked the comment
    if (!comment.likes.includes(userId)) {
      return next(createError(400, "You have not liked this comment"))
    }

    // Remove user from likes array and decrement like count
    comment.likes = comment.likes.filter((id) => id.toString() !== userId)
    comment.likeCount -= 1
    await comment.save()

    res.status(200).json({
      success: true,
      message: "Comment unliked successfully",
      likeCount: comment.likeCount,
    })
  } catch (error) {
    next(error)
  }
}

// Reply to comment
export const replyToComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { content, initiative } = req.body
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Validate initiative ID
    if (!mongoose.Types.ObjectId.isValid(initiative)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Check if parent comment exists
    const parentComment = await Comment.findById(id)
    if (!parentComment) {
      return next(createError(404, "Parent comment not found"))
    }

    // Check if initiative exists
    const initiativeExists = await Initiative.exists({ _id: initiative })
    if (!initiativeExists) {
      return next(createError(404, "Initiative not found"))
    }

    // Create reply
    const reply = new Comment({
      initiative,
      author: userId,
      content,
      isReply: true,
      parentComment: id,
    })

    await reply.save()

    // Increment comment count on initiative
    await Initiative.findByIdAndUpdate(initiative, { $inc: { commentCount: 1 } })

    // Populate author details
    await reply.populate("author", "name username avatar")

    // Create notification for parent comment author if it's not the same user
    if (parentComment.author.toString() !== userId) {
      await createNotification({
        recipient: parentComment.author,
        sender: userId,
        type: "reply",
        content: "Someone replied to your comment",
        relatedInitiative: initiative,
        relatedComment: reply._id,
        link: `/initiatives/${initiative}`,
      })
    }

    // Enregistrer l'activité
    await ActivityService.commentActivity(
      userId,
      "reply",
      initiative,
      (await Initiative.findById(initiative)).title,
      reply._id.toString()
    )

    res.status(201).json({
      success: true,
      message: "Reply added successfully",
      comment: reply,
    })
  } catch (error) {
    next(error)
  }
}

// Report comment
export const reportComment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { reason, description } = req.body
    const userId = req.user.id

    // Validate comment ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid comment ID"))
    }

    // Validate reason
    const validReasons = ["inappropriate", "spam", "offensive", "misleading", "other"]
    if (!validReasons.includes(reason)) {
      return next(createError(400, "Invalid reason"))
    }

    // Check if comment exists
    const comment = await Comment.findById(id)
    if (!comment) {
      return next(createError(404, "Comment not found"))
    }

    // Check if user is reporting their own comment
    if (comment.author.toString() === userId) {
      return next(createError(400, "You cannot report your own comment"))
    }

    // Create report in database
    const report = new Report({
      reporter: userId,
      type: "comment",
      reason,
      description,
      relatedComment: id,
      relatedInitiative: comment.initiative,
      status: "pending",
    })

    await report.save()

    // Mark comment as reported
    comment.isReported = true
    await comment.save()

    res.status(200).json({
      success: true,
      message: "Comment reported successfully",
    })
  } catch (error) {
    next(error)
  }
}


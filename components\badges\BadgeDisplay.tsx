"use client"

import { useState } from "react"
import { Card, CardContent } from "../ui/card"
import { Award } from "lucide-react"
import { Badge } from "../ui/badge"

interface BadgeAward {
  _id: string
  badge: {
    _id: string
    name: string
    arabicName: string
    description: string
    arabicDescription: string
    icon: string
    color: string
    category: string
    level: number
  }
  user: string
  initiative: {
    _id: string
    title: string
  }
  awardedBy: {
    _id: string
    name: string
  }
  reason: string
  awardedAt: string
}

interface BadgeDisplayProps {
  badges: BadgeAward[]
  showTitle?: boolean
}

export default function BadgeDisplay({ badges, showTitle = true }: BadgeDisplayProps) {
  const [selectedBadge, setSelectedBadge] = useState<BadgeAward | null>(null)

  const getCategoryText = (category: string) => {
    if (!category) return "غير محدد";

    switch (category) {
      case "participation": return "المشاركة"
      case "achievement": return "الإنجاز"
      case "contribution": return "المساهمة"
      case "special": return "خاص"
      case "skill": return "المهارة"
      default: return category
    }
  }

  const getBadgeLevelText = (level: number | undefined) => {
    if (level === undefined || level === null) return "غير محدد";

    switch (level) {
      case 1: return "المستوى الأول"
      case 2: return "المستوى الثاني"
      case 3: return "المستوى الثالث"
      case 4: return "المستوى الرابع"
      case 5: return "المستوى الخامس"
      default: return `المستوى ${level}`
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ar-DZ", {
      year: "numeric",
      month: "short",
      day: "numeric"
    })
  }

  if (badges.length === 0) {
    return (
      <div className="text-center py-6">
        <Award className="h-12 w-12 mx-auto text-gray-300 mb-2" />
        <p className="text-gray-500">لا توجد شارات بعد</p>
      </div>
    )
  }

  return (
    <div>
      {showTitle && <h3 className="text-lg font-semibold mb-4">الشارات</h3>}

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        {badges.map((badgeAward) => (
          <div
            key={badgeAward._id}
            className="cursor-pointer"
            onClick={() => setSelectedBadge(badgeAward)}
          >
            <div
              className="flex flex-col items-center justify-center p-3 rounded-lg border transition-all hover:shadow-md"
              style={{
                backgroundColor: `${badgeAward.badge?.color || "#888888"}10`,
                borderColor: `${badgeAward.badge?.color || "#888888"}30`
              }}
            >
              <div
                className="w-14 h-14 rounded-full flex items-center justify-center mb-2"
                style={{
                  backgroundColor: `${badgeAward.badge?.color || "#888888"}20`,
                  border: `2px solid ${badgeAward.badge?.color || "#888888"}`
                }}
              >
                <Award style={{ color: badgeAward.badge?.color || "#888888" }} className="h-7 w-7" />
              </div>
              <h4 className="text-sm font-medium text-center">{badgeAward.badge?.arabicName || badgeAward.badge?.name || "شارة"}</h4>
              <div className="flex flex-wrap justify-center gap-1 mt-1">
                <Badge variant="outline" className="text-xs">
                  {getCategoryText(badgeAward.badge?.category || "")}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {getBadgeLevelText(badgeAward.badge?.level)}
                </Badge>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedBadge && (
        <Card className="mt-6">
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              <div
                className="w-16 h-16 rounded-full flex-shrink-0 flex items-center justify-center"
                style={{
                  backgroundColor: `${selectedBadge.badge?.color || "#888888"}20`,
                  border: `2px solid ${selectedBadge.badge?.color || "#888888"}`
                }}
              >
                <Award style={{ color: selectedBadge.badge?.color || "#888888" }} className="h-8 w-8" />
              </div>

              <div className="flex-1">
                <h3 className="text-xl font-semibold">{selectedBadge.badge?.arabicName || selectedBadge.badge?.name || "شارة"}</h3>
                <div className="flex flex-wrap gap-2 my-2">
                  <Badge variant="outline">
                    {getCategoryText(selectedBadge.badge?.category || "")}
                  </Badge>
                  <Badge variant="outline">
                    {getBadgeLevelText(selectedBadge.badge?.level)}
                  </Badge>
                  <Badge variant="outline">
                    منحت في {formatDate(selectedBadge.awardedAt)}
                  </Badge>
                </div>

                <p className="text-gray-700 mb-3">{selectedBadge.badge?.arabicDescription || selectedBadge.badge?.description || ""}</p>

                {selectedBadge.reason && (
                  <div className="bg-gray-50 p-3 rounded-md mb-3">
                    <h4 className="font-medium mb-1">سبب المنح:</h4>
                    <p className="text-gray-600">{selectedBadge.reason}</p>
                  </div>
                )}

                <div className="text-sm text-gray-500">
                  <p>منحت بواسطة: {selectedBadge.awardedBy.name}</p>
                  <p>في مبادرة: {selectedBadge.initiative.title}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

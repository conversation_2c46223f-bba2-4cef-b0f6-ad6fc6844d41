"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, Save, ArrowLeft } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/components/auth-provider"
import { api } from "@/lib/api"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

export default function UserSettingsPage() {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // Profile settings
  const [profileData, setProfileData] = useState({
    name: "",
    bio: "",
    location: "",
    avatar: "",
    facebook: "",
    twitter: "",
    linkedin: "",
    instagram: "",
  })

  // Account settings
  const [accountData, setAccountData] = useState({
    email: "",
    username: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    initiativeUpdates: true,
    commentReplies: true,
    supportNotifications: true,
    marketingEmails: false,
  })

  // Use the auth context
  const { user, isAuthenticated, refreshUserData, logout } = useAuth()

  // Helper function to safely access user properties
  const getUserProperty = (user: any, path: string, defaultValue: any = "") => {
    if (!user) return defaultValue

    const parts = path.split('.')
    let value = user

    for (const part of parts) {
      if (value === null || value === undefined) return defaultValue
      value = value[part]
    }

    return value !== null && value !== undefined ? value : defaultValue
  }

  // Initialize form data from user context - NO API CALLS
  useEffect(() => {
    // Redirect if not logged in
    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    // Set loading state
    setIsLoading(true)

    // Use the user data from auth context directly - NO API CALLS
    try {
      // Set profile data
      setProfileData({
        name: getUserProperty(user, 'name'),
        bio: getUserProperty(user, 'bio'),
        location: getUserProperty(user, 'location'),
        avatar: getUserProperty(user, 'avatar'),
        facebook: getUserProperty(user, 'socialLinks.facebook'),
        twitter: getUserProperty(user, 'socialLinks.twitter'),
        linkedin: getUserProperty(user, 'socialLinks.linkedin'),
        instagram: getUserProperty(user, 'socialLinks.instagram'),
      })

      // Set account data
      setAccountData((prev) => ({
        ...prev,
        email: getUserProperty(user, 'email'),
        username: getUserProperty(user, 'username'),
      }))

      // Set notification settings
      setNotificationSettings({
        emailNotifications: getUserProperty(user, 'settings.emailNotifications', true) !== false,
        initiativeUpdates: getUserProperty(user, 'settings.initiativeUpdates', true) !== false,
        commentReplies: getUserProperty(user, 'settings.commentReplies', true) !== false,
        supportNotifications: getUserProperty(user, 'settings.supportNotifications', true) !== false,
        marketingEmails: getUserProperty(user, 'settings.marketingEmails', false) === true,
      })

      // Clear any previous errors
      setError("")
    } catch (err: any) {
      console.error('Error initializing form data:', err)
      setError("Failed to initialize form data. Please refresh the page.")
    } finally {
      setIsLoading(false)
    }

    // This effect should only run once when the component mounts
    // and when the user data changes
  }, [user, isAuthenticated, router])

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setProfileData((prev) => ({ ...prev, [name]: value }))
  }

  const handleAccountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setAccountData((prev) => ({ ...prev, [name]: value }))
  }

  const handleNotificationChange = (name: string, checked: boolean) => {
    setNotificationSettings((prev) => ({ ...prev, [name]: checked }))
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    setIsSaving(true)
    setError("")
    setSuccess("")

    try {
      // Use the API utility for consistent handling
      await api.put('/api/users/profile', {
        name: profileData.name,
        bio: profileData.bio,
        location: profileData.location,
        avatar: profileData.avatar,
        socialLinks: {
          facebook: profileData.facebook,
          twitter: profileData.twitter,
          linkedin: profileData.linkedin,
          instagram: profileData.instagram,
        },
      }, true)

      // Update the user in the auth context
      await refreshUserData()

      setSuccess("Profile updated successfully")
    } catch (err: any) {
      setError(err.message || "Failed to update profile")
    } finally {
      setIsSaving(false)
    }
  }

  const handleUpdateAccount = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    // Validate passwords if changing
    if (accountData.newPassword) {
      if (accountData.newPassword !== accountData.confirmPassword) {
        setError("New passwords do not match")
        return
      }

      if (!accountData.currentPassword) {
        setError("Current password is required to set a new password")
        return
      }
    }

    setIsSaving(true)
    setError("")
    setSuccess("")

    try {
      // Use the API utility for consistent handling
      await api.put('/api/users/account', {
        email: accountData.email,
        username: accountData.username,
        currentPassword: accountData.currentPassword || undefined,
        newPassword: accountData.newPassword || undefined,
      }, true)

      // Update the user in the auth context
      await refreshUserData()

      setSuccess("Account updated successfully")

      // Clear password fields
      setAccountData((prev) => ({
        ...prev,
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      }))

      // The user data is already updated by refreshUserData
    } catch (err: any) {
      setError(err.message || "Failed to update account")
    } finally {
      setIsSaving(false)
    }
  }

  const handleUpdateNotifications = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    setIsSaving(true)
    setError("")
    setSuccess("")

    try {
      // Use the API utility for consistent handling
      await api.put('/api/users/settings', {
        settings: notificationSettings,
      }, true)

      // Refresh user data to update the cache
      await refreshUserData()

      setSuccess("Notification settings updated successfully")
    } catch (err: any) {
      setError(err.message || "Failed to update notification settings")
    } finally {
      setIsSaving(false)
    }
  }

  const handleDeleteAccount = async () => {
    if (!isAuthenticated || !user) {
      router.push("/auth/login")
      return
    }

    setIsSaving(true)

    try {
      // Use the API utility for consistent handling
      await api.delete('/api/users/account', true)

      // Use the logout function from auth context to properly clean up
      // This will clear user data and redirect to home page
      logout()
    } catch (err: any) {
      setError(err.message || "Failed to delete account")
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading settings...</span>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href={`/users/${user?.id}`} className="flex items-center text-green-600 hover:text-green-800">
          <ArrowLeft className="h-4 w-4 ml-1" />
          العودة إلى الملف الشخصي
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-8 text-right">إعدادات الحساب</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <AlertCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="profile" className="mb-8" dir="rtl">
        <TabsList className="mb-6">
          <TabsTrigger value="profile">الملف الشخصي</TabsTrigger>
          <TabsTrigger value="account">الحساب</TabsTrigger>
          <TabsTrigger value="notifications">الإشعارات</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="text-right">معلومات الملف الشخصي</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdateProfile} className="space-y-6">
                <div className="space-y-4">
                  <div className="text-right">
                    <Label htmlFor="name">الاسم</Label>
                    <Input
                      id="name"
                      name="name"
                      value={profileData.name}
                      onChange={handleProfileChange}
                      placeholder="اسمك الكامل"
                      required
                      dir="rtl"
                    />
                  </div>

                  <div className="text-right">
                    <Label htmlFor="bio">نبذة عنك</Label>
                    <Textarea
                      id="bio"
                      name="bio"
                      value={profileData.bio}
                      onChange={handleProfileChange}
                      placeholder="أخبرنا عن نفسك"
                      className="min-h-[100px]"
                      dir="rtl"
                    />
                  </div>

                  <div className="text-right">
                    <Label htmlFor="location">الموقع</Label>
                    <Input
                      id="location"
                      name="location"
                      value={profileData.location}
                      onChange={handleProfileChange}
                      placeholder="المدينة، البلد"
                      dir="rtl"
                    />
                  </div>

                  <div className="text-right">
                    <Label htmlFor="avatar">رابط الصورة الشخصية</Label>
                    <Input
                      id="avatar"
                      name="avatar"
                      value={profileData.avatar}
                      onChange={handleProfileChange}
                      placeholder="رابط صورتك الشخصية"
                      dir="rtl"
                    />
                  </div>

                  <div className="pt-4">
                    <h3 className="text-lg font-medium mb-4 text-right">روابط التواصل الاجتماعي</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="text-right">
                        <Label htmlFor="facebook">فيسبوك</Label>
                        <Input
                          id="facebook"
                          name="facebook"
                          value={profileData.facebook}
                          onChange={handleProfileChange}
                          placeholder="رابط حسابك على فيسبوك"
                          dir="rtl"
                        />
                      </div>

                      <div className="text-right">
                        <Label htmlFor="twitter">تويتر</Label>
                        <Input
                          id="twitter"
                          name="twitter"
                          value={profileData.twitter}
                          onChange={handleProfileChange}
                          placeholder="رابط حسابك على تويتر"
                          dir="rtl"
                        />
                      </div>

                      <div className="text-right">
                        <Label htmlFor="linkedin">لينكد إن</Label>
                        <Input
                          id="linkedin"
                          name="linkedin"
                          value={profileData.linkedin}
                          onChange={handleProfileChange}
                          placeholder="رابط حسابك على لينكد إن"
                          dir="rtl"
                        />
                      </div>

                      <div className="text-right">
                        <Label htmlFor="instagram">انستغرام</Label>
                        <Input
                          id="instagram"
                          name="instagram"
                          value={profileData.instagram}
                          onChange={handleProfileChange}
                          placeholder="رابط حسابك على انستغرام"
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" className="bg-green-600 hover:bg-green-700" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="ml-2 h-4 w-4" />
                        حفظ الملف الشخصي
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle className="text-right">معلومات الحساب</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdateAccount} className="space-y-6">
                <div className="space-y-4">
                  <div className="text-right">
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={accountData.email}
                      onChange={handleAccountChange}
                      placeholder="عنوان بريدك الإلكتروني"
                      required
                      dir="rtl"
                    />
                  </div>

                  <div className="text-right">
                    <Label htmlFor="username">اسم المستخدم</Label>
                    <Input
                      id="username"
                      name="username"
                      value={accountData.username}
                      onChange={handleAccountChange}
                      placeholder="اسم المستخدم الخاص بك"
                      required
                      dir="rtl"
                    />
                  </div>

                  <div className="pt-4">
                    <h3 className="text-lg font-medium mb-4 text-right">تغيير كلمة المرور</h3>
                    <div className="space-y-4">
                      <div className="text-right">
                        <Label htmlFor="currentPassword">كلمة المرور الحالية</Label>
                        <Input
                          id="currentPassword"
                          name="currentPassword"
                          type="password"
                          value={accountData.currentPassword}
                          onChange={handleAccountChange}
                          placeholder="أدخل كلمة المرور الحالية"
                          dir="rtl"
                        />
                      </div>

                      <div className="text-right">
                        <Label htmlFor="newPassword">كلمة المرور الجديدة</Label>
                        <Input
                          id="newPassword"
                          name="newPassword"
                          type="password"
                          value={accountData.newPassword}
                          onChange={handleAccountChange}
                          placeholder="أدخل كلمة المرور الجديدة"
                          dir="rtl"
                        />
                      </div>

                      <div className="text-right">
                        <Label htmlFor="confirmPassword">تأكيد كلمة المرور الجديدة</Label>
                        <Input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          value={accountData.confirmPassword}
                          onChange={handleAccountChange}
                          placeholder="تأكيد كلمة المرور الجديدة"
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" className="bg-green-600 hover:bg-green-700" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="ml-2 h-4 w-4" />
                        حفظ الحساب
                      </>
                    )}
                  </Button>
                </div>
              </form>

              <div className="mt-12 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-medium text-red-600 mb-4 text-right">منطقة الخطر</h3>
                <p className="text-gray-600 mb-4 text-right">
                  بمجرد حذف حسابك، لا يمكن التراجع عن ذلك. يرجى التأكد من رغبتك في الحذف.
                </p>
                <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <DialogTrigger asChild>
                    <Button variant="destructive">حذف الحساب</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="text-right">هل أنت متأكد تماماً؟</DialogTitle>
                      <DialogDescription className="text-right">
                        لا يمكن التراجع عن هذا الإجراء. سيؤدي هذا إلى حذف حسابك نهائياً وإزالة بياناتك
                        من خوادمنا.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                        إلغاء
                      </Button>
                      <Button variant="destructive" onClick={handleDeleteAccount} disabled={isSaving}>
                        {isSaving ? (
                          <>
                            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                            جاري الحذف...
                          </>
                        ) : (
                          "حذف الحساب"
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="text-right">إعدادات الإشعارات</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdateNotifications} className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5 text-right">
                      <Label htmlFor="emailNotifications">إشعارات البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-500">استلام الإشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={notificationSettings.emailNotifications}
                      onCheckedChange={(checked) => handleNotificationChange("emailNotifications", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5 text-right">
                      <Label htmlFor="initiativeUpdates">تحديثات المبادرات</Label>
                      <p className="text-sm text-gray-500">الحصول على إشعارات حول تحديثات المبادرات التي تدعمها</p>
                    </div>
                    <Switch
                      id="initiativeUpdates"
                      checked={notificationSettings.initiativeUpdates}
                      onCheckedChange={(checked) => handleNotificationChange("initiativeUpdates", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5 text-right">
                      <Label htmlFor="commentReplies">الردود على التعليقات</Label>
                      <p className="text-sm text-gray-500">الحصول على إشعارات عندما يرد شخص ما على تعليقاتك</p>
                    </div>
                    <Switch
                      id="commentReplies"
                      checked={notificationSettings.commentReplies}
                      onCheckedChange={(checked) => handleNotificationChange("commentReplies", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5 text-right">
                      <Label htmlFor="supportNotifications">إشعارات الدعم</Label>
                      <p className="text-sm text-gray-500">الحصول على إشعارات عندما يدعم شخص ما مبادرتك</p>
                    </div>
                    <Switch
                      id="supportNotifications"
                      checked={notificationSettings.supportNotifications}
                      onCheckedChange={(checked) => handleNotificationChange("supportNotifications", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5 text-right">
                      <Label htmlFor="marketingEmails">رسائل البريد التسويقية</Label>
                      <p className="text-sm text-gray-500">استلام رسائل البريد الإلكتروني التسويقية والترويجية</p>
                    </div>
                    <Switch
                      id="marketingEmails"
                      checked={notificationSettings.marketingEmails}
                      onCheckedChange={(checked) => handleNotificationChange("marketingEmails", checked)}
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" className="bg-green-600 hover:bg-green-700" disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="ml-2 h-4 w-4" />
                        حفظ التفضيلات
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}


// Script to create a test banner
const { MongoClient } = require('mongodb');

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const dbName = 'initiatives_dz';

async function createTestBanner() {
  const client = new MongoClient(uri);
  
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const banners = db.collection('banners');
    
    // Create test banner
    const banner = {
      image: '/placeholder.svg',
      mainText: 'Welcome to Initiatives DZ',
      subText: 'Join thousands of volunteers supporting local initiatives',
      mainTextColor: '#FFFFFF',
      subTextColor: '#FFFFFF',
      order: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await banners.insertOne(banner);
    
    console.log(`Created test banner with ID: ${result.insertedId}`);
  } catch (error) {
    console.error('Error creating test banner:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
createTestBanner();

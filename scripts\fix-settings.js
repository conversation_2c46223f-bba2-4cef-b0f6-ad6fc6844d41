// Script to fix settings collection
const { MongoClient } = require('mongodb');

async function fixSettings() {
  const uri = 'mongodb://localhost:27017/initiatives_dz';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const settingsCollection = db.collection('settings');

    // Drop all indexes
    console.log('Dropping all indexes on settings collection...');
    await settingsCollection.dropIndexes();
    
    // Delete all documents in the settings collection
    console.log('Deleting all documents in settings collection...');
    await settingsCollection.deleteMany({});
    
    // Create a new settings document with our structured format
    console.log('Creating new settings document...');
    await settingsCollection.insertOne({
      siteName: 'منصة المبادرات',
      siteDescription: 'منصة لإدارة ومتابعة المبادرات المجتمعية',
      contactEmail: '<EMAIL>',
      supportPhone: '+213 000 000 000',
      maintenanceMode: false,
      allowRegistration: true,
      requireEmailVerification: true,
      maxUploadSize: 5,
      termsAndConditions: '',
      privacyPolicy: '',
      aboutUs: '',
      socialLinks: {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: ''
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    console.log('Settings collection fixed successfully!');
  } catch (error) {
    console.error('Error fixing settings collection:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

fixSettings().catch(console.error);

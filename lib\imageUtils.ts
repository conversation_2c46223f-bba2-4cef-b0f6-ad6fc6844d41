/**
 * Utility functions for handling image URLs
 */

// Backend server URL
const BACKEND_URL = 'http://localhost:5000';

/**
 * Ensures an image URL is absolute and points to the correct server
 * @param imagePath The image path or URL
 * @returns The absolute URL to the image
 */
export function getImageUrl(imagePath: string): string {
  // If the path is empty or undefined, return a placeholder
  if (!imagePath) {
    return '/placeholder.svg?height=600&width=1200';
  }

  // If the path is a placeholder or built-in image, use it directly
  if (imagePath.startsWith('/placeholder') || imagePath.startsWith('/banner')) {
    return imagePath;
  }

  // If the path is already an absolute URL, return it as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // If the path starts with /uploads, it's a backend resource
  if (imagePath.startsWith('/uploads/')) {
    return `${BACKEND_URL}${imagePath}`;
  }

  // Otherwise, return the path as is
  return imagePath;
}

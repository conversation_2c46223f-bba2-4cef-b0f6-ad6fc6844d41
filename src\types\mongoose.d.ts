import { Document, Model } from 'mongoose';

// Extend the mongoose module to fix TypeScript errors
declare module 'mongoose' {
  interface Model<T extends Document, QueryHelpers = {}> {
    findOne: any;
    findById: any;
    findByIdAndUpdate: any;
    findOneAndUpdate: any;
    findByIdAndDelete: any;
    findOneAndDelete: any;
    create: any;
    updateOne: any;
    updateMany: any;
    deleteOne: any;
    deleteMany: any;
    countDocuments: any;
    aggregate: any;
  }
}

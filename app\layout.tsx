import type { Metadata } from "next"
import { Inter, Cairo } from "next/font/google"
import "./globals.css"
import "../styles/header-fix.css"
import { ClientLayout } from "./client-layout"

// Police Inter pour le texte latin
const inter = Inter({ subsets: ["latin"], variable: '--font-inter' })

// Police Cairo pour le texte arabe
const cairo = Cairo({
  subsets: ["arabic"],
  variable: '--font-cairo',
  weight: ['400', '500', '600', '700', '800']
})

export const metadata: Metadata = {
  title: "منصة المبادرات المواطنة الجزائرية",
  description: "منصة تفاعلية لاقتراح وتنفيذ المبادرات المجتمعية في الجزائر",
  icons: {
    icon: '/logo.png',
    apple: '/logo.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className={`${cairo.variable} ${inter.variable} light rtl`} suppressHydrationWarning>
      <body className="font-cairo">
        <ClientLayout>
          {children}
        </ClientLayout>
      </body>
    </html>
  )
}

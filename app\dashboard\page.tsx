"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, PlusCircle, ThumbsUp, MessageSquare, Eye, Clock, Package, Truck, Ban } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { api } from "@/lib/api"
import { getCurrentUser } from "@/lib/auth"

interface Initiative {
  _id: string
  title: string
  shortDescription: string
  category: {
    _id: string
    name: string
    arabicName: string
    color: string
  }
  location: string
  mainImage: string
  supportCount: number
  commentCount: number
  status: string
  progress: number
  createdAt: string
}

interface Notification {
  _id: string
  type: string
  content: string
  isRead: boolean
  createdAt: string
  relatedInitiative?: {
    _id: string
    title: string
  }
}

export default function DashboardPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  const [myInitiatives, setMyInitiatives] = useState<Initiative[]>([])
  const [supportedInitiatives, setSupportedInitiatives] = useState<Initiative[]>([])
  const [recentNotifications, setRecentNotifications] = useState<Notification[]>([])
  const [stats, setStats] = useState({
    totalInitiatives: 0,
    supportedInitiatives: 0,
    commentsCount: 0,
    unreadNotifications: 0,
  })

  // Get current user
  const user = getCurrentUser()

  useEffect(() => {
    // Redirect if not logged in
    if (!user) {
      router.push("/auth/login")
      return
    }

    const fetchDashboardData = async () => {
      try {
        // Fetch user's initiatives
        const initiativesResponse = await api.get(`/api/initiatives/user/${user.id}`)
        setMyInitiatives(initiativesResponse.initiatives || [])

        // Fetch supported initiatives
        const supportedResponse = await api.get(`/api/users/${user.id}/supported-initiatives`)
        setSupportedInitiatives(supportedResponse.initiatives || [])

        // Fetch recent notifications
        const notificationsResponse = await api.get("/api/notifications?limit=5")
        setRecentNotifications(notificationsResponse.notifications || [])

        // Fetch user stats
        const statsResponse = await api.get("/api/users/stats")
        setStats(statsResponse.stats)
      } catch (err: any) {
        setError(err.message || "An error occurred")
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [router, user])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const markAllNotificationsAsRead = async () => {
    try {
      await api.put("/api/notifications/mark-all-read", {})

      // Update notifications state
      setRecentNotifications((prev) => prev.map((notification) => ({ ...notification, isRead: true })))

      // Update stats
      setStats((prev) => ({
        ...prev,
        unreadNotifications: 0,
      }))
    } catch (err: any) {
      setError(err.message || "Failed to mark notifications as read")
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2">Loading dashboard...</span>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">لوحة التحكم</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">مبادراتي</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <PlusCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-2xl font-bold">{stats.totalInitiatives}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">المبادرات التي أدعمها</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ThumbsUp className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-2xl font-bold">{stats.supportedInitiatives}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">التعليقات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <MessageSquare className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-2xl font-bold">{stats.commentsCount}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">إشعارات غير مقروءة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Eye className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-2xl font-bold">{stats.unreadNotifications}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Tabs defaultValue="my-initiatives" className="mb-8">
            <TabsList className="mb-6">
              <TabsTrigger value="my-initiatives">مبادراتي</TabsTrigger>
              <TabsTrigger value="supported">المبادرات التي أدعمها</TabsTrigger>
            </TabsList>

            <TabsContent value="my-initiatives">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">مبادراتي</h2>
                <Link href="/initiatives/create">
                  <Button className="bg-green-600 hover:bg-green-700">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    إنشاء مبادرة جديدة
                  </Button>
                </Link>
              </div>

              {myInitiatives.length === 0 ? (
                <Card>
                  <CardContent className="py-8 text-center text-gray-500">
                    <p>لم تقم بإنشاء أي مبادرات بعد.</p>
                    <Link href="/initiatives/create">
                      <Button className="mt-4 bg-green-600 hover:bg-green-700">إنشاء مبادرة جديدة</Button>
                    </Link>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {myInitiatives.map((initiative) => (
                    <Card key={initiative._id} className="overflow-hidden">
                      <div className="flex flex-col md:flex-row">
                        <div className="md:w-1/4 h-[120px] md:h-auto">
                          <Image
                            src={initiative.mainImage || "/placeholder.svg"}
                            alt={initiative.title}
                            width={200}
                            height={150}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="p-4 md:p-6 flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <Badge style={{ backgroundColor: initiative.category.color }}>
                                {initiative.category.name}
                              </Badge>
                              <h3 className="text-lg font-bold mt-2">
                                <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600">
                                  {initiative.title}
                                </Link>
                              </h3>
                            </div>
                            <Badge
                              variant="outline"
                              className={
                                initiative.status === "active"
                                  ? "text-green-600 border-green-200 bg-green-50"
                                  : initiative.status === "completed"
                                    ? "text-blue-600 border-blue-200 bg-blue-50"
                                    : initiative.status === "pending"
                                      ? "text-yellow-600 border-yellow-200 bg-yellow-50"
                                      : "text-gray-600 border-gray-200 bg-gray-50"
                              }
                            >
                              {initiative.status === "active"
                                ? "نشطة"
                                : initiative.status === "completed"
                                  ? "مكتملة"
                                  : initiative.status === "pending"
                                    ? "قيد المراجعة"
                                    : initiative.status}
                            </Badge>
                          </div>

                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">{initiative.shortDescription}</p>

                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="flex-1">
                              <div className="flex justify-between mb-1 text-sm">
                                <span>التقدم</span>
                                <span>{initiative.progress}%</span>
                              </div>
                              <Progress value={initiative.progress} className="h-2" />
                            </div>

                            <div className="flex gap-2">
                              <Link href={`/initiatives/${initiative._id}/edit`}>
                                <Button variant="outline" size="sm">
                                  تعديل
                                </Button>
                              </Link>
                              <Link href={`/initiatives/${initiative._id}`}>
                                <Button size="sm" className="bg-green-600 hover:bg-green-700">
                                  عرض
                                </Button>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}

              {myInitiatives.length > 0 && (
                <div className="mt-4 text-center">
                  <Link href={`/users/${user?.id}`}>
                    <Button variant="outline">عرض جميع مبادراتي</Button>
                  </Link>
                </div>
              )}
            </TabsContent>

            <TabsContent value="supported">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">المبادرات التي أدعمها</h2>
                <Link href="/initiatives">
                  <Button variant="outline">استكشاف المزيد من المبادرات</Button>
                </Link>
              </div>

              {supportedInitiatives.length === 0 ? (
                <Card>
                  <CardContent className="py-8 text-center text-gray-500">
                    <p>لم تقم بدعم أي مبادرات بعد.</p>
                    <Link href="/initiatives">
                      <Button className="mt-4 bg-green-600 hover:bg-green-700">استكشاف المبادرات</Button>
                    </Link>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {supportedInitiatives.map((initiative) => (
                    <Card key={initiative._id} className="overflow-hidden">
                      <div className="flex flex-col md:flex-row">
                        <div className="md:w-1/4 h-[120px] md:h-auto">
                          <Image
                            src={initiative.mainImage || "/placeholder.svg"}
                            alt={initiative.title}
                            width={200}
                            height={150}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="p-4 md:p-6 flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <Badge style={{ backgroundColor: initiative.category.color }}>
                                {initiative.category.name}
                              </Badge>
                              <h3 className="text-lg font-bold mt-2">
                                <Link href={`/initiatives/${initiative._id}`} className="hover:text-green-600">
                                  {initiative.title}
                                </Link>
                              </h3>
                            </div>
                            <Badge
                              variant="outline"
                              className={
                                initiative.status === "active"
                                  ? "text-green-600 border-green-200 bg-green-50"
                                  : initiative.status === "completed"
                                    ? "text-blue-600 border-blue-200 bg-blue-50"
                                    : "text-gray-600 border-gray-200 bg-gray-50"
                              }
                            >
                              {initiative.status === "active"
                                ? "نشطة"
                                : initiative.status === "completed"
                                  ? "مكتملة"
                                  : initiative.status}
                            </Badge>
                          </div>

                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">{initiative.shortDescription}</p>

                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="flex-1">
                              <div className="flex justify-between mb-1 text-sm">
                                <span>التقدم</span>
                                <span>{initiative.progress}%</span>
                              </div>
                              <Progress value={initiative.progress} className="h-2" />
                            </div>

                            <Link href={`/initiatives/${initiative._id}`}>
                              <Button size="sm" className="bg-green-600 hover:bg-green-700">
                                عرض
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}

              {supportedInitiatives.length > 0 && (
                <div className="mt-4 text-center">
                  <Link href={`/users/${user?.id}?tab=supported`}>
                    <Button variant="outline">عرض جميع المبادرات التي أدعمها</Button>
                  </Link>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle>آخر الإشعارات</CardTitle>
                {stats.unreadNotifications > 0 && (
                  <Button variant="ghost" size="sm" onClick={markAllNotificationsAsRead}>
                    تعيين الكل كمقروء
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {recentNotifications.length === 0 ? (
                <div className="py-6 text-center text-gray-500">
                  <p>لا توجد إشعارات جديدة</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentNotifications.map((notification) => (
                    <div
                      key={notification._id}
                      className={`p-3 rounded-lg ${notification.isRead ? "bg-gray-50" : "bg-green-50 border-r-2 border-green-600"}`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`mt-1 ${notification.isRead ? "text-gray-400" : "text-green-600"}`}>
                          {notification.type === "support" && <ThumbsUp className="h-5 w-5" />}
                          {notification.type === "comment" && <MessageSquare className="h-5 w-5" />}
                          {notification.type === "system" && <Eye className="h-5 w-5" />}
                          {notification.type === "resource_offer" && <Package className="h-5 w-5" />}
                          {notification.type === "resource_status_update" && <Truck className="h-5 w-5" />}
                          {notification.type === "resource_canceled" && <Ban className="h-5 w-5" />}
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-700">{notification.content}</p>
                          {notification.relatedInitiative && (
                            <Link
                              href={`/initiatives/${notification.relatedInitiative._id}`}
                              className="text-sm text-green-600 hover:underline"
                            >
                              {notification.relatedInitiative.title}
                            </Link>
                          )}
                          <div className="flex items-center mt-1 text-xs text-gray-500">
                            <Clock className="h-3 w-3 mr-1" />
                            <span>{formatDate(notification.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="mt-4 text-center">
                <Link href="/notifications">
                  <Button variant="outline" className="w-full">
                    عرض جميع الإشعارات
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}


import { Request, Response, NextFunction } from "express"
import { User } from "../models"
import { createError } from "../utils/error"
import multer from "multer"
import path from "path"
import fs from "fs"
import { v4 as uuidv4 } from "uuid"

// Créer le répertoire de stockage s'il n'existe pas
const uploadDir = path.join(process.cwd(), "public/uploads/avatars")
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

// Configuration de multer pour le stockage des fichiers
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // Générer un nom de fichier unique avec extension
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`
    cb(null, uniqueFilename)
  }
})

// Filtre pour n'accepter que les images
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.mimetype.startsWith("image/")) {
    cb(null, true)
  } else {
    cb(createError(400, "Only image files are allowed"))
  }
}

// Configuration de l'upload
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB max
  }
})

// Middleware pour gérer l'upload d'avatar
export const uploadAvatar = (req: Request, res: Response, next: NextFunction) => {
  // Vérifier si le corps de la requête contient des données
  if (!req.headers['content-type'] || !req.headers['content-type'].includes('multipart/form-data')) {
    return next(createError(400, "Invalid content type. Expected multipart/form-data"))
  }

  const uploadSingle = upload.single("avatar")

  uploadSingle(req, res, (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === "LIMIT_FILE_SIZE") {
          return next(createError(400, "File size too large. Maximum size is 2MB"))
        } else if (err.code === "LIMIT_UNEXPECTED_FILE") {
          return next(createError(400, "Unexpected field. Expected 'avatar'"))
        }
      }
      return next(createError(400, `File upload error: ${err.message}`))
    }

    if (!req.file) {
      return next(createError(400, "No file uploaded"))
    }

    // File uploaded successfully
    next()
  })
}

// Contrôleur pour mettre à jour l'avatar de l'utilisateur
export const updateAvatar = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Update avatar controller
    const { userId } = req.params

    // Vérifier si l'utilisateur est autorisé à mettre à jour cet avatar
    if (req.user && req.user.id !== userId && req.user.role !== "admin") {
      return next(createError(403, "You are not authorized to update this avatar"))
    }

    // Vérifier si le fichier a été uploadé
    if (!req.file) {
      return next(createError(400, "No file uploaded"))
    }

    // Trouver l'utilisateur
    const user = await User.findById(userId)
    if (!user) {
      return next(createError(404, "User not found"))
    }

    // Supprimer l'ancien avatar s'il existe et n'est pas l'avatar par défaut
    if (user.avatar && !user.avatar.includes("default-avatar") && !user.avatar.includes("placeholder")) {
      const oldAvatarPath = path.join(process.cwd(), "public", user.avatar)

      if (fs.existsSync(oldAvatarPath)) {
        fs.unlinkSync(oldAvatarPath)
      }
    }

    // Mettre à jour l'avatar de l'utilisateur
    const avatarUrl = `/uploads/avatars/${req.file.filename}`

    user.avatar = avatarUrl
    await user.save()

    res.status(200).json({
      success: true,
      message: "Avatar updated successfully",
      avatarUrl
    })
  } catch (error) {
    next(error)
  }
}

const { MongoClient } = require('mongodb');

// Connection URI
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/initiatives_dz';

async function checkMongoDBConnection() {
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  
  try {
    console.log('Checking MongoDB connection...');
    await client.connect();
    console.log('MongoDB connection successful!');
    return true;
  } catch (error) {
    console.error('MongoDB connection failed:', error.message);
    console.error('\nPlease make sure MongoDB is running on your machine.');
    console.error('You can download MongoDB from: https://www.mongodb.com/try/download/community');
    console.error('Or use MongoDB Atlas: https://www.mongodb.com/cloud/atlas\n');
    return false;
  } finally {
    await client.close();
  }
}

// Run the check if this script is executed directly
if (require.main === module) {
  checkMongoDBConnection()
    .then(isConnected => {
      if (!isConnected) {
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = { checkMongoDBConnection };

// Using built-in fetch API

async function testAPI() {
  try {
    console.log('Testing API connection...');

    // Test health endpoint
    console.log('\nTesting health endpoint:');
    const healthResponse = await fetch('http://localhost:5000/api/health');
    const healthData = await healthResponse.json();
    console.log('Health endpoint response:', healthData);

    // Test initiatives endpoint
    console.log('\nTesting initiatives endpoint:');
    const initiativesResponse = await fetch('http://localhost:5000/api/initiatives');
    const initiativesData = await initiativesResponse.json();
    console.log('Initiatives endpoint status:', initiativesResponse.status);
    console.log('Initiatives count:', initiativesData.count);
    console.log('First initiative title:', initiativesData.initiatives?.[0]?.title || 'No initiatives found');

    // Test specific initiative endpoint
    if (initiativesData.initiatives && initiativesData.initiatives.length > 0) {
      const initiativeId = initiativesData.initiatives[0]._id;
      console.log('\nTesting specific initiative endpoint:');
      console.log('Initiative ID:', initiativeId);

      const initiativeResponse = await fetch(`http://localhost:5000/api/initiatives/${initiativeId}`);
      const initiativeData = await initiativeResponse.json();
      console.log('Initiative endpoint status:', initiativeResponse.status);
      console.log('Initiative title:', initiativeData.initiative?.title || 'Initiative not found');
    }

    console.log('\nAPI test completed successfully!');
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();

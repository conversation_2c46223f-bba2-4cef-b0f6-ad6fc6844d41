import { Request, Response } from "express";
import { Initiative, User, Comment, Resource, ResourceNeed } from "../models/index";

/**
 * Get platform statistics
 * @route GET /api/stats
 * @access Public
 */
export const getPlatformStats = async (req: Request, res: Response) => {
  try {
    // Use default values in case of errors
    let totalInitiatives = 0;
    let implementedInitiatives = 0;
    let activeUsers = 0;
    let uniqueLocations = 0;
    let totalComments = 0;
    let totalCategories = 0;
    let totalResources = 0;
    let deliveredResources = 0;
    let totalResourceNeeds = 0;
    let fulfilledResourceNeeds = 0;

    try {
      // Count total initiatives
      totalInitiatives = await Initiative.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting initiatives:", err);
    }

    try {
      // Count implemented initiatives (status: completed OR progress: 100)
      implementedInitiatives = await Initiative.countDocuments({
        $or: [
          { status: "completed" },
          { progress: 100 }
        ]
      }) || 0;
    } catch (err) {
      console.error("Error counting completed initiatives:", err);
    }

    try {
      // Count active users
      activeUsers = await User.countDocuments({ isActive: true }) || 0;
    } catch (err) {
      console.error("Error counting active users:", err);
    }

    try {
      // Count unique locations (wilayas)
      uniqueLocations = await Initiative.distinct("location").then(locations => locations?.length || 0);
    } catch (err) {
      console.error("Error counting unique locations:", err);
    }

    try {
      // Count total comments
      totalComments = await Comment.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting comments:", err);
    }

    try {
      // Count total categories
      totalCategories = await Initiative.distinct("category").then(categories => categories?.length || 0);
    } catch (err) {
      console.error("Error counting categories:", err);
    }

    try {
      // Count total resources
      totalResources = await Resource.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting resources:", err);
    }

    try {
      // Count delivered resources
      deliveredResources = await Resource.countDocuments({ status: "delivered" }) || 0;
    } catch (err) {
      console.error("Error counting delivered resources:", err);
    }

    try {
      // Count total resource needs
      totalResourceNeeds = await ResourceNeed.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting resource needs:", err);
    }

    try {
      // Count fulfilled resource needs
      fulfilledResourceNeeds = await ResourceNeed.countDocuments({ status: "fulfilled" }) || 0;
    } catch (err) {
      console.error("Error counting fulfilled resource needs:", err);
    }

    // Return statistics
    return res.status(200).json({
      success: true,
      statistics: [
        { key: "initiatives", value: `${totalInitiatives}+`, label: "مبادرة مقترحة" },
        { key: "implemented", value: `${implementedInitiatives}+`, label: "مبادرة منفذة" },
        { key: "users", value: `${activeUsers}+`, label: "مشارك نشط" },
        { key: "locations", value: `${uniqueLocations}`, label: "ولاية مستفيدة" },
      ],
      additionalStats: {
        totalComments,
        totalCategories,
        resources: {
          total: totalResources,
          delivered: deliveredResources,
          deliveryRate: totalResources > 0 ? Math.round((deliveredResources / totalResources) * 100) : 0
        },
        resourceNeeds: {
          total: totalResourceNeeds,
          fulfilled: fulfilledResourceNeeds,
          fulfillmentRate: totalResourceNeeds > 0 ? Math.round((fulfilledResourceNeeds / totalResourceNeeds) * 100) : 0
        }
      }
    });
  } catch (error) {
    console.error("Error fetching platform statistics:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch platform statistics",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

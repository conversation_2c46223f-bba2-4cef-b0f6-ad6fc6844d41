import { Request, Response } from "express";
import { Initiative, User, Comment, Resource, ResourceNeed } from "../models/index";

/**
 * Get platform statistics
 * @route GET /api/stats
 * @access Public
 */
export const getPlatformStats = async (req: Request, res: Response) => {
  try {
    // Use default values in case of errors
    let totalInitiatives = 0;
    let implementedInitiatives = 0;
    let activeUsers = 0;
    let uniqueLocations = 0;
    let totalComments = 0;
    let totalCategories = 0;
    let totalResources = 0;
    let deliveredResources = 0;
    let totalResourceNeeds = 0;
    let fulfilledResourceNeeds = 0;

    try {
      // Count total initiatives
      totalInitiatives = await Initiative.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting initiatives:", err);
    }

    try {
      // Count implemented initiatives (status: completed OR progress: 100)
      implementedInitiatives = await Initiative.countDocuments({
        $or: [
          { status: "completed" },
          { progress: 100 }
        ]
      }) || 0;
    } catch (err) {
      console.error("Error counting completed initiatives:", err);
    }

    try {
      // Count active users with a progressive approach
      // Try different criteria in order of preference

      // First, try to count users with recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      let recentlyActiveUsers = 0;
      try {
        recentlyActiveUsers = await User.countDocuments({
          isBlocked: { $ne: true },
          $or: [
            { lastLogin: { $gte: thirtyDaysAgo } },
            { createdAt: { $gte: thirtyDaysAgo } }
          ]
        });
      } catch (recentErr) {
        console.log("Could not count recently active users:", recentErr.message);
      }

      // If we have recently active users, use that count
      if (recentlyActiveUsers > 0) {
        activeUsers = recentlyActiveUsers;
      } else {
        // Fallback: count users with initiatives or supports
        try {
          const usersWithActivity = await User.countDocuments({
            isBlocked: { $ne: true },
            $or: [
              { initiatives: { $exists: true, $ne: [] } },
              { supportedInitiatives: { $exists: true, $ne: [] } }
            ]
          });

          if (usersWithActivity > 0) {
            activeUsers = usersWithActivity;
          } else {
            // Final fallback: count all non-blocked users
            activeUsers = await User.countDocuments({ isBlocked: { $ne: true } }) || 0;

            // If still 0, count all users
            if (activeUsers === 0) {
              activeUsers = await User.countDocuments() || 0;
            }
          }
        } catch (activityErr) {
          console.log("Could not count users with activity:", activityErr.message);
          // Ultimate fallback: count all users
          activeUsers = await User.countDocuments() || 0;
        }
      }
    } catch (err) {
      console.error("Error counting active users:", err);
      // Final fallback: count all users
      try {
        activeUsers = await User.countDocuments() || 0;
      } catch (fallbackErr) {
        console.error("Error counting all users:", fallbackErr);
        activeUsers = 0;
      }
    }

    try {
      // Count unique locations (wilayas)
      uniqueLocations = await Initiative.distinct("location").then(locations => locations?.length || 0);
    } catch (err) {
      console.error("Error counting unique locations:", err);
    }

    try {
      // Count total comments
      totalComments = await Comment.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting comments:", err);
    }

    try {
      // Count total categories
      totalCategories = await Initiative.distinct("category").then(categories => categories?.length || 0);
    } catch (err) {
      console.error("Error counting categories:", err);
    }

    try {
      // Count total resources
      totalResources = await Resource.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting resources:", err);
    }

    try {
      // Count delivered resources
      deliveredResources = await Resource.countDocuments({ status: "delivered" }) || 0;
    } catch (err) {
      console.error("Error counting delivered resources:", err);
    }

    try {
      // Count total resource needs
      totalResourceNeeds = await ResourceNeed.countDocuments() || 0;
    } catch (err) {
      console.error("Error counting resource needs:", err);
    }

    try {
      // Count fulfilled resource needs
      fulfilledResourceNeeds = await ResourceNeed.countDocuments({ status: "fulfilled" }) || 0;
    } catch (err) {
      console.error("Error counting fulfilled resource needs:", err);
    }

    // Return statistics
    return res.status(200).json({
      success: true,
      statistics: [
        { key: "initiatives", value: `${totalInitiatives}+`, label: "مبادرة مقترحة" },
        { key: "implemented", value: `${implementedInitiatives}+`, label: "مبادرة منفذة" },
        { key: "users", value: `${activeUsers}+`, label: "مشارك نشط" },
        { key: "locations", value: `${uniqueLocations}`, label: "ولاية مستفيدة" },
      ],
      additionalStats: {
        totalComments,
        totalCategories,
        resources: {
          total: totalResources,
          delivered: deliveredResources,
          deliveryRate: totalResources > 0 ? Math.round((deliveredResources / totalResources) * 100) : 0
        },
        resourceNeeds: {
          total: totalResourceNeeds,
          fulfilled: fulfilledResourceNeeds,
          fulfillmentRate: totalResourceNeeds > 0 ? Math.round((fulfilledResourceNeeds / totalResourceNeeds) * 100) : 0
        }
      }
    });
  } catch (error) {
    console.error("Error fetching platform statistics:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch platform statistics",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

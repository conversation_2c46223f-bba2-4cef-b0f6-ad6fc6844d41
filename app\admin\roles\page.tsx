"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { api } from "@/lib/api"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Loader2, Plus, Trash, Edit, Shield } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/components/ui/use-toast"
import { To<PERSON> } from "@/components/ui/toaster"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"

interface Permission {
  _id: string
  name: string
  description: string
  code: string
  category: string
}

interface Role {
  _id: string
  name: string
  description: string
  code: string
  permissions: Permission[]
  isDefault: boolean
  isSystem: boolean
}

export default function RolesPage() {
  const router = useRouter()
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Record<string, Permission[]>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    code: "",
    permissions: [] as string[],
    isDefault: false,
  })

  // Fetch roles and permissions
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError("")

        // Fetch roles
        const rolesResponse = await api.get("/api/admin/roles")
        if (rolesResponse.success) {
          setRoles(rolesResponse.roles)
        } else {
          setError(rolesResponse.message || "Failed to fetch roles")
          toast({
            title: "Error",
            description: "Failed to fetch roles",
            variant: "destructive",
          })
        }

        // Fetch permissions
        const permissionsResponse = await api.get("/api/admin/roles/permissions/all")
        if (permissionsResponse.success) {
          setPermissions(permissionsResponse.permissions)
        } else {
          setError(permissionsResponse.message || "Failed to fetch permissions")
          toast({
            title: "Error",
            description: "Failed to fetch permissions",
            variant: "destructive",
          })
        }
      } catch (err: any) {
        console.error("Error fetching data:", err)
        setError(err.message || "An error occurred")
        toast({
          title: "Error",
          description: err.message || "An error occurred",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle create role
  const handleCreateRole = async () => {
    try {
      setIsProcessing(true)

      // Validate form
      if (!newRole.name || !newRole.description || !newRole.code) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        })
        return
      }

      // Create role
      const response = await api.post("/api/admin/roles", newRole)

      if (response.success) {
        toast({
          title: "Success",
          description: "Role created successfully",
        })

        // Add new role to list
        setRoles([...roles, response.role])

        // Reset form and close dialog
        setNewRole({
          name: "",
          description: "",
          code: "",
          permissions: [],
          isDefault: false,
        })
        setShowCreateDialog(false)
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create role",
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error("Error creating role:", err)
      toast({
        title: "Error",
        description: err.message || "An error occurred",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle update role
  const handleUpdateRole = async () => {
    if (!selectedRole) return

    try {
      setIsProcessing(true)

      // Update role
      const response = await api.put(`/api/admin/roles/${selectedRole._id}`, {
        name: selectedRole.name,
        description: selectedRole.description,
        permissions: selectedRole.permissions.map(p => p._id),
        isDefault: selectedRole.isDefault,
      })

      if (response.success) {
        toast({
          title: "Success",
          description: "Role updated successfully",
        })

        // Update role in list
        setRoles(roles.map(role => (role._id === selectedRole._id ? response.role : role)))

        // Close dialog
        setShowEditDialog(false)
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update role",
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error("Error updating role:", err)
      toast({
        title: "Error",
        description: err.message || "An error occurred",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle delete role
  const handleDeleteRole = async () => {
    if (!selectedRole) return

    try {
      setIsProcessing(true)

      // Delete role
      const response = await api.delete(`/api/admin/roles/${selectedRole._id}`)

      if (response.success) {
        toast({
          title: "Success",
          description: "Role deleted successfully",
        })

        // Remove role from list
        setRoles(roles.filter(role => role._id !== selectedRole._id))

        // Close dialog
        setShowDeleteDialog(false)
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete role",
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error("Error deleting role:", err)
      toast({
        title: "Error",
        description: err.message || "An error occurred",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Toggle permission in new role
  const togglePermission = (permissionId: string) => {
    if (newRole.permissions.includes(permissionId)) {
      setNewRole({
        ...newRole,
        permissions: newRole.permissions.filter(id => id !== permissionId),
      })
    } else {
      setNewRole({
        ...newRole,
        permissions: [...newRole.permissions, permissionId],
      })
    }
  }

  // Toggle permission in selected role
  const togglePermissionInSelectedRole = (permission: Permission) => {
    if (!selectedRole) return

    const hasPermission = selectedRole.permissions.some(p => p._id === permission._id)

    if (hasPermission) {
      setSelectedRole({
        ...selectedRole,
        permissions: selectedRole.permissions.filter(p => p._id !== permission._id),
      })
    } else {
      setSelectedRole({
        ...selectedRole,
        permissions: [...selectedRole.permissions, permission],
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">إدارة الأدوار والصلاحيات</h1>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          إضافة دور جديد
        </Button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 gap-6">
        {roles.map(role => (
          <Card key={role._id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-xl">
                  {role.name}
                  {role.isDefault && (
                    <Badge className="ml-2 bg-blue-500">افتراضي</Badge>
                  )}
                  {role.isSystem && (
                    <Badge className="ml-2 bg-purple-500">نظام</Badge>
                  )}
                </CardTitle>
                <CardDescription>{role.description}</CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedRole(role)
                    setShowEditDialog(true)
                  }}
                  disabled={role.code === "admin"} // Prevent editing admin role
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 border-red-600 hover:bg-red-50"
                  onClick={() => {
                    setSelectedRole(role)
                    setShowDeleteDialog(true)
                  }}
                  disabled={role.isSystem} // Prevent deleting system roles
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mt-2">
                <h3 className="text-sm font-medium mb-2">الصلاحيات:</h3>
                <div className="flex flex-wrap gap-1">
                  {role.permissions.map(permission => (
                    <Badge key={permission._id} variant="secondary" className="text-xs">
                      {permission.name}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create Role Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>إضافة دور جديد</DialogTitle>
            <DialogDescription>
              أضف دورًا جديدًا وحدد الصلاحيات المرتبطة به.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-2 py-2 flex-grow overflow-hidden">
            <div className="grid grid-cols-4 items-center gap-2">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                value={newRole.name}
                onChange={e => setNewRole({ ...newRole, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-2">
              <Label htmlFor="code" className="text-right">
                الرمز
              </Label>
              <Input
                id="code"
                value={newRole.code}
                onChange={e => setNewRole({ ...newRole, code: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-2">
              <Label htmlFor="description" className="text-right">
                الوصف
              </Label>
              <Textarea
                id="description"
                value={newRole.description}
                onChange={e => setNewRole({ ...newRole, description: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-2">
              <Label htmlFor="isDefault" className="text-right">
                افتراضي
              </Label>
              <div className="col-span-3">
                <Checkbox
                  id="isDefault"
                  checked={newRole.isDefault}
                  onCheckedChange={checked => setNewRole({ ...newRole, isDefault: !!checked })}
                />
                <span className="mr-2 text-sm text-gray-500">
                  تعيين هذا الدور كدور افتراضي للمستخدمين الجدد
                </span>
              </div>
            </div>

            <div className="mt-2">
              <Label className="text-lg font-medium">الصلاحيات</Label>
              <ScrollArea className="h-[500px] mt-2 border rounded-md p-4">
                <Tabs defaultValue="initiatives">
                  <TabsList className="mb-4">
                    {Object.keys(permissions).map(category => (
                      <TabsTrigger key={category} value={category}>
                        {getCategoryName(category)}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {Object.entries(permissions).map(([category, categoryPermissions]) => (
                    <TabsContent key={category} value={category} className="space-y-4">
                      {categoryPermissions.map(permission => (
                        <div key={permission._id} className="flex items-start space-x-2 space-x-reverse">
                          <Checkbox
                            id={`permission-${permission._id}`}
                            checked={newRole.permissions.includes(permission._id)}
                            onCheckedChange={() => togglePermission(permission._id)}
                          />
                          <div className="grid gap-1.5 leading-none">
                            <label
                              htmlFor={`permission-${permission._id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {permission.name}
                            </label>
                            <p className="text-sm text-muted-foreground">
                              {permission.description}
                            </p>
                          </div>
                        </div>
                      ))}
                    </TabsContent>
                  ))}
                </Tabs>
              </ScrollArea>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={handleCreateRole} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "إضافة"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>تعديل الدور</DialogTitle>
            <DialogDescription>
              قم بتعديل الدور وصلاحياته.
            </DialogDescription>
          </DialogHeader>

          {selectedRole && (
            <div className="grid gap-2 py-2 flex-grow overflow-hidden">
              <div className="grid grid-cols-4 items-center gap-2">
                <Label htmlFor="edit-name" className="text-right">
                  الاسم
                </Label>
                <Input
                  id="edit-name"
                  value={selectedRole.name}
                  onChange={e => setSelectedRole({ ...selectedRole, name: e.target.value })}
                  className="col-span-3"
                  disabled={selectedRole.isSystem}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-2">
                <Label htmlFor="edit-code" className="text-right">
                  الرمز
                </Label>
                <Input
                  id="edit-code"
                  value={selectedRole.code}
                  className="col-span-3"
                  disabled={true} // Code cannot be changed
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-2">
                <Label htmlFor="edit-description" className="text-right">
                  الوصف
                </Label>
                <Textarea
                  id="edit-description"
                  value={selectedRole.description}
                  onChange={e => setSelectedRole({ ...selectedRole, description: e.target.value })}
                  className="col-span-3"
                  disabled={selectedRole.isSystem}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-2">
                <Label htmlFor="edit-isDefault" className="text-right">
                  افتراضي
                </Label>
                <div className="col-span-3">
                  <Checkbox
                    id="edit-isDefault"
                    checked={selectedRole.isDefault}
                    onCheckedChange={checked => setSelectedRole({ ...selectedRole, isDefault: !!checked })}
                    disabled={selectedRole.code === "admin"} // Admin cannot be default
                  />
                  <span className="mr-2 text-sm text-gray-500">
                    تعيين هذا الدور كدور افتراضي للمستخدمين الجدد
                  </span>
                </div>
              </div>

              <div className="mt-2">
                <Label className="text-lg font-medium">الصلاحيات</Label>
                <ScrollArea className="h-[500px] mt-2 border rounded-md p-4">
                  <Tabs defaultValue="initiatives">
                    <TabsList className="mb-4">
                      {Object.keys(permissions).map(category => (
                        <TabsTrigger key={category} value={category}>
                          {getCategoryName(category)}
                        </TabsTrigger>
                      ))}
                    </TabsList>

                    {Object.entries(permissions).map(([category, categoryPermissions]) => (
                      <TabsContent key={category} value={category} className="space-y-4">
                        {categoryPermissions.map(permission => (
                          <div key={permission._id} className="flex items-start space-x-2 space-x-reverse">
                            <Checkbox
                              id={`edit-permission-${permission._id}`}
                              checked={selectedRole.permissions.some(p => p._id === permission._id)}
                              onCheckedChange={() => togglePermissionInSelectedRole(permission)}
                              disabled={selectedRole.code === "admin"} // Admin has all permissions
                            />
                            <div className="grid gap-1.5 leading-none">
                              <label
                                htmlFor={`edit-permission-${permission._id}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {permission.name}
                              </label>
                              <p className="text-sm text-muted-foreground">
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </TabsContent>
                    ))}
                  </Tabs>
                </ScrollArea>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={handleUpdateRole} disabled={isProcessing || selectedRole?.code === "admin"}>
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "حفظ التغييرات"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Role Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف الدور</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف هذا الدور؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteRole}
              disabled={isProcessing || selectedRole?.isSystem}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                "حذف"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Toaster />
    </div>
  )
}

// Helper function to get category name in Arabic
function getCategoryName(category: string): string {
  const categoryNames: Record<string, string> = {
    initiatives: "المبادرات",
    comments: "التعليقات",
    users: "المستخدمين",
    reports: "التقارير",
    admin: "الإدارة",
    system: "النظام",
  }

  return categoryNames[category] || category
}

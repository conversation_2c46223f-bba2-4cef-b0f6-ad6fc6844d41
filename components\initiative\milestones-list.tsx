"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "../ui/button"
import { Input } from "../ui/input"
import { Textarea } from "../ui/textarea"
import { <PERSON>, CardContent, CardHeader } from "../ui/card"
import { Alert, AlertDescription } from "../ui/alert"
import {
  AlertCircle,
  Loader2,
  Plus,
  CheckCircle2,
  Circle,
  Calendar,
  ArrowUp,
  ArrowDown,
  Edit,
  Trash2,
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog"

interface Milestone {
  _id: string
  title: string
  description: string
  targetDate: string
  completedDate?: string
  isCompleted: boolean
  order: number
}

interface MilestonesListProps {
  initiativeId: string
  initialMilestones: Milestone[]
  isAuthor: boolean
}

export default function MilestonesList({ initiativeId, initialMilestones, isAuthor }: MilestonesListProps) {
  const router = useRouter()
  const [milestones, setMilestones] = useState<Milestone[]>(initialMilestones || [])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    targetDate: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [editingMilestone, setEditingMilestone] = useState<string | null>(null)

  // Get current user from localStorage or sessionStorage
  const getCurrentUser = () => {
    if (typeof window !== "undefined") {
      const userStr = localStorage.getItem("user") || sessionStorage.getItem("user")
      return userStr ? JSON.parse(userStr) : null
    }
    return null
  }

  const user = getCurrentUser()

  // Get token from localStorage or sessionStorage
  const getToken = () => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("accessToken") || sessionStorage.getItem("accessToken")
    }
    return null
  }

  useEffect(() => {
    if (initialMilestones && initialMilestones.length > 0) {
      // Sort milestones by order
      const sortedMilestones = [...initialMilestones].sort((a, b) => a.order - b.order)
      setMilestones(sortedMilestones)
    } else {
      fetchMilestones()
    }
  }, [initiativeId, initialMilestones])

  const fetchMilestones = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/initiative/${initiativeId}`,
      )

      if (!response.ok) {
        throw new Error("Failed to fetch milestones")
      }

      const data = await response.json()
      // Sort milestones by order
      const sortedMilestones = [...(data.milestones || [])].sort((a, b) => a.order - b.order)
      setMilestones(sortedMilestones)
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      router.push("/auth/login")
      return
    }

    if (!formData.title.trim() || !formData.targetDate) return

    setIsSubmitting(true)

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const endpoint = editingMilestone
        ? `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${editingMilestone}`
        : `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones`

      const method = editingMilestone ? "PUT" : "POST"

      const response = await fetch(endpoint, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          initiative: initiativeId,
          title: formData.title,
          description: formData.description,
          targetDate: formData.targetDate,
          order: editingMilestone ? undefined : milestones.length + 1,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to save milestone")
      }

      if (editingMilestone) {
        // Update existing milestone
        setMilestones((prev) => prev.map((m) => (m._id === editingMilestone ? data.milestone : m)))
      } else {
        // Add new milestone to the list
        setMilestones((prev) => [...prev, data.milestone])
      }

      // Reset form
      setFormData({
        title: "",
        description: "",
        targetDate: "",
      })
      setShowForm(false)
      setEditingMilestone(null)
    } catch (err: any) {
      setError(err.message || "Failed to save milestone")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleToggleComplete = async (milestoneId: string, currentStatus: boolean) => {
    if (!user) {
      router.push("/auth/login")
      return
    }

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${milestoneId}`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isCompleted: !currentStatus,
            completedDate: !currentStatus ? new Date().toISOString() : null,
          }),
        },
      )

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || "Failed to update milestone")
      }

      // Update milestone in the list
      setMilestones((prev) => prev.map((m) => (m._id === milestoneId ? data.milestone : m)))
    } catch (err: any) {
      setError(err.message || "Failed to update milestone")
    }
  }

  const handleDelete = async (milestoneId: string) => {
    if (!user) {
      router.push("/auth/login")
      return
    }

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${milestoneId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error?.message || "Failed to delete milestone")
      }

      // Remove milestone from the list
      setMilestones((prev) => prev.filter((m) => m._id !== milestoneId))
    } catch (err: any) {
      setError(err.message || "Failed to delete milestone")
    }
  }

  const handleEdit = (milestone: Milestone) => {
    setFormData({
      title: milestone.title,
      description: milestone.description,
      targetDate: new Date(milestone.targetDate).toISOString().split("T")[0],
    })
    setEditingMilestone(milestone._id)
    setShowForm(true)
  }

  const handleMoveUp = async (index: number) => {
    if (index === 0) return

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const newMilestones = [...milestones]
      const currentMilestone = newMilestones[index]
      const prevMilestone = newMilestones[index - 1]

      // Swap orders
      const tempOrder = currentMilestone.order
      currentMilestone.order = prevMilestone.order
      prevMilestone.order = tempOrder

      // Update in database
      await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${currentMilestone._id}`, {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ order: currentMilestone.order }),
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${prevMilestone._id}`, {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ order: prevMilestone.order }),
        }),
      ])

      // Swap in array
      ;[newMilestones[index], newMilestones[index - 1]] = [newMilestones[index - 1], newMilestones[index]]
      setMilestones(newMilestones)
    } catch (err: any) {
      setError(err.message || "Failed to reorder milestones")
    }
  }

  const handleMoveDown = async (index: number) => {
    if (index === milestones.length - 1) return

    try {
      const token = getToken()

      if (!token) {
        router.push("/auth/login")
        return
      }

      const newMilestones = [...milestones]
      const currentMilestone = newMilestones[index]
      const nextMilestone = newMilestones[index + 1]

      // Swap orders
      const tempOrder = currentMilestone.order
      currentMilestone.order = nextMilestone.order
      nextMilestone.order = tempOrder

      // Update in database
      await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${currentMilestone._id}`, {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ order: currentMilestone.order }),
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"}/api/milestones/${nextMilestone._id}`, {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ order: nextMilestone.order }),
        }),
      ])

      // Swap in array
      ;[newMilestones[index], newMilestones[index + 1]] = [newMilestones[index + 1], newMilestones[index]]
      setMilestones(newMilestones)
    } catch (err: any) {
      setError(err.message || "Failed to reorder milestones")
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold">مراحل المبادرة</h2>
        {isAuthor && (
          <Button
            onClick={() => {
              setFormData({
                title: "",
                description: "",
                targetDate: "",
              })
              setEditingMilestone(null)
              setShowForm(!showForm)
            }}
            className="bg-green-600 hover:bg-green-700"
          >
            <Plus className="ml-2 h-4 w-4" />
            إضافة مرحلة جديدة
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Milestone Form */}
      {showForm && (
        <Card className="mb-8">
          <CardHeader>
            <h3 className="text-lg font-semibold">{editingMilestone ? "تعديل المرحلة" : "إضافة مرحلة جديدة"}</h3>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="title" className="font-medium">
                  العنوان
                </label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="عنوان المرحلة"
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="font-medium">
                  الوصف
                </label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="وصف هذه المرحلة..."
                  className="min-h-[100px]"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="targetDate" className="font-medium">
                  تاريخ الاستحقاق
                </label>
                <Input
                  id="targetDate"
                  name="targetDate"
                  type="date"
                  value={formData.targetDate}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowForm(false)
                    setEditingMilestone(null)
                  }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || !formData.title.trim() || !formData.targetDate}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : editingMilestone ? (
                    "تحديث المرحلة"
                  ) : (
                    "إضافة المرحلة"
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Progress Summary */}
      {milestones.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold">التقدم العام</h3>
            <div className="text-sm font-medium">
              {milestones.filter(m => m.isCompleted).length} / {milestones.length} مكتملة
              ({Math.round((milestones.filter(m => m.isCompleted).length / milestones.length) * 100)}%)
            </div>
          </div>
          <div className="bg-gray-100 h-2 rounded-full overflow-hidden">
            <div
              className="bg-green-500 h-full rounded-full"
              style={{ width: `${(milestones.filter(m => m.isCompleted).length / milestones.length) * 100}%` }}
            ></div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div className="bg-green-50 p-3 rounded-lg border border-green-100">
              <div className="text-sm text-gray-500">مكتملة</div>
              <div className="text-xl font-bold text-green-600">
                {milestones.filter(m => m.isCompleted).length}
              </div>
            </div>
            <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-100">
              <div className="text-sm text-gray-500">قيد التنفيذ</div>
              <div className="text-xl font-bold text-yellow-600">
                {milestones.filter(m => !m.isCompleted && new Date(m.targetDate) >= new Date()).length}
              </div>
            </div>
            <div className="bg-red-50 p-3 rounded-lg border border-red-100">
              <div className="text-sm text-gray-500">متأخرة</div>
              <div className="text-xl font-bold text-red-600">
                {milestones.filter(m => !m.isCompleted && new Date(m.targetDate) < new Date()).length}
              </div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-100">
              <div className="text-sm text-gray-500">المجموع</div>
              <div className="text-xl font-bold text-blue-600">
                {milestones.length}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Milestones List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-green-600" />
            <span className="mr-2">جاري تحميل المراحل...</span>
          </div>
        ) : milestones.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>لم يتم تحديد أي مراحل لهذه المبادرة بعد.</p>
            {isAuthor && (
              <Button onClick={() => setShowForm(true)} variant="link" className="text-green-600">
                إضافة المرحلة الأولى
              </Button>
            )}
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute right-4 top-6 bottom-6 w-0.5 bg-gray-200"></div>

            {milestones.map((milestone, index) => {
              // Calculate if milestone is overdue
              const isOverdue = !milestone.isCompleted && new Date(milestone.targetDate) < new Date();

              // Calculate days remaining or overdue
              const today = new Date();
              const targetDate = new Date(milestone.targetDate);
              const diffTime = Math.abs(targetDate.getTime() - today.getTime());
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              return (
                <div key={milestone._id} className="relative pr-12 pb-8">
                  {/* Timeline dot */}
                  <div
                    className={`absolute right-0 top-1.5 h-8 w-8 rounded-full flex items-center justify-center border-2 ${
                      milestone.isCompleted
                        ? "bg-green-100 border-green-600 text-green-600"
                        : isOverdue
                          ? "bg-red-100 border-red-600 text-red-600"
                          : "bg-white border-gray-300 text-gray-400"
                    }`}
                  >
                    {milestone.isCompleted ? <CheckCircle2 className="h-5 w-5" /> : <Circle className="h-5 w-5" />}
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className={`text-xl font-bold ${
                            milestone.isCompleted
                              ? "text-green-600"
                              : isOverdue
                                ? "text-red-600"
                                : ""
                          }`}>
                            {milestone.title}
                          </h3>
                          {isOverdue && !milestone.isCompleted && (
                            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                              متأخرة بـ {diffDays} يوم
                            </span>
                          )}
                          {!isOverdue && !milestone.isCompleted && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              متبقي {diffDays} يوم
                            </span>
                          )}
                        </div>

                        <div className="flex flex-wrap items-center text-sm text-gray-500 mt-2 gap-4">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 ml-1" />
                            <span>الموعد المستهدف: {formatDate(milestone.targetDate)}</span>
                          </div>
                          {milestone.completedDate && (
                            <div className="flex items-center text-green-600">
                              <CheckCircle2 className="h-4 w-4 ml-1" />
                              <span>تم الإكمال: {formatDate(milestone.completedDate)}</span>
                            </div>
                          )}
                        </div>

                        {milestone.description && (
                          <div className="mt-3 text-gray-700 bg-gray-50 p-3 rounded-md border border-gray-100">
                            {milestone.description}
                          </div>
                        )}
                      </div>

                      {isAuthor && (
                        <div className="flex flex-wrap items-center gap-2">
                          <Button
                            variant={milestone.isCompleted ? "outline" : "default"}
                            size="sm"
                            className={milestone.isCompleted ? "" : "bg-green-600 hover:bg-green-700"}
                            onClick={() => handleToggleComplete(milestone._id, milestone.isCompleted)}
                          >
                            {milestone.isCompleted ? (
                              <>
                                <Circle className="ml-1 h-4 w-4" />
                                إلغاء الإكمال
                              </>
                            ) : (
                              <>
                                <CheckCircle2 className="ml-1 h-4 w-4" />
                                تعيين كمكتملة
                              </>
                            )}
                          </Button>

                          <div className="flex items-center">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEdit(milestone)}
                              className="text-blue-600"
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">تعديل</span>
                            </Button>

                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-red-600"
                              onClick={() => {
                                if (confirm("هل أنت متأكد من رغبتك في حذف هذه المرحلة؟ هذا الإجراء لا يمكن التراجع عنه.")) {
                                  handleDelete(milestone._id);
                                }
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">حذف</span>
                            </Button>

                            <div className="flex">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                disabled={index === 0}
                                onClick={() => handleMoveUp(index)}
                              >
                                <ArrowUp className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                disabled={index === milestones.length - 1}
                                onClick={() => handleMoveDown(index)}
                              >
                                <ArrowDown className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Progress bar for this milestone */}
                    {milestone.isCompleted ? (
                      <div className="mt-4 bg-gray-100 h-2 rounded-full overflow-hidden">
                        <div className="bg-green-500 h-full rounded-full w-full"></div>
                      </div>
                    ) : (
                      <div className="mt-4 bg-gray-100 h-2 rounded-full overflow-hidden">
                        <div
                          className={`h-full rounded-full ${isOverdue ? "bg-red-500" : "bg-yellow-500"}`}
                          style={{ width: "0%" }}
                        ></div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  )
}


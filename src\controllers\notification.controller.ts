import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Notification } from "../models"
import { createError } from "../utils/error"

// Get user notifications
export const getUserNotifications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id
    const page = Number.parseInt(req.query.page as string) || 1
    const limit = Number.parseInt(req.query.limit as string) || 10
    const skip = (page - 1) * limit

    // Build filter
    const filter: any = { recipient: userId }

    // Filter by read status if specified
    if (req.query.read === "true") {
      filter.isRead = true
    } else if (req.query.read === "false") {
      filter.isRead = false
    }

    // Get notifications
    const notifications = await Notification.find(filter)
      .populate("sender", "name username avatar")
      .populate("relatedInitiative", "title shortDescription mainImage")
      .sort("-createdAt")
      .skip(skip)
      .limit(limit)

    // Get total count
    const total = await Notification.countDocuments(filter)

    res.status(200).json({
      success: true,
      count: notifications.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      notifications,
    })
  } catch (error) {
    next(error)
  }
}

// Get unread notification count
export const getUnreadCount = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id

    // Count unread notifications
    const count = await Notification.countDocuments({
      recipient: userId,
      isRead: false,
    })

    res.status(200).json({
      success: true,
      count,
    })
  } catch (error) {
    next(error)
  }
}

// Mark notification as read
export const markAsRead = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate notification ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid notification ID"))
    }

    // Find notification
    const notification = await Notification.findById(id)

    if (!notification) {
      return next(createError(404, "Notification not found"))
    }

    // Check if user is the recipient
    if (notification.recipient.toString() !== userId) {
      return next(createError(403, "You are not authorized to mark this notification as read"))
    }

    // Mark as read - utiliser updateOne pour éviter les problèmes de validation
    await Notification.updateOne(
      { _id: id },
      { $set: { isRead: true } }
    )

    res.status(200).json({
      success: true,
      message: "Notification marked as read",
    })
  } catch (error) {
    next(error)
  }
}

// Mark all notifications as read
export const markAllAsRead = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id

    // Update all unread notifications for the user
    await Notification.updateMany({ recipient: userId, isRead: false }, { isRead: true })

    res.status(200).json({
      success: true,
      message: "All notifications marked as read",
    })
  } catch (error) {
    next(error)
  }
}

// Delete notification
export const deleteNotification = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const userId = req.user.id

    // Validate notification ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid notification ID"))
    }

    // Find notification
    const notification = await Notification.findById(id)

    if (!notification) {
      return next(createError(404, "Notification not found"))
    }

    // Check if user is the recipient
    if (notification.recipient.toString() !== userId) {
      return next(createError(403, "You are not authorized to delete this notification"))
    }

    // Delete notification
    await Notification.findByIdAndDelete(id)

    res.status(200).json({
      success: true,
      message: "Notification deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Delete all notifications
export const deleteAllNotifications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id

    // Delete all notifications for the user
    await Notification.deleteMany({ recipient: userId })

    res.status(200).json({
      success: true,
      message: "All notifications deleted successfully",
    })
  } catch (error) {
    next(error)
  }
}

// Delete all read notifications
export const deleteAllReadNotifications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user.id

    // Delete all read notifications for the user
    const result = await Notification.deleteMany({
      recipient: userId,
      isRead: true
    })

    res.status(200).json({
      success: true,
      count: result.deletedCount,
      message: `${result.deletedCount} read notifications deleted successfully`,
    })
  } catch (error) {
    next(error)
  }
}


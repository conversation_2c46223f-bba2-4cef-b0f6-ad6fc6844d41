"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, MapPin, Calendar, Award, ThumbsUp, MessageSquare, Users, Loader2, ChevronLeft, ChevronRight } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { api } from "@/lib/api"

// Define types for community data
type Member = {
  id: string;
  name: string;
  username: string;
  avatar: string;
  location: string;
  joinDate: string;
  bio: string;
  initiatives: number;
  contributions: number;
  badges: string[];
  recentActivity: {
    type: string;
    title: string;
    date: string;
  }[];
};

type Post = {
  id: string;
  author: {
    id: string;
    name: string;
    username: string;
    avatar: string;
  };
  date: string;
  content: string;
  likes: number;
  comments: number;
  image: string | null;
};

type CommunityInitiative = {
  id: string;
  title: string;
  category: string;
  location: string;
  supportCount: number;
  commentCount: number;
  description: string;
  image: string;
};

type PaginationData = {
  total: number;
  count: number;
  totalPages: number;
  currentPage: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
};

export default function CommunityPage() {
  const [activeTab, setActiveTab] = useState("feed")
  const [selectedMember, setSelectedMember] = useState<string | null>(null)
  const [members, setMembers] = useState<Member[]>([])
  const [posts, setPosts] = useState<Post[]>([])
  const [initiatives, setInitiatives] = useState<CommunityInitiative[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // Pagination state
  const [pagination, setPagination] = useState({
    members: {
      currentPage: 1,
      totalPages: 1,
      total: 0,
      limit: 10,
      hasNextPage: false,
      hasPrevPage: false
    },
    posts: {
      currentPage: 1,
      totalPages: 1,
      total: 0,
      limit: 10,
      hasNextPage: false,
      hasPrevPage: false
    },
    initiatives: {
      currentPage: 1,
      totalPages: 1,
      total: 0,
      limit: 10,
      hasNextPage: false,
      hasPrevPage: false
    }
  })

  // Function to fetch community data with filters and pagination
  const fetchCommunityData = async () => {
    setIsLoading(true)
    try {
      // Build query parameters
      let page = 1;
      let limit = 10;

      // Get the correct pagination values based on active tab
      if (activeTab === 'members' && pagination.members) {
        page = pagination.members.currentPage;
        limit = pagination.members.limit;
      } else if (activeTab === 'feed' && pagination.posts) {
        page = pagination.posts.currentPage;
        limit = pagination.posts.limit;
      } else if (activeTab === 'initiatives' && pagination.initiatives) {
        page = pagination.initiatives.currentPage;
        limit = pagination.initiatives.limit;
      }

      const queryParams = new URLSearchParams({
        tab: activeTab,
        page: page.toString(),
        limit: limit.toString()
      })

      // Add search term if any
      if (searchTerm) {
        queryParams.append('search', searchTerm)
      }

      console.log('Fetching community data with params:', queryParams.toString())

      // Use the API utility for consistent handling
      const response = await api.get(`/api/community?${queryParams.toString()}`, false)

      if (!response || !response.success) {
        throw new Error('Failed to fetch community data')
      }

      // Update state with fetched data
      setMembers(response.members || [])
      setPosts(response.posts || [])
      setInitiatives(response.initiatives || [])

      // Update pagination information
      if (response.pagination) {
        setPagination({
          members: response.pagination.members || pagination.members,
          posts: response.pagination.posts || pagination.posts,
          initiatives: response.pagination.initiatives || pagination.initiatives
        })
      }

      // Select the first member by default if available and none is selected
      if (activeTab === 'members' && response.members && response.members.length > 0 && !selectedMember) {
        setSelectedMember(response.members[0].id)
      } else if (activeTab === 'members' && response.members && response.members.length > 0 && selectedMember) {
        // Check if the selected member is still in the results
        const stillExists = response.members.some(member => member.id === selectedMember)
        if (!stillExists) {
          setSelectedMember(response.members[0].id)
        }
      }

      setError(null)
    } catch (err) {
      console.error('Error fetching community data:', err)
      setError('An error occurred while fetching data')
    } finally {
      setIsLoading(false)
    }
  }

  // Effect to fetch data when tab or search term changes
  useEffect(() => {
    fetchCommunityData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, searchTerm])

  // Effect to fetch data when pagination changes
  useEffect(() => {
    if (activeTab === 'members') {
      fetchCommunityData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.members.currentPage, activeTab])

  useEffect(() => {
    if (activeTab === 'feed') {
      fetchCommunityData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.posts.currentPage, activeTab])

  useEffect(() => {
    if (activeTab === 'initiatives') {
      fetchCommunityData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.initiatives.currentPage, activeTab])

  // Function to handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Reset selected member when changing tabs
    if (value !== 'members') {
      setSelectedMember(null)
    }
  }

  // Function to handle page change
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({
      ...prev,
      [activeTab]: {
        ...prev[activeTab],
        currentPage: newPage
      }
    }))
  }

  const getMemberById = (id: string) => {
    return members.find((member) => member.id === id)
  }

  return (
    <div className="bg-[#f5f5f5] min-h-screen" dir="rtl">
      <div className="bg-[#0a8754] text-white py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">مجتمع المبادرات</h1>
          <p className="text-lg opacity-90 mb-8">
            تواصل مع أعضاء المجتمع، تعرف على المبادرين النشطين، وشارك تجاربك وأفكارك
          </p>

          <div className="relative max-w-xl mx-auto">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="ابحث عن أعضاء أو منشورات..."
              className="pl-10 bg-white text-black border-0 h-12 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 md:p-8">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full mb-8">
          <TabsList className="bg-white">
            <TabsTrigger value="feed">المنشورات</TabsTrigger>
            <TabsTrigger value="members">الأعضاء</TabsTrigger>
            <TabsTrigger value="initiatives">المبادرات النشطة</TabsTrigger>
          </TabsList>

          <TabsContent value="feed" className="mt-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="ml-2">جاري التحميل...</span>
              </div>
            ) : error ? (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 className="text-xl font-bold mb-4">شارك منشوراً</h2>
                    <Textarea
                      placeholder="شارك أفكارك، تجاربك، أو أخبار مبادرتك مع المجتمع..."
                      className="mb-4 min-h-[100px]"
                    />
                    <div className="flex justify-between items-center">
                      <Button variant="outline" className="flex items-center gap-2">
                        <ImageIcon size={18} />
                        إضافة صورة
                      </Button>
                      <Button className="bg-[#0a8754] hover:bg-[#097548]">نشر</Button>
                    </div>
                  </div>

                  {posts.length === 0 ? (
                    <div className="text-center py-10 bg-white rounded-lg shadow-md">
                      <p className="text-gray-500">لا توجد منشورات مطابقة للبحث</p>
                    </div>
                  ) : (
                    <>
                      <div className="space-y-6">
                        {posts.map((post) => (
                          <Card key={post.id} className="overflow-hidden">
                            <CardHeader className="pb-3">
                              <div className="flex justify-between items-start">
                                <div className="flex items-center gap-3">
                                  <Avatar>
                                    <AvatarImage src={post.author.avatar} alt={post.author.name} />
                                    <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <CardTitle className="text-base">{post.author.name}</CardTitle>
                                    <CardDescription>{post.date}</CardDescription>
                                  </div>
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent className="pb-3">
                              <p className="text-gray-700 whitespace-pre-line">{post.content}</p>
                              {post.image && (
                                <div className="mt-3 rounded-md overflow-hidden">
                                  <img src={post.image || "/placeholder.svg"} alt="صورة المنشور" className="w-full h-auto" />
                                </div>
                              )}
                            </CardContent>
                            <CardFooter className="pt-0">
                              <div className="flex items-center gap-4 w-full">
                                <Button variant="ghost" className="flex items-center gap-1">
                                  <ThumbsUp size={16} />
                                  <span>{post.likes}</span>
                                </Button>
                                <Button variant="ghost" className="flex items-center gap-1">
                                  <MessageSquare size={16} />
                                  <span>{post.comments}</span>
                                </Button>
                                <div className="flex-grow"></div>
                                <Button variant="ghost" size="sm">
                                  مشاركة
                                </Button>
                              </div>
                            </CardFooter>
                          </Card>
                        ))}
                      </div>

                      {/* Pagination controls */}
                      {pagination.posts.totalPages > 1 && (
                        <div className="flex justify-center mt-8 gap-2">
                          <Button
                            variant="outline"
                            onClick={() => handlePageChange(pagination.posts.currentPage - 1)}
                            disabled={!pagination.posts.hasPrevPage || isLoading}
                          >
                            <ChevronRight className="h-4 w-4 ml-2" />
                            السابق
                          </Button>
                          <div className="flex items-center px-4">
                            <span className="text-sm">
                              الصفحة {pagination.posts.currentPage} من {pagination.posts.totalPages}
                            </span>
                          </div>
                          <Button
                            variant="outline"
                            onClick={() => handlePageChange(pagination.posts.currentPage + 1)}
                            disabled={!pagination.posts.hasNextPage || isLoading}
                          >
                            التالي
                            <ChevronLeft className="h-4 w-4 mr-2" />
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>

                <div className="lg:col-span-1">
                  <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 className="text-xl font-bold mb-4">الأعضاء النشطون</h2>
                    {members.length === 0 ? (
                      <div className="text-center py-4">
                        <p className="text-gray-500">لا يوجد أعضاء نشطون حالياً</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {members.slice(0, 3).map((member) => (
                          <div key={member.id} className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={member.avatar} alt={member.name} />
                              <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{member.name}</p>
                              <p className="text-sm text-gray-500">{member.username}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="mt-4 text-center">
                      <Button variant="link" className="text-[#0a8754]" onClick={() => handleTabChange("members")}>
                        عرض جميع الأعضاء
                      </Button>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-6">
                    <h2 className="text-xl font-bold mb-4">المبادرات الشائعة</h2>
                    {initiatives.length === 0 ? (
                      <div className="text-center py-4">
                        <p className="text-gray-500">لا توجد مبادرات شائعة حالياً</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {initiatives.slice(0, 3).map((initiative) => (
                          <div key={initiative.id} className="p-3 bg-gray-50 rounded-lg">
                            <h3 className="font-medium text-[#0a8754]">{initiative.title}</h3>
                            <p className="text-sm text-gray-500 mb-2">{initiative.supportCount} داعم • {initiative.commentCount} تعليق</p>
                            <Link href={`/initiatives/${initiative.id}`}>
                              <Button variant="link" className="text-[#0a8754] p-0 h-auto">
                                عرض المبادرة
                              </Button>
                            </Link>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="members" className="mt-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="ml-2">جاري التحميل...</span>
              </div>
            ) : error ? (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-1">
                  <div className="bg-white rounded-lg shadow-md p-4">
                    <h2 className="text-xl font-bold mb-4">الأعضاء</h2>
                    {members.length === 0 ? (
                      <div className="text-center py-10">
                        <p className="text-gray-500">لا يوجد أعضاء مطابقين للبحث</p>
                      </div>
                    ) : (
                      <>
                        <div className="space-y-3">
                          {members.map((member) => (
                            <div
                              key={member.id}
                              className={`p-3 rounded-lg cursor-pointer transition-colors ${
                                selectedMember === member.id ? "bg-[#0a8754] text-white" : "bg-gray-50 hover:bg-gray-100"
                              }`}
                              onClick={() => setSelectedMember(member.id)}
                            >
                              <div className="flex items-center gap-3">
                                <Avatar>
                                  <AvatarImage src={member.avatar} alt={member.name} />
                                  <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className={`font-medium ${selectedMember === member.id ? "text-white" : ""}`}>
                                    {member.name}
                                  </p>
                                  <p
                                    className={`text-sm ${selectedMember === member.id ? "text-white opacity-80" : "text-gray-500"}`}
                                  >
                                    {member.username}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Pagination controls */}
                        {pagination.members.totalPages > 1 && (
                          <div className="flex justify-center mt-4 gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePageChange(pagination.members.currentPage - 1)}
                              disabled={!pagination.members.hasPrevPage || isLoading}
                            >
                              <ChevronRight className="h-4 w-4 ml-1" />
                              السابق
                            </Button>
                            <div className="flex items-center px-2">
                              <span className="text-xs">
                                {pagination.members.currentPage} / {pagination.members.totalPages}
                              </span>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePageChange(pagination.members.currentPage + 1)}
                              disabled={!pagination.members.hasNextPage || isLoading}
                            >
                              التالي
                              <ChevronLeft className="h-4 w-4 mr-1" />
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>

                <div className="lg:col-span-2">
                  {selectedMember ? (
                    <div className="bg-white rounded-lg shadow-md p-6">
                      {(() => {
                        const member = getMemberById(selectedMember)
                        if (!member) return null

                        return (
                          <>
                            <div className="flex flex-col md:flex-row gap-6 mb-6">
                              <div className="flex flex-col items-center">
                                <Avatar className="h-24 w-24">
                                  <AvatarImage src={member.avatar} alt={member.name} />
                                  <AvatarFallback className="text-2xl">{member.name.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <div className="mt-4 text-center">
                                  <Button className="bg-[#0a8754] hover:bg-[#097548]">متابعة</Button>
                                </div>
                              </div>

                              <div className="flex-grow">
                                <h2 className="text-2xl font-bold text-[#0a8754] mb-1">{member.name}</h2>
                                <p className="text-gray-500 mb-3">{member.username}</p>

                                <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
                                  <div className="flex items-center gap-1">
                                    <MapPin size={16} />
                                    <span>{member.location}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Calendar size={16} />
                                    <span>انضم في {member.joinDate}</span>
                                  </div>
                                </div>

                                <p className="text-gray-700 mb-4">{member.bio}</p>

                                <div className="flex flex-wrap gap-2 mb-4">
                                  {member.badges.map((badge, index) => (
                                    <Badge key={index} className="bg-[#0a8754] hover:bg-[#0a8754]">
                                      <Award size={14} className="mr-1" />
                                      {badge}
                                    </Badge>
                                  ))}
                                </div>

                                <div className="flex flex-wrap gap-6 text-center">
                                  <div>
                                    <p className="text-2xl font-bold text-[#0a8754]">{member.initiatives}</p>
                                    <p className="text-sm text-gray-500">مبادرات</p>
                                  </div>
                                  <div>
                                    <p className="text-2xl font-bold text-[#0a8754]">{member.contributions}</p>
                                    <p className="text-sm text-gray-500">مساهمات</p>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div>
                              <h3 className="text-lg font-semibold mb-4">النشاط الأخير</h3>
                              {member.recentActivity.length === 0 ? (
                                <div className="text-center py-4 bg-gray-50 rounded-lg">
                                  <p className="text-gray-500">لا يوجد نشاط حديث</p>
                                </div>
                              ) : (
                                <div className="space-y-4">
                                  {member.recentActivity.map((activity, index) => (
                                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                      <div className="flex items-center gap-2 mb-1">
                                        {activity.type === "initiative" && <Users size={16} className="text-[#0a8754]" />}
                                        {activity.type === "comment" && (
                                          <MessageSquare size={16} className="text-[#0a8754]" />
                                        )}
                                        {activity.type === "support" && <ThumbsUp size={16} className="text-[#0a8754]" />}
                                        <p className="font-medium">{activity.title}</p>
                                      </div>
                                      <p className="text-sm text-gray-500">{activity.date}</p>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </>
                        )
                      })()}
                    </div>
                  ) : (
                    <div className="bg-white rounded-lg shadow-md p-6 text-center">
                      <div className="py-12">
                        <h3 className="text-xl font-semibold text-gray-700 mb-2">اختر عضواً من القائمة</h3>
                        <p className="text-gray-500">يرجى اختيار عضو من القائمة على اليمين لعرض ملفه الشخصي</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="initiatives" className="mt-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-green-600" />
                <span className="ml-2">جاري التحميل...</span>
              </div>
            ) : error ? (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : initiatives.length === 0 ? (
              <div className="text-center py-10 bg-white rounded-lg shadow-md">
                <p className="text-gray-500">لا توجد مبادرات مطابقة للبحث</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {initiatives.map((initiative) => (
                    <Card key={initiative.id} className="overflow-hidden">
                      <div className="h-[200px] overflow-hidden">
                        <img
                          src={initiative.image}
                          alt={initiative.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <CardHeader className="pb-2">
                        <Badge className="bg-green-500 mb-2">{initiative.category}</Badge>
                        <CardTitle className="text-xl">
                          <Link href={`/initiatives/${initiative.id}`} className="hover:text-[#0a8754] transition-colors">
                            {initiative.title}
                          </Link>
                        </CardTitle>
                        <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                          <span className="flex items-center gap-1">
                            <Users size={14} />
                            {initiative.supportCount} داعم
                          </span>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="line-clamp-3 text-gray-600">
                          {initiative.description}
                        </CardDescription>
                      </CardContent>
                      <CardFooter>
                        <Link href={`/initiatives/${initiative.id}`}>
                          <Button className="bg-[#0a8754] hover:bg-[#097548]">عرض المبادرة</Button>
                        </Link>
                      </CardFooter>
                    </Card>
                  ))}
                </div>

                {/* Pagination controls */}
                {pagination.initiatives.totalPages > 1 && (
                  <div className="flex justify-center mt-8 gap-2">
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(pagination.initiatives.currentPage - 1)}
                      disabled={!pagination.initiatives.hasPrevPage || isLoading}
                    >
                      <ChevronRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <div className="flex items-center px-4">
                      <span className="text-sm">
                        الصفحة {pagination.initiatives.currentPage} من {pagination.initiatives.totalPages}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(pagination.initiatives.currentPage + 1)}
                      disabled={!pagination.initiatives.hasNextPage || isLoading}
                    >
                      التالي
                      <ChevronLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                )}

                <div className="mt-8 text-center">
                  <Link href="/initiatives">
                    <Button className="bg-[#0a8754] hover:bg-[#097548]">عرض جميع المبادرات</Button>
                  </Link>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

// Missing component definition
function Textarea({ placeholder, className, value, onChange }: any) {
  return (
    <textarea
      placeholder={placeholder}
      className={`w-full rounded-md border border-gray-300 p-3 ${className}`}
      value={value}
      onChange={onChange}
    />
  )
}

function ImageIcon(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
      <circle cx="9" cy="9" r="2" />
      <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
    </svg>
  )
}

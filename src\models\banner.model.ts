import mongoose, { Document, Schema } from 'mongoose';

export interface IBanner extends Document {
  image: string;
  mainText: string;
  subText: string;
  mainTextColor: string;
  subTextColor: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const BannerSchema: Schema = new Schema(
  {
    image: {
      type: String,
      required: [true, 'Banner image is required'],
    },
    mainText: {
      type: String,
      required: [true, 'Main text is required'],
      maxlength: [100, 'Main text cannot exceed 100 characters'],
    },
    subText: {
      type: String,
      required: [true, 'Sub text is required'],
      maxlength: [200, 'Sub text cannot exceed 200 characters'],
    },
    mainTextColor: {
      type: String,
      default: '#FFFFFF', // Default white
      validate: {
        validator: (value: string) => /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value),
        message: 'Main text color must be a valid hex color code',
      },
    },
    subTextColor: {
      type: String,
      default: '#FFFFFF', // Default white
      validate: {
        validator: (value: string) => /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value),
        message: 'Sub text color must be a valid hex color code',
      },
    },
    order: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create and export the model
export const Banner = mongoose.models.Banner || mongoose.model<IBanner>('Banner', BannerSchema);

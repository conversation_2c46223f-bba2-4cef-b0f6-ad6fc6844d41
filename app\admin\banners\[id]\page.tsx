"use client"

import { useEffect, useState } from "react"
import { BannerForm } from "../banner-form"
import { api } from "@/lib/api"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface EditBannerPageProps {
  params: {
    id: string
  }
}

export default function EditBannerPage({ params }: EditBannerPageProps) {
  const [banner, setBanner] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const fetchBanner = async () => {
      try {
        setIsLoading(true)
        const response = await api.get(`/api/banners/${params.id}`, true)

        if (response.success && response.banner) {
          setBanner({
            id: response.banner._id,
            image: response.banner.image,
            mainText: response.banner.mainText,
            subText: response.banner.subText,
            mainTextColor: response.banner.mainTextColor,
            subTextColor: response.banner.subTextColor,
            order: response.banner.order,
            isActive: response.banner.isActive
          })
        } else {
          setError(response.message || "Failed to fetch banner")
        }
      } catch (err: any) {
        console.error("Error fetching banner:", err)
        setError(err.message || "An error occurred while fetching the banner")
      } finally {
        setIsLoading(false)
      }
    }

    if (params.id) {
      fetchBanner()
    }
  }, [params.id])

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-64 w-full" />
        <div className="space-y-2">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!banner) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>Banner not found</AlertDescription>
      </Alert>
    )
  }

  return (
    <BannerForm initialData={banner} />
  )
}

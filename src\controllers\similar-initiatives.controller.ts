import type { Request, Response, NextFunction } from "express"
import mongoose from "mongoose"
import { Initiative } from "../models"
import { createError } from "../utils/error"

/**
 * Get similar initiatives based on category, author, and tags
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const getSimilarInitiatives = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const limit = parseInt(req.query.limit as string) || 3

    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return next(createError(400, "Invalid initiative ID"))
    }

    // Get the initiative
    const initiative = await Initiative.findById(id)

    // Check if initiative exists
    if (!initiative) {
      return next(createError(404, "Initiative not found"))
    }

    // Build query for similar initiatives
    // We want to find initiatives that:
    // 1. Are not the current initiative
    // 2. Are active
    // 3. Match either the same category, author, or have similar tags
    const query = {
      _id: { $ne: id }, // Not the current initiative
      status: "active", // Only active initiatives
      $or: [
        { category: initiative.category }, // Same category
        { author: initiative.author }, // Same author
        // If there are tags, find initiatives with at least one matching tag
        ...(initiative.tags && initiative.tags.length > 0
          ? [{ tags: { $in: initiative.tags } }]
          : []),
      ],
    }

    // Find similar initiatives
    const similarInitiatives = await Initiative.find(query)
      .populate("author", "name username avatar")
      .populate("category", "name arabicName color")
      .sort({ supportCount: -1 }) // Sort by support count (most supported first)
      .limit(limit)

    // Return success response
    res.status(200).json({
      success: true,
      count: similarInitiatives.length,
      initiatives: similarInitiatives,
    })
  } catch (error) {
    next(error)
  }
}

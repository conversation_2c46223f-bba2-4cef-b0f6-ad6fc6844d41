/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Désactiver le mode strict pour éviter les doubles rendus
  // Désactiver le cache en développement
  onDemandEntries: {
    // période (en ms) où les pages compilées sont conservées en mémoire
    maxInactiveAge: 60 * 1000, // Augmenter à 60 secondes
    // nombre de pages à conserver en mémoire
    pagesBufferLength: 5, // Augmenter le nombre de pages en mémoire
  },
  images: {
    domains: ['localhost', 'placeholder.com', 'via.placeholder.com'],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5000',
        pathname: '/uploads/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5000',
        pathname: '/api/uploads/**',
      },
    ],
    unoptimized: true, // Désactiver l'optimisation d'image pour éviter les problèmes CORS
  },
  experimental: {
    serverActions: true,
  },
  // Désactiver la compression pour le développement
  compress: false,
  // Augmenter la taille maximale des pages
  staticPageGenerationTimeout: 120,
  // Désactiver le cache des modules webpack
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      config.cache = false;
    }
    return config;
  },
}

module.exports = nextConfig

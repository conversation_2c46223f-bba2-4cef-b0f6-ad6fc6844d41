import mongoose, { Schema, Document } from "mongoose";

export interface IPermission extends Document {
  name: string;
  description: string;
  code: string;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

const permissionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    category: {
      type: String,
      required: true,
      enum: ["initiatives", "users", "comments", "reports", "admin", "system"],
      default: "system",
    },
  },
  {
    timestamps: true,
  }
);

export const Permission = mongoose.model<IPermission>("Permission", permissionSchema);
